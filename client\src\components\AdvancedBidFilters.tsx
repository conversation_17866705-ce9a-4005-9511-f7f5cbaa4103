import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Separator } from "@/components/ui/separator";
import { Search, Filter, ChevronDown, ChevronUp, Package, Target, DollarSign, Calendar, X, Settings } from "lucide-react";

export interface AdvancedFilterState {
  // Basic filters
  searchTerm: string;
  statusFilter: string;
  
  // Advanced search
  searchInLineItems: boolean;
  searchInScope: boolean;
  searchInCostCodes: boolean;
  lineItemSearch: string;
  scopeSearch: string;
  costCodeSearch: string;
  
  // Data quality filters
  dataQualityRange: [number, number];
  hasStructuredData: boolean | null; // null = all, true = with structured data, false = without
  processingMethod: string; // all, structured_form, ai_extracted
  
  // Amount filters
  amountRange: [number, number];
  hasAmount: boolean | null;
  
  // Date filters
  submittedDateRange: { start: string; end: string };
  
  // Category filters
  costCodeCategories: string[];
  tradeCategories: string[];
  
  // Line item filters
  minLineItems: number;
  maxLineItems: number;
  hasScopeDefinition: boolean | null;
  
  // Advanced criteria
  confidenceThreshold: number;
  includeIncomplete: boolean;
}

interface AdvancedBidFiltersProps {
  filters: AdvancedFilterState;
  onFiltersChange: (filters: AdvancedFilterState) => void;
  availableCategories: string[];
  availableTradeCategories: string[];
  totalBids: number;
  filteredCount: number;
}

export default function AdvancedBidFilters({
  filters,
  onFiltersChange,
  availableCategories,
  availableTradeCategories,
  totalBids,
  filteredCount
}: AdvancedBidFiltersProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [activeFilterCount, setActiveFilterCount] = useState(0);

  // Count active filters
  const updateActiveFilterCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.statusFilter !== 'all') count++;
    if (filters.lineItemSearch) count++;
    if (filters.scopeSearch) count++;
    if (filters.costCodeSearch) count++;
    if (filters.hasStructuredData !== null) count++;
    if (filters.processingMethod !== 'all') count++;
    if (filters.hasAmount !== null) count++;
    if (filters.costCodeCategories.length > 0) count++;
    if (filters.tradeCategories.length > 0) count++;
    if (filters.hasScopeDefinition !== null) count++;
    if (filters.dataQualityRange[0] > 0 || filters.dataQualityRange[1] < 100) count++;
    return count;
  };

  const handleFilterChange = (key: keyof AdvancedFilterState, value: any) => {
    const newFilters = { ...filters, [key]: value };
    onFiltersChange(newFilters);
    setActiveFilterCount(updateActiveFilterCount());
  };

  const clearAllFilters = () => {
    const defaultFilters: AdvancedFilterState = {
      searchTerm: '',
      statusFilter: 'all',
      searchInLineItems: false,
      searchInScope: false,
      searchInCostCodes: false,
      lineItemSearch: '',
      scopeSearch: '',
      costCodeSearch: '',
      dataQualityRange: [0, 100],
      hasStructuredData: null,
      processingMethod: 'all',
      amountRange: [0, 1000000],
      hasAmount: null,
      submittedDateRange: { start: '', end: '' },
      costCodeCategories: [],
      tradeCategories: [],
      minLineItems: 0,
      maxLineItems: 1000,
      hasScopeDefinition: null,
      confidenceThreshold: 0,
      includeIncomplete: true,
    };
    onFiltersChange(defaultFilters);
    setActiveFilterCount(0);
  };

  const getFilterPresets = () => [
    {
      name: "High Quality Bids",
      filters: { ...filters, dataQualityRange: [80, 100] as [number, number], hasStructuredData: true, confidenceThreshold: 80 }
    },
    {
      name: "Structured Data Only",
      filters: { ...filters, hasStructuredData: true, processingMethod: 'structured_form' }
    },
    {
      name: "Recent Submissions",
      filters: { ...filters, submittedDateRange: { start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], end: '' } }
    },
    {
      name: "Complete Bids",
      filters: { ...filters, hasAmount: true, hasScopeDefinition: true, minLineItems: 1 }
    }
  ];

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Advanced Filters
            </CardTitle>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilterCount} active
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
          >
            {isAdvancedOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <CardDescription>
          Showing {filteredCount} of {totalBids} bids
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Basic Filters - Always Visible */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="search">Search Projects</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search by project title or location..."
                value={filters.searchTerm}
                onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                className="pl-9"
              />
            </div>
          </div>
          
          <div>
            <Label>Status</Label>
            <Select value={filters.statusFilter} onValueChange={(value) => handleFilterChange('statusFilter', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="under_review">Under Review</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Filter Presets */}
        <div className="flex flex-wrap gap-2">
          <Label className="text-xs text-muted-foreground">Quick Filters:</Label>
          {getFilterPresets().map((preset, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={() => onFiltersChange(preset.filters)}
              className="h-6 text-xs"
            >
              {preset.name}
            </Button>
          ))}
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="h-6 text-xs text-red-600 hover:text-red-700"
            >
              <X className="h-3 w-3 mr-1" />
              Clear All
            </Button>
          )}
        </div>

        {/* Advanced Filters - Collapsible */}
        <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
          <CollapsibleContent className="space-y-6">
            <Separator />

            {/* Advanced Search */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                <Label className="font-medium">Advanced Search</Label>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="searchLineItems"
                      checked={filters.searchInLineItems}
                      onCheckedChange={(checked) => handleFilterChange('searchInLineItems', checked)}
                    />
                    <Label htmlFor="searchLineItems" className="text-sm">Search in Line Items</Label>
                  </div>
                  {filters.searchInLineItems && (
                    <Input
                      placeholder="Search line item descriptions..."
                      value={filters.lineItemSearch}
                      onChange={(e) => handleFilterChange('lineItemSearch', e.target.value)}
                      className="text-sm"
                    />
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="searchScope"
                      checked={filters.searchInScope}
                      onCheckedChange={(checked) => handleFilterChange('searchInScope', checked)}
                    />
                    <Label htmlFor="searchScope" className="text-sm">Search in Scope</Label>
                  </div>
                  {filters.searchInScope && (
                    <Input
                      placeholder="Search scope definitions..."
                      value={filters.scopeSearch}
                      onChange={(e) => handleFilterChange('scopeSearch', e.target.value)}
                      className="text-sm"
                    />
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="searchCostCodes"
                      checked={filters.searchInCostCodes}
                      onCheckedChange={(checked) => handleFilterChange('searchInCostCodes', checked)}
                    />
                    <Label htmlFor="searchCostCodes" className="text-sm">Search in Cost Codes</Label>
                  </div>
                  {filters.searchInCostCodes && (
                    <Input
                      placeholder="Search cost codes..."
                      value={filters.costCodeSearch}
                      onChange={(e) => handleFilterChange('costCodeSearch', e.target.value)}
                      className="text-sm"
                    />
                  )}
                </div>
              </div>
            </div>

            <Separator />

            {/* Data Quality Filters */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                <Label className="font-medium">Data Quality</Label>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm">Quality Score Range: {filters.dataQualityRange[0]}% - {filters.dataQualityRange[1]}%</Label>
                  <Slider
                    value={filters.dataQualityRange}
                    onValueChange={(value) => handleFilterChange('dataQualityRange', value)}
                    max={100}
                    step={5}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">Structured Data</Label>
                  <Select 
                    value={filters.hasStructuredData === null ? 'all' : filters.hasStructuredData.toString()} 
                    onValueChange={(value) => handleFilterChange('hasStructuredData', value === 'all' ? null : value === 'true')}
                  >
                    <SelectTrigger className="text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Bids</SelectItem>
                      <SelectItem value="true">With Structured Data</SelectItem>
                      <SelectItem value="false">Document Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label className="text-sm">Processing Method</Label>
                <Select value={filters.processingMethod} onValueChange={(value) => handleFilterChange('processingMethod', value)}>
                  <SelectTrigger className="text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Methods</SelectItem>
                    <SelectItem value="structured_form">Structured Form</SelectItem>
                    <SelectItem value="ai_extracted">AI Extracted</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            {/* Amount and Date Filters */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                <Label className="font-medium">Amount & Timeline</Label>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm">Bid Amount</Label>
                  <Select 
                    value={filters.hasAmount === null ? 'all' : filters.hasAmount.toString()} 
                    onValueChange={(value) => handleFilterChange('hasAmount', value === 'all' ? null : value === 'true')}
                  >
                    <SelectTrigger className="text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Bids</SelectItem>
                      <SelectItem value="true">With Amount</SelectItem>
                      <SelectItem value="false">No Amount</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">Confidence Threshold: {filters.confidenceThreshold}%</Label>
                  <Slider
                    value={[filters.confidenceThreshold]}
                    onValueChange={(value) => handleFilterChange('confidenceThreshold', value[0])}
                    max={100}
                    step={5}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Category Filters */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <Label className="font-medium">Categories</Label>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableCategories.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm">Cost Code Categories</Label>
                    <div className="max-h-32 overflow-y-auto space-y-1 border rounded p-2">
                      {availableCategories.map(category => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category}`}
                            checked={filters.costCodeCategories.includes(category)}
                            onCheckedChange={(checked) => {
                              const newCategories = checked 
                                ? [...filters.costCodeCategories, category]
                                : filters.costCodeCategories.filter(c => c !== category);
                              handleFilterChange('costCodeCategories', newCategories);
                            }}
                          />
                          <Label htmlFor={`category-${category}`} className="text-xs">{category}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label className="text-sm">Scope Definition</Label>
                  <Select 
                    value={filters.hasScopeDefinition === null ? 'all' : filters.hasScopeDefinition.toString()} 
                    onValueChange={(value) => handleFilterChange('hasScopeDefinition', value === 'all' ? null : value === 'true')}
                  >
                    <SelectTrigger className="text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Bids</SelectItem>
                      <SelectItem value="true">With Scope Definition</SelectItem>
                      <SelectItem value="false">No Scope Definition</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}