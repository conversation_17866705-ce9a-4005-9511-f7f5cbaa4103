<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contractor Profile Entry Form</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .header h1 {
            color: #2d3748;
            font-size: 28px;
            margin-bottom: 8px;
        }

        .header p {
            color: #718096;
            font-size: 16px;
        }

        .form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .category-section {
            border-bottom: 1px solid #e2e8f0;
            padding: 24px;
        }

        .category-section:last-child {
            border-bottom: none;
        }

        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            cursor: pointer;
            padding: 8px 0;
        }

        .category-header:hover {
            background-color: #f7fafc;
            margin: -8px -8px 20px -8px;
            padding: 8px;
            border-radius: 6px;
        }

        .category-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-right: 12px;
        }

        .category-badge {
            background: #e2e8f0;
            color: #4a5568;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .toggle-icon {
            margin-left: auto;
            transition: transform 0.3s ease;
            color: #718096;
        }

        .category-section.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .category-section.collapsed .form-grid {
            display: none;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
            font-size: 14px;
        }

        .required {
            color: #dc2626;
        }

        .form-input, .form-select, .form-textarea {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            background: white;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #3b82f6;
        }

        .checkbox-item label {
            font-size: 14px;
            color: #374151;
            cursor: pointer;
        }

        .file-upload {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: border-color 0.2s, background-color 0.2s;
            cursor: pointer;
        }

        .file-upload:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .file-upload-text {
            color: #6b7280;
            font-size: 14px;
            margin-top: 8px;
        }

        .form-description {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            padding: 24px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .field-count {
            font-size: 12px;
            color: #6b7280;
            margin-left: 8px;
        }

        .progress-bar {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Contractor Profile Entry Form</h1>
            <p>Complete your contractor profile to participate in our prequalification process</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <form class="form-container" id="contractorForm">
            <!-- Business Identity & Contact -->
            <div class="category-section" data-category="business">
                <div class="category-header" onclick="toggleCategory('business')">
                    <h2 class="category-title">Business Identity & Contact</h2>
                    <span class="category-badge">8 fields</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Company Name <span class="required">*</span></label>
                        <input type="text" class="form-input" name="company_name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Legal Structure</label>
                        <select class="form-select" name="legal_structure">
                            <option value="">Select structure</option>
                            <option value="corporation">Corporation</option>
                            <option value="llc">LLC</option>
                            <option value="partnership">Partnership</option>
                            <option value="sole_proprietorship">Sole Proprietorship</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Tax ID/EIN <span class="required">*</span></label>
                        <input type="text" class="form-input" name="tax_id" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">DBA (If applicable)</label>
                        <input type="text" class="form-input" name="dba">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Primary Business Address <span class="required">*</span></label>
                        <input type="text" class="form-input" name="primary_address" required>
                        <div class="form-description">No P.O. boxes allowed</div>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Mailing Address (if different)</label>
                        <input type="text" class="form-input" name="mailing_address">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Primary Contact Name <span class="required">*</span></label>
                        <input type="text" class="form-input" name="primary_contact_name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Primary Contact Email <span class="required">*</span></label>
                        <input type="email" class="form-input" name="primary_contact_email" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Primary Contact Phone <span class="required">*</span></label>
                        <input type="tel" class="form-input" name="primary_contact_phone" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Primary Contact Title</label>
                        <input type="text" class="form-input" name="primary_contact_title">
                    </div>
                </div>
            </div>

            <!-- Classification & Capability Tags -->
            <div class="category-section" data-category="classification">
                <div class="category-header" onclick="toggleCategory('classification')">
                    <h2 class="category-title">Classification & Capability</h2>
                    <span class="category-badge">5 fields</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="form-grid">
                    <div class="form-group full-width">
                        <label class="form-label">Trade/Specialty Types <span class="required">*</span></label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="general_contractor" name="trades[]" value="general_contractor">
                                <label for="general_contractor">General Contractor</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="electrician" name="trades[]" value="electrician">
                                <label for="electrician">Electrical</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="plumber" name="trades[]" value="plumber">
                                <label for="plumber">Plumbing</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="hvac" name="trades[]" value="hvac">
                                <label for="hvac">HVAC</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="sitework" name="trades[]" value="sitework">
                                <label for="sitework">Site Work/Excavation</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="concrete" name="trades[]" value="concrete">
                                <label for="concrete">Concrete</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="masonry" name="trades[]" value="masonry">
                                <label for="masonry">Masonry</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="structural_steel" name="trades[]" value="structural_steel">
                                <label for="structural_steel">Structural Steel</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="carpentry" name="trades[]" value="carpentry">
                                <label for="carpentry">Carpentry</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="roofing" name="trades[]" value="roofing">
                                <label for="roofing">Roofing</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="waterproofing" name="trades[]" value="waterproofing">
                                <label for="waterproofing">Waterproofing</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="insulation" name="trades[]" value="insulation">
                                <label for="insulation">Insulation</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="drywall" name="trades[]" value="drywall">
                                <label for="drywall">Drywall</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="flooring" name="trades[]" value="flooring">
                                <label for="flooring">Flooring</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="painting" name="trades[]" value="painting">
                                <label for="painting">Painting</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="fire_protection" name="trades[]" value="fire_protection">
                                <label for="fire_protection">Fire Protection</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="security_systems" name="trades[]" value="security_systems">
                                <label for="security_systems">Security Systems</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="landscaping" name="trades[]" value="landscaping">
                                <label for="landscaping">Landscaping</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="asphalt_paving" name="trades[]" value="asphalt_paving">
                                <label for="asphalt_paving">Asphalt/Paving</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="surveying" name="trades[]" value="surveying">
                                <label for="surveying">Surveying</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="environmental" name="trades[]" value="environmental">
                                <label for="environmental">Environmental Services</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="demolition" name="trades[]" value="demolition">
                                <label for="demolition">Demolition</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="utilities" name="trades[]" value="utilities">
                                <label for="utilities">Utilities</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="telecommunications" name="trades[]" value="telecommunications">
                                <label for="telecommunications">Telecommunications</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="glazing" name="trades[]" value="glazing">
                                <label for="glazing">Glazing/Windows</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="metal_fabrication" name="trades[]" value="metal_fabrication">
                                <label for="metal_fabrication">Metal Fabrication</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="elevator" name="trades[]" value="elevator">
                                <label for="elevator">Elevator/Escalator</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="architectural_millwork" name="trades[]" value="architectural_millwork">
                                <label for="architectural_millwork">Architectural Millwork</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="specialty_other" name="trades[]" value="specialty_other">
                                <label for="specialty_other">Other Specialty</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Union Status</label>
                        <select class="form-select" name="union_status">
                            <option value="">Select status</option>
                            <option value="union">Union</option>
                            <option value="non_union">Non-Union</option>
                            <option value="open_shop">Open Shop</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Union Affiliations</label>
                        <input type="text" class="form-input" name="union_affiliations">
                        <div class="form-description">List specific local or national union affiliations</div>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Certification Designations</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="mbe" name="certifications[]" value="mbe">
                                <label for="mbe">MBE (Minority Business Enterprise)</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="wbe" name="certifications[]" value="wbe">
                                <label for="wbe">WBE (Women Business Enterprise)</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="vbe" name="certifications[]" value="vbe">
                                <label for="vbe">VBE (Veteran Business Enterprise)</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="dbe" name="certifications[]" value="dbe">
                                <label for="dbe">DBE (Disadvantaged Business Enterprise)</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="sbe" name="certifications[]" value="sbe">
                                <label for="sbe">SBE (Small Business Enterprise)</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Service Areas</label>
                        <textarea class="form-textarea" name="service_areas" placeholder="List geographic coverage by Country, State, postal codes"></textarea>
                    </div>
                </div>
            </div>

            <!-- Credentials & Compliance -->
            <div class="category-section" data-category="credentials">
                <div class="category-header" onclick="toggleCategory('credentials')">
                    <h2 class="category-title">Credentials & Compliance</h2>
                    <span class="category-badge">10 fields</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Contractor License Number <span class="required">*</span></label>
                        <input type="text" class="form-input" name="license_number" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">License State <span class="required">*</span></label>
                        <input type="text" class="form-input" name="license_state" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">License Expiration Date <span class="required">*</span></label>
                        <input type="date" class="form-input" name="license_expiration" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">License Upload</label>
                        <div class="file-upload" onclick="document.getElementById('licenseFile').click()">
                            <input type="file" id="licenseFile" name="license_file" accept=".pdf,.jpg,.png">
                            <div>📄 Upload License Document</div>
                            <div class="file-upload-text">PDF, JPG, PNG files accepted</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">General Liability Insurance</label>
                        <input type="text" class="form-input" name="general_liability" placeholder="Policy number, carrier, coverage limits">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Workers Compensation Insurance</label>
                        <input type="text" class="form-input" name="workers_comp" placeholder="Policy number, carrier, coverage limits">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Auto Insurance</label>
                        <input type="text" class="form-input" name="auto_insurance" placeholder="Policy number, carrier, coverage limits">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Insurance Certificates Upload</label>
                        <div class="file-upload" onclick="document.getElementById('insuranceFile').click()">
                            <input type="file" id="insuranceFile" name="insurance_files" accept=".pdf" multiple>
                            <div>📄 Upload Insurance Certificates</div>
                            <div class="file-upload-text">PDF files accepted (multiple files allowed)</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Bonding Capacity (Single Project)</label>
                        <input type="number" class="form-input" name="bonding_single" placeholder="Amount in USD">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Bonding Capacity (Aggregate)</label>
                        <input type="number" class="form-input" name="bonding_aggregate" placeholder="Amount in USD">
                    </div>
                    <div class="form-group">
                        <label class="form-label">EMR (Experience Modification Rate)</label>
                        <input type="number" class="form-input" name="emr" step="0.01" placeholder="e.g., 0.95">
                    </div>
                    <div class="form-group">
                        <label class="form-label">OSHA Forms Upload</label>
                        <div class="file-upload" onclick="document.getElementById('oshaFile').click()">
                            <input type="file" id="oshaFile" name="osha_files" accept=".pdf" multiple>
                            <div>📄 Upload OSHA Forms 300/300A</div>
                            <div class="file-upload-text">Last 3 years of records</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial & Reference Data -->
            <div class="category-section" data-category="financial">
                <div class="category-header" onclick="toggleCategory('financial')">
                    <h2 class="category-title">Financial & Reference Data</h2>
                    <span class="category-badge">8 fields</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Bank Reference</label>
                        <input type="text" class="form-input" name="bank_reference" placeholder="Bank name, officer name, phone">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Surety Company Reference</label>
                        <input type="text" class="form-input" name="surety_reference" placeholder="Surety company, contact details">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Financial Statements Upload</label>
                        <div class="file-upload" onclick="document.getElementById('financialFile').click()">
                            <input type="file" id="financialFile" name="financial_files" accept=".pdf" multiple>
                            <div>📄 Upload Financial Statements</div>
                            <div class="file-upload-text">Most recent audited or reviewed statements</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Credit Rating</label>
                        <input type="text" class="form-input" name="credit_rating" placeholder="Moody's/S&P rating or D&B number">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Payment Terms</label>
                        <input type="text" class="form-input" name="payment_terms" placeholder="e.g., Net 30, 5% retainage">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Litigation History</label>
                        <textarea class="form-textarea" name="litigation_history" placeholder="Any claims or judgments in the past 5 years"></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Project References</label>
                        <textarea class="form-textarea" name="project_references" placeholder="List recent major projects: name, owner/GC, contract value, completion date, scope, contact info"></textarea>
                    </div>
                </div>
            </div>

            <!-- Performance & Experience -->
            <div class="category-section" data-category="performance">
                <div class="category-header" onclick="toggleCategory('performance')">
                    <h2 class="category-title">Performance & Experience</h2>
                    <span class="category-badge">6 fields</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Years in Business <span class="required">*</span></label>
                        <input type="number" class="form-input" name="years_in_business" required>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Specialized Experience</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="design_build" name="specializations[]" value="design_build">
                                <label for="design_build">Design/Build</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="bim" name="specializations[]" value="bim">
                                <label for="bim">BIM</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="prefab" name="specializations[]" value="prefab">
                                <label for="prefab">Prefabrication</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="sustainable" name="specializations[]" value="sustainable">
                                <label for="sustainable">Sustainable Construction</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="heavy_civil" name="specializations[]" value="heavy_civil">
                                <label for="heavy_civil">Heavy Civil</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="emergency" name="specializations[]" value="emergency">
                                <label for="emergency">Emergency Work</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Awards & Accreditations</label>
                        <textarea class="form-textarea" name="awards" placeholder="Industry recognitions, ISO certifications, safety awards"></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Environmental & Sustainability Programs</label>
                        <textarea class="form-textarea" name="environmental_programs" placeholder="Environmental protection plans, sustainability certifications"></textarea>
                    </div>
                </div>
            </div>

            <!-- Operational Details -->
            <div class="category-section" data-category="operational">
                <div class="category-header" onclick="toggleCategory('operational')">
                    <h2 class="category-title">Operational Details</h2>
                    <span class="category-badge">4 fields</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Total Workforce Size</label>
                        <input type="number" class="form-input" name="workforce_size" placeholder="Number of employees">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Workforce by Trade</label>
                        <textarea class="form-textarea" name="workforce_breakdown" placeholder="Number of employees by trade, key management team"></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Equipment Owned/Rented</label>
                        <textarea class="form-textarea" name="equipment" placeholder="Inventory of major plant, machinery, and specialized equipment"></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Availability & Lead Times</label>
                        <textarea class="form-textarea" name="availability" placeholder="Typical bid response time, project start windows, current workload"></textarea>
                    </div>
                </div>
            </div>

            <!-- Digital Assets & Document Uploads -->
            <div class="category-section" data-category="digital">
                <div class="category-header" onclick="toggleCategory('digital')">
                    <h2 class="category-title">Digital Assets & Document Uploads</h2>
                    <span class="category-badge">3 fields</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">W-9 Form Upload</label>
                        <div class="file-upload" onclick="document.getElementById('w9File').click()">
                            <input type="file" id="w9File" name="w9_file" accept=".pdf">
                            <div>📄 Upload W-9 Form</div>
                            <div class="file-upload-text">PDF file required</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Project Photos Upload</label>
                        <div class="file-upload" onclick="document.getElementById('projectPhotos').click()">
                            <input type="file" id="projectPhotos" name="project_photos" accept=".jpg,.jpeg,.png" multiple>
                            <div>📸 Upload Project Photos</div>
                            <div class="file-upload-text">JPG, PNG files accepted (multiple files allowed)</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Safety Program Documentation</label>
                        <div class="file-upload" onclick="document.getElementById('safetyDocs').click()">
                            <input type="file" id="safetyDocs" name="safety_docs" accept=".pdf" multiple>
                            <div>📄 Upload Safety Program Documents</div>
                            <div class="file-upload-text">PDF files accepted (multiple files allowed)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Tags & Preferences -->
            <div class="category-section" data-category="custom">
                <div class="category-header" onclick="toggleCategory('custom')">
                    <h2 class="category-title">Custom Tags & Preferences</h2>
                    <span class="category-badge">3 fields</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="form-grid">
                    <div class="form-group full-width">
                        <label class="form-label">Keyword Tags</label>
                        <input type="text" class="form-input" name="keyword_tags" placeholder="e.g., union, emergency, high-voltage, design-build (comma-separated)">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Preferred Project Types</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="residential" name="project_types[]" value="residential">
                                <label for="residential">Residential</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="commercial" name="project_types[]" value="commercial">
                                <label for="commercial">Commercial</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="infrastructure" name="project_types[]