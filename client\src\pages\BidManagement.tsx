import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, Search, Filter, Eye, Calendar, DollarSign, Clock, Star, Building2, Info, Package, BarChart3, Download, Mail, TrendingUp } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import BidDetailsModal from "@/components/BidDetailsModal";
import BidComparisonModal from "@/components/BidComparisonModal";
import { BidStructuredDataIndicator } from "@/components/BidStructuredDataIndicator";

import { useToast } from "@/hooks/use-toast";

interface Bid {
  id: string;
  rfqId: string;
  contractorId: string;
  bidAmount: number | null;
  extractedAmount: number | null;
  timeline: string | null;
  extractedTimeline: string | null;
  scope: string | null;
  extractedScope: string | null;
  conditions: string | null;
  extractedConditions: string | null;
  extractionConfidence: number | null;
  confidenceScore: number;
  status: string;
  submittedAt: string;
  reviewedAt: string | null;
  reviewNotes: string | null;
  aiSummary: string | null;
  aiAnalysis: any;
  notes: string | null;
  totalBidAmount?: number | null;
  lineItemsTotal?: number | null;
  rfq?: {
    title: string;
    projectLocation: string;
    dueDate: string;
  };
  contractor?: {
    companyName: string;
    primaryContactName: string;
    primaryContactEmail: string;
  };
}

export default function BidManagement() {
  const [selectedBid, setSelectedBid] = useState<Bid | null>(null);
  const [comparisonBids, setComparisonBids] = useState<Bid[]>([]);
  const [showComparison, setShowComparison] = useState(false);
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Simple filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Fetch contractor's bids
  const { data: bids = [], isLoading } = useQuery<Bid[]>({
    queryKey: ['/api/contractors/bids'],
  });

  // Handle export functionality
  const handleExport = async (format: 'csv' | 'pdf' | 'analytics') => {
    console.log('🚀 Export initiated:', { format, bidsCount: filteredBids.length });
    
    if (filteredBids.length === 0) {
      console.log('❌ No bids to export');
      toast({ title: "No data to export", variant: "destructive" });
      return;
    }

    try {
      if (format === 'analytics') {
        console.log('📊 Starting analytics export...');
        await handleAnalyticsExport();
        return;
      }

      console.log('📥 Importing report utilities...');
      const { exportContractorBidsCSV, generateContractorBidsPDF } = await import("@/lib/reportUtils");
      console.log('✅ Report utilities imported successfully');
      
      if (format === 'csv') {
        console.log('📄 Starting CSV export with data:', filteredBids.slice(0, 2)); // Log first 2 bids for debugging
        await exportContractorBidsCSV(filteredBids);
        console.log('✅ CSV export completed');
        toast({ title: "CSV report downloaded successfully" });
      } else {
        console.log('📄 Starting PDF export with data:', filteredBids.slice(0, 2)); // Log first 2 bids for debugging
        await generateContractorBidsPDF(filteredBids);
        console.log('✅ PDF export completed');
        toast({ title: "PDF report generated - check print dialog" });
      }
    } catch (error) {
      const errorInfo = error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : { message: String(error) };
      
      console.error('❌ Export failed with detailed error:', {
        ...errorInfo,
        format,
        bidsLength: filteredBids.length
      });
      toast({ 
        title: "Export failed", 
        description: error instanceof Error ? error.message : "Please try again", 
        variant: "destructive" 
      });
    }
  };

  // Handle analytics export with charts
  const handleAnalyticsExport = async () => {
    try {
      console.log('📊 Importing analytics utilities...');
      const { generateBidAnalyticsReport } = await import("@/lib/reportUtils");
      console.log('✅ Analytics utilities imported, generating report...');
      await generateBidAnalyticsReport(filteredBids);
      console.log('✅ Analytics report completed');
      toast({ title: "Analytics report generated with charts" });
    } catch (error) {
      const errorInfo = error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : { message: String(error) };
      
      console.error('❌ Analytics export failed with detailed error:', {
        ...errorInfo,
        bidsLength: filteredBids.length
      });
      toast({ title: "Analytics export failed", description: error instanceof Error ? error.message : "Please try again", variant: "destructive" });
    }
  };

  // Handle email report functionality
  const handleEmailReport = async () => {
    try {
      // This would integrate with the backend to send email reports
      const response = await fetch('/api/reports/email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          reportType: 'contractor-bids',
          bidIds: filteredBids.map(bid => bid.id),
          filters: { searchTerm, statusFilter }
        })
      });

      if (response.ok) {
        toast({ title: "Email report scheduled for delivery" });
      } else {
        throw new Error('Failed to schedule email report');
      }
    } catch (error) {
      console.error('Email report failed:', error);
      toast({ title: "Email report failed", variant: "destructive" });
    }
  };

  const formatStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accept':
        return 'Accepted';
      case 'reject':
        return 'Rejected';
      case 'request info':
        return 'Request Info';
      case 'under_review':
        return 'Under Review';
      case 'submitted':
        return 'Submitted';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
    }
  };

  const getStatusColor = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    switch (normalizedStatus) {
      case 'submitted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'accepted':
      case 'accept':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
      case 'reject':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return 'Not specified';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Simple filtering logic using search and status only
  const filteredBids = bids.filter(bid => {
    // Search filter
    const matchesSearch = !searchTerm || 
      bid.rfq?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bid.rfq?.projectLocation?.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Status filter with normalization 
    const normalizeStatus = (status: string) => {
      if (status === 'accept') return 'accepted';
      if (status === 'reject') return 'rejected';
      return status;
    };
    
    const matchesStatus = statusFilter === 'all' || 
                         normalizeStatus(bid.status) === statusFilter ||
                         bid.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Group bids by status for dashboard (normalize status values)
  const groupedBids = {
    submitted: filteredBids.filter(bid => bid.status === 'submitted'),
    under_review: filteredBids.filter(bid => bid.status === 'under_review'),
    accepted: filteredBids.filter(bid => bid.status === 'accepted' || bid.status === 'accept'),
    rejected: filteredBids.filter(bid => bid.status === 'rejected' || bid.status === 'reject'),
  };

  // Calculate summary statistics
  const totalBids = bids.length;
  const winRate = totalBids > 0 ? (groupedBids.accepted.length / totalBids) * 100 : 0;
  const avgBidAmount = bids
    .filter(bid => bid.bidAmount)
    .reduce((sum, bid) => sum + (bid.bidAmount || 0), 0) / bids.filter(bid => bid.bidAmount).length;

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-64" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg" />
            ))}
          </div>
          <div className="h-96 bg-muted rounded-lg" />
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="container mx-auto py-8 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">My Bid Submissions</h1>
            <p className="text-muted-foreground">
              Track and manage all your bid submissions across different RFQs
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('csv')}
              className="bg-green-50 hover:bg-green-100 border-green-200"
            >
              <Download className="h-3 w-3 mr-1" />
              Export CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('pdf')}
              className="bg-blue-50 hover:bg-blue-100 border-blue-200"
            >
              <Download className="h-3 w-3 mr-1" />
              Export PDF
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('analytics')}
              className="bg-purple-50 hover:bg-purple-100 border-purple-200"
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              Analytics
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleEmailReport}
              className="bg-orange-50 hover:bg-orange-100 border-orange-200"
            >
              <Mail className="h-3 w-3 mr-1" />
              Email Report
            </Button>
          </div>
        </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Bids</p>
                <p className="text-2xl font-bold">{totalBids}</p>
              </div>
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Win Rate</p>
                <p className="text-2xl font-bold text-green-600">{winRate.toFixed(1)}%</p>
              </div>
              <Star className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Bid</p>
                <p className="text-2xl font-bold">{formatCurrency(avgBidAmount || 0)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Under Review</p>
                <p className="text-2xl font-bold text-yellow-600">{groupedBids.under_review.length}</p>
              </div>
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Bid History</CardTitle>
          <CardDescription>
            View and manage all your submitted bids
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Simple Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by project title or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="under_review">Under Review</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Results summary */}
          <div className="text-sm text-muted-foreground mb-4">
            Showing {filteredBids.length} of {totalBids} bids
          </div>
          
          {/* Bid Comparison Controls */}
          {comparisonBids.length >= 2 && (
            <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg mb-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  {comparisonBids.length} bid{comparisonBids.length !== 1 ? 's' : ''} selected for comparison
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  onClick={() => setShowComparison(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  size="sm"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Compare Bids
                </Button>
                <Button 
                  onClick={() => setComparisonBids([])}
                  variant="ghost"
                  size="sm"
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400"
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          )}

          {filteredBids.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">No bids found</h3>
              <p className="text-muted-foreground">
                {totalBids === 0 
                  ? "You haven't submitted any bids yet. Check available RFQs to get started."
                  : "No bids match your current search criteria."
                }
              </p>
            </div>
          ) : (
            <Tabs defaultValue="cards" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="cards">Card View</TabsTrigger>
                <TabsTrigger value="table">Table View</TabsTrigger>
              </TabsList>

              <TabsContent value="cards" className="mt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredBids.map((bid) => (
                    <Card key={bid.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg">
                              {bid.rfq?.title || 'Untitled Project'}
                            </CardTitle>
                            <CardDescription>
                              {bid.rfq?.projectLocation || 'Location not specified'}
                            </CardDescription>
                          </div>
                          <Badge className={getStatusColor(bid.status)}>
                            {formatStatus(bid.status)}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div>
                            <p className="text-muted-foreground">Bid Amount</p>
                            <p className="font-medium">{formatCurrency(bid.bidAmount)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Timeline</p>
                            <p className="font-medium">{bid.timeline || 'Not specified'}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Data Quality</p>
                            <BidStructuredDataIndicator 
                              compact={true}
                              hasStructuredData={bid.totalBidAmount && bid.lineItemsTotal ? bid.totalBidAmount === bid.lineItemsTotal : false}
                              dataQualityScore={Math.round(bid.confidenceScore * 100)}
                            />
                          </div>
                          <div>
                            <p className="text-muted-foreground">Submitted</p>
                            <p className="font-medium">
                              {formatDistanceToNow(new Date(bid.submittedAt), { addSuffix: true })}
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            className="flex-1"
                            onClick={() => setSelectedBid(bid)}
                          >
                            <Info className="h-4 w-4 mr-2" />
                            View Bid Details
                          </Button>
                          <Button 
                            variant="outline" 
                            className="flex-1"
                            onClick={() => navigate(`/rfq/${bid.rfqId}`)}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View RFQ
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="table" className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <input
                          type="checkbox"
                          checked={comparisonBids.length === filteredBids.length && filteredBids.length > 0}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setComparisonBids(filteredBids);
                            } else {
                              setComparisonBids([]);
                            }
                          }}
                          className="rounded border-gray-300"
                        />
                      </TableHead>
                      <TableHead>Project</TableHead>
                      <TableHead>Bid Amount</TableHead>
                      <TableHead>Timeline</TableHead>
                      <TableHead>Confidence</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredBids.map((bid) => (
                      <TableRow key={bid.id}>
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={comparisonBids.some(cb => cb.id === bid.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setComparisonBids(prev => [...prev, bid]);
                              } else {
                                setComparisonBids(prev => prev.filter(cb => cb.id !== bid.id));
                              }
                            }}
                            className="rounded border-gray-300"
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{bid.rfq?.title || 'Untitled Project'}</p>
                            <p className="text-sm text-muted-foreground">
                              {bid.rfq?.projectLocation || 'Location not specified'}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            {formatCurrency(bid.bidAmount)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              {bid.timeline || 'Not specified'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-muted-foreground" />
                            <span className={`text-sm font-medium ${getConfidenceColor(bid.confidenceScore)}`}>
                              {Math.round(bid.confidenceScore * 100)}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(bid.status)}>
                            {formatStatus(bid.status)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              {formatDistanceToNow(new Date(bid.submittedAt), { addSuffix: true })}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <BidStructuredDataIndicator 
                              compact={true}
                              hasStructuredData={bid.totalBidAmount && bid.lineItemsTotal ? bid.totalBidAmount === bid.lineItemsTotal : false}
                              dataQualityScore={Math.round(bid.confidenceScore * 100)}
                            />
                            <div className="flex items-center gap-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => setSelectedBid(bid)}
                              >
                                <Info className="h-4 w-4 mr-1" />
                                View Details
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => navigate(`/rfq/${bid.rfqId}`)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View RFQ
                              </Button>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
    
    {/* Bid Details Modal */}
    {selectedBid && (
      <BidDetailsModal
        bid={selectedBid}
        rfq={{
          title: selectedBid.rfq?.title || 'Untitled Project',
          projectLocation: selectedBid.rfq?.projectLocation || 'Location not specified',
          dueDate: selectedBid.rfq?.dueDate || '',
        }}
        isOpen={!!selectedBid}
        onClose={() => setSelectedBid(null)}
      />
    )}
    
    {/* Bid Comparison Modal */}
    {showComparison && comparisonBids.length >= 2 && (
      <BidComparisonModal
        bids={comparisonBids.map(bid => ({
          id: bid.id,
          bidAmount: bid.bidAmount,
          extractedAmount: bid.extractedAmount,
          timeline: bid.timeline,
          extractedTimeline: bid.extractedTimeline,
          scope: bid.scope,
          extractedScope: bid.extractedScope,
          conditions: bid.conditions,
          extractedConditions: bid.extractedConditions,
          extractionConfidence: bid.extractionConfidence,
          status: bid.status,
          submittedAt: bid.submittedAt,
          contractorId: bid.contractorId,
          contractorName: bid.contractor?.companyName,
          aiSummary: bid.aiSummary,
        }))}
        rfq={{
          title: comparisonBids[0]?.rfq?.title || 'Untitled Project',
          projectLocation: comparisonBids[0]?.rfq?.projectLocation || 'Location not specified',
          dueDate: comparisonBids[0]?.rfq?.dueDate || '',
        }}
        isOpen={showComparison}
        onClose={() => setShowComparison(false)}
      />
    )}
    </>
  );
}