import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Building2, 
  DollarSign, 
  Calendar, 
  TrendingUp, 
  TrendingDown, 
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { CategoryDetailModal } from '@/components/CategoryDetailModal';

interface ContractorBid {
  bidId: string;
  contractorId: string;
  contractorName: string;
  companyName: string;
  bidAmount: number;
  timeline: string;
  status: string;
  competitiveScore?: number;
  lineItems: BidLineItem[];
}

interface BidLineItem {
  id: string;
  costCode: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  unitOfMeasure: string;
  category: string;
}

interface CategorySummary {
  mainCostCode: string; // e.g., "02-000"
  categoryName: string; // e.g., "Site Construction"
  contractorBids: ContractorBid[];
  totalContractors: number;
  priceRange: {
    lowest: number;
    highest: number;
    average: number;
  };
}

interface CategoryComparisonProps {
  rfqId: string;
}

// Utility function to extract main cost code category (XX-000 format)
const getMainCostCode = (costCode: string): string => {
  if (!costCode) return 'Other';
  
  // Extract first 2 digits and add -000
  const match = costCode.match(/^(\d{2})/);
  return match ? `${match[1]}-000` : 'Other';
};

// Utility function to get category name from cost code
const getCategoryName = (mainCostCode: string): string => {
  const categoryNames: Record<string, string> = {
    '01-000': 'General Requirements',
    '02-000': 'Site Construction',
    '03-000': 'Concrete',
    '04-000': 'Masonry',
    '05-000': 'Metals',
    '06-000': 'Wood & Plastics',
    '07-000': 'Thermal & Moisture Protection',
    '08-000': 'Doors & Windows',
    '09-000': 'Finishes',
    '10-000': 'Specialties',
    '11-000': 'Equipment',
    '12-000': 'Furnishings',
    '13-000': 'Special Construction',
    '14-000': 'Conveying Systems',
    '15-000': 'Mechanical',
    '16-000': 'Electrical',
  };
  
  return categoryNames[mainCostCode] || mainCostCode;
};

export function CategoryComparisonView({ rfqId }: CategoryComparisonProps) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  const { data: categorySummaries = [], isLoading } = useQuery<CategorySummary[]>({
    queryKey: ['/api/rfqs', rfqId, 'category-analysis'],
    enabled: !!rfqId,
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getPriceVarianceColor = (amount: number, average: number) => {
    const variance = (amount - average) / average;
    if (variance > 0.1) return 'text-red-600 dark:text-red-400';
    if (variance < -0.1) return 'text-green-600 dark:text-green-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accepted':
      case 'accept':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
      case 'reject':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Cost Code Category Analysis
          </CardTitle>
          <CardDescription>
            Analyzing bids by cost code categories...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (categorySummaries.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Cost Code Category Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-muted-foreground">No bid data available for category analysis.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Cost Code Category Analysis
          </CardTitle>
          <CardDescription>
            Compare bids by main cost code categories (XX-000 format)
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid gap-6">
        {categorySummaries.map((category) => (
          <Card key={category.mainCostCode} className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">
                    {category.mainCostCode} - {category.categoryName}
                  </CardTitle>
                  <CardDescription>
                    {category.totalContractors} contractor{category.totalContractors !== 1 ? 's' : ''} submitted bids
                  </CardDescription>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">Price Range</div>
                  <div className="font-semibold">
                    {formatCurrency(category.priceRange.lowest)} - {formatCurrency(category.priceRange.highest)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Avg: {formatCurrency(category.priceRange.average)}
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              <div className="space-y-4">
                {/* Contractor Comparison Table */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Contractor</TableHead>
                      <TableHead>Total Amount</TableHead>
                      <TableHead>Timeline</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Score</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {category.contractorBids.map((bid) => (
                      <TableRow key={bid.bidId}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{bid.companyName}</div>
                            <div className="text-sm text-muted-foreground">{bid.contractorName}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className={`font-medium ${getPriceVarianceColor(bid.bidAmount, category.priceRange.average)}`}>
                              {formatCurrency(bid.bidAmount)}
                            </span>
                            {bid.bidAmount < category.priceRange.average && (
                              <TrendingDown className="h-4 w-4 text-green-600" />
                            )}
                            {bid.bidAmount > category.priceRange.average && (
                              <TrendingUp className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{bid.timeline || 'Not specified'}</span>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(bid.status)}>
                            {bid.status === 'accept' ? 'Accepted' : 
                             bid.status === 'reject' ? 'Rejected' : 
                             bid.status}
                          </Badge>  
                        </TableCell>
                        <TableCell>
                          {bid.competitiveScore && (
                            <div className="flex items-center gap-1">
                              <span className="text-sm font-medium">
                                {Math.round(bid.competitiveScore * 100)}%
                              </span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center gap-2 justify-end">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="outline" size="sm">
                                  <Eye className="h-4 w-4 mr-1" />
                                  Details
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                                <DialogHeader>
                                  <DialogTitle>
                                    {category.mainCostCode} - {category.categoryName} Details
                                  </DialogTitle>
                                </DialogHeader>
                                <CategoryDetailModal 
                                  categoryData={category}
                                  selectedBidId={bid.bidId}
                                />
                              </DialogContent>
                            </Dialog>
                            
                            {bid.status !== 'accept' && bid.status !== 'accepted' && (
                              <Button 
                                variant="default" 
                                size="sm"
                                className="bg-green-600 hover:bg-green-700 text-white"
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Accept Bid
                              </Button>
                            )}
                            
                            {bid.status !== 'reject' && bid.status !== 'rejected' && (
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="border-red-300 text-red-600 hover:bg-red-50"
                              >
                                <XCircle className="h-4 w-4 mr-1" />
                                Reject
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}