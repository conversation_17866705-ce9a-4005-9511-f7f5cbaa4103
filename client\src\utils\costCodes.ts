export interface CostCode {
  code: string;
  description: string;
  category: string;
}

let costCodeCache: CostCode[] | null = null;

export async function loadCostCodes(): Promise<CostCode[]> {
  if (costCodeCache) {
    return costCodeCache;
  }

  try {
    const response = await fetch('/api/cost-codes');
    const csvText = await response.text();
    
    const lines = csvText.split('\n');
    const costCodes: CostCode[] = [];
    
    // Skip header row (line 0)
    const seenCodes = new Set<string>();
    
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      // Parse CSV line - handle commas within quotes if needed
      const columns = line.split(',');
      
      if (columns.length >= 2) {
        const masterCode = columns[0]?.trim().replace(/^﻿/, ''); // Remove BOM if present
        const masterDescription = columns[1]?.trim();
        
        if (masterCode && masterDescription && !seenCodes.has(masterCode)) {
          seenCodes.add(masterCode);
          // Determine category based on code prefix
          let category = 'General';
          const codePrefix = masterCode.split('-')[0];
          
          switch (codePrefix) {
            case '01':
              category = 'General Requirements';
              break;
            case '02':
              category = 'Site Construction';
              break;
            case '03':
              category = 'Concrete';
              break;
            case '04':
              category = 'Masonry';
              break;
            case '05':
              category = 'Metals';
              break;
            case '06':
              category = 'Wood & Plastics';
              break;
            case '07':
              category = 'Thermal & Moisture';
              break;
            case '08':
              category = 'Openings';
              break;
            case '09':
              category = 'Finishes';
              break;
            case '10':
              category = 'Specialties';
              break;
            case '11':
              category = 'Equipment';
              break;
            case '12':
              category = 'Furnishings';
              break;
            case '13':
              category = 'Special Construction';
              break;
            case '14':
              category = 'Conveying Equipment';
              break;
            case '15':
              category = 'Mechanical';
              break;
            case '16':
              category = 'Electrical';
              break;
            case '21':
              category = 'Fire Suppression';
              break;
            case '22':
              category = 'Plumbing';
              break;
            case '23':
              category = 'HVAC';
              break;
            case '26':
              category = 'Electrical';
              break;
            case '27':
              category = 'Communications';
              break;
            case '28':
              category = 'Electronic Safety';
              break;
            default:
              category = 'Other';
              break;
          }
          
          costCodes.push({
            code: masterCode,
            description: masterDescription,
            category: category
          });
        }
      }
    }
    
    // Cache the results and log the count for verification
    costCodeCache = costCodes;
    console.log(`✅ Loaded ${costCodes.length} unique cost codes from consolidated CSV`);
    console.log(`📊 Categories found: ${Object.keys(getCostCodesByCategory(costCodes)).length}`);
    
    return costCodes;
  } catch (error) {
    console.error('Failed to load cost codes:', error);
    // Return empty array if loading fails
    return [];
  }
}

export function getCostCodesByCategory(costCodes: CostCode[]): Record<string, CostCode[]> {
  const grouped: Record<string, CostCode[]> = {};
  
  costCodes.forEach(code => {
    if (!grouped[code.category]) {
      grouped[code.category] = [];
    }
    grouped[code.category].push(code);
  });
  
  return grouped;
}