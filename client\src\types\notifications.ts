// Notification types exported from the backend NotificationService
export const NotificationTypes = {
  // RFQ Events
  RFQ_UPLOADED: 'rfq_uploaded',
  RFQ_DISTRIBUTED: 'rfq_distributed',
  RFQ_CLOSED: 'rfq_closed',
  RFQ_DEADLINE_REMINDER: 'rfq_deadline_reminder',
  
  // Bid Events
  BID_SUBMITTED: 'bid_submitted',
  BID_ACCEPTED: 'bid_accepted',
  BID_REJECTED: 'bid_rejected',
  BID_REQUEST_INFO: 'bid_request_info',
  
  // System Events
  SYSTEM_MAINTENANCE: 'system_maintenance',
  ACCOUNT_UPDATE: 'account_update',
  
  // Legacy types for backwards compatibility
  BID_RECEIVED: 'bid_received',
  RFQ_DUE_SOON: 'rfq_due_soon',
  DOCUMENT_UPLOADED: 'document_uploaded',
  CONTRACTOR_APPROVED: 'contractor_approved',
} as const;

export type NotificationType = typeof NotificationTypes[keyof typeof NotificationTypes];

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface NotificationData {
  rfqId?: string;
  bidId?: string;
  projectName?: string;
  contractorName?: string;
  amount?: string;
  deadlineDate?: string;
  reminderType?: string;
  [key: string]: any;
}
