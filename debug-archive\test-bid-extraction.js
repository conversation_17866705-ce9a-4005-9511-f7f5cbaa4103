#!/usr/bin/env node

/**
 * Test script to verify bid PDF extraction works with scanned/image PDFs
 * Run: node test-bid-extraction.js
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

const __dirname = dirname(fileURLToPath(import.meta.url));

async function testBidExtraction() {
  console.log('🧪 Testing bid PDF extraction...');
  
  try {
    // Dynamic import to avoid ESM issues
    const { processBidDocument } = await import('./server/services/aiService.js');
    
    // Test with the test PDF file
    const testPdfPath = join(__dirname, 'test.pdf');
    console.log('📄 Testing with file:', testPdfPath);
    
    const result = await processBidDocument(testPdfPath, 'test.pdf');
    
    console.log('✅ Extraction successful!');
    console.log('📊 Text length:', result.text?.length || 0);
    console.log('📊 Confidence:', result.confidence);
    console.log('📊 Structured data keys:', Object.keys(result.structuredData || {}));
    console.log('💰 Extracted bid amount:', result.structuredData?.bidAmount);
    console.log('⏱️ Timeline:', result.structuredData?.timeline);
    
    if (result.text && result.text.length > 50) {
      console.log('🎉 SUCCESS: Bid extraction working correctly');
    } else {
      console.log('⚠️ WARNING: Very little text extracted');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('🔍 This might be expected if the PDF is a scanned image');
    console.log('💡 The system should now handle this via OCR fallback');
  }
}

testBidExtraction().catch(console.error);
