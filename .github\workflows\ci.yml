name: CI

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Typecheck (server)
        run: npm run check

      - name: Lint
        run: npm run lint

      - name: Secret scan (gitleaks)
        uses: gitleaks/gitleaks-action@v2
        with:
          args: detect --source . --no-git -v --redact

  security-tests:
    runs-on: ubuntu-latest
    needs: build
    env:
      PORT: 5001
      NODE_ENV: development
      BIDAIBLE_TEST: '1'
      BASE_URL: http://localhost:5001
      # Provide a Neon/PG-compatible DATABASE_URL via secrets for full end-to-end tests.
      # If not provided, this job will be skipped.
      DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install root deps
        run: npm ci

      - name: Install test deps
        run: npm --prefix tests ci

      - name: Skip if no TEST_DATABASE_URL
        if: ${{ env.DATABASE_URL == '' }}
        run: |
          echo "No TEST_DATABASE_URL provided. Skipping security tests." && exit 0

      - name: Start API-only server (background)
        run: |
          nohup npm run dev:api > server.log 2>&1 &
          echo $! > server.pid

      - name: Wait for server
        run: |
          for i in {1..60}; do
            if curl -sS "${BASE_URL}/api/health" >/dev/null; then echo "Server up"; exit 0; fi
            sleep 1
          done
          echo "Server failed to start" && tail -n 200 server.log && exit 1

      - name: Generate route map
        run: node scripts/generate-route-map.cjs

      - name: Run RFQ update access test
        run: node tests/test-rfq-update-access.js

      - name: Run docs/files access test
        run: node tests/test-docs-files-access.js

      - name: Run sensitive endpoint rate limit test
        run: node tests/test-rate-limits.js

      - name: Run admin routes denial test
        run: node tests/test-admin-routes.js

      - name: Upload route map artifacts
        uses: actions/upload-artifact@v4
        with:
          name: route-map
          path: |
            docs/route-map.md
            docs/route-map.json

      - name: Show server logs on failure
        if: ${{ failure() }}
        run: |
          echo "==== SERVER LOG ===="
          tail -n 400 server.log || true

      - name: Stop server
        if: always()
        run: |
          if [ -f server.pid ]; then kill $(cat server.pid) || true; fi

