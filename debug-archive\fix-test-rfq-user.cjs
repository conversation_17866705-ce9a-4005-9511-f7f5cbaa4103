const { drizzle } = require('drizzle-orm/neon-http');
const { neon } = require('@neondatabase/serverless');
const { eq } = require('drizzle-orm');

// Import schema (this might need adjustment based on your schema structure)
// For now, I'll use raw SQL to update the RFQ

async function fixTestRFQUser() {
  console.log('🔧 Fixing test RFQ to belong to real authenticated user...');
  
  try {
    // Database connection
    const sql = neon(process.env.DATABASE_URL);
    const db = drizzle(sql);
    
    // Your real user ID from the server logs
    const realUserId = 'user_319LPVLDMzru79fF2TZXTX5xZ6i';
    
    // Find recent test RFQs (created by test endpoint)
    const testRfqs = await sql`
      SELECT id, project_name, created_by, created_at 
      FROM rfqs 
      WHERE created_by LIKE 'test-user-%' 
      ORDER BY created_at DESC 
      LIMIT 10
    `;
    
    console.log(`📋 Found ${testRfqs.length} test RFQs:`);
    testRfqs.forEach((rfq, index) => {
      console.log(`  ${index + 1}. ${rfq.id} - "${rfq.project_name}" (${rfq.created_by})`);
    });
    
    if (testRfqs.length === 0) {
      console.log('❌ No test RFQs found to update.');
      return;
    }
    
    // Update the most recent test RFQ to belong to the real user
    const mostRecentTestRFQ = testRfqs[0];
    
    console.log(`🔄 Updating RFQ ${mostRecentTestRFQ.id} to belong to user ${realUserId}...`);
    
    const result = await sql`
      UPDATE rfqs 
      SET created_by = ${realUserId}
      WHERE id = ${mostRecentTestRFQ.id}
    `;
    
    console.log('✅ Successfully updated RFQ ownership!');
    console.log(`   RFQ "${mostRecentTestRFQ.project_name}" now belongs to ${realUserId}`);
    console.log('');
    console.log('🎉 The RFQ should now appear in your Dashboard and My RFQs page!');
    console.log('   Please refresh your browser to see the changes.');
    
  } catch (error) {
    console.error('❌ Failed to update RFQ:', error.message);
    if (error.message.includes('DATABASE_URL')) {
      console.log('💡 Make sure your DATABASE_URL environment variable is set correctly.');
    }
  }
}

// Also show current RFQs for the real user
async function checkUserRFQs() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    const realUserId = 'user_319LPVLDMzru79fF2TZXTX5xZ6i';
    
    console.log(`\n🔍 Checking RFQs for user ${realUserId}:`);
    
    const userRfqs = await sql`
      SELECT id, project_name, status, created_at
      FROM rfqs 
      WHERE created_by = ${realUserId}
      ORDER BY created_at DESC
      LIMIT 5
    `;
    
    if (userRfqs.length > 0) {
      console.log(`📋 Found ${userRfqs.length} RFQs for your user:`);
      userRfqs.forEach((rfq, index) => {
        console.log(`  ${index + 1}. ${rfq.project_name} (${rfq.status}) - ${rfq.created_at}`);
      });
    } else {
      console.log('❌ No RFQs found for your user yet.');
    }
    
  } catch (error) {
    console.error('❌ Failed to check user RFQs:', error.message);
  }
}

async function run() {
  await fixTestRFQUser();
  await checkUserRFQs();
}

run().catch(console.error);
