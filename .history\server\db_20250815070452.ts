import { config } from 'dotenv';
import path from 'path';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

// Ensure environment variables are loaded before database connection
// Check for Railway environment indicators to determine if we're in Railway deployment
const isRailwayDeployment = !!(process.env.RAILWAY_ENVIRONMENT || process.env.RAILWAY_PROJECT_ID || process.env.NIXPACKS_METADATA);

if (!isRailwayDeployment && process.env.NODE_ENV !== 'production') {
  // Only load .env if we're in local development (not Railway)
  config({ path: path.resolve(process.cwd(), '.env') });
  console.log('🔧 Database module loaded .env file for local development');
}

neonConfig.webSocketConstructor = ws;

if (!process.env.DATABASE_URL) {
  console.error("FATAL: DATABASE_URL is not defined.");
  console.error("Application cannot start without a database connection.");
  console.error("Ensure DATABASE_URL is set in your environment variables or .env file.");
  
  // Enhanced debugging for Railway deployment
  const isRailwayDeployment = !!(process.env.RAILWAY_ENVIRONMENT || process.env.RAILWAY_PROJECT_ID || process.env.NIXPACKS_METADATA);
  
  if (isRailwayDeployment || process.env.NODE_ENV === 'production') {
    console.error("=== RAILWAY DATABASE DEBUG ===");
    console.error("Railway Environment:", isRailwayDeployment ? 'YES' : 'NO');
    console.error("NODE_ENV:", process.env.NODE_ENV);
    console.error("Available environment variables:");
    Object.keys(process.env).forEach(key => {
      if (key.toLowerCase().includes('database') || key.toLowerCase().includes('db') || key.toLowerCase().includes('pg')) {
        console.error(`  ${key}: ${process.env[key] ? '[SET]' : '[NOT SET]'}`);
      }
    });
    console.error("Total env vars count:", Object.keys(process.env).length);
    
    // Log Railway-specific variables
    const railwayVars = Object.keys(process.env).filter(key => 
      key.startsWith('RAILWAY_') || key.startsWith('NIXPACKS_')
    );
    console.error("Railway vars found:", railwayVars.length);
    railwayVars.forEach(key => {
      console.error(`  ${key}: ${process.env[key]}`);
    });
    console.error("=== END RAILWAY DATABASE DEBUG ===");
    process.exit(1);
  }
  // For development, throwing an error provides a better debugging experience.
  throw new Error("DATABASE_URL must be set.");
}

// Parse DATABASE_URL to extract components safely
function parseDatabaseUrl(url: string) {
  try {
    const parsed = new URL(url);
    return {
      user: parsed.username,
      password: parsed.password,
      host: parsed.hostname,
      port: parseInt(parsed.port) || 5432,
      database: parsed.pathname.slice(1), // Remove leading slash
      ssl: parsed.searchParams.get('sslmode') === 'require'
    };
  } catch (error) {
    throw new Error("Invalid DATABASE_URL format");
  }
}

// Create connection configuration without exposing credentials in logs
const dbConfig = parseDatabaseUrl(process.env.DATABASE_URL);
const poolConfig = {
  user: dbConfig.user,
  password: dbConfig.password,
  host: dbConfig.host,
  port: dbConfig.port,
  database: dbConfig.database,
  ssl: dbConfig.ssl,
  // Override toString to prevent credential exposure in logs
  toString: () => `[Pool Config for ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}]`
};

export const pool = new Pool(poolConfig);
export const db = drizzle({ client: pool, schema });
