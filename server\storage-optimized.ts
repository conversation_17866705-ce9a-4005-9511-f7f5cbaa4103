// Optimized storage methods to eliminate N+1 queries
// These methods should replace the current implementations in storage.ts

import { db } from './database';
import { bids, bidLineItems, rfqs, users, organizations } from '../shared/schema';
import { eq, and, inArray } from 'drizzle-orm';

/**
 * Batch fetch RFQs with their bids and line items in a single optimized query
 * Eliminates N+1 pattern in QuickBooks export and other bid analysis endpoints
 */
export async function getRfqsWithBidsAndLineItems(rfqIds: string[]): Promise<any[]> {
  if (rfqIds.length === 0) return [];

  // Single query with joins to get all data at once
  const results = await db
    .select({
      // RFQ fields
      rfqId: rfqs.id,
      rfqProjectName: rfqs.projectName,
      rfqDescription: rfqs.description,
      rfqDueDate: rfqs.dueDate,
      rfqStatus: rfqs.status,
      rfqOrganizationId: rfqs.organizationId,
      
      // Bid fields  
      bidId: bids.id,
      bidContractorId: bids.contractorId,
      bidTotalAmount: bids.totalAmount,
      bidStatus: bids.status,
      bidSubmittedAt: bids.submittedAt,
      
      // Line item fields
      lineItemId: bidLineItems.id,
      lineItemDescription: bidLineItems.description,
      lineItemQuantity: bidLineItems.quantity,
      lineItemUnitPrice: bidLineItems.unitPrice,
      lineItemTotalPrice: bidLineItems.totalPrice,
      lineItemCostCode: bidLineItems.costCode,
    })
    .from(rfqs)
    .leftJoin(bids, eq(rfqs.id, bids.rfqId))
    .leftJoin(bidLineItems, eq(bids.id, bidLineItems.bidId))
    .where(inArray(rfqs.id, rfqIds));

  // Transform flat result into nested structure
  const rfqMap = new Map();
  
  for (const row of results) {
    if (!rfqMap.has(row.rfqId)) {
      rfqMap.set(row.rfqId, {
        id: row.rfqId,
        projectName: row.rfqProjectName,
        description: row.rfqDescription,
        dueDate: row.rfqDueDate,
        status: row.rfqStatus,
        organizationId: row.rfqOrganizationId,
        bids: new Map()
      });
    }
    
    const rfq = rfqMap.get(row.rfqId);
    
    if (row.bidId && !rfq.bids.has(row.bidId)) {
      rfq.bids.set(row.bidId, {
        id: row.bidId,
        contractorId: row.bidContractorId,
        totalAmount: row.bidTotalAmount,
        status: row.bidStatus,
        submittedAt: row.bidSubmittedAt,
        lineItems: []
      });
    }
    
    if (row.lineItemId && row.bidId) {
      const bid = rfq.bids.get(row.bidId);
      bid.lineItems.push({
        id: row.lineItemId,
        description: row.lineItemDescription,
        quantity: row.lineItemQuantity,
        unitPrice: row.lineItemUnitPrice,
        totalPrice: row.lineItemTotalPrice,
        costCode: row.lineItemCostCode
      });
    }
  }
  
  // Convert maps to arrays
  return Array.from(rfqMap.values()).map(rfq => ({
    ...rfq,
    bids: Array.from(rfq.bids.values())
  }));
}

/**
 * Batch fetch bids with their line items for multiple bids
 * Eliminates N+1 pattern when loading bid details
 */
export async function getBidsWithLineItems(bidIds: string[]): Promise<any[]> {
  if (bidIds.length === 0) return [];

  const results = await db
    .select({
      // Bid fields
      bidId: bids.id,
      bidRfqId: bids.rfqId,
      bidContractorId: bids.contractorId,
      bidTotalAmount: bids.totalAmount,
      bidStatus: bids.status,
      bidSubmittedAt: bids.submittedAt,
      bidCreatedAt: bids.createdAt,
      
      // Line item fields
      lineItemId: bidLineItems.id,
      lineItemDescription: bidLineItems.description,
      lineItemQuantity: bidLineItems.quantity,
      lineItemUnitPrice: bidLineItems.unitPrice,
      lineItemTotalPrice: bidLineItems.totalPrice,
      lineItemCostCode: bidLineItems.costCode,
    })
    .from(bids)
    .leftJoin(bidLineItems, eq(bids.id, bidLineItems.bidId))
    .where(inArray(bids.id, bidIds));

  // Transform to nested structure
  const bidMap = new Map();
  
  for (const row of results) {
    if (!bidMap.has(row.bidId)) {
      bidMap.set(row.bidId, {
        id: row.bidId,
        rfqId: row.bidRfqId,
        contractorId: row.bidContractorId,
        totalAmount: row.bidTotalAmount,
        status: row.bidStatus,
        submittedAt: row.bidSubmittedAt,
        createdAt: row.bidCreatedAt,
        lineItems: []
      });
    }
    
    if (row.lineItemId) {
      const bid = bidMap.get(row.bidId);
      bid.lineItems.push({
        id: row.lineItemId,
        description: row.lineItemDescription,
        quantity: row.lineItemQuantity,
        unitPrice: row.lineItemUnitPrice,
        totalPrice: row.lineItemTotalPrice,
        costCode: row.lineItemCostCode
      });
    }
  }
  
  return Array.from(bidMap.values());
}

/**
 * Batch fetch users by organization IDs
 * Eliminates N+1 pattern when loading users for multiple organizations
 */
export async function getUsersByOrganizations(organizationIds: string[]): Promise<Map<string, any[]>> {
  if (organizationIds.length === 0) return new Map();

  const results = await db
    .select()
    .from(users)
    .where(and(
      inArray(users.organizationId, organizationIds),
      eq(users.isActive, true)
    ))
    .orderBy(users.createdAt);

  // Group users by organization
  const usersByOrg = new Map<string, any[]>();
  
  for (const user of results) {
    if (!usersByOrg.has(user.organizationId)) {
      usersByOrg.set(user.organizationId, []);
    }
    usersByOrg.get(user.organizationId)!.push(user);
  }
  
  return usersByOrg;
}

/**
 * Batch fetch users by IDs with organization data
 * Eliminates individual user fetches in notification service
 */
export async function getUsersWithOrganizations(userIds: string[]): Promise<any[]> {
  if (userIds.length === 0) return [];

  return await db
    .select({
      // User fields
      userId: users.id,
      userEmail: users.email,
      userFirstName: users.firstName,
      userLastName: users.lastName,
      userOrganizationId: users.organizationId,
      
      // Organization fields
      orgName: organizations.name,
      orgSlug: organizations.slug,
    })
    .from(users)
    .leftJoin(organizations, eq(users.organizationId, organizations.id))
    .where(inArray(users.id, userIds));
}

/**
 * Usage example for routes.ts to replace N+1 patterns:
 * 
 * // OLD N+1 pattern:
 * const rfqsWithBids = await Promise.all(
 *   rfqs.map(async (rfq) => {
 *     const bids = await storage.getBidsByRfq(rfq.id);
 *     const bidsWithLineItems = await Promise.all(
 *       bids.map(async (bid) => {
 *         const lineItems = await storage.getBidLineItems(bid.id);
 *         return { ...bid, lineItems };
 *       })
 *     );
 *     return { ...rfq, bids: bidsWithLineItems };
 *   })
 * );
 * 
 * // NEW optimized version:
 * const rfqsWithBids = await getRfqsWithBidsAndLineItems(rfqs.map(r => r.id));
 */
