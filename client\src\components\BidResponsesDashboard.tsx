import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { FileText, Eye, Calendar, DollarSign, Clock } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useState } from "react";

interface BidResponsesDashboardProps {
  rfqId: string;
}

interface Bid {
  id: string;
  contractorId: string;
  bidAmount: number | null;
  timeline: string | null;
  scope: string | null;
  conditions: string | null;
  status: string;
  submittedAt: string;
  reviewedAt: string | null;
  reviewNotes: string | null;
  contractor?: {
    companyName: string;
    primaryContactName: string;
    primaryContactEmail: string;
  };
}

export function BidResponsesDashboard({ rfqId }: BidResponsesDashboardProps) {
  const [selectedBid, setSelectedBid] = useState<Bid | null>(null);
  const { data: bids = [], isLoading } = useQuery<Bid[]>({
    queryKey: ['/api/rfqs', rfqId, 'bids'],
  });

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'submitted':
        return 'border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-200';
      case 'under_review':
        return 'border-yellow-200 bg-yellow-50 text-yellow-700 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200';
      case 'accepted':
        return 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-200';
      case 'rejected':
        return 'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-200';
      default:
        return 'border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-200';
    }
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return 'Not specified';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Bid Responses...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (bids.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Bid Responses
          </CardTitle>
          <CardDescription>No bids have been submitted for this RFQ yet.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">
              Contractors will be able to submit their bids once the RFQ is distributed.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate summary statistics
  const totalBids = bids.length;
  // Calculate average bid amount from all submitted bids (including accepted and rejected)
  // Handle both string and number bid amounts
  const bidsWithAmounts = bids.filter(bid => {
    const amount = typeof bid.bidAmount === 'string' ? parseFloat(bid.bidAmount) : bid.bidAmount;
    return amount && amount > 0;
  });
  const avgBidAmount = bidsWithAmounts.length > 0 
    ? bidsWithAmounts.reduce((sum, bid) => {
        const amount = typeof bid.bidAmount === 'string' ? parseFloat(bid.bidAmount) : bid.bidAmount;
        return sum + (amount || 0);
      }, 0) / bidsWithAmounts.length
    : 0;
  


  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Bids</p>
                <p className="text-2xl font-bold">{totalBids}</p>
                <p className="text-xs text-muted-foreground">
                  {bids.filter(b => b.submittedAt > new Date(Date.now() - 24*60*60*1000).toISOString()).length} in last 24h
                </p>
              </div>
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Bid</p>
                <p className="text-2xl font-bold">
                  {avgBidAmount > 0 ? formatCurrency(avgBidAmount) : 'Not specified'}
                </p>
                <p className="text-xs text-muted-foreground">
                  {bidsWithAmounts.length > 1 ? (
                    `Range: ${formatCurrency(Math.min(...bidsWithAmounts.map(b => typeof b.bidAmount === 'string' ? parseFloat(b.bidAmount) : b.bidAmount!)))} - ${formatCurrency(Math.max(...bidsWithAmounts.map(b => typeof b.bidAmount === 'string' ? parseFloat(b.bidAmount) : b.bidAmount!)))}`
                  ) : (
                    `${bidsWithAmounts.length} with amounts`
                  )}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Response Rate</p>
                <p className="text-2xl font-bold">
                  {Math.round((totalBids / Math.max(1, totalBids + 5)) * 100)}%
                </p>
                <p className="text-xs text-muted-foreground">
                  {bids.filter(b => b.status === 'submitted').length} pending review
                </p>
              </div>
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bids Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Bid Responses ({totalBids})
          </CardTitle>
          <CardDescription>
            Review and manage all bid submissions for this RFQ
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contractor</TableHead>
                <TableHead>Bid Amount</TableHead>
                <TableHead>Timeline</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bids.map((bid) => (
                <TableRow key={bid.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">
                        {bid.contractor?.companyName || 'Unknown Company'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {bid.contractor?.primaryContactName || 'No contact name'}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {formatCurrency(typeof bid.bidAmount === 'string' ? parseFloat(bid.bidAmount) : bid.bidAmount)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {bid.timeline || 'Not specified'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${getStatusColor(bid.status)}`}>
                      {bid.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {formatDistanceToNow(new Date(bid.submittedAt), { addSuffix: true })}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setSelectedBid(bid)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Bid Details Modal */}
      <Dialog open={!!selectedBid} onOpenChange={() => setSelectedBid(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Bid Details - {selectedBid?.contractor?.companyName}</DialogTitle>
          </DialogHeader>
          {selectedBid && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Company Information</h4>
                  <p><strong>Company:</strong> {selectedBid.contractor?.companyName}</p>
                  <p><strong>Contact:</strong> {selectedBid.contractor?.primaryContactName}</p>
                  <p><strong>Email:</strong> {selectedBid.contractor?.primaryContactEmail}</p>
                  <p><strong>Status:</strong> <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${getStatusColor(selectedBid.status)}`}>{selectedBid.status}</span></p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Bid Information</h4>
                  <p><strong>Amount:</strong> {formatCurrency(selectedBid.bidAmount)}</p>
                  <p><strong>Timeline:</strong> {selectedBid.timeline || 'Not specified'}</p>
                  <p><strong>Confidence:</strong> {Math.round(selectedBid.confidenceScore * 100)}%</p>
                  <p><strong>Submitted:</strong> {formatDistanceToNow(new Date(selectedBid.submittedAt), { addSuffix: true })}</p>
                </div>
              </div>
              
              {selectedBid.scope && (
                <div>
                  <h4 className="font-semibold mb-2">Scope of Work</h4>
                  <p className="text-sm bg-muted p-3 rounded-lg">{selectedBid.scope}</p>
                </div>
              )}
              
              {selectedBid.conditions && (
                <div>
                  <h4 className="font-semibold mb-2">Terms & Conditions</h4>
                  <p className="text-sm bg-muted p-3 rounded-lg">{selectedBid.conditions}</p>
                </div>
              )}
              
              {selectedBid.reviewNotes && (
                <div>
                  <h4 className="font-semibold mb-2">Review Notes</h4>
                  <p className="text-sm bg-muted p-3 rounded-lg">{selectedBid.reviewNotes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}