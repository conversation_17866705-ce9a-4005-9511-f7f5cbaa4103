// Test PDF extraction in production-like environment
import { unifiedPDFExtractor } from './server/services/core/pdfExtractor.ts';
import fs from 'fs';
import path from 'path';

// Set production environment
process.env.NODE_ENV = 'production';

async function testPDFExtraction() {
  console.log('🧪 Testing PDF extraction in production mode...');
  
  try {
    // Use a known PDF file from attached_assets
    const testFile = 'RFQ #2021-301 Response - ACCENT ELECTRICAL_1753707304170.pdf';
    const filePath = path.join('./attached_assets', testFile);
    
    if (!fs.existsSync(filePath)) {
      console.log('❌ Test PDF file not found:', filePath);
      return;
    }
    
    const fileBuffer = fs.readFileSync(filePath);
    
    console.log(`📄 Testing with file: ${testFile}`);
    console.log(`📊 File size: ${fileBuffer.length} bytes`);
    
    // Test extraction
    const result = await unifiedPDFExtractor.extractText(fileBuffer);
    
    console.log('✅ Extraction Results:');
    console.log(`   Success: ${result.success}`);
    console.log(`   Method: ${result.extractionMethod}`);
    console.log(`   Processing Time: ${result.processingTime}ms`);
    console.log(`   Page Count: ${result.pageCount}`);
    console.log(`   Confidence: ${result.confidence}%`);
    console.log(`   Text Length: ${result.text.length} characters`);
    
    if (result.text.length > 0) {
      console.log(`   Text Preview: ${result.text.substring(0, 200)}...`);
    }
    
    if (result.success) {
      console.log('🎉 PDF extraction test PASSED - Ready for Railway deployment!');
    } else {
      console.log('❌ PDF extraction test FAILED');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

testPDFExtraction();
