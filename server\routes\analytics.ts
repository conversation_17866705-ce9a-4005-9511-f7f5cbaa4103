/**
 * Enhanced Analytics API Routes
 */

import { Router } from "express";
import { protectedRoute, addUserToRequest } from "../clerkAuth";
import { storage } from "../storage";
import { 
  generateCompetitiveIntelligence,
  generateEnhancedRiskAssessment,
  generatePredictiveAnalytics,
  calculateWeightedEvaluationScores,
  EvaluationCriteria
} from "../services/enhancedBidAnalytics";
import { z } from "zod";

const router = Router();

// Default evaluation criteria
const DEFAULT_EVALUATION_CRITERIA: EvaluationCriteria = {
  technical: {
    weight: 0.3,
    maxScore: 100,
    factors: {
      experience: 0.4,
      expertise: 0.3,
      methodology: 0.2,
      innovation: 0.1,
    },
  },
  financial: {
    weight: 0.4,
    maxScore: 100,
    factors: {
      pricing: 0.5,
      costBreakdown: 0.2,
      valueEngineering: 0.2,
      paymentTerms: 0.1,
    },
  },
  timeline: {
    weight: 0.2,
    maxScore: 100,
    factors: {
      duration: 0.4,
      milestones: 0.3,
      resourceAvailability: 0.2,
      riskMitigation: 0.1,
    },
  },
  compliance: {
    weight: 0.1,
    maxScore: 100,
    factors: {
      licensing: 0.3,
      insurance: 0.3,
      safety: 0.2,
      certifications: 0.2,
    },
  },
};

/**
 * GET /api/analytics/competitive-intelligence/:rfqId
 * Get competitive intelligence analysis for an RFQ
 */
router.get("/competitive-intelligence/:rfqId", protectedRoute, addUserToRequest, async (req: any, res) => {
  try {
    const rfqId = req.params.rfqId;
    const rfq = await storage.getRfq(rfqId);
    if (!rfq) return res.status(404).json({ message: 'RFQ not found' });
    const user = await storage.getUser(req.user?.claims?.sub);
    if (user && user.userClassification === 'general_contractor') {
      if (!user.organizationId || rfq.organizationId !== user.organizationId) {
        return res.status(403).json({ message: 'Access denied' });
      }
    }

    const intelligence = await generateCompetitiveIntelligence(rfqId);
    res.json(intelligence);
  } catch (error) {
    console.error("Competitive intelligence error:", error);
    res.status(500).json({ message: "Failed to generate competitive intelligence" });
  }
});

/**
 * GET /api/analytics/risk-assessment/:bidId
 * Get enhanced risk assessment for a bid
 */
router.get("/risk-assessment/:bidId", protectedRoute, addUserToRequest, async (req: any, res) => {
  try {
    const bidId = req.params.bidId;
    const bid = await storage.getBid(bidId);
    if (!bid) return res.status(404).json({ message: 'Bid not found' });
    const rfq = bid.rfqId ? await storage.getRfq(bid.rfqId) : null;
    if (!rfq) return res.status(404).json({ message: 'Associated RFQ not found' });

    // Allow if requester is RFQ owner or bid owner
    const userId = req.user?.claims?.sub;
    const contractor = await storage.getContractorByUserId(userId);
    const isRfqOwner = rfq.createdBy === userId;
    const isBidOwner = contractor && bid.contractorId === contractor.id;
    if (!isRfqOwner && !isBidOwner) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const riskAssessment = await generateEnhancedRiskAssessment(bidId);
    res.json(riskAssessment);
  } catch (error) {
    console.error("Risk assessment error:", error);
    res.status(500).json({ message: "Failed to generate risk assessment" });
  }
});

/**
 * GET /api/analytics/predictive-analytics/:rfqId
 * Get predictive analytics for project success
 */
router.get("/predictive-analytics/:rfqId", protectedRoute, addUserToRequest, async (req: any, res) => {
  try {
    const rfqId = req.params.rfqId;
    const rfq = await storage.getRfq(rfqId);
    if (!rfq) return res.status(404).json({ message: 'RFQ not found' });
    const user = await storage.getUser(req.user?.claims?.sub);
    if (user && user.userClassification === 'general_contractor') {
      if (!user.organizationId || rfq.organizationId !== user.organizationId) {
        return res.status(403).json({ message: 'Access denied' });
      }
    }

    const predictiveAnalytics = await generatePredictiveAnalytics(rfqId);
    res.json(predictiveAnalytics);
  } catch (error) {
    console.error("Predictive analytics error:", error);
    res.status(500).json({ message: "Failed to generate predictive analytics" });
  }
});

/**
 * POST /api/analytics/evaluation-scores/:rfqId
 * Calculate weighted evaluation scores for all bids in an RFQ
 */
router.post("/evaluation-scores/:rfqId", protectedRoute, addUserToRequest, async (req, res) => {
  try {
    const rfqId = req.params.rfqId;
    const criteria = req.body.criteria || DEFAULT_EVALUATION_CRITERIA;
    
    const evaluationScores = await calculateWeightedEvaluationScores(rfqId, criteria);
    res.json(evaluationScores);
  } catch (error) {
    console.error("Evaluation scores error:", error);
    res.status(500).json({ message: "Failed to calculate evaluation scores" });
  }
});

/**
 * GET /api/analytics/bid-analysis/:rfqId
 * Get comprehensive bid analysis dashboard data
 */
router.get("/bid-analysis/:rfqId", protectedRoute, addUserToRequest, async (req: any, res) => {
  try {
    const rfqId = req.params.rfqId;
    const rfq = await storage.getRfq(rfqId);
    if (!rfq) return res.status(404).json({ message: 'RFQ not found' });
    const user = await storage.getUser(req.user?.claims?.sub);
    if (user && user.userClassification === 'general_contractor') {
      if (!user.organizationId || rfq.organizationId !== user.organizationId) {
        return res.status(403).json({ message: 'Access denied' });
      }
    }
    
    // Generate all analytics in parallel
    const [
      competitiveIntelligence,
      predictiveAnalytics,
      evaluationScores
    ] = await Promise.all([
      generateCompetitiveIntelligence(rfqId).catch(err => {
        console.error("Competitive intelligence failed:", err);
        return null;
      }),
      generatePredictiveAnalytics(rfqId).catch(err => {
        console.error("Predictive analytics failed:", err);
        return null;
      }),
      calculateWeightedEvaluationScores(rfqId, DEFAULT_EVALUATION_CRITERIA).catch(err => {
        console.error("Evaluation scores failed:", err);
        return [];
      })
    ]);

    const analysisData = {
      competitiveIntelligence,
      predictiveAnalytics,
      evaluationScores,
      generatedAt: new Date().toISOString(),
      rfqId,
    };

    res.json(analysisData);
  } catch (error) {
    console.error("Bid analysis error:", error);
    res.status(500).json({ message: "Failed to generate bid analysis" });
  }
});

/**
 * GET /api/analytics/market-intelligence/:region/:trade
 * Get market intelligence data for a specific region and trade
 */
router.get("/market-intelligence/:region/:trade", protectedRoute, addUserToRequest, async (req, res) => {
  try {
    const { region, trade } = req.params;
    
    // For now, return mock data structure
    // In production, this would query the market_intelligence table
    const marketData = {
      region,
      tradeType: trade,
      pricing: {
        avgPricePerSqFt: 125.50,
        medianBidAmount: 450000,
        avgLaborCost: 65.00,
        avgMaterialCost: 45.00,
        overheadPercentage: 15.5,
      },
      marketMetrics: {
        competitionLevel: 0.75,
        demandLevel: 'high',
        priceVolatility: 8.2,
        seasonalFactor: 1.05,
      },
      trends: {
        priceChangePercent: 3.2,
        trendDirection: 'up',
        marketConfidence: 0.82,
      },
      benchmarks: {
        industryAverage: 425000,
        topPerformer: 380000,
        budgetOption: 520000,
        premiumOption: 325000,
      },
      lastUpdated: new Date().toISOString(),
    };

    res.json(marketData);
  } catch (error) {
    console.error("Market intelligence error:", error);
    res.status(500).json({ message: "Failed to get market intelligence" });
  }
});

/**
 * GET /api/analytics/default-criteria
 * Get default evaluation criteria template
 */
router.get("/default-criteria", protectedRoute, addUserToRequest, async (req, res) => {
  try {
    res.json(DEFAULT_EVALUATION_CRITERIA);
  } catch (error) {
    console.error("Default criteria error:", error);
    res.status(500).json({ message: "Failed to get default criteria" });
  }
});

export default router;