#!/bin/bash

# Bidaible Unified File Processing Test Suite Setup Script
# This script helps set up the test environment and dependencies

set -e  # Exit on any error

echo "🚀 Setting up Bidaible Unified File Processing Test Suite"
echo "========================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js is installed: $NODE_VERSION"
        
        # Check if version is >= 16
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR" -lt 16 ]; then
            print_error "Node.js version 16 or higher is required. Current version: $NODE_VERSION"
            exit 1
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
}

# Check if npm is installed
check_npm() {
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_status "npm is installed: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
}

# Install test dependencies
install_dependencies() {
    print_info "Installing test dependencies..."
    
    if [ -f "package.json" ]; then
        npm install
        print_status "Dependencies installed successfully"
    else
        print_error "package.json not found. Make sure you're in the tests directory."
        exit 1
    fi
}

# Check if server is running
check_server() {
    print_info "Checking if Bidaible server is running..."
    
    if curl -s http://localhost:5000/api/debug/assets > /dev/null 2>&1; then
        print_status "Bidaible server is running on localhost:5000"
    else
        print_warning "Bidaible server is not running on localhost:5000"
        print_info "Please start the server with: npm run dev"
    fi
}

# Check environment variables
check_environment() {
    print_info "Checking environment variables..."
    
    # Check if .env file exists in parent directory
    if [ -f "../.env" ]; then
        print_status ".env file found in parent directory"
        
        # Check for required Wasabi variables
        if grep -q "WASABI_ACCESS_KEY_ID" "../.env" && grep -q "WASABI_SECRET_ACCESS_KEY" "../.env"; then
            print_status "Wasabi storage credentials configured"
        else
            print_warning "Wasabi storage credentials not found in .env file"
            print_info "Please add WASABI_ACCESS_KEY_ID and WASABI_SECRET_ACCESS_KEY to .env"
        fi
        
        # Check for AI service keys
        if grep -q "GROQ_API_KEY\|OPENAI_API_KEY\|GEMINI_API_KEY" "../.env"; then
            print_status "AI service API keys configured"
        else
            print_warning "No AI service API keys found in .env file"
            print_info "Please add at least one of: GROQ_API_KEY, OPENAI_API_KEY, GEMINI_API_KEY"
        fi
        
        # Check for database URL
        if grep -q "DATABASE_URL" "../.env"; then
            print_status "Database URL configured"
        else
            print_warning "DATABASE_URL not found in .env file"
        fi
        
    else
        print_warning ".env file not found in parent directory"
        print_info "Please create a .env file with required environment variables"
        print_info "See README.md for required variables"
    fi
}

# Create sample environment file
create_sample_env() {
    if [ ! -f "../.env" ] && [ ! -f "../.env.example" ]; then
        print_info "Creating sample .env.example file..."
        
        cat > "../.env.example" << EOF
# Wasabi Storage (Required)
WASABI_ACCESS_KEY_ID=your_wasabi_access_key_id
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_access_key
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com
WASABI_REGION=us-east-1

# AI Services (At least one required)
GROQ_API_KEY=gsk_your_groq_api_key
OPENAI_API_KEY=sk-your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# Database
DATABASE_URL=postgresql://username:password@host:port/database

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
EOF
        
        print_status "Created .env.example file"
        print_info "Copy .env.example to .env and fill in your actual values"
    fi
}

# Run a quick test
run_quick_test() {
    print_info "Running environment validation test..."
    
    if node -e "
        const axios = require('axios');
        axios.get('http://localhost:5000/api/debug/assets', { timeout: 5000 })
            .then(() => console.log('✅ Server connectivity test passed'))
            .catch(() => console.log('⚠️  Server connectivity test failed - server may not be running'));
    " 2>/dev/null; then
        print_status "Quick connectivity test completed"
    else
        print_warning "Could not run connectivity test - axios may not be installed yet"
    fi
}

# Main setup process
main() {
    echo
    print_info "Starting setup process..."
    echo
    
    # Step 1: Check prerequisites
    print_info "Step 1: Checking prerequisites..."
    check_node
    check_npm
    echo
    
    # Step 2: Install dependencies
    print_info "Step 2: Installing dependencies..."
    install_dependencies
    echo
    
    # Step 3: Check environment
    print_info "Step 3: Checking environment configuration..."
    check_environment
    create_sample_env
    echo
    
    # Step 4: Check server
    print_info "Step 4: Checking server status..."
    check_server
    echo
    
    # Step 5: Quick test
    print_info "Step 5: Running quick test..."
    run_quick_test
    echo
    
    # Final instructions
    echo "========================================================"
    print_status "Setup completed successfully!"
    echo
    print_info "Next steps:"
    echo "  1. Ensure your .env file has all required variables"
    echo "  2. Start the Bidaible server: npm run dev"
    echo "  3. Run the test suite: npm test"
    echo
    print_info "Available test commands:"
    echo "  npm test              # Run all tests"
    echo "  npm run test:wasabi   # Run Wasabi storage tests"
    echo "  npm run test:ai       # Run AI processing tests"
    echo "  npm run test:watch    # Run tests in watch mode"
    echo "  npm run report        # Open HTML test report"
    echo
    print_info "For detailed instructions, see README.md"
    echo "========================================================"
}

# Run main function
main "$@"
