
# Component Documentation

## Overview

This document provides detailed documentation for all React components in the Bidaible platform, organized by functionality and purpose.

## Core UI Components (`client/src/components/ui/`)

### Button Component
```typescript
// File: client/src/components/ui/button.tsx
interface ButtonProps {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  asChild?: boolean
}
```
**Purpose**: Reusable button component with multiple variants and sizes
**Usage**: Primary actions, form submissions, navigation

### Card Component
```typescript
// File: client/src/components/ui/card.tsx
interface CardProps {
  className?: string
  children: React.ReactNode
}
```
**Purpose**: Container component for grouped content
**Usage**: Dashboard widgets, forms, content sections

### Tabs Component
```typescript
// File: client/src/components/ui/tabs.tsx
interface TabsProps {
  defaultValue?: string
  value?: string
  onValueChange?: (value: string) => void
}
```
**Purpose**: Tabbed interface for organizing content
**Usage**: Settings pages, detailed views, multi-section forms

## Business Logic Components

### RfqDetailView
**File**: `client/src/components/RfqDetailView.tsx`
**Purpose**: Comprehensive RFQ detail display with tabbed interface
**Features**:
- Document viewing and management
- Master summary generation and display
- Bid comparison matrix
- AI-powered analysis
- Bid responses tracking
- Distribution management
- Favorites management

**Key Props**:
```typescript
interface RfqDetailViewProps {
  rfqId: string
}
```

**State Management**:
- Uses TanStack Query for data fetching
- Optimistic updates for user interactions
- Real-time bid tracking

### MasterSummaryView
**File**: `client/src/components/MasterSummaryView.tsx`
**Purpose**: Master summary generation and conflict detection
**Features**:
- AI-powered document consolidation
- Conflict flag visualization
- Summary regeneration
- Last updated tracking

**Key Props**:
```typescript
interface MasterSummaryViewProps {
  rfqId: string
}

interface ConflictFlag {
  type: 'date' | 'amount' | 'requirement' | 'specification'
  field: string
  documents: string[]
  values: any[]
  severity: 'low' | 'medium' | 'high'
}
```

### BidComparisonMatrix
**File**: `client/src/components/BidComparisonMatrix.tsx`
**Purpose**: Advanced bid comparison with inclusion/exclusion analysis
**Features**:
- Side-by-side bid comparison
- Inclusion/exclusion tracking
- Risk factor analysis
- Timeline and warranty comparison

**Key Props**:
```typescript
interface BidComparisonMatrixProps {
  rfqId: string
}

interface BidComparison {
  bidId: string
  contractorName: string
  bidAmount: number
  inclusions: string[]
  exclusions: string[]
  keyDifferentiators: string[]
  riskFactors: string[]
  timeline: string
  warranty: string
}
```

### BidAnalyticsDashboard
**File**: `client/src/components/BidAnalyticsDashboard.tsx`
**Purpose**: AI-powered bid analysis and competitive intelligence
**Features**:
- Executive summary generation
- Competitive scoring and ranking
- Risk assessment
- Market analysis
- Real-time AI processing with Groq

### DashboardStats
**File**: `client/src/components/DashboardStats.tsx`
**Purpose**: Main dashboard statistics display
**Features**:
- Active RFQ count
- Total bids received
- Average bid amounts
- Performance metrics

### ContractorProfileForm
**File**: `client/src/components/ContractorProfileForm.tsx`
**Purpose**: Comprehensive contractor profile management
**Features**:
- 29 trade specialties
- License and insurance tracking
- Contact information management
- Verification status

### RFQForm
**File**: `client/src/components/RFQForm.tsx`
**Purpose**: RFQ creation and editing
**Features**:
- Multi-step form process
- Document upload integration
- AI data extraction
- Contractor selection

## Modal Components

### RFQDetailsModal
**File**: `client/src/components/RFQDetailsModal.tsx`
**Purpose**: Quick RFQ overview in modal format
**Features**:
- Summary information display
- Document list
- Bid submission interface
- Action buttons

### BidDetailsModal
**File**: `client/src/components/BidDetailsModal.tsx`
**Purpose**: Detailed bid information display
**Features**:
- Bid amount and timeline
- Contractor information
- Supporting documents
- Analysis results

### ProfileModal
**File**: `client/src/components/ProfileModal.tsx`
**Purpose**: User profile management
**Features**:
- Account information editing
- Preference settings
- Security options

## Navigation Components

### Navbar
**File**: `client/src/components/Navbar.tsx`
**Purpose**: Top navigation with user controls
**Features**:
- User authentication status
- Profile dropdown
- Notification center
- Theme toggle

### Sidebar
**File**: `client/src/components/Sidebar.tsx`
**Purpose**: Main navigation sidebar
**Features**:
- Role-based menu items
- Active page highlighting
- Collapsible design
- Quick actions

## Utility Components

### PDFViewer
**File**: `client/src/components/PDFViewer.tsx`
**Purpose**: Enhanced PDF document viewing
**Features**:
- New tab opening
- Object storage integration
- Error handling
- Loading states

### UploadProgressIndicator
**File**: `client/src/components/UploadProgressIndicator.tsx`
**Purpose**: File upload progress tracking
**Features**:
- Real-time progress updates
- Error state handling
- Multi-file support
- Cancellation support

### ThemeProvider
**File**: `client/src/components/ThemeProvider.tsx`
**Purpose**: Theme management and context
**Features**:
- Light/dark theme support
- System preference detection
- Persistent theme storage
- Component theme context

## Form Components

### BidSubmissionForm
**File**: `client/src/components/BidSubmissionForm.tsx`
**Purpose**: Contractor bid submission interface
**Features**:
- Bid amount entry
- Timeline specification
- Document upload
- Terms and conditions

### RFQEditForm
**File**: `client/src/components/RFQEditForm.tsx`
**Purpose**: RFQ modification interface
**Features**:
- Editable project details
- Document management
- Deadline updates
- Contractor list modification

## Management Components

### ContractorFavoritesManager
**File**: `client/src/components/ContractorFavoritesManager.tsx`
**Purpose**: Contractor favorites management
**Features**:
- Add/remove favorites
- Bulk selection
- Category filtering
- Quick invite actions

### RfqDistributionManager
**File**: `client/src/components/RfqDistributionManager.tsx`
**Purpose**: RFQ distribution and invitation management
**Features**:
- Contractor selection
- Invitation tracking
- Response monitoring
- Deadline management

## Dashboard Widgets

### BidManagementWidget
**File**: `client/src/components/BidManagementWidget.tsx`
**Purpose**: Embedded bid management dashboard
**Features**:
- Prominently featured on main dashboard
- AI analysis integration
- Quick bid actions
- Real-time updates

### DocumentCount
**File**: `client/src/components/DocumentCount.tsx`
**Purpose**: Document count display widget
**Features**:
- Real-time document counting
- Upload status tracking
- Processing progress

### NotificationDropdown
**File**: `client/src/components/NotificationDropdown.tsx`
**Purpose**: Notification center interface
**Features**:
- Real-time notifications
- Categorized alerts
- Mark as read functionality
- Action shortcuts

## Component Architecture Patterns

### Data Fetching
All components use TanStack Query for:
- Caching
- Background updates
- Optimistic mutations
- Error handling

### State Management
- Local component state with useState
- Form state with React Hook Form
- Global state through React Context
- Server state via TanStack Query

### Error Handling
- Comprehensive error boundaries
- User-friendly error messages
- Fallback UI components
- Retry mechanisms

### Performance Optimization
- React.memo for expensive components
- useMemo for computed values
- useCallback for event handlers
- Lazy loading for large components

### Accessibility
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Focus management

## Testing Strategy

### Unit Tests
- Component rendering tests
- Props validation
- Event handling verification
- State management testing

### Integration Tests
- Component interaction testing
- API integration verification
- Form submission flows
- Navigation testing

### End-to-End Tests
- Complete user workflows
- Cross-browser compatibility
- Mobile responsiveness
- Performance benchmarks
