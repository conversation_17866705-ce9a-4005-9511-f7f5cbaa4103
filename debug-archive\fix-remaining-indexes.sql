-- Fix remaining sequential scans with correct column names
-- Based on actual schema in shared/schema.ts

-- ==============================================================================
-- NOTIFICATION TABLE INDEXES
-- Fix sequential scans for unread notification counts
-- ==============================================================================

-- Index for unread notifications (WHERE read_at IS NULL)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread 
  ON notifications(user_id, organization_id) 
  WHERE read_at IS NULL;

-- Index for notification priority filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_priority_created 
  ON notifications(priority, created_at DESC);

-- ==============================================================================  
-- RFQ TABLE INDEXES
-- Fix sequential scans for status filtering
-- ==============================================================================

-- Index for active RFQ counts (WHERE status = 'Active')
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfqs_active_status 
  ON rfqs(status) 
  WHERE status = 'Active';

-- Enhanced RFQ status filtering with organization isolation
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfqs_org_status_created 
  ON rfqs(organization_id, status, created_at DESC);

-- ==============================================================================
-- API KEY USAGE TABLE INDEXES  
-- Add the unique constraint needed for UPSERT operations
-- ==============================================================================

-- Unique constraint for UPSERT operations (prevents conflicts)
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_api_key_usage_unique_daily 
  ON api_key_usage(api_key_id, request_date, endpoint, method);

-- ==============================================================================
-- VERIFICATION QUERIES
-- Run these after applying indexes to check performance
-- ==============================================================================

/*
-- Test unread notification query performance
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*) as unread_count
FROM notifications 
WHERE read_at IS NULL 
  AND created_at >= NOW() - INTERVAL '30 days';

-- Test active RFQ count performance  
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*) as active_count
FROM rfqs 
WHERE status = 'Active';

-- Test API key UPSERT performance
EXPLAIN (ANALYZE, BUFFERS)
INSERT INTO api_key_usage (api_key_id, endpoint, method, request_count, request_date, last_request_at)
VALUES (gen_random_uuid(), '/api/test', 'GET', 1, CURRENT_DATE, NOW())
ON CONFLICT (api_key_id, request_date, endpoint, method) 
DO UPDATE SET 
  request_count = api_key_usage.request_count + 1,
  last_request_at = NOW();
*/
