module.exports = {
  root: true,
  ignorePatterns: ["dist/**", "node_modules/**"],
  env: { node: true, es2022: true },
  parser: "@typescript-eslint/parser",
  parserOptions: { sourceType: "module", ecmaVersion: "latest", project: false },
  plugins: ["@typescript-eslint"],
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  rules: {
    "no-console": ["warn", { allow: ["warn", "error", "info"] }],
    "@typescript-eslint/no-explicit-any": "off"
  },
};
