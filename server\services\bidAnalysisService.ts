/**
 * AI-Powered Bid Analysis Service
 * Provides competitive scoring, summaries, and insights for bid evaluation
 */

import { GoogleGenerativeAI } from "@google/generative-ai";
import OpenAI from "openai";
import { storage } from "../storage";
import type { Bid, Contractor } from "@shared/schema";

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY || "" });

// Primary model configuration
const PRIMARY_MODEL = process.env.PRIMARY_MODEL || "openai";

interface BidAnalysisResult {
  aiSummary: string;
  competitiveScore: number;
  aiAnalysis: {
    strengths: string[];
    concerns: string[];
    competitivePosition: string;
    priceAnalysis: string;
    timelineAssessment: string;
    contractorProfile: string;
    riskFactors: string[];
    recommendations: string[];
  };
}

interface CompetitiveContext {
  rfqId: string;
  allBids: Array<{
    bid: Bid;
    contractor: Contractor;
  }>;
  targetBid: Bid;
  targetContractor: Contractor;
}

/**
 * Generate comprehensive AI analysis for a bid with competitive context
 */
export async function generateBidAnalysis(
  bidId: string,
  refreshCache: boolean = false
): Promise<BidAnalysisResult> {
  try {
    // Get the target bid and contractor
    const bid = await storage.getBid(bidId);
    if (!bid) {
      throw new Error("Bid not found");
    }

    const contractor = await storage.getContractor(bid.contractorId!);
    if (!contractor) {
      throw new Error("Contractor not found");
    }

    // Get all bids for this RFQ for competitive analysis
    const allBids = await storage.getBidsByRfq(bid.rfqId!);
    const bidContractors = await Promise.all(
      allBids.map(async (b) => ({
        bid: b,
        contractor: await storage.getContractor(b.contractorId!),
      }))
    );

    const context: CompetitiveContext = {
      rfqId: bid.rfqId!,
      allBids: bidContractors.filter(bc => bc.contractor) as Array<{
        bid: Bid;
        contractor: Contractor;
      }>,
      targetBid: bid,
      targetContractor: contractor,
    };

    // Generate analysis using AI
    const analysis = await generateAIAnalysis(context);
    
    // Calculate competitive score
    const competitiveScore = calculateCompetitiveScore(context);

    const result: BidAnalysisResult = {
      aiSummary: analysis.summary,
      competitiveScore,
      aiAnalysis: analysis.detailed,
    };

    // Update the bid with analysis results
    await storage.updateBid(bidId, {
      aiSummary: result.aiSummary,
      competitiveScore: result.competitiveScore.toString(),
      aiAnalysis: result.aiAnalysis,
    });

    return result;
  } catch (error) {
    console.error("Bid analysis error:", error);
    throw new Error(`Failed to analyze bid: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate AI analysis using primary/fallback model approach
 */
async function generateAIAnalysis(context: CompetitiveContext): Promise<{
  summary: string;
  detailed: any;
}> {
  const prompt = buildAnalysisPrompt(context);
  
  try {
    if (PRIMARY_MODEL === "openai") {
      return await generateOpenAIAnalysis(prompt);
    } else {
      return await generateGeminiAnalysis(prompt);
    }
  } catch (error) {
    console.warn(`Primary model (${PRIMARY_MODEL}) failed, trying fallback:`, error);
    
    // Fallback to alternative model
    try {
      if (PRIMARY_MODEL === "openai") {
        return await generateGeminiAnalysis(prompt);
      } else {
        return await generateOpenAIAnalysis(prompt);
      }
    } catch (fallbackError) {
      console.error("Both AI models failed:", fallbackError);
      return {
        summary: "AI analysis unavailable - manual review required",
        detailed: {
          strengths: ["Manual review needed"],
          concerns: ["AI analysis failed"],
          competitivePosition: "Unable to determine",
          priceAnalysis: "Manual analysis required",
          timelineAssessment: "Manual analysis required",
          contractorProfile: context.targetContractor.companyName,
          riskFactors: ["AI analysis unavailable"],
          recommendations: ["Conduct manual bid review"],
        },
      };
    }
  }
}

/**
 * Generate analysis using OpenAI
 */
async function generateOpenAIAnalysis(prompt: string): Promise<{
  summary: string;
  detailed: any;
}> {
  const response = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content: "You are an expert construction bid analyst. Provide comprehensive, objective analysis of bid packages with competitive insights. Return valid JSON only.",
      },
      {
        role: "user",
        content: prompt,
      },
    ],
    temperature: 0.3,
    max_tokens: 2000,
  });

  const content = response.choices[0]?.message?.content;
  if (!content) {
    throw new Error("No response from OpenAI");
  }

  const parsed = JSON.parse(content);
  return {
    summary: parsed.summary,
    detailed: parsed.analysis,
  };
}

/**
 * Generate analysis using Gemini
 */
async function generateGeminiAnalysis(prompt: string): Promise<{
  summary: string;
  detailed: any;
}> {
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
  
  const result = await model.generateContent([
    {
      text: `You are an expert construction bid analyst. Provide comprehensive, objective analysis of bid packages with competitive insights. Return valid JSON only.\n\n${prompt}`,
    },
  ]);

  const content = result.response.text();
  if (!content) {
    throw new Error("No response from Gemini");
  }

  const parsed = JSON.parse(content);
  return {
    summary: parsed.summary,
    detailed: parsed.analysis,
  };
}

/**
 * Build comprehensive analysis prompt
 */
function buildAnalysisPrompt(context: CompetitiveContext): string {
  const { targetBid, targetContractor, allBids } = context;
  
  // Prepare competitive data
  const competitorData = allBids
    .filter(bc => bc.bid.id !== targetBid.id)
    .map(bc => ({
      contractor: bc.contractor.companyName,
      amount: bc.bid.bidAmount || bc.bid.extractedAmount,
      timeline: bc.bid.timeline || bc.bid.extractedTimeline,
      tradeTypes: bc.contractor.tradeTypes,
    }));

  const prompt = `
Analyze this construction bid package with competitive context:

TARGET BID:
- Contractor: ${targetContractor.companyName}
- Bid Amount: $${targetBid.bidAmount || targetBid.extractedAmount || 'Not specified'}
- Timeline: ${targetBid.timeline || targetBid.extractedTimeline || 'Not specified'}
- Scope: ${targetBid.extractedScope || 'Not specified'}
- Conditions: ${targetBid.extractedConditions || 'Not specified'}

CONTRACTOR PROFILE:
- Company: ${targetContractor.companyName}
- Trade Types: ${JSON.stringify(targetContractor.tradeTypes)}
- Years in Business: ${targetContractor.yearsInBusiness || 'Not specified'}
- Workforce Size: ${targetContractor.workforceSize || 'Not specified'}
- Certifications: ${JSON.stringify(targetContractor.certifications)}
- License: ${targetContractor.licenseNumber || 'Not specified'} (${targetContractor.licenseState || 'Not specified'})

COMPETITIVE CONTEXT:
Total Bids Received: ${allBids.length}
Competitor Bids:
${competitorData.map(comp => `- ${comp.contractor}: $${comp.amount || 'TBD'}, Timeline: ${comp.timeline || 'TBD'}`).join('\n')}

Return JSON with this exact structure:
{
  "summary": "3-4 sentence summary of this bid's key points and competitive position",
  "analysis": {
    "strengths": ["List 3-5 key strengths of this bid"],
    "concerns": ["List 2-4 potential concerns or risks"],
    "competitivePosition": "Assessment of how this bid ranks among competitors",
    "priceAnalysis": "Analysis of pricing competitiveness and value",
    "timelineAssessment": "Evaluation of proposed timeline feasibility",
    "contractorProfile": "Assessment of contractor qualifications and reliability",
    "riskFactors": ["List 2-3 main risk factors to consider"],
    "recommendations": ["List 2-3 specific recommendations for the RFQ owner"]
  }
}`;

  return prompt;
}

/**
 * Calculate competitive score based on multiple factors
 */
function calculateCompetitiveScore(context: CompetitiveContext): number {
  const { targetBid, allBids } = context;
  
  if (allBids.length < 2) {
    return 0.75; // Default score for single bids
  }

  let score = 0;
  let factors = 0;

  // Price competitiveness (40% weight)
  const targetAmount = Number(targetBid.bidAmount || targetBid.extractedAmount || 0);
  if (targetAmount > 0) {
    const amounts = allBids
      .map(bc => Number(bc.bid.bidAmount || bc.bid.extractedAmount || 0))
      .filter(amount => amount > 0)
      .sort((a, b) => a - b);
    
    if (amounts.length > 1) {
      const position = amounts.indexOf(targetAmount);
      const priceScore = position === 0 ? 1.0 : 
                        position === 1 ? 0.9 :
                        position === amounts.length - 1 ? 0.4 :
                        0.7;
      score += priceScore * 0.4;
      factors += 0.4;
    }
  }

  // Extraction confidence (20% weight)
  const confidence = Number(targetBid.extractionConfidence || 0);
  score += confidence * 0.2;
  factors += 0.2;

  // Submission timing (20% weight) - earlier submissions score higher
  if (targetBid.submittedAt && allBids.length > 1) {
    const submissions = allBids
      .filter(bc => bc.bid.submittedAt)
      .sort((a, b) => new Date(a.bid.submittedAt!).getTime() - new Date(b.bid.submittedAt!).getTime());
    
    const position = submissions.findIndex(bc => bc.bid.id === targetBid.id);
    if (position >= 0) {
      const timingScore = 1 - (position / submissions.length);
      score += timingScore * 0.2;
      factors += 0.2;
    }
  }

  // Completeness (20% weight)
  let completenessFactors = 0;
  let completenessScore = 0;
  
  if (targetBid.bidAmount || targetBid.extractedAmount) {
    completenessScore += 0.25;
  }
  if (targetBid.timeline || targetBid.extractedTimeline) {
    completenessScore += 0.25;
  }
  if (targetBid.extractedScope) {
    completenessScore += 0.25;
  }
  if (targetBid.proposalText || targetBid.extractedConditions) {
    completenessScore += 0.25;
  }
  
  score += completenessScore * 0.2;
  factors += 0.2;

  return factors > 0 ? Math.min(1.0, Math.max(0.0, score / factors)) : 0.5;
}

/**
 * Batch update analysis for all bids in an RFQ
 */
export async function updateRfqBidAnalysis(rfqId: string): Promise<void> {
  try {
    const bids = await storage.getBidsByRfq(rfqId);
    
    // Process all bids in parallel
    await Promise.all(
      bids.map(bid => generateBidAnalysis(bid.id, true))
    );
    
    console.log(`Updated analysis for ${bids.length} bids in RFQ ${rfqId}`);
  } catch (error) {
    console.error("Failed to update RFQ bid analysis:", error);
    throw error;
  }
}

/**
 * Get RFQ summary with bid totals and buffer calculations
 */
export async function getRfqBidSummary(rfqId: string, includeAIData: boolean = false): Promise<{
  rfq: any;
  bidSummary: {
    totalBids: number;
    acceptedBids: number;
    rejectedBids: number;
    pendingBids: number;
    baseTotal: number;
    bufferAmount: number;
    totalWithBuffer: number;
  };
  bids: Array<{
    bid: Bid;
    contractor: Contractor;
  }>;
}> {
  try {
    const [rfq, bids] = await Promise.all([
      storage.getRfq(rfqId),
      storage.getBidsByRfq(rfqId),
    ]);

    if (!rfq) {
      throw new Error("RFQ not found");
    }

    // Enhanced bid data retrieval with AI analysis when requested
    const bidContractors = await Promise.all(
      bids.map(async (bid) => {
        let enhancedBid = bid;
        
        // Include stored AI analysis data if requested
        if (includeAIData && (bid.aiAnalysis || bid.aiSummary)) {
          console.log(`Including stored AI data for bid ${bid.id}`);
          enhancedBid = {
            ...bid,
            // AI data is already included in the bid object from storage
          };
        }
        
        return {
          bid: enhancedBid,
          contractor: await storage.getContractor(bid.contractorId!),
        };
      })
    );

    // Calculate summary - handle both "accept"/"Accepted" and "reject"/"Rejected" status values
    const acceptedBids = bids.filter(b => 
      b.status?.toLowerCase() === 'accept' || b.status?.toLowerCase() === 'accepted'
    );
    const baseTotal = acceptedBids.reduce((sum, bid) => {
      const amount = Number(bid.bidAmount || bid.extractedAmount || 0);
      return sum + amount;
    }, 0);

    const bufferPercentage = Number(rfq.bufferPercentage || 10) / 100;
    const bufferAmount = baseTotal * bufferPercentage;
    const totalWithBuffer = baseTotal + bufferAmount;

    return {
      rfq,
      bidSummary: {
        totalBids: bids.length,
        acceptedBids: acceptedBids.length,
        rejectedBids: bids.filter(b => 
          b.status?.toLowerCase() === 'reject' || b.status?.toLowerCase() === 'rejected'
        ).length,
        pendingBids: bids.filter(b => {
          const status = b.status?.toLowerCase() || '';
          return !['accept', 'accepted', 'reject', 'rejected'].includes(status);
        }).length,
        baseTotal,
        bufferAmount,
        totalWithBuffer,
      },
      bids: bidContractors.filter(bc => bc.contractor) as Array<{
        bid: Bid;
        contractor: Contractor;
      }>,
    };
  } catch (error) {
    console.error("Failed to get RFQ bid summary:", error);
    throw error;
  }
}