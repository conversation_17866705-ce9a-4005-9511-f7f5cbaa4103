/**
 * Master Test Runner for Unified File Processing & Wasabi Storage Integration
 * Orchestrates all test suites and provides comprehensive reporting
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Import test suites
const wasabiTests = require('./test-wasabi-integration');
const aiProcessingTests = require('./test-ai-processing');

// Test configuration
const BASE_URL = 'http://localhost:5000';
const RESULTS_DIR = path.join(__dirname, 'results');

// Master test results
const masterResults = {
  startTime: null,
  endTime: null,
  totalDuration: 0,
  suites: [],
  summary: {
    totalTests: 0,
    totalPassed: 0,
    totalFailed: 0,
    successRate: 0
  },
  environment: {
    nodeVersion: process.version,
    platform: process.platform,
    timestamp: new Date().toISOString()
  }
};

// Utility functions
function log(message, type = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${type}] ${message}`);
}

function ensureResultsDir() {
  if (!fs.existsSync(RESULTS_DIR)) {
    fs.mkdirSync(RESULTS_DIR, { recursive: true });
    log('Created results directory');
  }
}

// Environment validation
async function validateEnvironment() {
  log('🔍 Validating test environment...');
  
  const checks = {
    serverRunning: false,
    wasabiConfigured: false,
    aiServicesConfigured: false,
    databaseConnected: false
  };
  
  try {
    // Check if server is running
    const healthResponse = await axios.get(`${BASE_URL}/api/waitlist/count`, { timeout: 5000 });
    checks.serverRunning = healthResponse.status === 200;
    log(`✅ Server running: ${checks.serverRunning}`);
  } catch (error) {
    log(`❌ Server not accessible: ${error.message}`);
  }
  
  // Check environment variables
  checks.wasabiConfigured = !!(
    process.env.WASABI_ACCESS_KEY_ID &&
    process.env.WASABI_SECRET_ACCESS_KEY &&
    process.env.WASABI_BUCKET_NAME
  );
  log(`${checks.wasabiConfigured ? '✅' : '❌'} Wasabi storage configured: ${checks.wasabiConfigured}`);
  
  checks.aiServicesConfigured = !!(
    process.env.GROQ_API_KEY ||
    process.env.OPENAI_API_KEY ||
    process.env.GEMINI_API_KEY
  );
  log(`${checks.aiServicesConfigured ? '✅' : '❌'} AI services configured: ${checks.aiServicesConfigured}`);
  
  checks.databaseConnected = !!process.env.DATABASE_URL;
  log(`${checks.databaseConnected ? '✅' : '❌'} Database configured: ${checks.databaseConnected}`);
  
  const allChecksPass = Object.values(checks).every(check => check);
  
  if (!allChecksPass) {
    log('⚠️  Some environment checks failed. Tests may not run correctly.');
    log('Please ensure:');
    log('  - Server is running on localhost:5000');
    log('  - Wasabi storage credentials are configured');
    log('  - At least one AI service API key is configured');
    log('  - Database connection is configured');
  }
  
  return { checks, allChecksPass };
}

// Run individual test suite
async function runTestSuite(suiteName, testRunner) {
  log(`🚀 Starting ${suiteName} test suite...`);
  const startTime = Date.now();
  
  try {
    const results = await testRunner.runTests();
    const duration = Date.now() - startTime;
    
    const suiteResult = {
      name: suiteName,
      status: results.failed === 0 ? 'PASS' : 'FAIL',
      duration,
      totalTests: results.total,
      passed: results.passed,
      failed: results.failed,
      successRate: results.total > 0 ? (results.passed / results.total) * 100 : 0,
      details: results.details || [],
      performance: results.performance || []
    };
    
    masterResults.suites.push(suiteResult);
    
    log(`✅ ${suiteName} completed: ${results.passed}/${results.total} passed (${suiteResult.successRate.toFixed(1)}%)`);
    return suiteResult;
    
  } catch (error) {
    const duration = Date.now() - startTime;
    log(`❌ ${suiteName} failed: ${error.message}`);
    
    const suiteResult = {
      name: suiteName,
      status: 'ERROR',
      duration,
      totalTests: 0,
      passed: 0,
      failed: 1,
      successRate: 0,
      error: error.message,
      details: [],
      performance: []
    };
    
    masterResults.suites.push(suiteResult);
    return suiteResult;
  }
}

// Generate comprehensive HTML report
function generateHTMLReport() {
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bidaible Unified File Processing Test Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .summary-card.success { border-left-color: #28a745; }
        .summary-card.warning { border-left-color: #ffc107; }
        .summary-card.danger { border-left-color: #dc3545; }
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .summary-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        .suite {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        .suite-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .suite-header h3 {
            margin: 0;
            color: #2c3e50;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pass { background: #d4edda; color: #155724; }
        .status-fail { background: #f8d7da; color: #721c24; }
        .status-error { background: #f8d7da; color: #721c24; }
        .test-details {
            padding: 20px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-name {
            font-weight: 500;
            color: #2c3e50;
        }
        .test-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-duration {
            color: #6c757d;
            font-size: 0.9em;
        }
        .performance-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .performance-item {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Bidaible Unified File Processing Test Report</h1>
            <p>Wasabi Storage Integration & AI Processing Pipeline</p>
            <p><strong>Generated:</strong> ${masterResults.environment.timestamp}</p>
            <p><strong>Duration:</strong> ${Math.round(masterResults.totalDuration / 1000)}s</p>
        </div>

        <div class="summary">
            <div class="summary-card ${masterResults.summary.successRate >= 90 ? 'success' : masterResults.summary.successRate >= 70 ? 'warning' : 'danger'}">
                <h3>Success Rate</h3>
                <div class="value">${masterResults.summary.successRate.toFixed(1)}%</div>
            </div>
            <div class="summary-card success">
                <h3>Tests Passed</h3>
                <div class="value">${masterResults.summary.totalPassed}</div>
            </div>
            <div class="summary-card ${masterResults.summary.totalFailed > 0 ? 'danger' : 'success'}">
                <h3>Tests Failed</h3>
                <div class="value">${masterResults.summary.totalFailed}</div>
            </div>
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="value">${masterResults.summary.totalTests}</div>
            </div>
        </div>

        ${masterResults.suites.map(suite => `
        <div class="suite">
            <div class="suite-header">
                <h3>${suite.name}</h3>
                <span class="status-badge status-${suite.status.toLowerCase()}">${suite.status}</span>
            </div>
            <div class="test-details">
                <p><strong>Duration:</strong> ${Math.round(suite.duration / 1000)}s | 
                   <strong>Tests:</strong> ${suite.totalTests} | 
                   <strong>Passed:</strong> ${suite.passed} | 
                   <strong>Failed:</strong> ${suite.failed}</p>
                
                ${suite.details.map(test => `
                <div class="test-item">
                    <div class="test-name">${test.test}</div>
                    <div class="test-status">
                        <span class="status-badge status-${test.status.toLowerCase()}">${test.status}</span>
                        <span class="test-duration">${test.duration}ms</span>
                    </div>
                </div>
                `).join('')}
                
                ${suite.error ? `<p style="color: #dc3545; margin-top: 15px;"><strong>Error:</strong> ${suite.error}</p>` : ''}
            </div>
        </div>
        `).join('')}

        ${masterResults.suites.some(s => s.performance && s.performance.length > 0) ? `
        <div class="performance-section">
            <h3>📈 Performance Metrics</h3>
            <div class="performance-grid">
                ${masterResults.suites.flatMap(suite => 
                    (suite.performance || []).map(perf => `
                    <div class="performance-item">
                        <h4>${perf.test}</h4>
                        ${perf.processingTime ? `<p><strong>Processing Time:</strong> ${Math.round(perf.processingTime/1000)}s</p>` : ''}
                        ${perf.fileSize ? `<p><strong>File Size:</strong> ${Math.round(perf.fileSize/1024)}KB</p>` : ''}
                        ${perf.extractedTextLength ? `<p><strong>Extracted Text:</strong> ${perf.extractedTextLength} chars</p>` : ''}
                        ${perf.aiSummaryLength ? `<p><strong>AI Summary:</strong> ${perf.aiSummaryLength} chars</p>` : ''}
                    </div>
                    `)
                ).join('')}
            </div>
        </div>
        ` : ''}

        <div class="footer">
            <p>Generated by Bidaible Test Suite | Node.js ${masterResults.environment.nodeVersion} | ${masterResults.environment.platform}</p>
        </div>
    </div>
</body>
</html>
  `;
  
  const reportPath = path.join(RESULTS_DIR, 'test-report.html');
  fs.writeFileSync(reportPath, htmlContent);
  log(`📄 HTML report generated: ${reportPath}`);
  
  return reportPath;
}

// Generate summary report
function generateSummaryReport() {
  const summary = {
    testRun: {
      timestamp: masterResults.environment.timestamp,
      duration: masterResults.totalDuration,
      platform: masterResults.environment.platform,
      nodeVersion: masterResults.environment.nodeVersion
    },
    results: masterResults.summary,
    suites: masterResults.suites.map(suite => ({
      name: suite.name,
      status: suite.status,
      duration: suite.duration,
      tests: {
        total: suite.totalTests,
        passed: suite.passed,
        failed: suite.failed,
        successRate: suite.successRate
      },
      error: suite.error || null
    })),
    recommendations: []
  };
  
  // Add recommendations based on results
  if (masterResults.summary.successRate < 90) {
    summary.recommendations.push('Review failed tests and address underlying issues');
  }
  
  if (masterResults.suites.some(s => s.performance && s.performance.some(p => p.processingTime > 30000))) {
    summary.recommendations.push('Optimize processing performance for large files');
  }
  
  if (masterResults.suites.some(s => s.status === 'ERROR')) {
    summary.recommendations.push('Check environment configuration and service availability');
  }
  
  const summaryPath = path.join(RESULTS_DIR, 'test-summary.json');
  fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
  log(`📊 Summary report generated: ${summaryPath}`);
  
  return summaryPath;
}

// Main test runner
async function runAllTests() {
  log('🎯 Starting Comprehensive Test Suite for Unified File Processing');
  log('================================================================');
  
  masterResults.startTime = Date.now();
  
  // Setup
  ensureResultsDir();
  
  // Environment validation
  const envValidation = await validateEnvironment();
  if (!envValidation.allChecksPass) {
    log('⚠️  Proceeding with tests despite environment issues...');
  }
  
  // Run test suites
  const suites = [
    { name: 'Wasabi Storage Integration', runner: wasabiTests },
    { name: 'AI Processing & PDF Extraction', runner: aiProcessingTests }
  ];
  
  for (const suite of suites) {
    await runTestSuite(suite.name, suite.runner);
  }
  
  // Calculate totals
  masterResults.endTime = Date.now();
  masterResults.totalDuration = masterResults.endTime - masterResults.startTime;
  
  masterResults.summary.totalTests = masterResults.suites.reduce((sum, suite) => sum + suite.totalTests, 0);
  masterResults.summary.totalPassed = masterResults.suites.reduce((sum, suite) => sum + suite.passed, 0);
  masterResults.summary.totalFailed = masterResults.suites.reduce((sum, suite) => sum + suite.failed, 0);
  masterResults.summary.successRate = masterResults.summary.totalTests > 0 ? 
    (masterResults.summary.totalPassed / masterResults.summary.totalTests) * 100 : 0;
  
  // Generate reports
  log('================================================================');
  log('📊 Generating Test Reports...');
  
  const htmlReportPath = generateHTMLReport();
  const summaryReportPath = generateSummaryReport();
  
  // Save master results
  const masterResultsPath = path.join(RESULTS_DIR, 'master-results.json');
  fs.writeFileSync(masterResultsPath, JSON.stringify(masterResults, null, 2));
  
  // Final summary
  log('================================================================');
  log('🎉 Test Suite Completed!');
  log(`📊 Overall Results:`);
  log(`   Total Tests: ${masterResults.summary.totalTests}`);
  log(`   Passed: ${masterResults.summary.totalPassed}`);
  log(`   Failed: ${masterResults.summary.totalFailed}`);
  log(`   Success Rate: ${masterResults.summary.successRate.toFixed(1)}%`);
  log(`   Duration: ${Math.round(masterResults.totalDuration / 1000)}s`);
  log('');
  log('📄 Reports Generated:');
  log(`   HTML Report: ${htmlReportPath}`);
  log(`   Summary: ${summaryReportPath}`);
  log(`   Raw Data: ${masterResultsPath}`);
  
  // Suite-by-suite breakdown
  log('\n📋 Suite Breakdown:');
  masterResults.suites.forEach(suite => {
    const status = suite.status === 'PASS' ? '✅' : suite.status === 'FAIL' ? '❌' : '⚠️';
    log(`   ${status} ${suite.name}: ${suite.passed}/${suite.totalTests} (${suite.successRate.toFixed(1)}%)`);
  });
  
  // Recommendations
  if (masterResults.summary.successRate < 100) {
    log('\n💡 Recommendations:');
    if (masterResults.summary.successRate < 90) {
      log('   • Review failed tests and address underlying issues');
    }
    if (masterResults.suites.some(s => s.status === 'ERROR')) {
      log('   • Check environment configuration and service availability');
    }
    log('   • See detailed HTML report for specific test failures');
  }
  
  return masterResults;
}

// Export for programmatic use
module.exports = {
  runAllTests,
  masterResults
};

// Run tests if called directly
if (require.main === module) {
  runAllTests().then(results => {
    const exitCode = results.summary.totalFailed > 0 ? 1 : 0;
    process.exit(exitCode);
  }).catch(error => {
    console.error('Master test runner failed:', error);
    process.exit(1);
  });
}
