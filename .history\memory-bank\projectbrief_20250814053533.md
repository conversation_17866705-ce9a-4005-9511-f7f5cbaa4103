# Bidaible Project Brief

## Project Overview
Bidaible is a sophisticated AI-powered construction bidding platform built with enterprise-grade TypeScript architecture. The system delivers "wow factor" bid analysis through advanced competitive intelligence, risk assessment, and predictive analytics.

## Core Purpose
- **Primary Goal**: Streamline construction bidding processes through AI automation
- **Target Users**: General Contractors (GCs) and Subcontractors in construction industry
- **Key Value**: Transform manual bidding workflows into intelligent, automated processes

## Key Features
1. **AI-Powered Document Processing**
   - Multi-file RFQ upload with automatic data extraction
   - PDF, TXT, CSV support with 95%+ success rate
   - Real-time progress tracking via Server-Sent Events

2. **Intelligent Bid Analysis**
   - Executive summary generation with competitive intelligence
   - Automated bid ranking and scoring (0-100 scale)
   - Risk assessment and market analysis
   - Sub-3-second analysis powered by multi-provider AI

3. **Multi-Tenant SaaS Architecture**
   - Organization-based data isolation
   - Role-based access control (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Editor, Viewer)
   - Comprehensive audit logging and security

4. **Dual Authentication System**
   - Clerk Authentication for web interface
   - JWT API keys for programmatic access
   - Scoped permissions and rate limiting

## Technical Foundation
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript + PostgreSQL
- **AI Processing**: Multi-provider (Groq, OpenAI, Gemini) with intelligent fallback
- **Authentication**: Clerk Auth + JWT API keys
- **Database**: PostgreSQL with Drizzle ORM + 30+ strategic indexes
