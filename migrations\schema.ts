import { pgTable, foreignKey, uuid, numeric, text, timestamp, varchar, jsonb, integer, boolean, index, unique, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const rfqStatus = pgEnum("rfq_status", ['Draft', 'Active', 'Review', 'Closed', 'Awarded'])
export const trade = pgEnum("trade", ['electrical', 'plumbing', 'hvac', 'concrete', 'general', 'site_work'])
export const userRole = pgEnum("user_role", ['SuperUser', 'Admin', 'Editor', 'Viewer'])


export const bids = pgTable("bids", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	rfqId: uuid("rfq_id"),
	contractorId: uuid("contractor_id"),
	bidAmount: numeric("bid_amount", { precision: 12, scale:  2 }),
	proposalText: text("proposal_text"),
	submittedAt: timestamp("submitted_at", { mode: 'string' }).defaultNow(),
	status: varchar().default('Pending'),
	timeline: text(),
	notes: text(),
	extractedAmount: numeric("extracted_amount", { precision: 12, scale:  2 }),
	extractedTimeline: text("extracted_timeline"),
	extractedScope: text("extracted_scope"),
	extractedConditions: text("extracted_conditions"),
	extractedData: jsonb("extracted_data"),
	extractionConfidence: numeric("extraction_confidence", { precision: 3, scale:  2 }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	reviewedAt: timestamp("reviewed_at", { mode: 'string' }),
	reviewedBy: varchar("reviewed_by"),
	reviewNotes: text("review_notes"),
	requestInfoMessage: text("request_info_message"),
	aiAnalysis: jsonb("ai_analysis"),
	competitiveScore: numeric("competitive_score", { precision: 3, scale:  2 }),
	aiSummary: text("ai_summary"),
}, (table) => [
	foreignKey({
			columns: [table.rfqId],
			foreignColumns: [rfqs.id],
			name: "bids_rfq_id_rfqs_id_fk"
		}),
	foreignKey({
			columns: [table.contractorId],
			foreignColumns: [contractors.id],
			name: "bids_contractor_id_contractors_id_fk"
		}),
]);

export const rfqDocuments = pgTable("rfq_documents", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	rfqId: uuid("rfq_id"),
	fileName: varchar("file_name").notNull(),
	fileUrl: varchar("file_url"),
	fileSize: integer("file_size"),
	mimeType: varchar("mime_type"),
	extractedText: text("extracted_text"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	objectKey: varchar("object_key"),
}, (table) => [
	foreignKey({
			columns: [table.rfqId],
			foreignColumns: [rfqs.id],
			name: "rfq_documents_rfq_id_rfqs_id_fk"
		}),
]);

export const contractors = pgTable("contractors", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id"),
	companyName: varchar("company_name").notNull(),
	legalStructure: varchar("legal_structure"),
	taxId: varchar("tax_id"),
	businessAddress: text("business_address"),
	contactEmail: varchar("contact_email"),
	contactPhone: varchar("contact_phone"),
	tradeTypes: jsonb("trade_types"),
	unionStatus: varchar("union_status"),
	yearsInBusiness: integer("years_in_business"),
	licenseNumber: varchar("license_number"),
	licenseExpiration: timestamp("license_expiration", { mode: 'string' }),
	isApproved: boolean("is_approved").default(false),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	dba: varchar(),
	primaryAddress: text("primary_address"),
	mailingAddress: text("mailing_address"),
	primaryContactName: varchar("primary_contact_name"),
	primaryContactEmail: varchar("primary_contact_email"),
	primaryContactPhone: varchar("primary_contact_phone"),
	primaryContactTitle: varchar("primary_contact_title"),
	unionAffiliations: text("union_affiliations"),
	certifications: jsonb(),
	serviceAreas: text("service_areas"),
	licenseState: varchar("license_state"),
	generalLiability: text("general_liability"),
	workersComp: text("workers_comp"),
	autoInsurance: text("auto_insurance"),
	bondingSingle: integer("bonding_single"),
	bondingAggregate: integer("bonding_aggregate"),
	emr: numeric({ precision: 4, scale:  2 }),
	bankReference: text("bank_reference"),
	suretyReference: text("surety_reference"),
	creditRating: varchar("credit_rating"),
	paymentTerms: varchar("payment_terms"),
	litigationHistory: text("litigation_history"),
	projectReferences: text("project_references"),
	specializations: jsonb(),
	awards: text(),
	environmentalPrograms: text("environmental_programs"),
	workforceSize: integer("workforce_size"),
	workforceBreakdown: text("workforce_breakdown"),
	equipment: text(),
	availability: text(),
	keywordTags: jsonb("keyword_tags"),
	preferredProjectTypes: jsonb("preferred_project_types"),
	companyWebsite: varchar("company_website"),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "contractors_user_id_users_id_fk"
		}),
]);

export const sessions = pgTable("sessions", {
	sid: varchar().primaryKey().notNull(),
	sess: jsonb().notNull(),
	expire: timestamp({ mode: 'string' }).notNull(),
}, (table) => [
	index("IDX_session_expire").using("btree", table.expire.asc().nullsLast().op("timestamp_ops")),
]);

export const bidDocuments = pgTable("bid_documents", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	bidId: uuid("bid_id"),
	fileName: varchar("file_name").notNull(),
	fileUrl: varchar("file_url").notNull(),
	fileSize: integer("file_size"),
	mimeType: varchar("mime_type"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.bidId],
			foreignColumns: [bids.id],
			name: "bid_documents_bid_id_bids_id_fk"
		}),
]);

export const users = pgTable("users", {
	id: varchar().primaryKey().notNull(),
	email: varchar(),
	firstName: varchar("first_name"),
	lastName: varchar("last_name"),
	profileImageUrl: varchar("profile_image_url"),
	role: userRole().default('Viewer'),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	unique("users_email_unique").on(table.email),
]);

export const forecastMaterials = pgTable("forecast_materials", {
	id: varchar().primaryKey().notNull(),
	category: varchar().notNull(),
	name: varchar().notNull(),
	unit: varchar().notNull(),
	currentPrice: numeric("current_price", { precision: 10, scale:  2 }),
	previousPrice: numeric("previous_price", { precision: 10, scale:  2 }),
	changePercent: numeric("change_percent", { precision: 5, scale:  2 }),
	volume: varchar(),
	time: varchar(),
	ytdChange: numeric("ytd_change", { precision: 5, scale:  2 }),
	ytdChangePercent: numeric("ytd_change_percent", { precision: 5, scale:  2 }),
	lastUpdated: timestamp("last_updated", { mode: 'string' }).defaultNow(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	costCode: varchar("cost_code").notNull(),
});

export const rfqDistribution = pgTable("rfq_distribution", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	rfqId: uuid("rfq_id").notNull(),
	contractorId: uuid("contractor_id").notNull(),
	sentAt: timestamp("sent_at", { mode: 'string' }).defaultNow(),
	viewedAt: timestamp("viewed_at", { mode: 'string' }),
	declinedAt: timestamp("declined_at", { mode: 'string' }),
	declineReason: text("decline_reason"),
	distributionMethod: varchar("distribution_method", { length: 20 }).default('favorites'),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_rfq_distribution_contractor").using("btree", table.contractorId.asc().nullsLast().op("uuid_ops")),
	index("idx_rfq_distribution_rfq").using("btree", table.rfqId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.rfqId],
			foreignColumns: [rfqs.id],
			name: "rfq_distribution_rfq_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.contractorId],
			foreignColumns: [contractors.id],
			name: "rfq_distribution_contractor_id_fkey"
		}).onDelete("cascade"),
	unique("rfq_distribution_rfq_id_contractor_id_key").on(table.rfqId, table.contractorId),
]);

export const contractorFavorites = pgTable("contractor_favorites", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id").notNull(),
	contractorId: uuid("contractor_id").notNull(),
	notes: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "contractor_favorites_user_id_fkey"
		}),
	foreignKey({
			columns: [table.contractorId],
			foreignColumns: [contractors.id],
			name: "contractor_favorites_contractor_id_fkey"
		}),
	unique("contractor_favorites_user_id_contractor_id_key").on(table.userId, table.contractorId),
]);

export const rfqs = pgTable("rfqs", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdBy: varchar("created_by"),
	projectName: varchar("project_name").notNull(),
	projectLocation: varchar("project_location").notNull(),
	tradeCategory: trade("trade_category").notNull(),
	description: text(),
	dueDate: timestamp("due_date", { mode: 'string' }).notNull(),
	status: rfqStatus().default('Draft'),
	extractedData: jsonb("extracted_data"),
	aiSummary: text("ai_summary"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	bufferPercentage: numeric("buffer_percentage", { precision: 5, scale:  2 }).default('10.00'),
	bufferNotes: text("buffer_notes"),
}, (table) => [
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "rfqs_created_by_users_id_fk"
		}),
]);
