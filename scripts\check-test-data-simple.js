/**
 * Simple Test Data Check - using existing db connection
 */

import { db } from '../server/db.js';
import { sql } from 'drizzle-orm';

console.log('🔍 Checking for test data in database...\n');

try {
  // Check test organizations
  const testOrgs = await db.execute(sql`
    SELECT name, slug, created_at 
    FROM organizations 
    WHERE name ILIKE '%test%' 
       OR name ILIKE '%demo%' 
       OR name ILIKE '%example%'
       OR slug ILIKE '%test%'
       OR slug ILIKE '%demo%'
  `);

  // Check test users
  const testUsers = await db.execute(sql`
    SELECT email, first_name, last_name, role, organization_id, created_at
    FROM users 
    WHERE email ILIKE '%test%' 
       OR email ILIKE '%demo%' 
       OR email ILIKE '%example%'
       OR email ILIKE '%@test.com'
       OR email ILIKE '%@example.com'
       OR first_name ILIKE 'test%'
       OR first_name ILIKE 'demo%'
  `);

  // Check API keys
  const testApiKeys = await db.execute(sql`
    SELECT ak.name, ak.permissions, ak.is_active, ak.last_used_at, ak.created_at, u.email
    FROM api_keys ak
    JOIN users u ON ak.user_id = u.id
    WHERE u.email ILIKE '%test%' 
       OR u.email ILIKE '%demo%' 
       OR u.email ILIKE '%example%'
       OR u.first_name ILIKE 'test%'
  `);

  console.log('📊 Test Data Summary:');
  console.log(`Organizations: ${testOrgs.rows?.length || 0}`);
  console.log(`Users: ${testUsers.rows?.length || 0}`);
  console.log(`API Keys: ${testApiKeys.rows?.length || 0}\n`);

  if (testOrgs.rows?.length > 0) {
    console.log('🏢 Test Organizations:');
    testOrgs.rows.forEach(org => {
      console.log(`  - ${org.name} (${org.slug}) - Created: ${new Date(org.created_at).toLocaleDateString()}`);
    });
    console.log('');
  }

  if (testUsers.rows?.length > 0) {
    console.log('👥 Test Users:');
    testUsers.rows.forEach(user => {
      console.log(`  - ${user.email || 'No email'} (${user.first_name} ${user.last_name}) - Role: ${user.role}`);
    });
    console.log('');
  }

  if (testApiKeys.rows?.length > 0) {
    console.log('🔑 Test API Keys:');
    testApiKeys.rows.forEach(key => {
      console.log(`  - ${key.name} - User: ${key.email} - Permissions: ${key.permissions}`);
    });
    console.log('');
  }

  if (!testOrgs.rows?.length && !testUsers.rows?.length && !testApiKeys.rows?.length) {
    console.log('✅ No obvious test data found!');
  } else {
    console.log(`⚠️  Found ${(testOrgs.rows?.length || 0) + (testUsers.rows?.length || 0) + (testApiKeys.rows?.length || 0)} test records`);
    console.log('To clean up test data, create and run a proper cleanup script.');
  }

} catch (error) {
  console.error('❌ Error checking test data:', error.message);
  process.exit(1);
}

process.exit(0);
