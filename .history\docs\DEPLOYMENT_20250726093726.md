# Deployment Guide

## Overview

This guide covers deploying the Bidaible AI-powered construction bidding platform with its sophisticated bid analysis system, dual authentication infrastructure, enterprise-level API capabilities, and prominently featured embedded Bid Management Dashboard for immediate access to competitive intelligence.

## Prerequisites

### System Requirements
- Node.js 18+ with npm
- PostgreSQL 13+ database with strategic indexing support
- 4GB+ RAM for AI processing workloads
- 50GB+ storage for document uploads and analysis data
- Redis (optional) for enhanced caching performance

### Required Services
- PostgreSQL database (Neon serverless recommended for production)
- Google Gemini API access for primary AI bid analysis
- OpenAI API access for fallback AI processing
- Replit Auth configuration for OAuth authentication
- Replit Object Storage for secure file management

### Environment Variables
Ensure all required environment variables are configured:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database
PGHOST=your_postgres_host
PGPORT=5432
PGUSER=your_postgres_user
PGPASSWORD=your_postgres_password
PGDATABASE=your_database_name

# AI Service APIs (Required for bid analysis)
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key

# Authentication & Security
SESSION_SECRET=your_secure_session_secret
JWT_SECRET=your_jwt_secret_for_api_keys
REPL_ID=your_replit_app_id
ISSUER_URL=https://replit.com/oidc

# Application Configuration
NODE_ENV=production
PRIMARY_MODEL=gemini
API_RATE_LIMIT=1000
CACHE_TTL=300
REPLIT_DOMAINS=your-app.replit.app,your-custom-domain.com
```

## Replit Deployment (Recommended)

### Step 1: Environment Setup
1. Clone the repository to Replit
2. Configure environment variables in Replit Secrets
3. Enable Replit Deployments for your project

### Step 2: Database Setup
```bash
# Install dependencies
npm install

# Push database schema
npm run db:push
```

### Step 3: Authentication Configuration
1. Configure Replit Auth in your Replit app settings
2. Set authorized domains in `REPLIT_DOMAINS`
3. Configure OAuth redirect URLs

### Step 4: Deploy
```bash
# Build the application
npm run build

# The application will auto-deploy via Replit Deployments
```

### Step 5: Post-Deployment
1. Test authentication flow
2. Upload a test document to verify AI processing
3. Check database connectivity
4. Verify file upload functionality

## Manual Deployment

### Step 1: Server Setup
```bash
# Clone repository
git clone <repository-url>
cd bidaible

# Install dependencies
npm install

# Build application
npm run build
```

### Step 2: Database Migration
```bash
# Set DATABASE_URL environment variable
export DATABASE_URL="your_database_connection_string"

# Run database migrations
npm run db:push
```

### Step 3: Process Management
Use PM2 for production process management:

```bash
# Install PM2 globally
npm install -g pm2

# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
pm2 startup
```

### Step 4: Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Handle file uploads
    client_max_body_size 50M;
}
```

## Docker Deployment

### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Set ownership
RUN chown -R nextjs:nodejs /app
USER nextjs

# Expose port
EXPOSE 5000

# Start application
CMD ["npm", "start"]
```

### Docker Compose
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - SESSION_SECRET=${SESSION_SECRET}
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=bidaible
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### Deploy with Docker
```bash
# Build and start services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f app
```

## Cloud Platform Deployment

### Vercel Deployment
```json
{
  "version": 2,
  "builds": [
    {
      "src": "server/index.ts",
      "use": "@vercel/node"
    },
    {
      "src": "client/**/*",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/server/index.ts"
    },
    {
      "src": "/(.*)",
      "dest": "/client/$1"
    }
  ]
}
```

### AWS ECS Deployment
1. Create ECS task definition
2. Configure load balancer
3. Set up RDS for PostgreSQL
4. Deploy container to ECS cluster

### Google Cloud Run
```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: bidaible
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
    spec:
      containerConcurrency: 80
      containers:
      - image: gcr.io/project-id/bidaible:latest
        ports:
        - containerPort: 5000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              key: database-url
              name: bidaible-secrets
```

## Database Setup

### Neon (Recommended)
1. Create Neon project
2. Create database
3. Get connection string
4. Set DATABASE_URL environment variable

### Self-Hosted PostgreSQL
```sql
-- Create database
CREATE DATABASE bidaible;

-- Create user
CREATE USER bidaible_user WITH PASSWORD 'secure_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE bidaible TO bidaible_user;

-- Enable extensions (if needed)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

## File Storage Configuration

### Local File Storage (Development)
Files are stored in the `/uploads` directory by default.

### Cloud Storage (Production)
For production deployments, consider using cloud storage:

#### AWS S3
```javascript
// Configure in server/routes.ts
import AWS from 'aws-sdk';

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});
```

#### Google Cloud Storage
```javascript
import { Storage } from '@google-cloud/storage';

const storage = new Storage({
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE
});
```

## SSL/TLS Configuration

### Let's Encrypt (Certbot)
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Cloudflare (Recommended)
1. Add domain to Cloudflare
2. Update DNS records
3. Enable SSL/TLS encryption
4. Configure security settings

## Monitoring and Logging

### Application Monitoring
```javascript
// Add to server/index.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

### Health Check Endpoint
```javascript
// Add to server/routes.ts
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});
```

### Error Tracking
Consider integrating error tracking services:
- Sentry
- Bugsnag
- Rollbar

## Performance Optimization

### Caching Strategy
```javascript
// Redis caching
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

// Cache frequent queries
app.get('/api/rfqs', async (req, res) => {
  const cacheKey = 'rfqs:list';
  const cached = await redis.get(cacheKey);
  
  if (cached) {
    return res.json(JSON.parse(cached));
  }
  
  const rfqs = await storage.getRfqs();
  await redis.setex(cacheKey, 300, JSON.stringify(rfqs));
  res.json(rfqs);
});
```

### Database Optimization
```sql
-- Add indexes for frequently queried fields
CREATE INDEX idx_rfqs_created_by ON rfqs(created_by);
CREATE INDEX idx_rfqs_status ON rfqs(status);
CREATE INDEX idx_rfqs_trade_category ON rfqs(trade_category);
```

### CDN Configuration
Configure CDN for static assets:
- Images and documents
- CSS and JavaScript files
- Font files

## Security Considerations

### Environment Security
- Use strong, unique passwords
- Rotate API keys regularly
- Implement rate limiting
- Enable CORS protection
- Use HTTPS everywhere

### Database Security
- Enable SSL for database connections
- Use connection pooling
- Implement query sanitization
- Regular security updates

### File Upload Security
- Validate file types and sizes
- Scan uploads for malware
- Store files outside web root
- Implement access controls

## Backup and Recovery

### Database Backup
```bash
# Automated daily backup
#!/bin/bash
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql
aws s3 cp backup_$(date +%Y%m%d).sql s3://your-backup-bucket/
```

### File Backup
```bash
# Sync uploads directory
rsync -av /app/uploads/ s3://your-backup-bucket/uploads/
```

### Disaster Recovery Plan
1. Database restoration procedures
2. File restoration from backups
3. DNS failover configuration
4. Application deployment automation

## Scaling Considerations

### Horizontal Scaling
- Load balancer configuration
- Session sharing (Redis)
- Database read replicas
- File storage distribution

### Vertical Scaling
- Monitor resource usage
- Optimize database queries
- Implement caching
- Code optimization

## Troubleshooting

### Common Issues
1. **Database connection errors**: Check connection string and network access
2. **File upload failures**: Verify permissions and disk space
3. **AI processing errors**: Check API keys and rate limits
4. **Authentication issues**: Verify OAuth configuration

### Debug Mode
Enable debug logging:
```env
NODE_ENV=development
DEBUG=*
```

### Log Analysis
```bash
# Monitor application logs
tail -f /var/log/bidaible/app.log

# Check error patterns
grep "ERROR" /var/log/bidaible/app.log | tail -20
```

## Maintenance

### Regular Tasks
- Update dependencies monthly
- Monitor resource usage
- Review security logs
- Test backup restoration
- Performance optimization

### Update Procedure
```bash
# 1. Backup current version
npm run backup

# 2. Pull latest changes
git pull origin main

# 3. Install dependencies
npm install

# 4. Run database migrations
npm run db:push

# 5. Build application
npm run build

# 6. Restart services
pm2 restart bidaible
```