// Test script for GitHub integration
import fetch from 'node-fetch';

async function testGitHubAPI() {
  const token = process.env.GITHUB_TOKEN;
  const owner = process.env.GITHUB_REPO_OWNER || 'roygatling';
  const repo = process.env.GITHUB_REPO_NAME || 'Bidaible';
  
  if (!token) {
    console.error('❌ GITHUB_TOKEN not found in environment');
    return;
  }

  console.log('✅ GitHub token found');
  console.log(`📍 Target repo: ${owner}/${repo}`);
  
  // Test creating a GitHub issue directly
  const issueData = {
    title: '🧪 Test GitHub Integration',
    body: `**Feedback Type**: Bug Report
**Priority**: medium
**User ID**: test-user-123
**Organization**: Test Organization
**Submitted**: ${new Date().toISOString()}

---

Test GitHub integration - This is a test issue created by the GitHub integration system. You can safely close this issue.

---
*Internal Reference: bidaible-feedback-test-${Date.now()}*`,
    labels: ['user-feedback', 'bug'],
    assignee: 'roygatling',
  };

  try {
    const response = await fetch(`https://api.github.com/repos/${owner}/${repo}/issues`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/vnd.github+json',
        'X-GitHub-Api-Version': '2022-11-28',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(issueData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ GitHub API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });
      return;
    }

    const issue = await response.json();
    console.log('✅ GitHub issue created successfully!');
    console.log(`   Issue Number: ${issue.number}`);
    console.log(`   Issue URL: ${issue.html_url}`);
    console.log(`   🔗 View at: https://github.com/users/roygatling/projects/2`);
    
  } catch (error) {
    console.error('❌ Error creating GitHub issue:', error);
  }
}

testGitHubAPI();
