import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

// Add debugging for deployment issues
console.log("🚀 main.tsx: Starting app initialization");
console.log("🌍 Environment:", {
  mode: import.meta.env.MODE,
  dev: import.meta.env.DEV,
  prod: import.meta.env.PROD
});

const root = document.getElementById("root");
if (!root) {
  console.error("❌ Root element not found!");
  document.body.innerHTML = `
    <div style="padding: 20px; text-align: center; font-family: system-ui;">
      <h1>App Initialization Error</h1>
      <p>Root element not found. Check console for details.</p>
    </div>
  `;
} else {
  console.log("✅ Root element found, rendering app");
  
  // Hide the fallback loading indicator
  if (window.hideFallback) {
    window.hideFallback();
  }
  
  try {
    createRoot(root).render(<App />);
    console.log("✅ App rendered successfully");
  } catch (error) {
    console.error("❌ Error rendering app:", error);
    root.innerHTML = `
      <div style="padding: 20px; text-align: center; font-family: system-ui;">
        <h1>App Rendering Error</h1>
        <p>Failed to render app. Check console for details.</p>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; text-align: left; margin: 20px 0;">
${error instanceof Error ? error.message : String(error)}
        </pre>
        <button onclick="window.location.reload()" style="background: #f97316; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; margin-top: 16px;">
          Reload Page
        </button>
      </div>
    `;
  }
}
