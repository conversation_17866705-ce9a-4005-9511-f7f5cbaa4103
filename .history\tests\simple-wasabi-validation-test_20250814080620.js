/**
 * Simple Wasabi Validation Test
 * Quick test to validate Wasabi API key and bucket access
 */

// Load environment variables from root .env file
require('dotenv').config({ path: '.env' });

const AWS = require('aws-sdk');

async function simpleWasabiValidationTest() {
  console.log('🪣 Simple Wasabi Validation Test');
  console.log('=================================');
  
  try {
    // Step 1: Check environment variables
    console.log('🔍 Step 1: Checking Wasabi environment variables...');
    
    // Check for common variable name variations (prioritize the ones used by server)
    const accessKey = process.env.WASABI_ACCESS_KEY_ID || 
                     process.env.WASABI_ACCESS_KEY ||
                     process.env.AWS_ACCESS_KEY_ID;
    const secretKey = process.env.WASABI_SECRET_ACCESS_KEY || 
                     process.env.WASABI_SECRET_KEY ||
                     process.env.AWS_SECRET_ACCESS_KEY;
    const bucketName = process.env.WASABI_BUCKET_NAME || 
                      process.env.WASABI_BUCKET || 
                      'bidaible-storage';
    const region = process.env.WASABI_REGION || 'us-east-1';
    
    console.log('📋 Environment check:');
    console.log('  - WASABI_ACCESS_KEY:', process.env.WASABI_ACCESS_KEY ? '✅ Set' : '❌ Missing');
    console.log('  - WASABI_ACCESS_KEY_ID:', process.env.WASABI_ACCESS_KEY_ID ? '✅ Set' : '❌ Missing');
    console.log('  - AWS_ACCESS_KEY_ID:', process.env.AWS_ACCESS_KEY_ID ? '✅ Set' : '❌ Missing');
    console.log('  - WASABI_SECRET_KEY:', process.env.WASABI_SECRET_KEY ? '✅ Set' : '❌ Missing');
    console.log('  - WASABI_SECRET_ACCESS_KEY:', process.env.WASABI_SECRET_ACCESS_KEY ? '✅ Set' : '❌ Missing');
    console.log('  - AWS_SECRET_ACCESS_KEY:', process.env.AWS_SECRET_ACCESS_KEY ? '✅ Set' : '❌ Missing');
    console.log('  - WASABI_BUCKET_NAME:', bucketName);
    console.log('  - WASABI_REGION:', region);
    console.log('  - Using Access Key:', accessKey ? accessKey.substring(0, 8) + '...' : 'None found');
    console.log('  - Using Secret Key:', secretKey ? secretKey.substring(0, 8) + '...' : 'None found');
    
    if (!accessKey || !secretKey) {
      console.log('❌ Missing Wasabi credentials in environment variables');
      return { 
        success: false, 
        error: 'Missing Wasabi credentials',
        hasCredentials: false,
        recommendation: 'Set WASABI_ACCESS_KEY and WASABI_SECRET_KEY environment variables'
      };
    }
    
    // Step 2: Configure Wasabi S3 client
    console.log('🔧 Step 2: Configuring Wasabi S3 client...');
    const s3 = new AWS.S3({
      accessKeyId: accessKey,
      secretAccessKey: secretKey,
      endpoint: 'https://s3.wasabisys.com',
      region: region,
      s3ForcePathStyle: true,
      signatureVersion: 'v4'
    });
    
    console.log('✅ S3 client configured for Wasabi');
    
    // Step 3: Test bucket access
    console.log('🪣 Step 3: Testing bucket access...');
    
    try {
      // Try to list objects in the bucket (this tests both credentials and bucket access)
      const listParams = {
        Bucket: bucketName,
        MaxKeys: 1 // Just get 1 object to test access
      };
      
      const listResult = await s3.listObjectsV2(listParams).promise();
      console.log('✅ Bucket access successful!');
      console.log('📊 Bucket info:');
      console.log('  - Objects found:', listResult.Contents?.length || 0);
      console.log('  - Bucket accessible:', true);
      
      return {
        success: true,
        hasCredentials: true,
        bucketAccessible: true,
        objectCount: listResult.Contents?.length || 0,
        bucketName: bucketName
      };
      
    } catch (bucketError) {
      console.log('❌ Bucket access failed:', bucketError.message);
      
      if (bucketError.code === 'NoSuchBucket') {
        console.log('🔍 Bucket does not exist or is not accessible');
        return {
          success: false,
          hasCredentials: true,
          bucketAccessible: false,
          error: 'Bucket does not exist',
          bucketName: bucketName,
          recommendation: `Create bucket '${bucketName}' in Wasabi or update WASABI_BUCKET_NAME`
        };
      } else if (bucketError.code === 'AccessDenied') {
        console.log('🔒 Access denied to bucket');
        return {
          success: false,
          hasCredentials: true,
          bucketAccessible: false,
          error: 'Access denied to bucket',
          bucketName: bucketName,
          recommendation: 'Check bucket permissions and access key permissions'
        };
      } else if (bucketError.code === 'InvalidAccessKeyId') {
        console.log('🔑 Invalid access key');
        return {
          success: false,
          hasCredentials: false,
          bucketAccessible: false,
          error: 'Invalid access key',
          recommendation: 'Verify WASABI_ACCESS_KEY is correct'
        };
      } else if (bucketError.code === 'SignatureDoesNotMatch') {
        console.log('🔐 Invalid secret key');
        return {
          success: false,
          hasCredentials: false,
          bucketAccessible: false,
          error: 'Invalid secret key',
          recommendation: 'Verify WASABI_SECRET_KEY is correct'
        };
      } else {
        console.log('⚠️ Unexpected bucket error:', bucketError.code, bucketError.message);
        return {
          success: false,
          hasCredentials: true,
          bucketAccessible: false,
          error: bucketError.message,
          errorCode: bucketError.code
        };
      }
    }
    
  } catch (error) {
    console.log('💥 Test setup failed:', error.message);
    return { 
      success: false, 
      error: error.message,
      hasCredentials: false,
      bucketAccessible: false
    };
  }
}

// Run the test
if (require.main === module) {
  simpleWasabiValidationTest()
    .then(result => {
      console.log('\n🎯 Wasabi Validation Results:');
      console.log('=============================');
      console.log('Success:', result.success);
      console.log('Has Credentials:', result.hasCredentials);
      console.log('Bucket Accessible:', result.bucketAccessible);
      
      if (result.success) {
        console.log('\n🎉 WASABI CONFIGURATION WORKING!');
        console.log('✅ Credentials are valid');
        console.log('✅ Bucket is accessible');
        console.log('✅ Ready for file upload tests');
        if (result.objectCount !== undefined) {
          console.log(`📊 Found ${result.objectCount} objects in bucket`);
        }
      } else {
        console.log('\n❌ Wasabi configuration needs attention');
        console.log('🔧 Issue:', result.error);
        if (result.recommendation) {
          console.log('💡 Recommendation:', result.recommendation);
        }
        
        if (!result.hasCredentials) {
          console.log('\n📝 To fix credentials:');
          console.log('   export WASABI_ACCESS_KEY="your-access-key"');
          console.log('   export WASABI_SECRET_KEY="your-secret-key"');
        }
        
        if (result.hasCredentials && !result.bucketAccessible) {
          console.log('\n📝 To fix bucket access:');
          console.log('   1. Verify bucket exists in Wasabi console');
          console.log('   2. Check bucket permissions');
          console.log('   3. Verify access key has bucket permissions');
        }
      }
      
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { simpleWasabiValidationTest };
