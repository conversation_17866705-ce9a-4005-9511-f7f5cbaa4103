
import { useQuery } from "@tanstack/react-query";
import { FileText } from "lucide-react";

interface DocumentCountProps {
  rfqId: string;
}

export function DocumentCount({ rfqId }: DocumentCountProps) {
  const { data: documents = [] } = useQuery({
    queryKey: [`/api/rfqs/${rfqId}/documents`],
  });

  return (
    <div className="flex items-center">
      <FileText className="h-3 w-3 mr-1" />
      <span>{documents.length} docs</span>
    </div>
  );
}
