
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, X, AlertTriangle, Download, TrendingUp, TrendingDown } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface BidComparison {
  bidId: string;
  contractorName: string;
  bidAmount: number;
  inclusions: string[];
  exclusions: string[];
  keyDifferentiators: string[];
  riskFactors: string[];
  timeline: string;
  warranty: string;
  confidence: number;
}

interface ComparisonReport {
  comparisons: BidComparison[];
  report: string;
  recommendations: string[];
  totalBids: number;
  averageBid: number;
  lowestBid: number;
  highestBid: number;
}

interface BidComparisonMatrixProps {
  rfqId: string;
}

export function BidComparisonMatrix({ rfqId }: BidComparisonMatrixProps) {
  const { data: comparison, isLoading, error } = useQuery<ComparisonReport>({
    queryKey: [`/api/rfqs/${rfqId}/bid-comparison`],
    enabled: !!rfqId
  });

  const handleExport = async (format: 'json' | 'csv') => {
    try {
      const response = await fetch(`/api/rfqs/${rfqId}/bid-comparison/export?format=${format}`, {
        credentials: 'include'
      });
      
      if (format === 'csv') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bid-comparison-${rfqId}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        const data = await response.json();
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bid-comparison-${rfqId}.json`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading bid comparison...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load bid comparison data. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!comparison || comparison.comparisons.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Bid Comparison Matrix</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">No bids submitted yet for comparison.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const allInclusions = [...new Set(
    comparison.comparisons.flatMap(c => c.inclusions)
  )];

  const getBidVarianceColor = (bidAmount: number, average: number) => {
    const variance = (bidAmount - average) / average;
    if (variance > 0.1) return "text-red-600";
    if (variance < -0.1) return "text-green-600";
    return "text-gray-600";
  };

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{comparison.totalBids}</div>
            <div className="text-sm text-muted-foreground">Total Bids</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">${comparison.averageBid.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Average Bid</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">${comparison.lowestBid.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Lowest Bid</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">${comparison.highestBid.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Highest Bid</div>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      {comparison.recommendations.length > 0 && (
        <Alert>
          <TrendingUp className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {comparison.recommendations.map((rec, idx) => (
                <div key={idx}>• {rec}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Comparison Matrix */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Bid Comparison Matrix</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => handleExport('csv')}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleExport('json')}>
                <Download className="h-4 w-4 mr-2" />
                Export JSON
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr>
                  <th className="border p-3 text-left bg-muted/50">Contractor</th>
                  <th className="border p-3 text-center bg-muted/50">Bid Amount</th>
                  <th className="border p-3 text-center bg-muted/50">Timeline</th>
                  <th className="border p-3 text-center bg-muted/50">Warranty</th>
                  <th className="border p-3 text-center bg-muted/50">Risk Level</th>
                </tr>
              </thead>
              <tbody>
                {comparison.comparisons.map(bid => (
                  <tr key={bid.bidId}>
                    <td className="border p-3">
                      <div className="font-medium">{bid.contractorName}</div>
                      <div className="text-sm text-muted-foreground">
                        Confidence: {bid.confidence}%
                      </div>
                    </td>
                    <td className={`border p-3 text-center font-medium ${getBidVarianceColor(bid.bidAmount, comparison.averageBid)}`}>
                      ${bid.bidAmount.toLocaleString()}
                      {bid.bidAmount < comparison.averageBid && (
                        <TrendingDown className="h-4 w-4 inline ml-1" />
                      )}
                      {bid.bidAmount > comparison.averageBid && (
                        <TrendingUp className="h-4 w-4 inline ml-1" />
                      )}
                    </td>
                    <td className="border p-3 text-center">{bid.timeline}</td>
                    <td className="border p-3 text-center">{bid.warranty}</td>
                    <td className="border p-3 text-center">
                      {bid.riskFactors.length === 0 ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Low Risk
                        </Badge>
                      ) : (
                        <Badge variant="destructive">
                          {bid.riskFactors.length} Risk{bid.riskFactors.length > 1 ? 's' : ''}
                        </Badge>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Detailed Inclusions/Exclusions Matrix */}
          {allInclusions.length > 0 && (
            <div className="mt-8">
              <h4 className="text-lg font-semibold mb-4">Scope Inclusions Comparison</h4>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr>
                      <th className="border p-2 text-left bg-muted/50">Scope Item</th>
                      {comparison.comparisons.map(bid => (
                        <th key={bid.bidId} className="border p-2 text-center bg-muted/50 min-w-[120px]">
                          {bid.contractorName}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {allInclusions.slice(0, 10).map(inclusion => (
                      <tr key={inclusion}>
                        <td className="border p-2 font-medium">{inclusion}</td>
                        {comparison.comparisons.map(bid => (
                          <td key={bid.bidId} className="border p-2 text-center">
                            {bid.inclusions.includes(inclusion) ? (
                              <Check className="h-5 w-5 text-green-500 mx-auto" />
                            ) : bid.exclusions.includes(inclusion) ? (
                              <X className="h-5 w-5 text-red-500 mx-auto" />
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* AI Analysis Report */}
      {comparison.report && (
        <Card>
          <CardHeader>
            <CardTitle>AI Analysis Report</CardTitle>
          </CardHeader>
          <CardContent>
            <div 
              className="prose dark:prose-invert max-w-none"
              dangerouslySetInnerHTML={{ __html: comparison.report }}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
