// Quick test script to debug the category analysis endpoint
const express = require('express');

async function testCategoryAnalysis() {
  // This would normally call the storage functions
  console.log('Testing category analysis endpoint logic...');
  
  // Mock bid data that should match our database
  const mockBids = [
    {
      bidId: 'af17219e-6cf1-43e9-aeb2-6a3e15393faa',
      contractorId: '7988b07a-4583-43de-8b2e-fa2d231f5cf8',
      contractorName: '<PERSON>',
      companyName: 'Advanced Electrical Solutions',
      bidAmount: 145000,
      timeline: '8 weeks',
      status: 'submitted',
      lineItems: [
        { costCode: '16.010', description: 'Electrical Complete by Sub-Contractor', totalPrice: 46250, category: 'Electrical' },
        { costCode: '16.015', description: 'Electrical Fixtures', totalPrice: 15750, category: 'Electrical' },
        { costCode: '16.040', description: 'Electrical Service/Gear', totalPrice: 25500, category: 'Electrical' },
        { costCode: '16.100', description: 'Generator Backup Systems', totalPrice: 35000, category: 'Electrical' },
        { costCode: '16.130', description: 'Lighting Controls', totalPrice: 22500, category: 'Electrical' }
      ]
    }
  ];

  // Helper function to extract main cost code (XX-000 format)
  const getMainCostCode = (costCode) => {
    if (!costCode) return 'Other';
    const match = costCode.match(/^(\d{2})/);
    return match ? `${match[1]}-000` : 'Other';
  };

  // Group bids by cost code categories
  const categoryGroups = {};
  
  mockBids.forEach(bid => {
    // Group line items by main cost code and calculate totals per category
    const bidCategoryTotals = {};
    const bidCategoryItems = {};

    bid.lineItems.forEach(lineItem => {
      const mainCostCode = getMainCostCode(lineItem.costCode);
      const itemTotal = Number(lineItem.totalPrice || 0);
      
      if (!bidCategoryTotals[mainCostCode]) {
        bidCategoryTotals[mainCostCode] = 0;
        bidCategoryItems[mainCostCode] = [];
      }
      
      bidCategoryTotals[mainCostCode] += itemTotal;
      bidCategoryItems[mainCostCode].push(lineItem);
    });

    // Add bid to each category it participates in
    Object.keys(bidCategoryTotals).forEach(mainCostCode => {
      if (!categoryGroups[mainCostCode]) {
        categoryGroups[mainCostCode] = [];
      }
      
      categoryGroups[mainCostCode].push({
        ...bid,
        categoryAmount: bidCategoryTotals[mainCostCode],
        lineItems: bidCategoryItems[mainCostCode]
      });
    });
  });

  console.log('Category Groups:', JSON.stringify(categoryGroups, null, 2));
}

testCategoryAnalysis();