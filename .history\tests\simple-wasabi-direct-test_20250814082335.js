/**
 * Simple Direct Wasabi Test
 * Direct test using AWS SDK to upload to Wasabi - bypasses TypeScript issues
 */

// Load environment variables from root .env file
require('dotenv').config({ path: '.env' });

const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');

async function simpleWasabiDirectTest() {
  console.log('🚀 Simple Direct Wasabi Test');
  console.log('============================');
  
  try {
    // Initialize Wasabi S3 client (same config as objectStorageService.ts)
    const s3Client = new S3Client({
      endpoint: process.env.WASABI_ENDPOINT || 'https://s3.wasabisys.com',
      region: process.env.WASABI_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.WASABI_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.WASABI_SECRET_ACCESS_KEY || '',
      },
      forcePathStyle: true, // Required for <PERSON>abi
    });

    const BUCKET_NAME = process.env.WASABI_BUCKET_NAME || 'bidaible-storage';
    
    console.log('🔧 Configuration:');
    console.log(`  - Endpoint: ${process.env.WASABI_ENDPOINT || 'https://s3.wasabisys.com'}`);
    console.log(`  - Region: ${process.env.WASABI_REGION || 'us-east-1'}`);
    console.log(`  - Bucket: ${BUCKET_NAME}`);
    console.log(`  - Access Key: ${process.env.WASABI_ACCESS_KEY_ID ? process.env.WASABI_ACCESS_KEY_ID.substring(0, 8) + '...' : 'NOT SET'}`);
    console.log(`  - Secret Key: ${process.env.WASABI_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET'}`);
    
    // Create a simple test file
    const testContent = `Direct Wasabi test file uploaded at ${new Date().toISOString()}`;
    const testBuffer = Buffer.from(testContent, 'utf8');
    const objectKey = `test-uploads/direct-test-${Date.now()}.txt`;
    
    console.log('\n📄 Test file details:');
    console.log(`  - Object Key: ${objectKey}`);
    console.log(`  - Size: ${testBuffer.length} bytes`);
    console.log(`  - Content: ${testContent}`);
    
    console.log('\n⬆️ Uploading to Wasabi...');
    
    // Create the upload command
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: objectKey,
      Body: testBuffer,
      ContentType: 'text/plain',
      ContentLength: testBuffer.length,
    });
    
    // Execute the upload
    const result = await s3Client.send(command);
    
    console.log('\n✅ UPLOAD SUCCESSFUL!');
    console.log('📊 Upload result:');
    console.log(`  - ETag: ${result.ETag}`);
    console.log(`  - Object Key: ${objectKey}`);
    console.log(`  - Bucket: ${BUCKET_NAME}`);
    console.log(`  - File Size: ${testBuffer.length} bytes`);
    console.log(`  - Upload Time: ${new Date().toISOString()}`);
    
    return {
      success: true,
      result: {
        objectKey,
        bucket: BUCKET_NAME,
        fileSize: testBuffer.length,
        etag: result.ETag
      },
      message: 'File uploaded successfully to Wasabi'
    };
    
  } catch (error) {
    console.log('\n❌ UPLOAD FAILED!');
    console.log('💥 Error details:');
    console.log(`  - Error: ${error.message}`);
    console.log(`  - Type: ${error.constructor.name}`);
    
    // Log additional error details if available
    if (error.Code) {
      console.log(`  - AWS Code: ${error.Code}`);
    }
    if (error.$metadata) {
      console.log(`  - HTTP Status: ${error.$metadata.httpStatusCode}`);
      console.log(`  - Request ID: ${error.$metadata.requestId}`);
    }
    
    return {
      success: false,
      error: error.message,
      errorCode: error.Code,
      httpStatus: error.$metadata?.httpStatusCode
    };
  }
}

// Run the test
if (require.main === module) {
  simpleWasabiDirectTest()
    .then(result => {
      console.log('\n🎯 FINAL RESULT:');
      console.log('================');
      
      if (result.success) {
        console.log('✅ PASS - Wasabi upload working correctly');
        console.log('🎉 Your Wasabi integration is functional!');
        console.log('📝 This confirms your credentials and bucket access are working.');
        process.exit(0);
      } else {
        console.log('❌ FAIL - Wasabi upload not working');
        console.log('🔧 Issue:', result.error);
        
        // Provide specific troubleshooting guidance
        if (result.errorCode === 'InvalidAccessKeyId') {
          console.log('💡 Fix: Check WASABI_ACCESS_KEY_ID environment variable');
        } else if (result.errorCode === 'SignatureDoesNotMatch') {
          console.log('💡 Fix: Check WASABI_SECRET_ACCESS_KEY environment variable');
        } else if (result.errorCode === 'NoSuchBucket') {
          console.log('💡 Fix: Check WASABI_BUCKET_NAME or create the bucket');
        } else if (result.httpStatus === 403) {
          console.log('💡 Fix: Check bucket permissions and access key permissions');
        } else if (result.httpStatus === 404) {
          console.log('💡 Fix: Bucket not found - check WASABI_BUCKET_NAME');
        } else {
          console.log('💡 Check network connectivity and Wasabi service status');
        }
        
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test setup failed:', error);
      process.exit(1);
    });
}

module.exports = { simpleWasabiDirectTest };
