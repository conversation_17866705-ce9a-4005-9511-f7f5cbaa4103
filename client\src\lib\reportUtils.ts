import { format } from "date-fns";

export interface BidReportData {
  rfq: {
    projectName: string;
    projectLocation: string;
    dueDate: string;
    budgetRange?: string;
  };
  bidSummary: {
    totalBids: number;
    acceptedBids: number;
    rejectedBids: number;
    pendingBids: number;
    lowestBid: number;
    highestBid: number;
    averageBid: number;
    baseTotal: number;
    bufferAmount: number;
    totalWithBuffer: number;
  };
  bids: Array<{
    bid: {
      id: string;
      extractedAmount: number;
      timeline: string;
      status: string;
      submittedAt: string;
      competitiveScore?: number;
      scope?: string;
    };
    contractor: {
      companyName: string;
      primaryContactName: string;
      primaryContactEmail: string;
      primaryContactPhone?: string;
    };
  }>;
}

/**
 * Generate and download a CSV report of bid data
 */
// Export accepted bids with detailed cost code breakdown
export async function exportAcceptedBidDetailsCSV(rfqId: string) {
  try {
    // Fetch comprehensive bid analysis data
    const response = await fetch(`/api/rfqs/${rfqId}/comprehensive-bid-analysis`);
    if (!response.ok) {
      throw new Error('Failed to fetch bid analysis data');
    }
    const bidData = await response.json();

    // Filter for accepted bids only
    const acceptedBids = bidData.filter((bid: any) => 
      bid.status === 'accepted' || bid.status === 'accept'
    );

    if (acceptedBids.length === 0) {
      throw new Error('No accepted bids found');
    }

    const headers = [
      'Contractor Company',
      'Contact Name',
      'Contact Email', 
      'Contact Phone',
      'Cost Code',
      'Cost Description',
      'Quantity',
      'Unit Price',
      'Unit of Measure',
      'Line Total',
      'Category',
      'Total Bid Amount',
      'Timeline',
      'Status'
    ];

    const rows: string[][] = [];

    // Add header row for project info
    rows.push(['=== ACCEPTED BID DETAILS ===', '', '', '', '', '', '', '', '', '', '', '', '', '']);
    rows.push(['', '', '', '', '', '', '', '', '', '', '', '', '', '']);

    acceptedBids.forEach((bid: any) => {
      if (bid.lineItems && bid.lineItems.length > 0) {
        bid.lineItems.forEach((item: any, index: number) => {
          rows.push([
            index === 0 ? bid.companyName : '', // Only show contractor info on first line
            index === 0 ? bid.contractorName : '',
            index === 0 ? (bid.contractorInfo?.primaryContactEmail || '') : '',
            index === 0 ? (bid.contractorInfo?.primaryContactPhone || '') : '',
            item.costCode,
            item.description,
            item.quantity || '',
            item.unitPrice ? `$${parseFloat(item.unitPrice).toLocaleString()}` : '',
            item.unitOfMeasure || '',
            item.totalPrice ? `$${parseFloat(item.totalPrice).toLocaleString()}` : '',
            item.category || '',
            index === 0 ? `$${(bid.bidAmount || 0).toLocaleString()}` : '', // Only show total on first line
            index === 0 ? (bid.timeline || '') : '',
            index === 0 ? 'Accepted' : ''
          ]);
        });
        // Add spacer between contractors
        rows.push(['', '', '', '', '', '', '', '', '', '', '', '', '', '']);
      } else {
        // If no line items, show basic contractor info
        rows.push([
          bid.companyName,
          bid.contractorName,
          bid.contractorInfo?.primaryContactEmail || '',
          bid.contractorInfo?.primaryContactPhone || '',
          'No detailed breakdown available',
          '',
          '',
          '',
          '',
          '',
          '',
          `$${(bid.bidAmount || 0).toLocaleString()}`,
          bid.timeline || '',
          'Accepted'
        ]);
        rows.push(['', '', '', '', '', '', '', '', '', '', '', '', '', '']);
      }
    });

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `accepted_bid_details_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    console.log('✅ Accepted bid details CSV export completed');
    return true;
  } catch (error) {
    console.error('❌ Failed to export accepted bid details:', error);
    throw error;
  }
}

export async function exportBidDataCSV(data: BidReportData) {
  const headers = [
    'Contractor',
    'Contact Name', 
    'Contact Email',
    'Contact Phone',
    'Bid Amount',
    'Timeline',
    'Status',
    'AI Score',
    'Submitted Date',
    'Scope'
  ];

  const rows = data.bids.map(({ bid, contractor }) => [
    contractor.companyName,
    contractor.primaryContactName,
    contractor.primaryContactEmail,
    contractor.primaryContactPhone || '',
    `$${(bid.extractedAmount || 0).toLocaleString()}`,
    bid.timeline || '',
    bid.status === 'accept' ? 'Accepted' : 
    bid.status === 'reject' ? 'Rejected' : 
    bid.status.charAt(0).toUpperCase() + bid.status.slice(1),
    bid.competitiveScore ? `${Math.round(bid.competitiveScore * 100)}%` : '',
    format(new Date(bid.submittedAt), 'MM/dd/yyyy HH:mm'),
    (bid.scope || '').replace(/[",\n\r]/g, ' ').substring(0, 100)
  ]);

  // Calculate missing metrics from actual bid data
  const bidAmounts = data.bids.map(({bid}) => bid.extractedAmount || 0).filter(amount => amount > 0);
  const lowestBid = bidAmounts.length > 0 ? Math.min(...bidAmounts) : 0;
  const highestBid = bidAmounts.length > 0 ? Math.max(...bidAmounts) : 0;
  const averageBid = bidAmounts.length > 0 ? bidAmounts.reduce((sum, amount) => sum + amount, 0) / bidAmounts.length : 0;

  // Add summary row
  rows.unshift(['=== PROJECT SUMMARY ===', '', '', '', '', '', '', '', '', '']);
  rows.unshift(['Project Name', data.rfq.projectName, '', '', '', '', '', '', '', '']);
  rows.unshift(['Project Location', data.rfq.projectLocation || 'Not specified', '', '', '', '', '', '', '', '']);
  rows.unshift(['Due Date', data.rfq.dueDate ? format(new Date(data.rfq.dueDate), 'MM/dd/yyyy') : 'Not specified', '', '', '', '', '', '', '', '']);
  rows.unshift(['Total Bids', data.bidSummary.totalBids.toString(), '', '', '', '', '', '', '', '']);
  rows.unshift(['Lowest Bid', lowestBid > 0 ? `$${lowestBid.toLocaleString()}` : 'N/A', '', '', '', '', '', '', '', '']);
  rows.unshift(['Highest Bid', highestBid > 0 ? `$${highestBid.toLocaleString()}` : 'N/A', '', '', '', '', '', '', '', '']);
  rows.unshift(['Average Bid', averageBid > 0 ? `$${Math.round(averageBid).toLocaleString()}` : 'N/A', '', '', '', '', '', '', '', '']);
  rows.unshift(['Base Total', data.bidSummary.baseTotal ? `$${data.bidSummary.baseTotal.toLocaleString()}` : 'N/A', '', '', '', '', '', '', '', '']);
  rows.unshift(['Total with Buffer', data.bidSummary.totalWithBuffer ? `$${data.bidSummary.totalWithBuffer.toLocaleString()}` : 'N/A', '', '', '', '', '', '', '', '']);
  rows.unshift(['', '', '', '', '', '', '', '', '', '']); // Spacer
  rows.unshift(['=== BID DETAILS ===', '', '', '', '', '', '', '', '', '']);

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n');

  // Download CSV
  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `bid-report-${data.rfq.projectName.replace(/[^a-zA-Z0-9]/g, '-')}-${format(new Date(), 'yyyy-MM-dd')}.csv`;
  a.click();
  window.URL.revokeObjectURL(url);
}

/**
 * Generate and download a PDF report of bid data
 */
export async function generateBidReportPDF(data: BidReportData) {
  // Calculate missing metrics from actual bid data
  const bidAmounts = data.bids.map(({bid}) => bid.extractedAmount || 0).filter(amount => amount > 0);
  const lowestBid = bidAmounts.length > 0 ? Math.min(...bidAmounts) : 0;
  const highestBid = bidAmounts.length > 0 ? Math.max(...bidAmounts) : 0;
  const averageBid = bidAmounts.length > 0 ? bidAmounts.reduce((sum, amount) => sum + amount, 0) / bidAmounts.length : 0;
  
  // For now, create a formatted HTML report that can be printed to PDF
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Bid Analysis Report - ${data.rfq.projectName}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { border-bottom: 2px solid #f75100; padding-bottom: 20px; margin-bottom: 30px; }
        .project-title { color: #f75100; font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .project-details { color: #666; font-size: 14px; }
        .summary-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 30px 0; }
        .summary-card { padding: 15px; border: 1px solid #ddd; border-radius: 8px; text-align: center; }
        .summary-value { font-size: 20px; font-weight: bold; color: #f75100; }
        .summary-label { font-size: 12px; color: #666; margin-top: 5px; }
        .bids-table { width: 100%; border-collapse: collapse; margin-top: 30px; }
        .bids-table th, .bids-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .bids-table th { background-color: #f8f9fa; font-weight: bold; }
        .status-accepted { color: #28a745; font-weight: bold; }
        .status-rejected { color: #dc3545; font-weight: bold; }
        .status-pending { color: #007bff; font-weight: bold; }
        .footer { margin-top: 40px; text-align: center; color: #666; font-size: 12px; }
        @media print { 
          body { margin: 20px; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="project-title">${data.rfq.projectName}</div>
        <div class="project-details">
          📍 ${data.rfq.projectLocation || 'Not specified'} &nbsp;&nbsp;&nbsp; 
          📅 Due: ${data.rfq.dueDate ? format(new Date(data.rfq.dueDate), 'MMMM dd, yyyy') : 'Not specified'} &nbsp;&nbsp;&nbsp;
          📊 Generated: ${format(new Date(), 'MMMM dd, yyyy HH:mm')}
        </div>
      </div>

      <div class="summary-grid">
        <div class="summary-card">
          <div class="summary-value">${data.bidSummary.totalBids}</div>
          <div class="summary-label">Total Bids</div>
        </div>
        <div class="summary-card">
          <div class="summary-value">${lowestBid > 0 ? `$${lowestBid.toLocaleString()}` : 'N/A'}</div>
          <div class="summary-label">Lowest Bid</div>
        </div>
        <div class="summary-card">
          <div class="summary-value">${averageBid > 0 ? `$${Math.round(averageBid).toLocaleString()}` : 'N/A'}</div>
          <div class="summary-label">Average Bid</div>
        </div>
        <div class="summary-card">
          <div class="summary-value">${highestBid > 0 ? `$${highestBid.toLocaleString()}` : 'N/A'}</div>
          <div class="summary-label">Highest Bid</div>
        </div>
        <div class="summary-card">
          <div class="summary-value">${data.bidSummary.acceptedBids}</div>
          <div class="summary-label">Accepted</div>
        </div>
        <div class="summary-card">
          <div class="summary-value">${data.bidSummary.totalWithBuffer ? `$${data.bidSummary.totalWithBuffer.toLocaleString()}` : 'N/A'}</div>
          <div class="summary-label">Total with Buffer</div>
        </div>
      </div>

      <table class="bids-table">
        <thead>
          <tr>
            <th>Contractor</th>
            <th>Contact</th>
            <th>Bid Amount</th>
            <th>Timeline</th>
            <th>Status</th>
            <th>AI Score</th>
            <th>Submitted</th>
          </tr>
        </thead>
        <tbody>
          ${data.bids.map(({ bid, contractor }) => `
            <tr>
              <td><strong>${contractor.companyName}</strong></td>
              <td>${contractor.primaryContactName}<br><small>${contractor.primaryContactEmail}</small></td>
              <td><strong>$${(bid.extractedAmount || 0).toLocaleString()}</strong></td>
              <td>${bid.timeline || '—'}</td>
              <td class="status-${bid.status === 'accept' ? 'accepted' : bid.status === 'reject' ? 'rejected' : 'pending'}">
                ${bid.status === 'accept' ? 'Accepted' : 
                  bid.status === 'reject' ? 'Rejected' : 
                  bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
              </td>
              <td>${bid.competitiveScore ? `${Math.round(bid.competitiveScore * 100)}%` : '—'}</td>
              <td>${format(new Date(bid.submittedAt), 'MM/dd/yy')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="footer">
        <p>Generated by Bidaible.com - AI-Powered Construction Procurement Platform</p>
        <p class="no-print">
          <button onclick="window.print()" style="padding: 10px 20px; background: #f75100; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Print / Save as PDF
          </button>
        </p>
      </div>
    </body>
    </html>
  `;

  // Open in new window for printing/PDF saving
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
    }, 250);
  }
}

/**
 * Export contractor bids to CSV format
 */
export async function exportContractorBidsCSV(bids: any[]) {
  const headers = [
    'RFQ Title',
    'Project Location', 
    'Bid Amount',
    'Extracted Amount',
    'Timeline',
    'Status',
    'Confidence Score',
    'Submitted Date',
    'Review Date',
    'Review Notes',
    'Scope',
    'Conditions'
  ];

  const rows = bids.map(bid => [
    bid.rfq?.title || '',
    bid.rfq?.projectLocation || '',
    bid.bidAmount ? `$${bid.bidAmount.toLocaleString()}` : '',
    bid.extractedAmount ? `$${bid.extractedAmount.toLocaleString()}` : '',
    bid.timeline || bid.extractedTimeline || '',
    bid.status === 'accept' ? 'Accepted' : 
    bid.status === 'reject' ? 'Rejected' : 
    bid.status.charAt(0).toUpperCase() + bid.status.slice(1),
    bid.confidenceScore ? `${Math.round(bid.confidenceScore * 100)}%` : '',
    format(new Date(bid.submittedAt), 'MM/dd/yyyy HH:mm'),
    bid.reviewedAt ? format(new Date(bid.reviewedAt), 'MM/dd/yyyy HH:mm') : '',
    (bid.reviewNotes || '').replace(/[",\n\r]/g, ' ').substring(0, 100),
    (bid.scope || bid.extractedScope || '').replace(/[",\n\r]/g, ' ').substring(0, 200),
    (bid.conditions || bid.extractedConditions || '').replace(/[",\n\r]/g, ' ').substring(0, 200)
  ]);

  // Add summary row
  const totalBids = bids.length;
  const acceptedBids = bids.filter(bid => bid.status === 'accept' || bid.status === 'accepted').length;
  const winRate = totalBids > 0 ? (acceptedBids / totalBids * 100).toFixed(1) : '0';
  const avgBidAmount = bids
    .filter(bid => bid.bidAmount || bid.extractedAmount)
    .reduce((sum, bid) => sum + (bid.bidAmount || bid.extractedAmount || 0), 0) / 
    bids.filter(bid => bid.bidAmount || bid.extractedAmount).length;

  rows.unshift(['=== CONTRACTOR BID SUMMARY ===', '', '', '', '', '', '', '', '', '', '', '']);
  rows.unshift(['Total Bids', totalBids.toString(), '', '', '', '', '', '', '', '', '', '']);
  rows.unshift(['Accepted Bids', acceptedBids.toString(), '', '', '', '', '', '', '', '', '', '']);
  rows.unshift(['Win Rate', `${winRate}%`, '', '', '', '', '', '', '', '', '', '']);
  rows.unshift(['Average Bid Amount', avgBidAmount ? `$${avgBidAmount.toLocaleString()}` : 'N/A', '', '', '', '', '', '', '', '', '', '']);
  rows.unshift(['Generated', format(new Date(), 'MM/dd/yyyy HH:mm'), '', '', '', '', '', '', '', '', '', '']);
  rows.unshift(['', '', '', '', '', '', '', '', '', '', '', '']); // Spacer
  rows.unshift(['=== DETAILED BID DATA ===', '', '', '', '', '', '', '', '', '', '', '']);

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n');

  // Download CSV
  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `contractor-bids-${format(new Date(), 'yyyy-MM-dd')}.csv`;
  a.click();
  window.URL.revokeObjectURL(url);
}

/**
 * Generate contractor bids PDF report
 */
export async function generateContractorBidsPDF(bids: any[]) {
  const totalBids = bids.length;
  const acceptedBids = bids.filter(bid => bid.status === 'accept' || bid.status === 'accepted').length;
  const rejectedBids = bids.filter(bid => bid.status === 'reject' || bid.status === 'rejected').length;
  const pendingBids = totalBids - acceptedBids - rejectedBids;
  const winRate = totalBids > 0 ? (acceptedBids / totalBids * 100).toFixed(1) : '0';
  
  const avgBidAmount = bids
    .filter(bid => bid.bidAmount || bid.extractedAmount)
    .reduce((sum, bid) => sum + (bid.bidAmount || bid.extractedAmount || 0), 0) / 
    bids.filter(bid => bid.bidAmount || bid.extractedAmount).length;

  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Contractor Bid Report - ${format(new Date(), 'MMMM yyyy')}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { border-bottom: 2px solid #f75100; padding-bottom: 20px; margin-bottom: 30px; }
        .report-title { color: #f75100; font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .report-details { color: #666; font-size: 14px; }
        .summary-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 30px 0; }
        .summary-card { padding: 15px; border: 1px solid #ddd; border-radius: 8px; text-align: center; }
        .summary-value { font-size: 20px; font-weight: bold; color: #f75100; }
        .summary-label { font-size: 12px; color: #666; margin-top: 5px; }
        .bids-table { width: 100%; border-collapse: collapse; margin-top: 30px; }
        .bids-table th, .bids-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; font-size: 12px; }
        .bids-table th { background-color: #f8f9fa; font-weight: bold; }
        .status-accepted { color: #28a745; font-weight: bold; }
        .status-rejected { color: #dc3545; font-weight: bold; }
        .status-pending { color: #007bff; font-weight: bold; }
        .footer { margin-top: 40px; text-align: center; color: #666; font-size: 12px; }
        @media print { 
          body { margin: 20px; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="report-title">Contractor Bid Performance Report</div>
        <div class="report-details">
          📊 Generated: ${format(new Date(), 'MMMM dd, yyyy HH:mm')} &nbsp;&nbsp;&nbsp; 
          📈 Total Period: ${totalBids} Bids &nbsp;&nbsp;&nbsp;
          🎯 Win Rate: ${winRate}%
        </div>
      </div>

      <div class="summary-grid">
        <div class="summary-card">
          <div class="summary-value">${totalBids}</div>
          <div class="summary-label">Total Bids</div>
        </div>
        <div class="summary-card">
          <div class="summary-value">${acceptedBids}</div>
          <div class="summary-label">Accepted</div>
        </div>
        <div class="summary-card">
          <div class="summary-value">${winRate}%</div>
          <div class="summary-label">Win Rate</div>
        </div>
        <div class="summary-card">
          <div class="summary-value">$${avgBidAmount ? avgBidAmount.toLocaleString() : 'N/A'}</div>
          <div class="summary-label">Average Bid</div>
        </div>
      </div>

      <table class="bids-table">
        <thead>
          <tr>
            <th>RFQ Title</th>
            <th>Location</th>
            <th>Bid Amount</th>
            <th>Timeline</th>
            <th>Status</th>
            <th>Confidence</th>
            <th>Submitted</th>
          </tr>
        </thead>
        <tbody>
          ${bids.map(bid => `
            <tr>
              <td><strong>${bid.rfq?.title || 'N/A'}</strong></td>
              <td>${bid.rfq?.projectLocation || 'N/A'}</td>
              <td><strong>$${(bid.bidAmount || bid.extractedAmount || 0).toLocaleString()}</strong></td>
              <td>${bid.timeline || bid.extractedTimeline || '—'}</td>
              <td class="status-${bid.status === 'accept' ? 'accepted' : bid.status === 'reject' ? 'rejected' : 'pending'}">
                ${bid.status === 'accept' ? 'Accepted' : 
                  bid.status === 'reject' ? 'Rejected' : 
                  bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
              </td>
              <td>${bid.confidenceScore ? `${Math.round(bid.confidenceScore * 100)}%` : '—'}</td>
              <td>${format(new Date(bid.submittedAt), 'MM/dd/yy')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="footer">
        <p>Generated by Bidaible.com - AI-Powered Construction Procurement Platform</p>
        <p class="no-print">
          <button onclick="window.print()" style="padding: 10px 20px; background: #f75100; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Print / Save as PDF
          </button>
        </p>
      </div>
    </body>
    </html>
  `;

  // Open in new window for printing/PDF saving
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
    }, 250);
  }
}

/**
 * Generate advanced analytics report with charts and visualizations
 */
export async function generateBidAnalyticsReport(bids: any[]) {
  // Calculate analytics data
  const statusCounts = bids.reduce((acc, bid) => {
    const status = bid.status === 'accept' ? 'accepted' : 
                  bid.status === 'reject' ? 'rejected' : bid.status;
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  const monthlyData = bids.reduce((acc, bid) => {
    const month = format(new Date(bid.submittedAt), 'yyyy-MM');
    if (!acc[month]) {
      acc[month] = { bids: 0, totalAmount: 0, accepted: 0 };
    }
    acc[month].bids++;
    acc[month].totalAmount += (bid.bidAmount || bid.extractedAmount || 0);
    if (bid.status === 'accept' || bid.status === 'accepted') {
      acc[month].accepted++;
    }
    return acc;
  }, {});

  const avgConfidence = bids
    .filter(bid => bid.confidenceScore)
    .reduce((sum, bid) => sum + bid.confidenceScore, 0) / 
    bids.filter(bid => bid.confidenceScore).length;

  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Bid Analytics Report - ${format(new Date(), 'MMMM yyyy')}</title>
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { border-bottom: 2px solid #f75100; padding-bottom: 20px; margin-bottom: 30px; }
        .analytics-title { color: #f75100; font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .analytics-details { color: #666; font-size: 14px; }
        .chart-container { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .chart-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; }
        .metrics-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 30px 0; }
        .metric-card { padding: 20px; border: 1px solid #ddd; border-radius: 8px; text-align: center; background: #f8f9fa; }
        .metric-value { font-size: 24px; font-weight: bold; color: #f75100; }
        .metric-label { font-size: 14px; color: #666; margin-top: 5px; }
        canvas { max-height: 400px; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="analytics-title">Bid Performance Analytics</div>
        <div class="analytics-details">
          📊 Generated: ${format(new Date(), 'MMMM dd, yyyy HH:mm')} &nbsp;&nbsp;&nbsp; 
          📈 Analysis Period: ${bids.length} Bids &nbsp;&nbsp;&nbsp;
          🎯 Data Points: Multiple KPIs
        </div>
      </div>

      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-value">${((Object.keys(statusCounts).includes('accepted') ? statusCounts.accepted : 0) / bids.length * 100).toFixed(1)}%</div>
          <div class="metric-label">Win Rate</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${avgConfidence ? (avgConfidence * 100).toFixed(1) + '%' : 'N/A'}</div>
          <div class="metric-label">Avg Confidence</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${Object.keys(monthlyData).length}</div>
          <div class="metric-label">Active Months</div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-title">Bid Status Distribution</div>
        <canvas id="statusChart" width="400" height="200"></canvas>
      </div>

      <div class="chart-container">
        <div class="chart-title">Monthly Bid Activity</div>
        <canvas id="monthlyChart" width="400" height="200"></canvas>
      </div>

      <script>
        // Status distribution chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
          type: 'doughnut',
          data: {
            labels: ${JSON.stringify(Object.keys(statusCounts))},
            datasets: [{
              data: ${JSON.stringify(Object.values(statusCounts))},
              backgroundColor: ['#28a745', '#dc3545', '#007bff', '#ffc107', '#6c757d']
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: { position: 'bottom' }
            }
          }
        });

        // Monthly activity chart
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyLabels = ${JSON.stringify(Object.keys(monthlyData))};
        const monthlyBids = ${JSON.stringify(Object.values(monthlyData).map((d: any) => d.bids))};
        const monthlyAccepted = ${JSON.stringify(Object.values(monthlyData).map((d: any) => d.accepted))};
        
        new Chart(monthlyCtx, {
          type: 'line',
          data: {
            labels: monthlyLabels,
            datasets: [
              {
                label: 'Total Bids',
                data: monthlyBids,
                borderColor: '#f75100',
                backgroundColor: 'rgba(247, 81, 0, 0.1)',
                tension: 0.4
              },
              {
                label: 'Accepted Bids',
                data: monthlyAccepted,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
              }
            ]
          },
          options: {
            responsive: true,
            scales: {
              y: { beginAtZero: true }
            }
          }
        });

        // Auto-print after charts load
        setTimeout(() => {
          window.print();
        }, 1000);
      </script>
    </body>
    </html>
  `;

  // Open in new window for printing/PDF saving
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
  }
}