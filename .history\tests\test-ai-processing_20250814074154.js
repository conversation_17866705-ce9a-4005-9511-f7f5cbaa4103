/**
 * AI Processing & PDF Extraction Test Suite
 * Tests the unified PDF processing pipeline with AI analysis
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');
const { testAuth } = require('./test-auth-helper');

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:5000';
const TEST_DATA_DIR = path.join(__dirname, 'test-data');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: [],
  performance: []
};

// Utility functions
function log(message, type = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${type}] ${message}`);
}

function recordTest(testName, passed, duration, notes = '', performance = null) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    log(`✅ ${testName} - PASSED (${duration}ms)`, 'PASS');
  } else {
    testResults.failed++;
    log(`❌ ${testName} - FAILED (${duration}ms)`, 'FAIL');
  }
  
  testResults.details.push({
    test: testName,
    status: passed ? 'PASS' : 'FAIL',
    duration,
    notes,
    timestamp: new Date().toISOString()
  });

  if (performance) {
    testResults.performance.push({
      test: testName,
      ...performance,
      timestamp: new Date().toISOString()
    });
  }
}

// Create test data directory
function ensureTestDataDir() {
  if (!fs.existsSync(TEST_DATA_DIR)) {
    fs.mkdirSync(TEST_DATA_DIR, { recursive: true });
    log('Created test data directory');
  }
}

// Create realistic construction RFQ PDF
function createConstructionRFQ(filename, pages = 10) {
  const filePath = path.join(TEST_DATA_DIR, filename);
  
  // Create a more realistic PDF with construction content
  const pdfHeader = '%PDF-1.4\n';
  let pdfContent = `1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n`;
  
  // Add pages with construction-related content
  const constructionContent = [
    'REQUEST FOR QUOTATION - COMMERCIAL CONSTRUCTION PROJECT',
    'Project Name: Downtown Office Complex Phase II',
    'Project Location: 123 Main Street, Downtown City, TX 75201',
    'Trade Category: General Construction',
    'Scope of Work: Complete interior buildout including electrical, plumbing, HVAC',
    'Due Date: 30 days from RFQ issue date',
    'Project Description: This project involves the complete renovation of a 50,000 sq ft office space.',
    'Requirements: All work must comply with local building codes and ADA requirements.',
    'Materials: Contractor to provide all materials unless otherwise specified.',
    'Timeline: Project completion required within 120 days of contract award.',
    'Insurance: General liability minimum $2M, Workers comp as required by law.',
    'Bonding: Performance and payment bonds required for contracts over $100K.',
    'Cost Codes: 03.01 Concrete Work, 05.01 Structural Steel, 09.01 Finishes',
    'Specifications: See attached technical specifications document.',
    'Drawings: Architectural and engineering drawings available upon request.',
    'Contact: John Smith, Project Manager, (555) 123-4567, <EMAIL>'
  ];
  
  // Generate multiple pages
  for (let i = 0; i < pages; i++) {
    const pageNum = i + 3; // Start from object 3
    const contentObj = pageNum + pages;
    
    pdfContent += `${pageNum} 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents ${contentObj} 0 R\n>>\nendobj\n`;
    
    const pageContent = constructionContent[i % constructionContent.length];
    const contentLength = pageContent.length + 50; // Approximate with BT/ET wrapper
    
    pdfContent += `${contentObj} 0 obj\n<<\n/Length ${contentLength}\n>>\nstream\nBT\n/F1 12 Tf\n50 700 Td\n(${pageContent}) Tj\nET\nendstream\nendobj\n`;
  }
  
  // Add pages object
  const pagesArray = Array.from({length: pages}, (_, i) => `${i + 3} 0 R`).join(' ');
  pdfContent = `2 0 obj\n<<\n/Type /Pages\n/Kids [${pagesArray}]\n/Count ${pages}\n>>\nendobj\n` + pdfContent;
  
  // Add xref and trailer
  const objCount = 3 + (pages * 2);
  pdfContent += `xref\n0 ${objCount}\n`;
  for (let i = 0; i < objCount; i++) {
    pdfContent += `${String(i * 50).padStart(10, '0')} 00000 ${i === 0 ? 'f' : 'n'} \n`;
  }
  pdfContent += `trailer\n<<\n/Size ${objCount}\n/Root 1 0 R\n>>\nstartxref\n${pdfContent.length + 100}\n%%EOF\n`;
  
  const fullContent = pdfHeader + pdfContent;
  fs.writeFileSync(filePath, fullContent);
  
  log(`Created construction RFQ PDF: ${filename} (${pages} pages, ${Math.round(fullContent.length / 1024)}KB)`);
  return filePath;
}

// Test PDF.js Primary Extraction
async function testPDFExtractionPrimary() {
  const testName = 'PDF.js Primary Extraction';
  const startTime = Date.now();
  
  try {
    // Create realistic construction RFQ
    const testFile = createConstructionRFQ('test-construction-rfq.pdf', 5);
    
    const form = new FormData();
    form.append('documents', fs.createReadStream(testFile));
    form.append('projectName', 'PDF Extraction Test RFQ');
    form.append('projectLocation', 'Test Location');
    form.append('description', 'Testing PDF text extraction capabilities');
    form.append('tradeCategory', 'general');
    
    const authHeaders = await testAuth.getFormHeaders(form);
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: authHeaders,
      timeout: 45000
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === 201 && response.data.id) {
      const rfqId = response.data.id;
      
      // Get documents to check extracted text with authentication
      const authClient = await testAuth.createAuthenticatedClient();
      const documentsResponse = await authClient.get(`/api/rfqs/${rfqId}/documents`);
      
      if (documentsResponse.data.length > 0) {
        const document = documentsResponse.data[0];
        const extractedText = document.extractedText || '';
        const textLength = extractedText.length;
        
        // Check if meaningful text was extracted
        const hasProjectName = extractedText.includes('Downtown Office Complex');
        const hasLocation = extractedText.includes('Main Street');
        const hasTradeInfo = extractedText.includes('Construction');
        
        const performance = {
          fileSize: fs.statSync(testFile).size,
          extractedTextLength: textLength,
          extractionTime: duration,
          hasStructuredData: hasProjectName && hasLocation && hasTradeInfo
        };
        
        if (textLength > 50 && (hasProjectName || hasLocation || hasTradeInfo)) {
          recordTest(testName, true, duration, 
            `Extracted ${textLength} characters with structured content`, performance);
        } else {
          recordTest(testName, false, duration, 
            `Poor extraction: ${textLength} chars, structured: ${hasProjectName || hasLocation || hasTradeInfo}`, performance);
        }
      } else {
        recordTest(testName, false, duration, 'No documents found');
      }
    } else {
      recordTest(testName, false, duration, `Upload failed: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Test AI Processing with Groq
async function testAIProcessingGroq() {
  const testName = 'AI Processing - Groq Integration';
  const startTime = Date.now();
  
  try {
    // Create construction RFQ with rich content
    const testFile = createConstructionRFQ('test-ai-processing.pdf', 8);
    
    const form = new FormData();
    form.append('documents', fs.createReadStream(testFile));
    form.append('projectName', 'AI Processing Test Project');
    form.append('projectLocation', 'Downtown City, TX');
    form.append('description', 'Testing AI analysis of construction RFQ documents');
    form.append('tradeCategory', 'general');
    
    const authHeaders = await testAuth.getFormHeaders(form);
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: authHeaders,
      timeout: 60000 // Longer timeout for AI processing
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === 201 && response.data.id) {
      const rfq = response.data;
      
      // Check if AI processing occurred
      const hasAISummary = rfq.aiSummary && rfq.aiSummary.length > 0;
      const hasExtractedData = rfq.extractedData && Object.keys(rfq.extractedData).length > 0;
      
      const performance = {
        aiProcessingTime: duration,
        aiSummaryLength: rfq.aiSummary ? rfq.aiSummary.length : 0,
        extractedDataFields: rfq.extractedData ? Object.keys(rfq.extractedData).length : 0,
        hasStructuredExtraction: hasExtractedData
      };
      
      if (hasAISummary || hasExtractedData) {
        recordTest(testName, true, duration, 
          `AI processing successful - Summary: ${hasAISummary}, Data: ${hasExtractedData}`, performance);
      } else {
        recordTest(testName, false, duration, 
          'AI processing failed - no summary or extracted data', performance);
      }
    } else {
      recordTest(testName, false, duration, `Upload failed: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Test Multi-file AI Processing with Priorities
async function testMultiFileAIProcessing() {
  const testName = 'Multi-file AI Processing with Priorities';
  const startTime = Date.now();
  
  try {
    // Create main RFQ and supporting documents
    const mainRFQ = createConstructionRFQ('main-rfq.pdf', 10);
    const specsFile = path.join(TEST_DATA_DIR, 'specifications.txt');
    const drawingsFile = path.join(TEST_DATA_DIR, 'drawings-list.txt');
    
    // Create supporting documents
    fs.writeFileSync(specsFile, `
TECHNICAL SPECIFICATIONS
========================

1. CONCRETE WORK (03.01)
   - Ready-mix concrete, 4000 PSI minimum
   - Reinforcing steel, Grade 60
   - Formwork: Plywood, steel frame

2. ELECTRICAL WORK (16.01)
   - 200A main panel
   - LED lighting throughout
   - Emergency lighting system

3. PLUMBING (22.01)
   - Copper supply lines
   - PVC waste lines
   - Low-flow fixtures

4. HVAC (23.01)
   - Variable air volume system
   - Energy recovery ventilation
   - Programmable thermostats
    `);
    
    fs.writeFileSync(drawingsFile, `
DRAWING LIST
============

A-001: Site Plan
A-101: Floor Plan - Level 1
A-102: Floor Plan - Level 2
A-201: Exterior Elevations
A-301: Building Sections
E-101: Electrical Plan
M-101: Mechanical Plan
P-101: Plumbing Plan
    `);
    
    const form = new FormData();
    form.append('documents', fs.createReadStream(mainRFQ));
    form.append('documents', fs.createReadStream(specsFile));
    form.append('documents', fs.createReadStream(drawingsFile));
    
    form.append('projectName', 'Multi-file AI Processing Test');
    form.append('projectLocation', 'Test City, TX');
    form.append('description', 'Testing prioritized AI processing of multiple documents');
    form.append('tradeCategory', 'general');
    form.append('uploadBatchId', `batch_${Date.now()}`);
    
    // Add file metadata with priorities
    form.append('fileMetadata[0]', JSON.stringify({
      id: 'main-1',
      fileType: 'main',
      sequence: 1,
      fileName: 'main-rfq.pdf'
    }));
    form.append('fileMetadata[1]', JSON.stringify({
      id: 'specs-2',
      fileType: 'specifications',
      sequence: 2,
      fileName: 'specifications.txt'
    }));
    form.append('fileMetadata[2]', JSON.stringify({
      id: 'drawings-3',
      fileType: 'drawings',
      sequence: 3,
      fileName: 'drawings-list.txt'
    }));
    
    const authHeaders = await testAuth.getFormHeaders(form);
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: authHeaders,
      timeout: 90000 // Extended timeout for multi-file processing
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === 201 && response.data.id) {
      const rfqId = response.data.id;
      
      // Check documents were processed
      const documentsResponse = await axios.get(`${BASE_URL}/api/rfqs/${rfqId}/documents`);
      const documents = documentsResponse.data;
      
      const mainDoc = documents.find(d => d.fileType === 'main');
      const specsDoc = documents.find(d => d.fileType === 'specifications');
      const drawingsDoc = documents.find(d => d.fileType === 'drawings');
      
      const performance = {
        totalFiles: documents.length,
        processingTime: duration,
        mainDocProcessed: !!mainDoc,
        specsDocProcessed: !!specsDoc,
        drawingsDocProcessed: !!drawingsDoc,
        batchProcessingSuccessful: documents.length === 3
      };
      
      if (documents.length === 3 && mainDoc && specsDoc && drawingsDoc) {
        recordTest(testName, true, duration, 
          'All 3 files processed with correct priorities', performance);
      } else {
        recordTest(testName, false, duration, 
          `Processing incomplete: ${documents.length}/3 files`, performance);
      }
    } else {
      recordTest(testName, false, duration, `Upload failed: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Test AI Processing Fallback
async function testAIProcessingFallback() {
  const testName = 'AI Processing Fallback Mechanisms';
  const startTime = Date.now();
  
  try {
    // Test with a document that might challenge the AI
    const testFile = createConstructionRFQ('fallback-test.pdf', 3);
    
    // Temporarily modify content to be more challenging
    const challengingContent = 'COMPLEX TECHNICAL SPECIFICATIONS WITH UNUSUAL FORMATTING AND MIXED CONTENT TYPES';
    fs.appendFileSync(testFile, challengingContent);
    
    const form = new FormData();
    form.append('documents', fs.createReadStream(testFile));
    form.append('projectName', 'AI Fallback Test');
    form.append('projectLocation', 'Fallback City, TX');
    form.append('description', 'Testing AI processing fallback mechanisms');
    form.append('tradeCategory', 'general');
    
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': 'Bearer test-token'
      },
      timeout: 45000
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === 201) {
      // Even if AI processing partially fails, the system should handle it gracefully
      recordTest(testName, true, duration, 'System handled challenging content gracefully');
    } else {
      recordTest(testName, false, duration, `System failed to handle challenging content: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    // Partial failure is acceptable for fallback testing
    if (error.response && error.response.status < 500) {
      recordTest(testName, true, duration, 'System gracefully handled processing challenges');
    } else {
      recordTest(testName, false, duration, `Unexpected system failure: ${error.message}`);
    }
  }
}

// Test Large File Performance
async function testLargeFilePerformance() {
  const testName = 'Large File AI Processing Performance';
  const startTime = Date.now();
  
  try {
    // Create large construction document (50+ pages)
    const testFile = createConstructionRFQ('large-performance-test.pdf', 50);
    const fileSize = fs.statSync(testFile).size;
    
    log(`Testing large file performance: ${Math.round(fileSize / 1024 / 1024)}MB`);
    
    const form = new FormData();
    form.append('documents', fs.createReadStream(testFile));
    form.append('projectName', 'Large File Performance Test');
    form.append('projectLocation', 'Performance City, TX');
    form.append('description', 'Testing AI processing performance on large documents');
    form.append('tradeCategory', 'general');
    
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': 'Bearer test-token'
      },
      timeout: 120000 // 2 minute timeout for large files
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === 201 && response.data.id) {
      const rfqId = response.data.id;
      const documentsResponse = await axios.get(`${BASE_URL}/api/rfqs/${rfqId}/documents`);
      
      if (documentsResponse.data.length > 0) {
        const document = documentsResponse.data[0];
        const extractedTextLength = document.extractedText ? document.extractedText.length : 0;
        
        const performance = {
          fileSize,
          fileSizeMB: Math.round(fileSize / 1024 / 1024),
          processingTime: duration,
          processingTimeSeconds: Math.round(duration / 1000),
          extractedTextLength,
          processingRate: Math.round(fileSize / duration * 1000), // bytes per second
          meetsPerformanceTarget: duration < 30000 // 30 second target
        };
        
        if (duration < 30000 && extractedTextLength > 100) {
          recordTest(testName, true, duration, 
            `Large file processed efficiently: ${Math.round(fileSize/1024/1024)}MB in ${Math.round(duration/1000)}s`, performance);
        } else {
          recordTest(testName, false, duration, 
            `Performance below target: ${Math.round(duration/1000)}s for ${Math.round(fileSize/1024/1024)}MB`, performance);
        }
      } else {
        recordTest(testName, false, duration, 'No documents found after processing');
      }
    } else {
      recordTest(testName, false, duration, `Large file processing failed: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Test Confidence Scoring
async function testConfidenceScoring() {
  const testName = 'PDF Extraction Confidence Scoring';
  const startTime = Date.now();
  
  try {
    // Create high-quality document
    const testFile = createConstructionRFQ('confidence-test.pdf', 15);
    
    const form = new FormData();
    form.append('documents', fs.createReadStream(testFile));
    form.append('projectName', 'Confidence Scoring Test');
    form.append('projectLocation', 'Confidence City, TX');
    form.append('description', 'Testing PDF extraction confidence scoring');
    form.append('tradeCategory', 'general');
    
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': 'Bearer test-token'
      },
      timeout: 45000
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === 201 && response.data.extractedData) {
      const extractedData = response.data.extractedData;
      
      // Look for confidence indicators in the extracted data
      const hasConfidenceScore = extractedData.confidence !== undefined;
      const hasQualityMetrics = extractedData.extractionMethod !== undefined;
      
      const performance = {
        hasConfidenceScore,
        confidenceScore: extractedData.confidence || 0,
        extractionMethod: extractedData.extractionMethod || 'unknown',
        hasQualityMetrics
      };
      
      if (hasConfidenceScore || hasQualityMetrics) {
        recordTest(testName, true, duration, 
          `Confidence scoring working: ${extractedData.confidence || 'N/A'}`, performance);
      } else {
        recordTest(testName, false, duration, 
          'No confidence scoring detected', performance);
      }
    } else {
      recordTest(testName, false, duration, `Processing failed: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Main test runner
async function runTests() {
  log('🤖 Starting AI Processing & PDF Extraction Tests');
  log('==================================================');
  
  // Setup
  ensureTestDataDir();
  
  // Run tests in order
  await testPDFExtractionPrimary();
  await testAIProcessingGroq();
  await testMultiFileAIProcessing();
  await testAIProcessingFallback();
  await testLargeFilePerformance();
  await testConfidenceScoring();
  
  // Generate report
  log('==================================================');
  log('📊 AI Processing Test Results Summary');
  log(`Total Tests: ${testResults.total}`);
  log(`Passed: ${testResults.passed}`);
  log(`Failed: ${testResults.failed}`);
  log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  // Performance summary
  if (testResults.performance.length > 0) {
    log('\n📈 Performance Metrics Summary:');
    testResults.performance.forEach(perf => {
      if (perf.processingTime) {
        log(`  ${perf.test}: ${Math.round(perf.processingTime/1000)}s`);
      }
    });
  }
  
  // Save detailed results
  const reportPath = path.join(__dirname, 'test-results-ai-processing.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  log(`📄 Detailed results saved to: ${reportPath}`);
  
  // Cleanup test files
  if (fs.existsSync(TEST_DATA_DIR)) {
    fs.rmSync(TEST_DATA_DIR, { recursive: true, force: true });
    log('🧹 Cleaned up test data files');
  }
  
  return testResults;
}

// Export for use in other test files
module.exports = {
  runTests,
  testResults
};

// Run tests if called directly
if (require.main === module) {
  runTests().then(results => {
    process.exit(results.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('AI Processing test runner failed:', error);
    process.exit(1);
  });
}
