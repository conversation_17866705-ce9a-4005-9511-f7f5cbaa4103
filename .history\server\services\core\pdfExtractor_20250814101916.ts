// Import PDF.js with proper Node.js configuration
import * as pdfjsLib from "pdfjs-dist/legacy/build/pdf.mjs";

/**
 * Unified PDF Extraction Service
 * Consolidates enhanced PDF text extraction logic from multiple services
 * Provides consistent, reliable PDF processing for both RFQ and Bid documents
 */

interface ExtractionOptions {
  useStreaming?: boolean;
  timeout?: number;
  onProgress?: (progress: { stage: string; percentage: number; message: string }) => void;
}

interface ExtractionResult {
  text: string;
  success: boolean;
  extractionMethod: 'pdfjs' | 'pdf-parse' | 'gemini-vision';
  processingTime: number;
  pageCount: number;
  confidence: number;
}

export class UnifiedPDFExtractor {
  private readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private readonly PDF_CONFIG = {
    disableFontFace: true,
    disableRange: true,
    disableStream: true
  };

  /**
   * Main extraction method with automatic fallback strategy
   */
  async extractText(fileBuffer: Buffer, options: ExtractionOptions = {}): Promise<ExtractionResult> {
    console.log('🔧 UnifiedPDFExtractor: Starting PDF text extraction');
    console.log('🔧 Buffer size:', fileBuffer.length, 'bytes');
    console.log('🔧 Streaming mode:', options.useStreaming || false);

    const startTime = Date.now();

    try {
      // Primary: Enhanced PDF.js extraction
      const result = await this.enhancedPDFJsExtraction(fileBuffer, options);
      const processingTime = Date.now() - startTime;

      if (result.text.length >= 50) {
        console.log('✅ Enhanced PDF.js extraction successful:', result.text.length, 'characters');
        return {
          ...result,
          success: true,
          extractionMethod: 'pdfjs',
          processingTime,
          confidence: this.calculateConfidence(result.text, result.pageCount)
        };
      }

      console.log('⚠️ PDF.js extraction insufficient, trying fallbacks...');
      throw new Error('PDF.js extraction yielded insufficient text');

    } catch (pdfJsError: any) {
      console.log('❌ PDF.js extraction failed:', pdfJsError?.message || pdfJsError);
      
      try {
        // Fallback 1: pdf-parse library
        const result = await this.fallbackPdfParse(fileBuffer);
        const processingTime = Date.now() - startTime;
        
        if (result.text.length >= 50) {
          console.log('✅ pdf-parse fallback successful:', result.text.length, 'characters');
          return {
            ...result,
            success: true,
            extractionMethod: 'pdf-parse',
            processingTime,
            confidence: this.calculateConfidence(result.text, result.pageCount)
          };
        }
        
        throw new Error('pdf-parse extraction yielded insufficient text');
        
      } catch (pdfParseError: any) {
        console.log('❌ pdf-parse fallback failed:', pdfParseError?.message || pdfParseError);
        
        // Fallback 2: Gemini Vision (if API key available)
        if (process.env.GEMINI_API_KEY) {
          try {
            const result = await this.fallbackGeminiVision(fileBuffer);
            const processingTime = Date.now() - startTime;
            
            console.log('✅ Gemini Vision fallback successful:', result.text.length, 'characters');
            return {
              ...result,
              success: true,
              extractionMethod: 'gemini-vision',
              processingTime,
              confidence: this.calculateConfidence(result.text, 1) // Vision doesn't provide page count
            };
            
          } catch (geminiError: any) {
            console.log('❌ Gemini Vision fallback failed:', geminiError?.message || geminiError);
          }
        }
        
        // All extraction methods failed
        const processingTime = Date.now() - startTime;
        return {
          text: '',
          success: false,
          extractionMethod: 'pdfjs',
          processingTime,
          pageCount: 0,
          confidence: 0
        };
      }
    }
  }

  /**
   * Enhanced PDF.js extraction with robust text item processing
   */
  private async enhancedPDFJsExtraction(fileBuffer: Buffer, options: ExtractionOptions): Promise<{ text: string; pageCount: number }> {
    console.log('🔧 Starting enhanced PDF.js extraction...');
    
    
    // Buffer integrity validation
    this.validateBuffer(fileBuffer);
    
    const loadingTask = pdfjsLib.getDocument({
      data: new Uint8Array(fileBuffer),
      ...this.PDF_CONFIG
    });

    const pdf = await loadingTask.promise;
    console.log(`📄 PDF loaded successfully: ${pdf.numPages} pages`);

    let fullText = '';
    const totalPages = pdf.numPages;

    // Process pages with progress tracking
    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();

      console.log(`📄 Page ${pageNum} - Processing ${textContent.items?.length || 0} text items`);

      // Use enhanced text extraction
      const pageText = this.extractTextFromItems(textContent.items, pageNum);
      
      if (pageText.length > 0) {
        fullText += `\n--- Page ${pageNum} ---\n${pageText}`;
      } else {
        console.log(`⚠️ Page ${pageNum} - No text extracted, trying alternative methods`);
        
        // Alternative extraction for problematic pages
        const altText = await this.alternativePageExtraction(page, pageNum);
        if (altText.length > 0) {
          fullText += `\n--- Page ${pageNum} ---\n${altText}`;
        } else {
          fullText += `\n--- Page ${pageNum} ---\n`;
        }
      }

      // Progress callback
      if (options.onProgress) {
        const percentage = Math.round((pageNum / totalPages) * 70); // Reserve 30% for post-processing
        options.onProgress({
          stage: 'text_extraction',
          percentage,
          message: `Extracted text from page ${pageNum} of ${totalPages}`
        });
      }
    }

    // Clean up extracted text
    const cleanedText = this.cleanExtractedText(fullText);
    console.log(`✅ PDF.js extraction complete: ${cleanedText.length} characters extracted`);

    return {
      text: cleanedText,
      pageCount: totalPages
    };
  }

  /**
   * Consolidated enhanced text extraction from PDF.js text items
   * Combines the best logic from both aiService.ts and aiStreamProcessor.ts
   */
  private extractTextFromItems(items: any[], pageNum?: number): string {
    if (!items || !Array.isArray(items)) {
      return '';
    }

    const textParts: string[] = [];
    
    for (const item of items) {
      try {
        let text = '';
        
        // Handle different PDF.js text item structures
        if (typeof item === 'string') {
          text = item;
        } else if (item && typeof item === 'object') {
          // Primary extraction properties (in order of reliability)
          if (typeof item.str === 'string') {
            text = item.str;
          } else if (typeof item.text === 'string') {
            text = item.text;
          } else if (typeof item.textContent === 'string') {
            text = item.textContent;
          } else if (typeof item.unicode === 'string') {
            text = item.unicode;
          } else if (typeof item.chars === 'string') {
            text = item.chars;
          } else if (typeof item.content === 'string') {
            text = item.content;
          } else if (typeof item.value === 'string') {
            text = item.value;
          }
          // Handle nested items
          else if (Array.isArray(item.items)) {
            text = this.extractTextFromItems(item.items, pageNum);
          }
        }
        
        // Include text with custom font encoding support
        if (text) {
          if (text.trim()) {
            textParts.push(text.trim());
          } else if (text.length > 0) {
            // Include control characters for custom font encoding
            textParts.push(text);
          }
        }
        
      } catch (itemError) {
        console.warn(`Page ${pageNum || '?'} - Skipping malformed text item:`, itemError);
        continue;
      }
    }
    
    return textParts.join(' ');
  }

  /**
   * Alternative page extraction for problematic pages
   */
  private async alternativePageExtraction(page: any, pageNum: number): Promise<string> {
    try {
      // Try with different textContent options
      const altTextContent = await page.getTextContent({
        includeMarkedContent: true,
        disableCombineTextItems: false,
        normalizeWhitespace: false
      });
      
      if (altTextContent.items?.length > 0) {
        return altTextContent.items.map((item: any) => {
          return item?.str || item?.unicode || item?.chars || item?.text || '';
        }).join(' ');
      }
      
    } catch (error) {
      console.warn(`Alternative extraction failed for page ${pageNum}:`, error);
    }
    
    return '';
  }

  /**
   * pdf-parse fallback extraction
   */
  private async fallbackPdfParse(fileBuffer: Buffer): Promise<{ text: string; pageCount: number }> {
    console.log('🔧 Attempting pdf-parse fallback...');
    
    const pdfParse = await import("pdf-parse") as any;
    const parseFunction = pdfParse.default || pdfParse;
    const pdfData = await parseFunction(fileBuffer);
    
    return {
      text: pdfData.text || '',
      pageCount: pdfData.numpages || 0
    };
  }

  /**
   * Gemini Vision fallback extraction
   */
  private async fallbackGeminiVision(fileBuffer: Buffer): Promise<{ text: string; pageCount: number }> {
    console.log('🔧 Attempting Gemini Vision fallback...');
    
    const { GoogleGenerativeAI } = await import("@google/generative-ai");
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

    const prompt = "Extract all text content from this document. Focus on preserving the structure and all readable text.";

    const result = await model.generateContent([
      prompt,
      {
        inlineData: {
          data: fileBuffer.toString('base64'),
          mimeType: 'application/pdf'
        }
      }
    ]);

    const response = await result.response;
    const text = response.text() || '';
    
    return {
      text,
      pageCount: 1 // Vision doesn't provide accurate page count
    };
  }

  /**
   * Validate buffer integrity
   */
  private validateBuffer(fileBuffer: Buffer): void {
    if (!Buffer.isBuffer(fileBuffer)) {
      throw new Error('Invalid buffer provided');
    }
    
    if (fileBuffer.length === 0) {
      throw new Error('Empty buffer provided');
    }
    
    // Validate PDF header
    const pdfHeader = fileBuffer.slice(0, 4).toString();
    if (pdfHeader !== '%PDF') {
      console.warn('⚠️ Buffer may not be a valid PDF file (missing %PDF header)');
    }
    
    console.log('✅ Buffer validation passed:', {
      size: fileBuffer.length,
      isPDF: pdfHeader === '%PDF'
    });
  }

  /**
   * Clean and normalize extracted text
   */
  private cleanExtractedText(text: string): string {
    return text
      .replace(/\s+/g, ' ')           // Normalize whitespace
      .replace(/\n\s*\n/g, '\n')      // Remove extra line breaks
      .replace(/^\s+|\s+$/g, '')      // Trim start and end
      .trim();
  }

  /**
   * Initialize PDF.js with proper Node.js configuration
   */
  private async initializePDFJs(): Promise<any> {
    try {
      const pdfjs = await import('pdfjs-dist');
      
      // Configure for Node.js environment
      if (typeof window === 'undefined') {
        // Mock DOM APIs for PDF.js
        (global as any).DOMMatrix = class DOMMatrix {
          constructor() {
            this.a = 1; this.b = 0; this.c = 0; this.d = 1; this.e = 0; this.f = 0;
          }
        };
        
        (global as any).document = {
          createElement: () => ({}),
          getElementsByTagName: () => []
        };
        
        (global as any).window = {};
        (global as any).navigator = { userAgent: 'Node.js' };
      }
      
      return pdfjs;
    } catch (error) {
      console.error('Failed to initialize PDF.js:', error);
      throw new Error('PDF.js initialization failed');
    }
  }

  /**
   * Calculate extraction confidence score
   */
  private calculateConfidence(text: string, pageCount: number): number {
    if (!text || text.length === 0) return 0;
    
    const factors = {
      length: Math.min(text.length / 1000, 1) * 0.4,      // Text length factor
      structure: (text.match(/\n/g)?.length || 0) > 0 ? 0.3 : 0, // Structure presence
      pages: pageCount > 0 ? 0.3 : 0                      // Page detection
    };
    
    return Math.round((factors.length + factors.structure + factors.pages) * 100);
  }
}

// Export singleton instance
export const unifiedPDFExtractor = new UnifiedPDFExtractor();
