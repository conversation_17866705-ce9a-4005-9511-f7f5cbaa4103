// Test contractor creation and update using existing organization
import { storage } from './server/storage.ts';

async function testWithExistingOrg() {
  try {
    console.log('🧪 Testing contractor creation with existing organization...');
    
    // First, check for existing organizations
    console.log('🔍 Looking for existing organizations...');
    const organizations = await storage.getOrganizations();
    console.log('✅ Found', organizations.length, 'organizations in database');
    
    let orgId;
    if (organizations.length > 0) {
      orgId = organizations[0].id;
      console.log('📋 Using existing organization:', organizations[0].name, '(ID:', orgId, ')');
    } else {
      // Create a test organization if none exist
      console.log('🏢 No organizations found, creating test organization...');
      const newOrg = await storage.createOrganization({
        name: 'Test Construction Company',
        slug: 'test-construction-' + Date.now(),
        description: 'Test organization for contractor testing',
        userLimit: 15,
        isActive: true
      });
      
      if (newOrg) {
        orgId = newOrg.id;
        console.log('✅ Organization created successfully:', newOrg.name, '(ID:', orgId, ')');
      } else {
        throw new Error('Failed to create test organization');
      }
    }
    
    // Now create a contractor with the organization ID
    console.log('📝 Creating test contractor...');
    const newContractor = await storage.createContractor({
      organizationId: orgId, // Required field - in production this comes from Clerk
      companyName: 'Test Contractor Inc.',
      primaryContactName: 'John Doe',
      primaryContactEmail: '<EMAIL>',
      primaryContactPhone: '555-0123',
      primaryAddress: '123 Test St, Test City, TS 12345',
      licenseNumber: 'LIC123456',
      yearsInBusiness: 10,
      tradeTypes: ['General Construction', 'Electrical'],
      isApproved: false
    });
    
    if (newContractor) {
      console.log('✅ Contractor created successfully:', newContractor.id, newContractor.companyName);
      
      // Now test the update functionality
      console.log('🔄 Testing update functionality...');
      const updatedName = 'Updated Test Contractor - ' + Date.now();
      const result = await storage.updateContractor(newContractor.id, {
        companyName: updatedName,
        primaryContactName: 'Jane Doe Updated',
        yearsInBusiness: 15
      });
      
      if (result) {
        console.log('✅ Update successful!');
        console.log('   - New company name:', result.companyName);
        console.log('   - New contact name:', result.primaryContactName);
        console.log('   - New years in business:', result.yearsInBusiness);
        console.log('   - ID remains:', result.id);
      } else {
        console.log('❌ Update returned null/undefined');
      }
      
      // Verify the update by fetching the contractor again
      console.log('🔍 Verifying update by fetching contractor...');
      const fetchedContractor = await storage.getContractor(newContractor.id);
      if (fetchedContractor) {
        console.log('✅ Verification successful:');
        console.log('   - Fetched company name:', fetchedContractor.companyName);
        console.log('   - Fetched contact name:', fetchedContractor.primaryContactName);
        console.log('   - Fetched years in business:', fetchedContractor.yearsInBusiness);
      } else {
        console.log('❌ Could not fetch updated contractor');
      }
      
    } else {
      console.log('❌ Failed to create contractor');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
  process.exit(0);
}

testWithExistingOrg();
