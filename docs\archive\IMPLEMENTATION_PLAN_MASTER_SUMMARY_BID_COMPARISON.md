# Implementation Plan: Master Consolidated Summary & Enhanced Bid Comparison

## Overview
This document provides a detailed, step-by-step implementation plan for two key features:
1. **Master Consolidated Summary** - Automatically consolidate multiple document summaries with conflict detection
2. **Enhanced Bid Comparison** - Advanced bid analysis with inclusion/exclusion tracking and report generation

## Feature 1: Master Consolidated Summary

### Step 1: Database Schema Updates

```sql
-- Add master summary fields to RFQs table
ALTER TABLE rfqs ADD COLUMN IF NOT EXISTS master_summary TEXT;
ALTER TABLE rfqs ADD COLUMN IF NOT EXISTS conflict_flags JSONB;
ALTER TABLE rfqs ADD COLUMN IF NOT EXISTS summary_generated_at TIMESTAMP;
```

### Step 2: Create Master Summary Service

**File: `server/services/masterSummaryService.ts`**

```typescript
import { db } from "../db";
import { rfqs, rfqDocuments } from "@shared/schema";
import { eq } from "drizzle-orm";
import { generateConsolidatedSummary } from "./aiService";

interface ConflictFlag {
  type: 'date' | 'amount' | 'requirement' | 'specification';
  field: string;
  documents: string[];
  values: any[];
  severity: 'low' | 'medium' | 'high';
}

export async function generateMasterSummary(rfqId: string): Promise<{
  masterSummary: string;
  conflicts: ConflictFlag[];
}> {
  try {
    console.log(`[MasterSummary] Starting generation for RFQ: ${rfqId}`);
    
    // Get RFQ and all documents
    const [rfq] = await db.select().from(rfqs).where(eq(rfqs.id, rfqId));
    if (!rfq) throw new Error("RFQ not found");
    
    const documents = await db.select().from(rfqDocuments).where(eq(rfqDocuments.rfqId, rfqId));
    
    // Extract all summaries and text
    const documentSummaries = documents.map(doc => ({
      fileName: doc.fileName,
      extractedText: doc.extractedText || '',
      summary: rfq.aiSummary || ''
    }));
    
    // Detect conflicts
    const conflicts = detectConflicts(documents, rfq.extractedData);
    
    // Generate consolidated summary using AI
    const masterSummary = await generateConsolidatedSummary({
      projectName: rfq.projectName,
      projectDescription: rfq.description,
      documents: documentSummaries,
      extractedData: rfq.extractedData,
      conflicts
    });
    
    // Update database
    await db.update(rfqs)
      .set({
        masterSummary,
        conflictFlags: conflicts,
        summaryGeneratedAt: new Date()
      })
      .where(eq(rfqs.id, rfqId));
    
    console.log(`[MasterSummary] Generation complete for RFQ: ${rfqId}`);
    return { masterSummary, conflicts };
    
  } catch (error) {
    console.error(`[MasterSummary] Error generating summary:`, error);
    throw error;
  }
}

function detectConflicts(documents: any[], extractedData: any): ConflictFlag[] {
  const conflicts: ConflictFlag[] = [];
  
  // Date conflict detection
  const dateFields = ['dueDate', 'finalAwardDate', 'completionDate'];
  for (const field of dateFields) {
    const values = extractUniqueDates(documents, extractedData, field);
    if (values.length > 1) {
      conflicts.push({
        type: 'date',
        field,
        documents: values.map(v => v.source),
        values: values.map(v => v.value),
        severity: field === 'dueDate' ? 'high' : 'medium'
      });
    }
  }
  
  // Amount conflict detection
  const amountFields = ['budgetAmount', 'estimatedValue'];
  for (const field of amountFields) {
    const values = extractUniqueAmounts(documents, extractedData, field);
    if (values.length > 1) {
      const variance = calculateVariance(values.map(v => v.value));
      conflicts.push({
        type: 'amount',
        field,
        documents: values.map(v => v.source),
        values: values.map(v => v.value),
        severity: variance > 0.2 ? 'high' : 'medium'
      });
    }
  }
  
  return conflicts;
}
```

### Step 3: Add API Endpoint

**Update: `server/routes.ts`**

```typescript
// Generate master summary for RFQ
app.post('/api/rfqs/:rfqId/generate-master-summary', isAuthenticated, async (req: any, res) => {
  try {
    const { rfqId } = req.params;
    const user = req.user;
    
    // Verify ownership
    const rfq = await storage.getRfq(rfqId);
    if (!rfq || rfq.createdBy !== user.claims.sub) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    // Generate master summary
    const { generateMasterSummary } = await import('./services/masterSummaryService');
    const result = await generateMasterSummary(rfqId);
    
    res.json(result);
  } catch (error) {
    console.error('Error generating master summary:', error);
    res.status(500).json({ message: 'Failed to generate master summary' });
  }
});
```

### Step 4: Frontend Component

**File: `client/src/components/MasterSummaryView.tsx`**

```typescript
import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, FileWarning, CheckCircle2 } from "lucide-react";
import { queryClient } from "@/lib/queryClient";

interface MasterSummaryViewProps {
  rfqId: string;
}

export function MasterSummaryView({ rfqId }: MasterSummaryViewProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  
  const { data: rfq } = useQuery({
    queryKey: [`/api/rfqs/${rfqId}`],
  });
  
  const generateMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/rfqs/${rfqId}/generate-master-summary`, {
        method: 'POST',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to generate summary');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/rfqs/${rfqId}`] });
      setIsGenerating(false);
    },
    onError: () => {
      setIsGenerating(false);
    }
  });
  
  const handleGenerate = () => {
    setIsGenerating(true);
    generateMutation.mutate();
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Master Summary</CardTitle>
      </CardHeader>
      <CardContent>
        {rfq?.masterSummary ? (
          <div className="space-y-4">
            <div className="prose dark:prose-invert max-w-none">
              <div dangerouslySetInnerHTML={{ __html: rfq.masterSummary }} />
            </div>
            
            {rfq.conflictFlags && rfq.conflictFlags.length > 0 && (
              <Alert variant="warning">
                <FileWarning className="h-4 w-4" />
                <AlertDescription>
                  {rfq.conflictFlags.length} conflicts detected between documents
                </AlertDescription>
              </Alert>
            )}
            
            <Button onClick={handleGenerate} variant="outline" disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Regenerating...
                </>
              ) : (
                <>
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Regenerate Summary
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">
              No master summary generated yet
            </p>
            <Button onClick={handleGenerate} disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Master Summary'
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

## Feature 2: Enhanced Bid Comparison

### Step 1: Database Schema Updates

```sql
-- Add comparison fields to bids table
ALTER TABLE bids ADD COLUMN IF NOT EXISTS inclusions_exclusions JSONB;
ALTER TABLE bids ADD COLUMN IF NOT EXISTS comparison_data JSONB;
ALTER TABLE bids ADD COLUMN IF NOT EXISTS cost_code_breakdown JSONB;
```

### Step 2: Enhanced Bid Analysis Service

**Update: `server/services/bidAnalysisService.ts`**

```typescript
export interface BidComparison {
  bidId: string;
  contractorName: string;
  bidAmount: number;
  inclusions: string[];
  exclusions: string[];
  keyDifferentiators: string[];
  riskFactors: string[];
  timeline: string;
  warranty: string;
}

export async function generateBidComparisonReport(rfqId: string): Promise<{
  comparisons: BidComparison[];
  report: string;
}> {
  try {
    console.log(`[BidComparison] Generating report for RFQ: ${rfqId}`);
    
    // Get all bids with contractors
    const bidData = await storage.getBidsByRfqWithContractors(rfqId);
    
    // Process each bid for comparison
    const comparisons = await Promise.all(
      bidData.map(async ({ bid, contractor }) => {
        const analysis = await analyzeBidInclusionsExclusions(bid);
        return {
          bidId: bid.id,
          contractorName: contractor.companyName,
          bidAmount: parseFloat(bid.bidAmount || '0'),
          inclusions: analysis.inclusions,
          exclusions: analysis.exclusions,
          keyDifferentiators: analysis.differentiators,
          riskFactors: analysis.risks,
          timeline: bid.timeline || 'Not specified',
          warranty: bid.warranty || 'Standard'
        };
      })
    );
    
    // Generate comparison report
    const report = await generateComparisonReport(comparisons);
    
    return { comparisons, report };
    
  } catch (error) {
    console.error(`[BidComparison] Error generating report:`, error);
    throw error;
  }
}

async function analyzeBidInclusionsExclusions(bid: any) {
  // Use AI to analyze bid documents and extract inclusions/exclusions
  const prompt = `
    Analyze this bid submission and extract:
    1. What is included in the bid price
    2. What is excluded from the bid price
    3. Key differentiators from other bids
    4. Potential risk factors
    
    Bid Details:
    ${JSON.stringify({
      scope: bid.scope,
      conditions: bid.conditions,
      extractedText: bid.extractedText
    })}
  `;
  
  // Call AI service (implementation depends on your AI provider)
  const analysis = await callAIForAnalysis(prompt);
  
  return {
    inclusions: analysis.inclusions || [],
    exclusions: analysis.exclusions || [],
    differentiators: analysis.differentiators || [],
    risks: analysis.risks || []
  };
}
```

### Step 3: Comparison Matrix Component

**File: `client/src/components/BidComparisonMatrix.tsx`**

```typescript
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, X, AlertTriangle } from "lucide-react";

interface BidComparisonMatrixProps {
  rfqId: string;
}

export function BidComparisonMatrix({ rfqId }: BidComparisonMatrixProps) {
  const { data: comparison, isLoading } = useQuery({
    queryKey: [`/api/rfqs/${rfqId}/bid-comparison`],
  });
  
  if (isLoading) {
    return <div>Loading comparison...</div>;
  }
  
  if (!comparison || comparison.comparisons.length === 0) {
    return <div>No bids to compare</div>;
  }
  
  const allInclusions = [...new Set(
    comparison.comparisons.flatMap(c => c.inclusions)
  )];
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Bid Comparison Matrix</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="border p-2 text-left">Feature</th>
                {comparison.comparisons.map(bid => (
                  <th key={bid.bidId} className="border p-2 text-center">
                    {bid.contractorName}
                    <div className="text-sm font-normal">
                      ${bid.bidAmount.toLocaleString()}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {allInclusions.map(inclusion => (
                <tr key={inclusion}>
                  <td className="border p-2">{inclusion}</td>
                  {comparison.comparisons.map(bid => (
                    <td key={bid.bidId} className="border p-2 text-center">
                      {bid.inclusions.includes(inclusion) ? (
                        <Check className="h-5 w-5 text-green-500 mx-auto" />
                      ) : bid.exclusions.includes(inclusion) ? (
                        <X className="h-5 w-5 text-red-500 mx-auto" />
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
              <tr>
                <td className="border p-2 font-semibold">Timeline</td>
                {comparison.comparisons.map(bid => (
                  <td key={bid.bidId} className="border p-2 text-center">
                    {bid.timeline}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border p-2 font-semibold">Warranty</td>
                {comparison.comparisons.map(bid => (
                  <td key={bid.bidId} className="border p-2 text-center">
                    {bid.warranty}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border p-2 font-semibold">Risk Factors</td>
                {comparison.comparisons.map(bid => (
                  <td key={bid.bidId} className="border p-2">
                    {bid.riskFactors.length > 0 ? (
                      <div className="space-y-1">
                        {bid.riskFactors.map((risk, idx) => (
                          <Badge key={idx} variant="destructive" className="text-xs">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            {risk}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <span className="text-green-500">Low Risk</span>
                    )}
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
```

### Step 4: Report Generation Endpoint

**Update: `server/routes.ts`**

```typescript
// Get bid comparison report
app.get('/api/rfqs/:rfqId/bid-comparison-report', isAuthenticated, async (req: any, res) => {
  try {
    const { rfqId } = req.params;
    const { format = 'json' } = req.query;
    
    // Generate comparison report
    const { generateBidComparisonReport } = await import('./services/bidAnalysisService');
    const report = await generateBidComparisonReport(rfqId);
    
    if (format === 'pdf') {
      // Generate PDF report
      const pdfBuffer = await generatePDFReport(report);
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="bid-comparison-${rfqId}.pdf"`);
      res.send(pdfBuffer);
    } else {
      res.json(report);
    }
  } catch (error) {
    console.error('Error generating bid comparison report:', error);
    res.status(500).json({ message: 'Failed to generate report' });
  }
});
```

## Testing Scripts

### Test Script 1: Master Summary Generation

**File: `test-master-summary.js`**

```javascript
const fetch = require('node-fetch');

async function testMasterSummary() {
  const rfqId = 'YOUR_RFQ_ID';
  const apiKey = 'YOUR_API_KEY';
  
  try {
    console.log('Testing master summary generation...');
    
    const response = await fetch(`http://localhost:5000/api/rfqs/${rfqId}/generate-master-summary`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    console.log('Master Summary Generated:');
    console.log('- Summary length:', result.masterSummary.length);
    console.log('- Conflicts found:', result.conflicts.length);
    console.log('- Conflict types:', result.conflicts.map(c => c.type));
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testMasterSummary();
```

### Test Script 2: Bid Comparison

**File: `test-bid-comparison.js`**

```javascript
const fetch = require('node-fetch');

async function testBidComparison() {
  const rfqId = 'YOUR_RFQ_ID';
  const apiKey = 'YOUR_API_KEY';
  
  try {
    console.log('Testing bid comparison report...');
    
    const response = await fetch(`http://localhost:5000/api/rfqs/${rfqId}/bid-comparison-report`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    console.log('Bid Comparison Report:');
    console.log('- Total bids:', result.comparisons.length);
    console.log('- Contractors:', result.comparisons.map(c => c.contractorName));
    console.log('- Bid amounts:', result.comparisons.map(c => `$${c.bidAmount}`));
    
    // Test inclusion/exclusion analysis
    result.comparisons.forEach(comp => {
      console.log(`\n${comp.contractorName}:`);
      console.log('  Inclusions:', comp.inclusions.length);
      console.log('  Exclusions:', comp.exclusions.length);
      console.log('  Risk factors:', comp.riskFactors);
    });
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testBidComparison();
```

## Error Logging Enhancement

**File: `server/utils/logger.ts`**

```typescript
import winston from 'winston';

export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'bidaible' },
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Usage in services
logger.info('Master summary generation started', { rfqId });
logger.error('Failed to generate summary', { error, rfqId });
```

## Implementation Timeline

1. **Week 1**: Database schema updates and backend services
2. **Week 2**: API endpoints and AI integration
3. **Week 3**: Frontend components and UI integration
4. **Week 4**: Testing, error handling, and optimization

## Success Metrics

- Master summaries generated within 30 seconds
- 95% accuracy in conflict detection
- Bid comparison matrix loads in < 2 seconds
- PDF report generation in < 5 seconds
- Zero data loss during processing