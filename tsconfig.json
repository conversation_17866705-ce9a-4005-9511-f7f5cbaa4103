{"include": ["client/src/**/*", "shared/**/*", "server/**/*"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "target": "ES2020", "strict": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "downlevelIteration": true, "useUnknownInCatchVariables": false, "baseUrl": ".", "types": ["node"], "typeRoots": ["./node_modules/@types", "./server/types"], "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"]}}}