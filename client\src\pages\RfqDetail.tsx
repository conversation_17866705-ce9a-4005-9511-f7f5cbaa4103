import { useState } from "react";
import { useParams } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { FileText, Calendar, MapPin, Users, Send, Eye, Download, ArrowLeft } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { BidSubmissionForm } from "@/components/BidSubmissionForm";
import { BidResponsesDashboard } from "@/components/BidResponsesDashboard";
import { RfqDistributionManager } from "@/components/RfqDistributionManager";
import { ContractorFavoritesManager } from "@/components/ContractorFavoritesManager";
import { ComprehensiveBidComparison } from "@/components/ComprehensiveBidComparison";
import { SimplePDFViewer } from "@/components/SimplePDFViewer";
import { useLocation } from "wouter";

interface RfqDocument {
  id: string;
  fileName: string;
  fileSize: number;
  objectKey: string;
  uploadedAt: string;
}

interface Rfq {
  id: string;
  title: string;
  description: string | null;
  projectLocation: string | null;
  dueDate: string;
  bidProposalDeadlineAt: string;
  status: string;
  createdBy: string;
  createdAt: string;
}

export default function RfqDetail() {
  const { rfqId } = useParams();
  const [, navigate] = useLocation();
  const [isBidFormOpen, setIsBidFormOpen] = useState(false);


  // Fetch RFQ details
  const { data: rfq, isLoading: rfqLoading } = useQuery<Rfq>({
    queryKey: ['/api/rfqs', rfqId],
  });

  // Fetch RFQ documents
  const { data: documents = [], isLoading: documentsLoading } = useQuery<RfqDocument[]>({
    queryKey: ['/api/rfqs', rfqId, 'documents'],
  });

  // Check if current user is the RFQ owner
  const { data: currentUser } = useQuery<{id: string; role: string}>({
    queryKey: ['/api/auth/user'],
  });

  const isRfqOwner = currentUser?.id === rfq?.createdBy || currentUser?.role === 'SuperUser' || currentUser?.role === 'Admin';

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'published':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'closed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'awarded':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleDocumentView = (doc: RfqDocument) => {
    // Open the file directly in a new tab - same as working RFQDetailsModal
    window.open(`/api/files/${doc.id}?view=true`, '_blank');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (rfqLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-64" />
          <div className="h-32 bg-muted rounded-lg" />
          <div className="h-96 bg-muted rounded-lg" />
        </div>
      </div>
    );
  }

  if (!rfq) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">RFQ Not Found</h3>
            <p className="text-muted-foreground">
              The requested RFQ could not be found or you don't have permission to view it.
            </p>
            <Button className="mt-4" onClick={() => navigate('/rfqs')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to RFQs
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Back Button */}
      <Button variant="outline" onClick={() => navigate('/rfqs')}>
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to RFQs
      </Button>
      {/* RFQ Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <CardTitle className="text-2xl">{rfq.title}</CardTitle>
                <Badge className={getStatusColor(rfq.status)}>
                  {rfq.status.toUpperCase()}
                </Badge>
              </div>
              <CardDescription className="text-base">
                {rfq.description || "No description provided"}
              </CardDescription>
            </div>
            {!isRfqOwner && rfq.status === 'published' && (
              <Dialog open={isBidFormOpen} onOpenChange={setIsBidFormOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Bid
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Submit Your Bid</DialogTitle>
                    <DialogDescription>
                      Submit your bid proposal for "{rfq.title}"
                    </DialogDescription>
                  </DialogHeader>
                  <BidSubmissionForm 
                    rfqId={rfq.id} 
                    onSuccess={() => setIsBidFormOpen(false)}
                  />
                </DialogContent>
              </Dialog>
            )}
          </div>
          
          {/* RFQ Metadata */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {rfq.projectLocation || "Location not specified"}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                Due: {formatDistanceToNow(new Date(rfq.dueDate), { addSuffix: true })}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                Created: {formatDistanceToNow(new Date(rfq.createdAt), { addSuffix: true })}
              </span>
            </div>
          </div>
        </CardHeader>
      </Card>
      {/* RFQ Content Tabs */}
      <Tabs defaultValue="documents" className="w-full">
        <TabsList className={`grid w-full ${isRfqOwner ? 'grid-cols-5' : 'grid-cols-1'}`}>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          {isRfqOwner && <TabsTrigger value="bids">Bid Responses</TabsTrigger>}
          {isRfqOwner && <TabsTrigger value="category-analysis">Bid Analysis</TabsTrigger>}
          {isRfqOwner && <TabsTrigger value="distribution">Distribution</TabsTrigger>}
          {isRfqOwner && <TabsTrigger value="favorites">Favorites</TabsTrigger>}
        </TabsList>

        {/* Documents Tab */}
        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Documents</CardTitle>
              <CardDescription>
                Review all project documents and specifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {documentsLoading ? (
                <div className="animate-pulse space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-16 bg-muted rounded-lg" />
                  ))}
                </div>
              ) : documents.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    No documents have been uploaded for this RFQ yet.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {documents.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{doc.fileName}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatFileSize(doc.fileSize)} • Uploaded {doc.uploadedAt ? formatDistanceToNow(new Date(doc.uploadedAt), { addSuffix: true }) : 'recently'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDocumentView(doc)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`/api/files/${doc.objectKey}?download=true`, '_blank')}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Bid Responses Tab (RFQ Owner Only) */}
        {isRfqOwner && (
          <TabsContent value="bids">
            <BidResponsesDashboard rfqId={rfq.id} />
          </TabsContent>
        )}

        {/* Category Analysis Tab (RFQ Owner Only) */}
        {isRfqOwner && (
          <TabsContent value="category-analysis">
            <ComprehensiveBidComparison rfqId={rfq.id} />
          </TabsContent>
        )}

        {/* Distribution Tab (RFQ Owner Only) */}
        {isRfqOwner && (
          <TabsContent value="distribution">
            <RfqDistributionManager rfqId={rfq.id} />
          </TabsContent>
        )}

        {/* Favorites Tab (RFQ Owner Only) */}
        {isRfqOwner && (
          <TabsContent value="favorites">
            <ContractorFavoritesManager />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}