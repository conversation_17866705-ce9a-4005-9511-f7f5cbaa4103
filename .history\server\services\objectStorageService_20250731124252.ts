
import { Client } from '@replit/object-storage';
import { Readable } from 'stream';

// Initialize Object Storage client
const client = new Client();

export interface StoredFile {
  fileName: string;
  objectKey: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: Date;
}

export interface UploadProgress {
  fileName: string;
  uploadedBytes: number;
  totalBytes: number;
  percentage: number;
  status: 'uploading' | 'processing' | 'complete' | 'error';
  stage: 'upload' | 'ai_processing' | 'storage' | 'complete';
}

export interface ChunkedUploadOptions {
  chunkSize?: number;
  onProgress?: (progress: UploadProgress) => void;
}

/**
 * Upload file to Replit Object Storage with progress tracking
 */
export async function uploadFile(
  fileBuffer: Buffer, 
  originalName: string, 
  mimeType: string,
  options?: ChunkedUploadOptions
): Promise<StoredFile> {
  // Generate unique object key with timestamp and original name
  const timestamp = Date.now();
  const objectKey = `rfq-documents/${timestamp}-${originalName}`;
  
  try {
    // Report initial progress
    if (options?.onProgress) {
      options.onProgress({
        fileName: originalName,
        uploadedBytes: 0,
        totalBytes: fileBuffer.length,
        percentage: 0,
        status: 'uploading',
        stage: 'upload'
      });
    }

    // Use chunked upload for large files (>10MB)
    if (fileBuffer.length > 10 * 1024 * 1024 && options?.onProgress) {
      await uploadFileChunked(objectKey, fileBuffer, originalName, options);
    } else {
      // Standard upload for smaller files
      await client.uploadFromBytes(objectKey, fileBuffer);
    }

    // Report completion
    if (options?.onProgress) {
      options.onProgress({
        fileName: originalName,
        uploadedBytes: fileBuffer.length,
        totalBytes: fileBuffer.length,
        percentage: 100,
        status: 'complete',
        stage: 'complete'
      });
    }
    
    return {
      fileName: originalName,
      objectKey,
      fileSize: fileBuffer.length,
      mimeType,
      uploadedAt: new Date()
    };
  } catch (error) {
    // Report error
    if (options?.onProgress) {
      options.onProgress({
        fileName: originalName,
        uploadedBytes: 0,
        totalBytes: fileBuffer.length,
        percentage: 0,
        status: 'error',
        stage: 'upload'
      });
    }
    console.error('Error uploading file to Object Storage:', error);
    throw new Error(`Failed to upload file: ${originalName}`);
  }
}

/**
 * Upload large files in chunks with progress tracking
 */
async function uploadFileChunked(
  objectKey: string,
  fileBuffer: Buffer,
  fileName: string,
  options: ChunkedUploadOptions
): Promise<void> {
  const chunkSize = options.chunkSize || 5 * 1024 * 1024; // 5MB chunks
  const totalSize = fileBuffer.length;
  let uploadedBytes = 0;

  // For large files, we simulate chunked progress while using the standard upload
  // Replit Object Storage doesn't support multipart uploads, so we'll show progress during the single upload
  const progressInterval = setInterval(() => {
    uploadedBytes = Math.min(uploadedBytes + chunkSize / 4, totalSize * 0.95); // Simulate progress up to 95%
    const percentage = Math.round((uploadedBytes / totalSize) * 100);
    
    if (options.onProgress) {
      options.onProgress({
        fileName,
        uploadedBytes,
        totalBytes: totalSize,
        percentage,
        status: 'uploading',
        stage: 'upload'
      });
    }
  }, 100);

  try {
    // Perform the actual upload
    await client.uploadFromBytes(objectKey, fileBuffer);
    clearInterval(progressInterval);
    
    // Complete the progress
    if (options.onProgress) {
      options.onProgress({
        fileName,
        uploadedBytes: totalSize,
        totalBytes: totalSize,
        percentage: 100,
        status: 'uploading',
        stage: 'storage'
      });
    }
  } catch (error) {
    clearInterval(progressInterval);
    throw error;
  }
}

/**
 * Download file from Replit Object Storage
 */
export async function downloadFile(objectKey: string): Promise<Buffer> {
  try {
    const fileData = await client.downloadAsBytes(objectKey);
    
    // Handle different response formats from Object Storage
    let fileBuffer: Buffer;
    
    if (Buffer.isBuffer(fileData)) {
      fileBuffer = fileData;
    } else if (fileData instanceof ArrayBuffer) {
      fileBuffer = Buffer.from(fileData);
    } else if (fileData instanceof Uint8Array) {
      fileBuffer = Buffer.from(fileData);
    } else if (typeof fileData === 'object' && fileData !== null) {
      // If it's an object, try to extract buffer data
      if ('data' in fileData && Buffer.isBuffer(fileData.data)) {
        fileBuffer = fileData.data;
      } else if ('buffer' in fileData && fileData.buffer) {
        fileBuffer = Buffer.from(fileData.buffer as ArrayBuffer);
      } else if ('value' in fileData && 'ok' in fileData) {
        // Handle Replit Object Storage response format
        const response = fileData as { ok: boolean; value: any };
        console.log('Object Storage response structure:', {
          ok: response.ok,
          valueType: typeof response.value,
          valueKeys: response.value && typeof response.value === 'object' ? Object.keys(response.value) : 'not an object',
          valueConstructor: response.value?.constructor?.name
        });
        
        if (Buffer.isBuffer(response.value)) {
          fileBuffer = response.value;
        } else if (response.value instanceof ArrayBuffer) {
          fileBuffer = Buffer.from(response.value);
        } else if (response.value instanceof Uint8Array) {
          fileBuffer = Buffer.from(response.value);
        } else if (response.value && typeof response.value === 'object') {
          // Check if value is an array (as indicated by logs)
          if (Array.isArray(response.value) && response.value.length > 0) {
            const firstElement = response.value[0];
            if (Buffer.isBuffer(firstElement)) {
              fileBuffer = firstElement;
            } else if (firstElement && typeof firstElement === 'object' && firstElement.constructor && firstElement.constructor.name === 'ArrayBuffer') {
              fileBuffer = Buffer.from(firstElement as ArrayBuffer);
            } else if (firstElement && typeof firstElement === 'object' && 'length' in firstElement && 'buffer' in firstElement) {
              fileBuffer = Buffer.from(firstElement as Uint8Array);
            } else {
              console.error('Array element is not a buffer:', typeof firstElement);
              throw new Error('Array element is not a valid buffer type');
            }
          }
          // Check if value has nested buffer data
          else if ('data' in response.value && Buffer.isBuffer((response.value as any).data)) {
            fileBuffer = (response.value as any).data;
          } else if ('buffer' in response.value && (response.value as any).buffer) {
            fileBuffer = Buffer.from((response.value as any).buffer as ArrayBuffer);
          } else if ('arrayBuffer' in response.value && (response.value as any).arrayBuffer) {
            fileBuffer = Buffer.from((response.value as any).arrayBuffer as ArrayBuffer);
          } else if ('bytes' in response.value && (response.value as any).bytes) {
            fileBuffer = Buffer.from((response.value as any).bytes as ArrayBuffer);
          } else {
            console.error('Unable to extract buffer from nested value object:', Object.keys(response.value));
            throw new Error('Unable to extract file data from Object Storage response');
          }
        } else {
          console.error('Unexpected value type in Object Storage response:', typeof response.value);
          throw new Error('Unexpected value type in Object Storage response');
        }
      } else {
        console.error('Unexpected object format:', typeof fileData, Object.keys(fileData));
        throw new Error('Unexpected file data format from Object Storage');
      }
    } else {
      console.error('Unexpected data type:', typeof fileData);
      throw new Error('Unexpected file data type from Object Storage');
    }
    
    // Ensure we have a valid buffer
    if (!fileBuffer || fileBuffer.length === 0) {
      throw new Error('File not found or empty');
    }
    
    return fileBuffer;
  } catch (error) {
    console.error('Error downloading file from Object Storage:', error);
    throw new Error(`Failed to download file: ${objectKey}`);
  }
}

/**
 * Delete file from Replit Object Storage
 */
export async function deleteFile(objectKey: string): Promise<void> {
  try {
    await client.delete(objectKey);
  } catch (error) {
    console.error('Error deleting file from Object Storage:', error);
    throw new Error(`Failed to delete file: ${objectKey}`);
  }
}

/**
 * Check if file exists in Object Storage
 */
export async function fileExists(objectKey: string): Promise<boolean> {
  try {
    const result = await client.list({ prefix: objectKey });
    if (result.ok && Array.isArray(result.objects)) {
      return result.objects.some((obj: any) => obj.name === objectKey);
    }
    return false;
  } catch (error) {
    console.error('Error checking file existence:', error);
    return false;
  }
}
