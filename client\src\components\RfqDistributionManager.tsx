import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Send, Heart, Radio, Users, Building2, Eye, Clock, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { formatDistanceToNow } from "date-fns";

interface RfqDistributionManagerProps {
  rfqId: string;
}

interface Contractor {
  id: string;
  companyName: string;
  primaryContactName: string;
  primaryContactEmail: string;
  tradeTypes: string[];
  verified: boolean;
}

interface RfqDistribution {
  id: string;
  contractorId: string;
  distributionMethod: "favorites" | "broadcast";
  distributedAt: string;
  viewedAt: string | null;
  declinedAt: string | null;
  declineReason: string | null;
  contractor: Contractor;
}

export function RfqDistributionManager({ rfqId }: RfqDistributionManagerProps) {
  const [isDistributeDialogOpen, setIsDistributeDialogOpen] = useState(false);
  const [distributionMethod, setDistributionMethod] = useState<"favorites" | "broadcast">("favorites");
  const [selectedContractors, setSelectedContractors] = useState<string[]>([]);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch current distributions
  const { data: distributions = [], isLoading: distributionsLoading } = useQuery<RfqDistribution[]>({
    queryKey: ['/api/rfqs', rfqId, 'distributions'],
  });

  // Fetch favorite contractors
  const { data: favoriteContractors = [], isLoading: favoritesLoading } = useQuery<Contractor[]>({
    queryKey: ['/api/contractors/favorites'],
    enabled: distributionMethod === 'favorites',
  });

  // Fetch all contractors for broadcast
  const { data: allContractors = [], isLoading: contractorsLoading } = useQuery<Contractor[]>({
    queryKey: ['/api/contractors'],
    enabled: distributionMethod === 'broadcast',
  });

  // Distribute RFQ mutation
  const distributeMutation = useMutation({
    mutationFn: async (data: { contractorIds: string[]; method: "favorites" | "broadcast" }) => {
      return apiRequest(`/api/rfqs/${rfqId}/distribute`, {
        method: 'POST',
        body: JSON.stringify(data),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/rfqs', rfqId, 'distributions'] });
      toast({
        title: "RFQ Distributed Successfully",
        description: `RFQ has been sent to ${selectedContractors.length} contractor(s).`,
      });
      setSelectedContractors([]);
      setIsDistributeDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Distribution Failed",
        description: error.message || "Failed to distribute RFQ to contractors.",
        variant: "destructive",
      });
    },
  });

  const handleDistribute = () => {
    if (selectedContractors.length === 0) {
      toast({
        title: "No Contractors Selected",
        description: "Please select at least one contractor to distribute the RFQ to.",
        variant: "destructive",
      });
      return;
    }

    distributeMutation.mutate({
      contractorIds: selectedContractors,
      method: distributionMethod,
    });
  };

  const handleContractorSelection = (contractorId: string, checked: boolean) => {
    if (checked) {
      setSelectedContractors(prev => [...prev, contractorId]);
    } else {
      setSelectedContractors(prev => prev.filter(id => id !== contractorId));
    }
  };

  const handleSelectAll = (contractors: Contractor[]) => {
    const contractorIds = contractors.map(c => c.id);
    setSelectedContractors(contractorIds);
  };

  const getStatusBadge = (distribution: RfqDistribution) => {
    if (distribution.declinedAt) {
      return <Badge variant="destructive">Declined</Badge>;
    }
    if (distribution.viewedAt) {
      return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Viewed</Badge>;
    }
    return <Badge variant="outline">Sent</Badge>;
  };

  const availableContractors = distributionMethod === 'favorites' 
    ? favoriteContractors 
    : allContractors.filter(contractor => 
        !distributions.some(dist => dist.contractorId === contractor.id)
      );

  return (
    <div className="space-y-6">
      {/* Distribution Status Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                RFQ Distribution
              </CardTitle>
              <CardDescription>
                Manage how this RFQ is distributed to contractors
              </CardDescription>
            </div>
            <Dialog open={isDistributeDialogOpen} onOpenChange={setIsDistributeDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Send className="h-4 w-4 mr-2" />
                  Distribute RFQ
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Distribute RFQ to Contractors</DialogTitle>
                  <DialogDescription>
                    Choose your distribution method and select contractors to send this RFQ to.
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  {/* Distribution Method Selection */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">Distribution Method</Label>
                    <RadioGroup
                      value={distributionMethod}
                      onValueChange={(value: "favorites" | "broadcast") => {
                        setDistributionMethod(value);
                        setSelectedContractors([]);
                      }}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="favorites" id="favorites" />
                        <Label htmlFor="favorites" className="flex items-center gap-2">
                          <Heart className="h-4 w-4" />
                          Send to Favorite Contractors ({favoriteContractors.length})
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="broadcast" id="broadcast" />
                        <Label htmlFor="broadcast" className="flex items-center gap-2">
                          <Radio className="h-4 w-4" />
                          Broadcast to All Contractors ({allContractors.length})
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Contractor Selection */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium">Select Contractors</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSelectAll(availableContractors)}
                      >
                        Select All ({availableContractors.length})
                      </Button>
                    </div>

                    {(distributionMethod === 'favorites' && favoritesLoading) || 
                     (distributionMethod === 'broadcast' && contractorsLoading) ? (
                      <div className="animate-pulse space-y-2">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="h-12 bg-muted rounded-lg" />
                        ))}
                      </div>
                    ) : availableContractors.length === 0 ? (
                      <div className="text-center py-8">
                        <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          {distributionMethod === 'favorites' 
                            ? "No favorite contractors available. Add some contractors to your favorites first."
                            : "No contractors available for distribution."
                          }
                        </p>
                      </div>
                    ) : (
                      <div className="max-h-60 overflow-y-auto border rounded-lg">
                        {availableContractors.map((contractor) => (
                          <div
                            key={contractor.id}
                            className="flex items-center space-x-3 p-3 border-b last:border-b-0"
                          >
                            <Checkbox
                              id={contractor.id}
                              checked={selectedContractors.includes(contractor.id)}
                              onCheckedChange={(checked) => 
                                handleContractorSelection(contractor.id, checked as boolean)
                              }
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <Building2 className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">{contractor.companyName}</span>
                                {contractor.verified && (
                                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    Verified
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {contractor.primaryContactName} • {contractor.primaryContactEmail}
                              </p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {(contractor.tradeTypes || []).slice(0, 3).map((trade) => (
                                  <Badge key={trade} variant="outline" className="text-xs">
                                    {trade}
                                  </Badge>
                                ))}
                                {(contractor.tradeTypes || []).length > 3 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{(contractor.tradeTypes || []).length - 3} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-3 pt-4 border-t">
                    <Button variant="outline" onClick={() => setIsDistributeDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleDistribute} 
                      disabled={distributeMutation.isPending || selectedContractors.length === 0}
                    >
                      {distributeMutation.isPending ? "Distributing..." : `Send to ${selectedContractors.length} Contractor(s)`}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {distributionsLoading ? (
            <div className="animate-pulse space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded-lg" />
              ))}
            </div>
          ) : distributions.length === 0 ? (
            <div className="text-center py-8">
              <Send className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground mb-4">
                This RFQ hasn't been distributed to any contractors yet.
              </p>
              <p className="text-sm text-muted-foreground">
                Use the "Distribute RFQ" button to send it to your favorite contractors or broadcast it to all contractors.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Contractor</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Distributed</TableHead>
                  <TableHead>Last Activity</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {distributions.map((distribution) => (
                  <TableRow key={distribution.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{distribution.contractor.companyName}</p>
                          <p className="text-sm text-muted-foreground">
                            {distribution.contractor.primaryContactName}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="flex items-center gap-1 w-fit">
                        {distribution.distributionMethod === 'favorites' ? (
                          <Heart className="h-3 w-3" />
                        ) : (
                          <Radio className="h-3 w-3" />
                        )}
                        {distribution.distributionMethod === 'favorites' ? 'Favorites' : 'Broadcast'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(distribution)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {formatDistanceToNow(new Date(distribution.distributedAt), { addSuffix: true })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {distribution.declinedAt ? (
                        <div className="flex items-center gap-1">
                          <X className="h-4 w-4 text-red-500" />
                          <span className="text-sm text-red-600">
                            Declined {formatDistanceToNow(new Date(distribution.declinedAt), { addSuffix: true })}
                          </span>
                        </div>
                      ) : distribution.viewedAt ? (
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-green-600">
                            Viewed {formatDistanceToNow(new Date(distribution.viewedAt), { addSuffix: true })}
                          </span>
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">Not viewed yet</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}