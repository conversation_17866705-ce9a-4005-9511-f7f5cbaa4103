# Unified File Upload & Processing Test Plan - Wasabi Storage Integration

**Date:** August 14, 2025  
**Version:** 1.0  
**Status:** Ready for Execution

## Test Environment Setup

### Prerequisites
- ✅ Wasabi S3-compatible storage configured
- ✅ AI services (Groq, OpenAI, Gemini) API keys
- ✅ PostgreSQL database with latest schema
- ✅ Server running on localhost:5000
- ✅ Clerk authentication working

### Environment Variables Required
```bash
WASABI_ACCESS_KEY_ID=your_wasabi_access_key_id
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_access_key
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com
WASABI_REGION=us-east-1
GROQ_API_KEY=gsk_your_groq_api_key
OPENAI_API_KEY=sk-your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
```

## Test Categories

### 🗂️ Category 1: Wasabi Storage Integration Tests

#### Test 1.1: Basic File Upload to Wasabi
**Objective:** Validate basic file upload functionality to Wasabi storage

**Steps:**
1. Upload single PDF file (< 10MB) via `/api/rfqs` endpoint
2. Verify file stored in Wasabi with correct object key format
3. Check file metadata in database
4. Test file retrieval via `/api/files/:documentId`

**Expected Results:**
- File uploaded successfully to Wasabi
- Object key format: `rfq-documents/{timestamp}-{filename}`
- Database record created with correct metadata
- File retrievable via API endpoint

**Test Data:** Sample construction RFQ PDF (5MB)

#### Test 1.2: Large File Chunked Upload
**Objective:** Test chunked upload for large files

**Steps:**
1. Upload large PDF file (> 10MB, up to 50MB)
2. Monitor progress tracking during upload
3. Verify file integrity after upload
4. Test download and compare checksums

**Expected Results:**
- Chunked upload completes successfully
- Progress updates received via SSE
- File integrity maintained
- Download matches original file

**Test Data:** Large construction specification PDF (25MB+)

#### Test 1.3: Multi-file Batch Upload
**Objective:** Test simultaneous upload of multiple files

**Steps:**
1. Upload batch of 5 files with different types and sizes
2. Monitor individual file progress
3. Verify all files stored with unique object keys
4. Check batch completion status

**Expected Results:**
- All files uploaded successfully
- Unique object keys generated
- Progress tracked per file
- Batch processing completes

**Test Data:** Mixed file batch (PDF, TXT, CSV)

### 🤖 Category 2: Unified PDF Processing Tests

#### Test 2.1: PDF.js Primary Extraction
**Objective:** Validate primary PDF text extraction method

**Steps:**
1. Upload standard construction RFQ PDF
2. Monitor extraction process
3. Verify extracted text quality and length
4. Check confidence scoring

**Expected Results:**
- Text extracted successfully (>50 characters)
- Confidence score calculated
- Processing time logged
- Structured data extracted

#### Test 2.2: Fallback Strategy Testing
