import type { Express } from 'express';
import { db } from '../db';
import { sql } from 'drizzle-orm';

export function addHealthRoutes(app: Express) {
  app.get('/api/health', async (_req, res) => {
    const started = Date.now();
    try {
      // simple DB ping
      let dbOk = false;
      try {
        await db.execute(sql`select 1`);
        dbOk = true;
      } catch (_e) {
        dbOk = false;
      }
      res.json({ ok: true, uptimeMs: process.uptime() * 1000, db: dbOk, startedAt: started, now: Date.now(), version: process.env.COMMIT_SHA || null });
    } catch (error: any) {
      res.status(500).json({ ok: false, error: error?.message || 'unknown' });
    }
  });
}
