/**
 * Master Test Runner for Unified File Processing & Wasabi Storage Integration
 * Orchestrates all test suites and provides comprehensive reporting
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Import test suites
const wasabiTests = require('./test-wasabi-integration');
const aiProcessingTests = require('./test-ai-processing');

// Test configuration
const BASE_URL = 'http://localhost:5000';
const RESULTS_DIR = path.join(__dirname, 'results');

// Master test results
const masterResults = {
  startTime: null,
  endTime: null,
  totalDuration: 0,
  suites: [],
  summary: {
    totalTests: 0,
    totalPassed: 0,
    totalFailed: 0,
    successRate: 0
  },
  environment: {
    nodeVersion: process.version,
    platform: process.platform,
    timestamp: new Date().toISOString()
  }
};

// Utility functions
function log(message, type = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${type}] ${message}`);
}

function ensureResultsDir() {
  if (!fs.existsSync(RESULTS_DIR)) {
    fs.mkdirSync(RESULTS_DIR, { recursive: true });
    log('Created results directory');
  }
}

// Environment validation
async function validateEnvironment() {
  log('🔍 Validating test environment...');
  
