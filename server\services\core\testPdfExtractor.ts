/**
 * Test script for Unified PDF Extractor
 * This will test the new unified extraction service with a real PDF
 */

import { promises as fs } from 'fs';
import path from 'path';
import { unifiedPDFExtractor } from './pdfExtractor';

export async function testUnifiedPDFExtractor() {
  console.log('🧪 Testing Unified PDF Extractor...');
  
  try {
    // Look for test PDFs in uploads directory
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const files = await fs.readdir(uploadsDir);
    const pdfFile = files.find(file => file.toLowerCase().endsWith('.pdf'));
    
    if (!pdfFile) {
      console.log('⚠️ No PDF files found in uploads directory for testing');
      return { success: false, message: 'No test PDF found' };
    }
    
    const pdfPath = path.join(uploadsDir, pdfFile);
    console.log('📄 Testing with PDF:', pdfFile);
    
    // Read the PDF file
    const fileBuffer = await fs.readFile(pdfPath);
    console.log('📊 File size:', fileBuffer.length, 'bytes');
    
    // Test the unified extractor
    const startTime = Date.now();
    const result = await unifiedPDFExtractor.extractText(fileBuffer, {
      onProgress: (progress) => {
        console.log(`📈 Progress: ${progress.stage} - ${progress.percentage}% - ${progress.message}`);
      }
    });
    
    const testTime = Date.now() - startTime;
    
    console.log('✅ Extraction Results:');
    console.log('  - Success:', result.success);
    console.log('  - Method:', result.extractionMethod);
    console.log('  - Text Length:', result.text.length);
    console.log('  - Page Count:', result.pageCount);
    console.log('  - Confidence:', result.confidence + '%');
    console.log('  - Processing Time:', testTime + 'ms');
    console.log('  - Text Preview:', result.text.substring(0, 200) + '...');
    
    // Verify we got meaningful text (not just page separators)
    const hasRealContent = result.text.length > 50 && 
                          !result.text.match(/^(\s*---\s*Page\s*\d+\s*---\s*)+$/);
    
    if (hasRealContent) {
      console.log('🎉 Test PASSED: Meaningful content extracted successfully');
      return { 
        success: true, 
        extractedLength: result.text.length,
        method: result.extractionMethod,
        processingTime: testTime
      };
    } else {
      console.log('❌ Test FAILED: Only page separators extracted (similar to original issue)');
      return { 
        success: false, 
        message: 'Only page separators extracted',
        extractedText: result.text
      };
    }
    
  } catch (error: any) {
    console.error('❌ Test FAILED with error:', error?.message || error);
    return { 
      success: false, 
      message: error?.message || 'Unknown test error' 
    };
  }
}

// Export test function for use in routes or manual testing
export async function runPDFExtractionTest() {
  const result = await testUnifiedPDFExtractor();
  return result;
}