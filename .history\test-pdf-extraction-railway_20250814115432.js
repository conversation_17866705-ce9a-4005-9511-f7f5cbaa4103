// Test PDF extraction in production-like environment
import { unifiedPDFExtractor } from './server/services/core/pdfExtractor.ts';
import fs from 'fs';
import path from 'path';

// Set production environment
process.env.NODE_ENV = 'production';

async function testPDFExtraction() {
  console.log('🧪 Testing PDF extraction in production mode...');
  
  try {
    // Look for a test PDF file in uploads directory
    const uploadsDir = './uploads';
    const files = fs.readdirSync(uploadsDir);
    
    // Find the first file (assuming it's a PDF)
    const testFile = files[0];
    if (!testFile) {
      console.log('❌ No test files found in uploads directory');
      return;
    }
    
    const filePath = path.join(uploadsDir, testFile);
    const fileBuffer = fs.readFileSync(filePath);
    
    console.log(`📄 Testing with file: ${testFile}`);
    console.log(`📊 File size: ${fileBuffer.length} bytes`);
    
    // Test extraction
    const result = await unifiedPDFExtractor.extractText(fileBuffer);
    
    console.log('✅ Extraction Results:');
    console.log(`   Success: ${result.success}`);
    console.log(`   Method: ${result.extractionMethod}`);
    console.log(`   Processing Time: ${result.processingTime}ms`);
