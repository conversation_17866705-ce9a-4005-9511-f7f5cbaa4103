/**
 * Email Service using Resend
 * Handles transactional email delivery for the notification system
 */

import { Resend } from 'resend';
import { storage } from '../storage';

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Email configuration - Using verified bidaible.com domain
const EMAIL_CONFIG = {
  FROM_EMAIL: '<EMAIL>', // Now verified domain
  FROM_NAME: 'Bidaible Notifications',
  REPLY_TO: '<EMAIL>', // Using verified bidaible.com domain
};

export class EmailService {
  
  /**
   * Send notification email using Resend
   */
  async sendNotificationEmail(
    recipientEmail: string,
    recipientName: string,
    subject: string,
    htmlContent: string,
    textContent?: string
  ) {
    try {
      console.log(`📧 Sending email to ${recipientEmail}: ${subject}`);
      
      const emailData = {
        from: `${EMAIL_CONFIG.FROM_NAME} <${EMAIL_CONFIG.FROM_EMAIL}>`,
        to: [recipientEmail],
        subject,
        html: htmlContent,
        text: textContent || this.htmlToText(htmlContent),
        reply_to: EMAIL_CONFIG.REPLY_TO,
      };
      
      const result = await resend.emails.send(emailData);
      
      if (result.error) {
        console.error(`❌ Email sending failed - Resend error:`, result.error);
        return { success: false, error: result.error };
      }
      
      console.log(`✅ Email sent successfully:`, result.data);
      return { success: true, messageId: result.data?.id, result };
      
    } catch (error: any) {
      console.error(`❌ Email sending failed - Exception:`, error);
      return { success: false, error: error?.message || String(error) };
    }
  }

  /**
   * Convert HTML to plain text (basic implementation)
   */
  private htmlToText(html: string): string {
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim();
  }

  /**
   * Generate HTML email template
   */
  generateEmailTemplate(
    title: string,
    message: string,
    actionButton?: { text: string; url: string },
    data?: any
  ): string {
    const buttonHtml = actionButton ? `
      <div style="text-align: center; margin: 30px 0;">
        <a href="${actionButton.url}" 
           style="background-color: #f75100; color: white; padding: 12px 24px; 
                  text-decoration: none; border-radius: 6px; font-weight: 600;
                  display: inline-block;">
          ${actionButton.text}
        </a>
      </div>
    ` : '';

    const dataHtml = data ? `
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #495057;">Details:</h3>
        ${Object.entries(data).map(([key, value]) => 
          `<p style="margin: 8px 0;"><strong>${key}:</strong> ${value}</p>`
        ).join('')}
      </div>
    ` : '';

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          
          <!-- Header -->
          <div style="background-color: #f75100; color: white; padding: 30px 40px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px; font-weight: 700;">Bidaible</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 16px;">Construction Procurement Platform</p>
          </div>
          
          <!-- Content -->
          <div style="padding: 40px;">
            <h2 style="color: #333; margin-top: 0; font-size: 24px;">${title}</h2>
            <p style="color: #666; line-height: 1.6; font-size: 16px; margin: 20px 0;">${message}</p>
            
            ${dataHtml}
            ${buttonHtml}
            
            <hr style="border: none; height: 1px; background-color: #e9ecef; margin: 30px 0;">
            
            <p style="color: #6c757d; font-size: 14px; margin: 0;">
              Best regards,<br>
              The Bidaible Team
            </p>
          </div>
          
          <!-- Footer -->
          <div style="background-color: #f8f9fa; padding: 20px 40px; text-align: center; border-top: 1px solid #e9ecef;">
            <p style="color: #6c757d; font-size: 12px; margin: 0;">
              This is an automated notification from Bidaible. Please do not reply to this email.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Pre-configured email templates for different notification types
   */
  
  async sendRfqUploadedEmail(recipientEmail: string, recipientName: string, rfqData: any) {
    const subject = `RFQ "${rfqData.projectName}" Successfully Uploaded`;
    const html = this.generateEmailTemplate(
      'RFQ Upload Confirmation',
      `Your RFQ for "${rfqData.projectName}" has been successfully uploaded to the Bidaible platform and is ready for contractor distribution.`,
      {
        text: 'View RFQ Dashboard',
        url: `${process.env.BASE_URL || 'http://localhost:5000'}/dashboard`
      },
      {
        'Project Name': rfqData.projectName,
        'Location': rfqData.projectLocation,
        'Due Date': new Date(rfqData.dueDate).toLocaleDateString(),
        'Trade Category': rfqData.tradeCategory
      }
    );
    
    return this.sendNotificationEmail(recipientEmail, recipientName, subject, html);
  }

  async sendBidAcceptedEmail(recipientEmail: string, recipientName: string, bidData: any) {
    const subject = `Congratulations! Your Bid for "${bidData.projectName}" Was Accepted`;
    const html = this.generateEmailTemplate(
      'Bid Accepted - Congratulations!',
      `Great news! Your bid of ${bidData.amount} for "${bidData.projectName}" has been accepted. Please check your dashboard for next steps.`,
      {
        text: 'View Bid Details',
        url: `${process.env.BASE_URL || 'http://localhost:5000'}/bids`
      },
      {
        'Project Name': bidData.projectName,
        'Bid Amount': bidData.amount,
        'Status': 'Accepted',
        'Next Steps': 'Check your dashboard for project details and contact information'
      }
    );
    
    return this.sendNotificationEmail(recipientEmail, recipientName, subject, html);
  }

  async sendBidSubmittedEmail(recipientEmail: string, recipientName: string, bidData: any) {
    const subject = `New Bid Received for "${bidData.projectName}"`;
    const html = this.generateEmailTemplate(
      'New Bid Submitted',
      `${bidData.contractorName} has submitted a bid for your project "${bidData.projectName}". Review the bid details in your dashboard.`,
      {
        text: 'Review Bid',
        url: `${process.env.BASE_URL || 'http://localhost:5000'}/dashboard`
      },
      {
        'Project Name': bidData.projectName,
        'Contractor': bidData.contractorName,
        'Submission Time': new Date().toLocaleString(),
        'Action Required': 'Review and respond to the bid'
      }
    );
    
    return this.sendNotificationEmail(recipientEmail, recipientName, subject, html);
  }

  /**
   * Test email function
   */
  async sendTestEmail(recipientEmail: string, recipientName: string, customMessage?: string) {
    const subject = 'Hello from Bidaible!';
    const message = customMessage || 'This is a test email to confirm that the Bidaible notification system is working correctly with Resend email delivery.';
    
    const html = this.generateEmailTemplate(
      'Hello from Bidaible!',
      message,
      {
        text: 'Visit Dashboard',
        url: `${process.env.BASE_URL || 'http://localhost:5000'}/dashboard`
      },
      {
        'Message Type': 'Test Email',
        'Service': 'Resend Email Delivery',
        'Timestamp': new Date().toLocaleString()
      }
    );
    
    return this.sendNotificationEmail(recipientEmail, recipientName, subject, html);
  }
}

export const emailService = new EmailService();