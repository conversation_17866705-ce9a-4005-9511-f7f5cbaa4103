// Test contractor creation and update functionality
import { storage } from './server/storage.ts';

async function testCreateAndUpdate() {
  try {
    console.log('🧪 Testing contractor creation and update...');
    
    // First, create a test contractor
    console.log('📝 Creating test contractor...');
    const newContractor = await storage.createContractor({
      companyName: 'Test Company Inc.',
      contactName: '<PERSON>',
      email: '<EMAIL>',
      phone: '555-0123',
      address: '123 Test St',
      city: 'Test City',
      state: 'TS',
      zipCode: '12345',
      licenseNumber: 'LIC123456',
      insuranceInfo: 'Test Insurance Co.',
      specialties: ['General Construction', 'Electrical'],
      yearsInBusiness: 10,
      website: 'https://testcompany.com'
    });
    
    if (newContractor) {
      console.log('✅ Contractor created successfully:', newContractor.id, newContractor.companyName);
      
      // Now test the update
      console.log('🔄 Testing update functionality...');
      const updatedName = 'Updated Test Company - ' + Date.now();
      const result = await storage.updateContractor(newContractor.id, {
        companyName: updatedName,
        contactName: '<PERSON> Updated'
      });
      
      if (result) {
        console.log('✅ Update successful!');
        console.log('   - New company name:', result.companyName);
        console.log('   - New contact name:', result.contactName);
        console.log('   - ID remains:', result.id);
      } else {
        console.log('❌ Update returned null/undefined');
      }
      
      // Verify the update by fetching the contractor again
      console.log('🔍 Verifying update by fetching contractor...');
      const fetchedContractor = await storage.getContractorById(newContractor.id);
      if (fetchedContractor) {
        console.log('✅ Verification successful:');
        console.log('   - Fetched company name:', fetchedContractor.companyName);
        console.log('   - Fetched contact name:', fetchedContractor.contactName);
      } else {
        console.log('❌ Could not fetch updated contractor');
      }
      
    } else {
      console.log('❌ Failed to create contractor');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
  process.exit(0);
}

testCreateAndUpdate();
