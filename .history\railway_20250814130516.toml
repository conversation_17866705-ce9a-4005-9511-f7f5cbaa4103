[build]
builder = "nixpacks"
buildCommand = "npm run build"

[deploy]
startCommand = "npm start"
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

# Add debug command for troubleshooting
[deploy.healthcheckCommand]
command = "npm run debug"

[environments.production]
variables = { NODE_ENV = "production" }

[environments.staging]
variables = { NODE_ENV = "staging" }

# Debug environment for troubleshooting
[environments.debug]
variables = { NODE_ENV = "production" }
startCommand = "npm run debug && npm start"
