# Bidaible Project Brief

## Project Overview
Bidaible is a sophisticated AI-powered construction bidding platform built with enterprise-grade TypeScript architecture. The system delivers "wow factor" bid analysis through advanced competitive intelligence, risk assessment, and predictive analytics.

## Core Purpose
- **Primary Goal**: Streamline construction bidding processes through AI automation
- **Target Users**: General Contractors (GCs) and Subcontractors in construction industry
- **Key Value**: Transform manual bidding workflows into intelligent, automated processes

## Key Features
1. **AI-Powered Document Processing**
   - Multi-file RFQ upload with automatic data extraction
   - PDF, TXT, CSV support with 95%+ success rate
   - Real-time progress tracking via Server-Sent Events

2. **Intelligent Bid Analysis**
   - Executive summary generation with competitive intelligence
   - Automated bid ranking and scoring (0-100 scale)
   - Risk assessment and market analysis
   - Sub-3-second analysis powered by multi-provider AI

3. **Multi-Tenant SaaS Architecture**
   - Organization-based data isolation
   - Role-based access control (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Editor, Viewer)
   - Comprehensive audit logging and security

4. **Dual Authentication System**
   - Clerk Authentication for web interface
   - JWT API keys for programmatic access
   - Scoped permissions and rate limiting

## Technical Foundation
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript + PostgreSQL
- **AI Processing**: Multi-provider (Groq, OpenAI, Gemini) with intelligent fallback
- **Authentication**: Clerk Auth + JWT API keys
- **Database**: PostgreSQL with Drizzle ORM + 30+ strategic indexes
- **File Storage**: Replit Object Storage
- **Deployment**: Replit with production-ready architecture

## Success Metrics
- **Processing Speed**: Sub-3-second AI analysis generation
- **Extraction Accuracy**: 95%+ success rate for document processing
- **System Reliability**: 100% uptime through multi-provider AI fallback
- **User Experience**: Streamlined workflows reducing manual bid analysis time

## Current Status
- **Phase**: Production-ready with active development
- **Documentation**: Recently consolidated and deduplicated (August 2025)
- **Architecture**: Mature multi-tenant SaaS platform
- **AI Integration**: Advanced multi-provider system with fallback strategies

## Project Constraints
- **File Size Limits**: 50MB per document, up to 8 files per RFQ
- **AI Processing**: 150K character limit with intelligent truncation
- **Multi-tenancy**: Complete data isolation between organizations
- **Security**: Enterprise-grade with comprehensive audit trails

This project represents a mature, production-ready construction technology platform with sophisticated AI capabilities and enterprise-grade architecture.
