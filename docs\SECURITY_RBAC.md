# Bidaible Security & Role-Based Access Control

## Overview

Bidaible implements enterprise-grade security with comprehensive role-based access control (RBAC) and multi-tenant organizational support. This document provides detailed information about security features, user roles, and compliance capabilities.

## Multi-Tenant Architecture

### Organizations
- **Isolated Data Access**: Each organization has completely separate data access with no cross-organization visibility
- **Automatic Scoping**: All database queries are automatically scoped to the user's organization
- **Resource Isolation**: RFQs, bids, contractors, and documents are organization-specific
- **Administrative Separation**: Organization admins can only manage users within their organization

### Default Organization
- **Bidaible Platform**: Default organization for initial users
- **Migration Path**: Existing users are automatically assigned to the default organization
- **Scalability**: New organizations can be created as needed for enterprise customers

## Role-Based Access Control (RBAC)

### Role Hierarchy

#### 1. SuperUser (Highest Privileges)
**Capabilities:**
- System-wide access across all organizations
- Complete user management (create, modify, delete any user)
- Role assignment including other SuperUsers
- Full audit log access for security monitoring
- Override organization restrictions for system administration
- Access to all API endpoints without restriction

**Use Cases:**
- Platform administrators
- System maintenance and troubleshooting
- Cross-organization data analysis
- Security incident response

#### 2. Admin (Organization-Level Management)
**Capabilities:**
- Complete control within assigned organization
- User administration within organization (cannot assign SuperUser role)
- Role assignment (Admin, Editor, Viewer within organization)
- Organization-specific audit logs and security monitoring
- Full CRUD access to organization data
- Access to administrative API endpoints

**Restrictions:**
- Cannot access other organizations' data
- Cannot assign SuperUser roles
- Cannot modify system-wide settings

**Use Cases:**
- Organization administrators
- Team leads with user management responsibilities
- Department heads requiring full organizational access

#### 3. Editor (Content Management)
**Capabilities:**
- Create, read, update operations within organization
- Full RFQ creation and management
- Bid submission and management within organization scope
- Contractor profile management
- Document upload and processing
- Standard business operations

**Restrictions:**
- Cannot modify user roles or access administrative functions
- Cannot access audit logs or security monitoring
- Limited to organization-scoped data access

**Use Cases:**
- Project managers
- Business users creating and managing RFQs
- Contractors submitting bids

#### 4. Viewer (Read-Only Access)
**Capabilities:**
- View all data within assigned organization
- Generate reports and export data
- Access dashboards and analytics
- Download documents and view project information

**Restrictions:**
- Cannot create, update, or delete any records
- No modification capabilities
- Limited to read-only API endpoints

**Use Cases:**
- Executives requiring visibility
- Auditors and compliance personnel
- External stakeholders needing read-only access

## Security Features

### Audit Logging

#### Role Change Audit Trail
**Tracked Information:**
- User whose role was changed
- Administrator who made the change
- Previous and new roles
- Timestamp of change
- IP address and user agent
- Reason for role change
- Organization context

**Compliance Benefits:**
- Complete accountability for privilege changes
- Regulatory compliance (SOX, GDPR, HIPAA)
- Security incident investigation
- Access review requirements

#### Access Attempt Monitoring
**Tracked Information:**
- User attempting access
- Requested resource/endpoint
- Success or failure status
- Reason for failure (insufficient permissions, etc.)
- IP address and timestamp
- Organization context

**Security Benefits:**
- Detect unauthorized access attempts
- Monitor suspicious activity patterns
- Identify potential security threats
- Support incident response

### Data Protection

#### Organization Isolation
- **Database Level**: All queries automatically include organization filtering
- **API Level**: Middleware ensures users can only access their organization's data
- **Session Level**: User sessions are tied to specific organizations
- **File Storage**: Documents are organization-scoped in Object Storage

#### Permission Enforcement
- **Route Protection**: Every API endpoint validates user permissions
- **Middleware Stack**: Layered security with authentication, authorization, and audit logging
- **Automatic Logging**: Failed access attempts are automatically logged
- **Error Handling**: Secure error messages that don't expose system information

## User Management

### Administrative Interface

#### User Management Tab (Settings Page)
**SuperUser View:**
- All users across all organizations
- Complete role assignment capabilities
- Cross-organization user transfers
- System-wide user statistics

**Admin View:**
- Users within their organization only
- Role assignment (Admin, Editor, Viewer)
- Organization-specific user management
- Cannot assign SuperUser roles

#### Role Assignment Process
1. Navigate to Settings → User Management
2. Click edit button for target user
3. Select new role from dropdown
4. Provide reason for change (required)
5. Confirm role assignment
6. System logs change with audit trail

#### Security Validations
- **Permission Check**: Verify administrator has rights to assign target role
- **Organization Scope**: Ensure users can only manage within their organization
- **Role Restrictions**: Prevent privilege escalation beyond administrator's level
- **Audit Requirements**: Mandatory reason for all role changes

### Audit Log Monitoring

#### Role Change Logs
- **Real-time Updates**: Changes appear immediately in audit logs
- **Detailed Information**: Complete context for each role modification
- **Searchable History**: Filter by user, date, role, or administrator
- **Export Capabilities**: Download audit trails for compliance reporting

#### Access Attempt Logs
- **Failed Login Attempts**: Track authentication failures
- **Unauthorized Access**: Log attempts to access restricted resources
- **Permission Denials**: Monitor when users try to exceed their privileges
- **Suspicious Patterns**: Identify potential security threats

## API Security

### Authentication Methods

#### Session-Based (Web Application)
- **Clerk Authentication**: Secure, managed authentication with JWTs
- **Role-Based Sessions**: User roles managed within Clerk and synced to the application
- **Automatic Expiration**: Configurable session timeouts
- **Organization Binding**: Sessions tied to specific organizations

#### API Key Authentication (Programmatic Access)
- **JWT-Based Tokens**: Cryptographically signed access tokens
- **Scoped Permissions**: read-only, upload-only, full-access levels
- **Rate Limiting**: Configurable per-key request limits
- **Usage Tracking**: Comprehensive analytics and monitoring

### Endpoint Protection

#### Role-Based Middleware
```typescript
// Example: Admin-only endpoint protection
router.get('/api/admin/users', 
  authenticateUser,
  requireRole(['Admin', 'SuperUser']),
  auditAccess,
  getUsersController
);
```

#### Automatic Audit Logging
- **Request Logging**: All administrative actions are logged
- **Response Tracking**: Success/failure status recorded
- **IP Address Capture**: Security context for all requests
- **Organization Filtering**: Automatic data scoping enforcement

## Compliance Features

### Regulatory Compliance
- **SOX Compliance**: Complete audit trails for financial data access
- **GDPR Compliance**: Data protection and user consent management
- **HIPAA Compatibility**: Secure handling of sensitive project information
- **SOC 2 Type II**: Security controls and monitoring capabilities

### Data Retention
- **Audit Log Retention**: Configurable retention periods for compliance
- **Secure Deletion**: Proper data sanitization when required
- **Backup Security**: Encrypted backups with role-based access
- **Export Capabilities**: Compliance reporting and data portability

### Access Reviews
- **Periodic Reviews**: Systematic review of user access rights
- **Role Certification**: Regular validation of role assignments
- **Automated Reporting**: Compliance reports for auditors
- **Exception Handling**: Process for temporary privilege elevation

## Implementation Details

### Database Schema

#### Organizations Table
```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY,
  name VARCHAR NOT NULL,
  slug VARCHAR UNIQUE NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Users Table (Enhanced)
```sql
ALTER TABLE users ADD COLUMN organization_id UUID REFERENCES organizations(id);
ALTER TABLE users ADD COLUMN role user_role_enum NOT NULL DEFAULT 'Viewer';
```

#### Audit Logging Tables
```sql
-- Role change audit trail
CREATE TABLE role_audit_log (
  id UUID PRIMARY KEY,
  target_user_id VARCHAR NOT NULL,
  changed_by_user_id VARCHAR NOT NULL,
  organization_id UUID NOT NULL,
  previous_role user_role_enum,
  new_role user_role_enum NOT NULL,
  reason TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Access attempt monitoring
CREATE TABLE access_audit_log (
  id UUID PRIMARY KEY,
  user_id VARCHAR,
  action VARCHAR NOT NULL,
  resource VARCHAR NOT NULL,
  organization_id UUID,
  success BOOLEAN NOT NULL,
  reason TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Middleware Implementation

#### Role Validation
```typescript
export const requireRole = (allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    if (!user || !allowedRoles.includes(user.role)) {
      // Log unauthorized access attempt
      auditLogger.logAccessAttempt({
        userId: user?.id,
        action: req.method + ' ' + req.path,
        resource: req.path,
        success: false,
        reason: `Insufficient permissions. Required: ${allowedRoles.join('|')}, User has: ${user?.role}`,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(403).json({ 
        message: 'Access denied: insufficient permissions' 
      });
    }
    next();
  };
};
```

#### Organization Scoping
```typescript
export const scopeToOrganization = (req: Request, res: Response, next: NextFunction) => {
  if (req.user?.role !== 'SuperUser') {
    // Automatically add organization filter to all queries
    req.organizationId = req.user?.organizationId;
  }
  next();
};
```

## Security Best Practices

### For Administrators
1. **Regular Access Reviews**: Conduct quarterly reviews of user permissions
2. **Principle of Least Privilege**: Assign minimum necessary permissions
3. **Monitor Audit Logs**: Review security logs for suspicious activity
4. **Strong Authentication**: Enforce strong password policies
5. **Session Management**: Configure appropriate session timeouts

### For Developers
1. **Input Validation**: Validate all user inputs and API parameters
2. **Error Handling**: Implement secure error messages that don't expose system details
3. **SQL Injection Prevention**: Use parameterized queries and ORM protections
4. **XSS Protection**: Sanitize all user-generated content
5. **CSRF Protection**: Implement proper CSRF tokens for state-changing operations

### For Users
1. **Password Security**: Use strong, unique passwords for accounts
2. **Session Security**: Log out from shared or public computers
3. **Suspicious Activity**: Report unusual account activity immediately
4. **Access Sharing**: Never share login credentials with other users
5. **Software Updates**: Keep browsers and software updated

## Incident Response

### Security Incident Procedure
1. **Detection**: Monitor audit logs for suspicious patterns
2. **Containment**: Immediately disable affected user accounts
3. **Investigation**: Review audit trails to determine scope
4. **Remediation**: Apply necessary security patches or policy changes
5. **Recovery**: Restore normal operations with enhanced monitoring
6. **Lessons Learned**: Update security procedures based on incident findings

### Emergency Contacts
- **Security Team**: <EMAIL>
- **System Administration**: <EMAIL>
- **24/7 Incident Response**: 1-800-SECURITY

---

*This document is maintained by the Bidaible Security Team and is updated quarterly or as needed for significant security changes.*

**Last Updated:** July 18, 2025  
**Version:** 1.0  
**Next Review:** October 18, 2025
