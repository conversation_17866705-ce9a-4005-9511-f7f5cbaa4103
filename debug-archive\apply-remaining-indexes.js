#!/usr/bin/env node

/**
 * Apply remaining database indexes to fix sequential scans
 */

import { config } from 'dotenv';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { readFileSync } from 'fs';
import ws from 'ws';

// Load environment variables
config();

// Configure Neon WebSocket
neonConfig.webSocketConstructor = ws;

// Database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

const pool = new Pool({ connectionString });

async function applyRemainingIndexes() {
  console.log('🔧 Applying Remaining Database Indexes');
  console.log('═'.repeat(50));
  
  try {
    // Read the SQL file
    const sqlContent = readFileSync('fix-remaining-indexes.sql', 'utf8');
    
    // Split into individual statements (improved parsing)
    const statements = sqlContent
      .split('\n')
      .filter(line => line.trim().startsWith('CREATE'))
      .map(line => {
        // Find the complete CREATE INDEX statement
        const startIndex = sqlContent.indexOf(line);
        const endIndex = sqlContent.indexOf(';', startIndex);
        return sqlContent.substring(startIndex, endIndex).trim();
      })
      .filter(stmt => stmt.length > 10);
    
    const client = await pool.connect();
    
    let successCount = 0;
    let skipCount = 0;
    let failCount = 0;
    
    console.log(`📄 Processing ${statements.length} index statements...`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        // Extract index name for better logging
        const indexMatch = statement.match(/CREATE\s+(?:UNIQUE\s+)?INDEX\s+(?:CONCURRENTLY\s+)?(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
        const indexName = indexMatch ? indexMatch[1] : `statement-${i + 1}`;
        
        console.log(`   🔨 Creating: ${indexName}...`);
        
        const startTime = Date.now();
        await client.query(statement + ';');
        const duration = Date.now() - startTime;
        
        console.log(`   ✅ ${indexName} created successfully (${duration}ms)`);
        successCount++;
        
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`   ⚠️  Index already exists - skipping`);
          skipCount++;
        } else {
          console.error(`   ❌ Failed to create index: ${error.message}`);
          failCount++;
        }
      }
    }
    
    client.release();
    
    console.log('\n📊 Index Creation Summary:');
    console.log(`   ✅ Successfully created: ${successCount} indexes`);
    console.log(`   ⚠️  Already existed: ${skipCount} indexes`);
    console.log(`   ❌ Failed: ${failCount} indexes`);
    console.log(`   📄 Total processed: ${statements.length} statements`);
    
    // Test the performance improvements
    console.log('\n🧪 Testing Performance Improvements...');
    await testPerformanceImprovements(client);
    
  } catch (error) {
    console.error('❌ Index application failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
  
  console.log('\n🎉 Remaining index optimizations completed!');
  console.log('   All sequential scans should now be eliminated.');
}

/**
 * Test the performance improvements after applying indexes
 */
async function testPerformanceImprovements() {
  console.log('   Running performance verification...');
  
  try {
    const client = await pool.connect();
    
    // Test 1: Unread notifications (should now use index)
    console.log('   📊 Testing unread notifications query...');
    const result1 = await client.query(`
      EXPLAIN (ANALYZE, FORMAT JSON) 
      SELECT COUNT(*) as unread_count
      FROM notifications 
      WHERE read_at IS NULL 
        AND created_at >= NOW() - INTERVAL '30 days'
    `);
    
    const plan1 = result1.rows[0]['QUERY PLAN'][0];
    const hasSeqScan1 = JSON.stringify(plan1).includes('Seq Scan');
    const executionTime1 = plan1['Execution Time'];
    
    console.log(`   ${hasSeqScan1 ? '⚠️' : '✅'} Notifications: ${executionTime1.toFixed(2)}ms ${hasSeqScan1 ? '(Seq Scan)' : '(Index)'}`);
    
    // Test 2: Active RFQs count (should now use index)
    console.log('   📊 Testing active RFQs query...');
    const result2 = await client.query(`
      EXPLAIN (ANALYZE, FORMAT JSON)
      SELECT COUNT(*) as active_count
      FROM rfqs 
      WHERE status = 'Active'
    `);
    
    const plan2 = result2.rows[0]['QUERY PLAN'][0];
    const hasSeqScan2 = JSON.stringify(plan2).includes('Seq Scan');
    const executionTime2 = plan2['Execution Time'];
    
    console.log(`   ${hasSeqScan2 ? '⚠️' : '✅'} Active RFQs: ${executionTime2.toFixed(2)}ms ${hasSeqScan2 ? '(Seq Scan)' : '(Index)'}`);
    
    client.release();
    
    // Summary
    const totalSeqScans = (hasSeqScan1 ? 1 : 0) + (hasSeqScan2 ? 1 : 0);
    console.log(`   📈 Result: ${2 - totalSeqScans}/2 queries now use indexes`);
    
    if (totalSeqScans === 0) {
      console.log('   🎯 Perfect! All sequential scans eliminated.');
    } else {
      console.log(`   ⚠️  ${totalSeqScans} queries still use sequential scans.`);
    }
    
  } catch (error) {
    console.log(`   ❌ Performance test failed: ${error.message}`);
  }
}

applyRemainingIndexes().catch(console.error);
