import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";  
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, Area, AreaChart } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, Clock, Users, FileText, Calendar, Target, Bug, Lightbulb, MessageSquare, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { useUserRole } from "@/hooks/useUserRole";
import { ContractorPerformanceAnalytics } from "@/components/ContractorPerformanceAnalytics";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

// Mock analytics data
const monthlyRfqData = [
  { month: 'Jan', rfqs: 45, bids: 234, awarded: 23 },
  { month: 'Feb', rfqs: 52, bids: 287, awarded: 31 },
  { month: 'Mar', rfqs: 38, bids: 198, awarded: 19 },
  { month: 'Apr', rfqs: 61, bids: 342, awarded: 28 },
  { month: 'May', rfqs: 58, bids: 318, awarded: 35 },
  { month: 'Jun', rfqs: 67, bids: 389, awarded: 42 },
  { month: 'Jul', rfqs: 74, bids: 421, awarded: 38 },
];

const tradeDistribution = [
  { name: 'Electrical', value: 28, color: '#f39c12' },
  { name: 'Plumbing', value: 22, color: '#3498db' },
  { name: 'HVAC', value: 18, color: '#e74c3c' },
  { name: 'Concrete', value: 15, color: '#27ae60' },
  { name: 'General', value: 10, color: '#9b59b6' },
  { name: 'Site Work', value: 7, color: '#34495e' },
];

const responseTimeData = [
  { range: '0-6h', count: 42 },
  { range: '6-24h', count: 128 },
  { range: '1-3d', count: 89 },
  { range: '3-7d', count: 34 },
  { range: '7d+', count: 12 },
];

const topContractors = [
  { name: 'Elite Electrical Solutions', projects: 47, rating: 4.8, avgBid: '$125,000' },
  { name: 'Precision Plumbing & Mechanical', projects: 35, rating: 4.6, avgBid: '$89,000' },
  { name: 'Northwest Concrete & Construction', projects: 52, rating: 4.9, avgBid: '$245,000' },
  { name: 'Metro HVAC Systems', projects: 28, rating: 4.7, avgBid: '$156,000' },
  { name: 'Superior Site Works', projects: 31, rating: 4.5, avgBid: '$78,000' },
];

const projectValueData = [
  { range: '$0-50K', count: 89 },
  { range: '$50-100K', count: 134 },
  { range: '$100-250K', count: 98 },
  { range: '$250-500K', count: 67 },
  { range: '$500K+', count: 45 },
];

// User Feedback Management Component
function UserFeedbackManagement() {
  const [adminNotes, setAdminNotes] = useState<{[key: string]: string}>({});
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: feedbackData, isLoading: feedbackLoading } = useQuery({
    queryKey: ['/api/user-feedback'],
  });

  const { data: feedbackStats, isLoading: statsLoading } = useQuery({
    queryKey: ['/api/user-feedback/stats'],
  });

  const updateFeedbackMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: any }) => {
      const response = await fetch(`/api/user-feedback/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });
      if (!response.ok) throw new Error('Failed to update feedback');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user-feedback'] });
      queryClient.invalidateQueries({ queryKey: ['/api/user-feedback/stats'] });
      toast({
        title: "Feedback updated successfully",
        description: "The feedback status has been updated.",
      });
    },
    onError: () => {
      toast({
        title: "Error updating feedback",
        description: "Please try again later.",
        variant: "destructive",
      });
    },
  });

  const handleStatusUpdate = (id: string, status: string) => {
    const notes = adminNotes[id];
    updateFeedbackMutation.mutate({
      id,
      updates: {
        status,
        ...(notes && { adminNotes: notes }),
      },
    });
  };

  if (feedbackLoading || statsLoading) {
    return <div className="text-center py-8">Loading feedback data...</div>;
  }

  const feedback = Array.isArray(feedbackData) ? feedbackData : [];
  const stats = (feedbackStats as any) || { total: 0, bugs: 0, suggestions: 0, open: 0, resolved: 0 };

  return (
    <div className="space-y-6">
      {/* Feedback Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Feedback</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Bug className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">Bug Reports</p>
                <p className="text-2xl font-bold">{stats.bugs}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Lightbulb className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">Suggestions</p>
                <p className="text-2xl font-bold">{stats.suggestions}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm text-muted-foreground">Open</p>
                <p className="text-2xl font-bold">{stats.open}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Resolved</p>
                <p className="text-2xl font-bold">{stats.resolved}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Feedback List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>User Feedback</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {feedback.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">No feedback submitted yet.</p>
            ) : (
              feedback.map((item: any) => (
                <div key={item.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      {item.type === 'bug' ? (
                        <Bug className="h-5 w-5 text-red-600" />
                      ) : (
                        <Lightbulb className="h-5 w-5 text-yellow-600" />
                      )}
                      <div>
                        <p className="font-medium">{item.user?.name || 'Unknown User'}</p>
                        <p className="text-sm text-muted-foreground">{item.user?.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={item.status === 'resolved' ? 'default' : 'secondary'}>
                        {item.status}
                      </Badge>
                      <Badge variant="outline">
                        {item.type}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-900 rounded p-3">
                    <p className="text-sm">{item.message}</p>
                  </div>

                  {item.adminNotes && (
                    <div className="bg-blue-50 dark:bg-blue-950 rounded p-3">
                      <p className="text-sm text-blue-800 dark:text-blue-300">
                        <strong>Admin Notes:</strong> {item.adminNotes}
                      </p>
                    </div>
                  )}

                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <span>Submitted: {new Date(item.createdAt).toLocaleDateString()}</span>
                    {item.resolvedAt && (
                      <span>• Resolved: {new Date(item.resolvedAt).toLocaleDateString()}</span>
                    )}
                  </div>

                  {item.status !== 'resolved' && (
                    <div className="flex items-end space-x-2 pt-2">
                      <div className="flex-1">
                        <Textarea
                          placeholder="Add admin notes..."
                          value={adminNotes[item.id] || ''}
                          onChange={(e) => setAdminNotes({ ...adminNotes, [item.id]: e.target.value })}
                          className="resize-none"
                          rows={2}
                        />
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(item.id, 'in_progress')}
                          disabled={updateFeedbackMutation.isPending}
                        >
                          In Progress
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleStatusUpdate(item.id, 'resolved')}
                          disabled={updateFeedbackMutation.isPending}
                        >
                          Mark Resolved
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function Analytics() {
  const { role } = useUserRole();

  // Show contractor-specific performance analytics for contractors
  if (role === 'contractor') {
    return <ContractorPerformanceAnalytics />;
  }

  // Show general analytics dashboard for GCs and admins
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">Comprehensive insights into your RFQ performance and contractor metrics</p>
        </div>
        <Badge variant="outline" className="bg-green-50 dark:bg-green-950 text-green-700 dark:text-green-400">
          <TrendingUp className="h-3 w-3 mr-1" />
          Up 12% from last month
        </Badge>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total RFQs</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">395</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+18.2%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bids</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,189</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+24.1%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18.4h</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">-8.5%</span> improvement
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87.3%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+5.2%</span> from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly RFQ Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly RFQ Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={monthlyRfqData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="rfqs" stackId="1" stroke="#f39c12" fill="#f39c12" fillOpacity={0.6} />
                <Area type="monotone" dataKey="bids" stackId="1" stroke="#3498db" fill="#3498db" fillOpacity={0.6} />
                <Area type="monotone" dataKey="awarded" stackId="1" stroke="#27ae60" fill="#27ae60" fillOpacity={0.6} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Trade Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Bid Distribution by Trade</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={tradeDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {tradeDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Response Time Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={responseTimeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="range" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#f39c12" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Project Value Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Project Value Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={projectValueData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="range" type="category" width={80} />
                <Tooltip />
                <Bar dataKey="count" fill="#3498db" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Contractors Table */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Contractors</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Contractor</th>
                  <th className="text-left p-2">Projects Completed</th>
                  <th className="text-left p-2">Average Rating</th>
                  <th className="text-left p-2">Average Bid Value</th>
                </tr>
              </thead>
              <tbody>
                {topContractors.map((contractor, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-2 font-medium">{contractor.name}</td>
                    <td className="p-2">{contractor.projects}</td>
                    <td className="p-2">
                      <div className="flex items-center">
                        <span className="mr-1">{contractor.rating}</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <span
                              key={i}
                              className={`text-xs ${
                                i < Math.floor(contractor.rating)
                                  ? 'text-yellow-400'
                                  : 'text-gray-300 dark:text-gray-600'
                              }`}
                            >
                              ★
                            </span>
                          ))}
                        </div>
                      </div>
                    </td>
                    <td className="p-2 font-mono">{contractor.avgBid}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* User Feedback Management - Org Admin Only (server-enforced) */}
      {/* Hidden client-side; access controlled on server */}
    </div>
  );
}