/**
 * Sensitive Endpoint Rate Limit Test
 * - Create RFQ
 * - Hit GET /api/rfqs/:id repeatedly until 429
 */
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const { attachDebugInterceptors, getApiKey, whoAmI } = require('./test-utils');

const BASE_URL = process.env.BASE_URL || 'http://localhost:5000';

async function createUserWithKey({ userId, classification, organizationId }) {
  await axios.post(`${BASE_URL}/api/test/user`, { id: userId, organizationId, userClassification: classification });
  const apiKey = await getApiKey(BASE_URL, userId);
  const client = axios.create({ baseURL: BASE_URL, headers: { Authorization: `Bearer ${apiKey}` }, timeout: 30000 });
  attachDebugInterceptors(client, `client:${userId}`);
  await whoAmI(BASE_URL, apiKey);
  return { userId, client };
}

async function run() {
  console.log('🧪 Sensitive Endpoint Rate Limit Test');
  const { data: orgA } = await axios.post(`${BASE_URL}/api/test/org`, { id: uuidv4(), name: 'Org A', slug: `org-a-${Date.now()}` });
  const gcA = await createUserWithKey({ userId: `gc_a_${Date.now()}`, classification: 'general_contractor', organizationId: orgA.id });

  const { data: rfq } = await axios.post(`${BASE_URL}/api/test/rfq`, { id: uuidv4(), createdBy: gcA.userId, organizationId: orgA.id, projectName: 'RL Test', projectLocation: 'SF', tradeCategory: 'general', dueDate: new Date(), status: 'Active' });

  let got429 = false;
  for (let i = 0; i < 120; i++) {
    try {
      await gcA.client.get(`/api/rfqs/${rfq.id}`);
    } catch (e) {
      if (e.response && e.response.status === 429) {
        got429 = true; console.log(`✅ 429 received at request #${i+1}`); break;
      } else { throw e; }
    }
  }
  if (!got429) throw new Error('Did not hit rate limit (adjust test or limiter thresholds)');
  console.log('\n🎉 Sensitive Endpoint Rate Limit Test PASSED');
}

if (require.main === module) {
  run().catch(err => { console.error('💥 Test failed:', err && err.response ? err.response.data || err.response.status : err); process.exit(1); });
}

module.exports = { run };
