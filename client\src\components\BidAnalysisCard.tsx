import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Building2, Calendar, Shield, DollarSign } from 'lucide-react';

interface BidLineItem {
  id: string;
  costCode: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  unitOfMeasure: string;
  category: string;
}

interface ContractorInfo {
  companyName: string;
  primaryContactName: string;
  yearsInBusiness: number;
  licenseNumber?: string;
  generalLiability?: string;
  bondingSingle?: string;
  bondingAggregate?: string;
}

interface BidAnalysisData {
  bidId: string;
  contractorId: string;
  contractorInfo: ContractorInfo;
  bidAmount: number;
  timeline: string;
  status: string;
  lineItems: BidLineItem[];
  proposalText?: string;
  notes?: string;
  includedItems: string[];
  excludedItems: string[];
}

interface BidAnalysisCardProps {
  bidData: BidAnalysisData;
  onAccept?: (bidId: string) => void;
  onReject?: (bidId: string) => void;
  isSelected?: boolean;
}

// Function to extract inclusions and exclusions from proposal text
const extractInclusionsExclusions = (proposalText?: string, notes?: string): { inclusions: string[], exclusions: string[] } => {
  const text = `${proposalText || ''} ${notes || ''}`.toLowerCase();
  const inclusions: string[] = [];
  const exclusions: string[] = [];

  // Common inclusion patterns
  const inclusionPatterns = [
    /includes?\s+([^.]+)/gi,
    /covered\s+([^.]+)/gi,
    /provided\s+([^.]+)/gi,
    /base\s+bid\s+includes\s+([^.]+)/gi,
  ];

  // Common exclusion patterns  
  const exclusionPatterns = [
    /excludes?\s+([^.]+)/gi,
    /not\s+included\s+([^.]+)/gi,
    /additional\s+cost\s+([^.]+)/gi,
    /extra\s+([^.]+)/gi,
  ];

  // Extract inclusions
  inclusionPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(text)) !== null) {
      inclusions.push(match[1].trim());
    }
  });

  // Extract exclusions
  exclusionPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(text)) !== null) {
      exclusions.push(match[1].trim());
    }
  });

  return { inclusions, exclusions };
};

// Function to format currency
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
  }).format(amount);
};

// Function to get status color
const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'accepted':
    case 'accept':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'under_review':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    default:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
  }
};

export function BidAnalysisCard({ bidData, onAccept, onReject, isSelected }: BidAnalysisCardProps) {
  const { inclusions, exclusions } = extractInclusionsExclusions(bidData.proposalText, bidData.notes);
  
  // Group line items by category
  const itemsByCategory = bidData.lineItems.reduce((acc, item) => {
    const category = item.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(item);
    return acc;
  }, {} as Record<string, BidLineItem[]>);

  // Calculate total from line items (more accurate than bidAmount which might be null)
  const calculatedTotal = bidData.lineItems.reduce((sum, item) => sum + item.totalPrice, 0);
  const displayTotal = bidData.bidAmount || calculatedTotal;

  return (
    <Card className={`w-full max-w-lg ${isSelected ? 'ring-2 ring-primary' : ''}`}>
      {/* HEADER */}
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-bold text-primary">
            {bidData.contractorInfo.companyName}
          </CardTitle>
          <Badge className={getStatusColor(bidData.status)}>
            {bidData.status.toUpperCase()}
          </Badge>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <span className="text-xl font-bold text-green-600">
              {formatCurrency(displayTotal)}
            </span>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Building2 className="h-4 w-4" />
              <span>{bidData.contractorInfo.yearsInBusiness} years</span>
            </div>
            {bidData.contractorInfo.licenseNumber && (
              <div className="flex items-center gap-1">
                <Shield className="h-4 w-4" />
                <span>Lic# {bidData.contractorInfo.licenseNumber}</span>
              </div>
            )}
            {bidData.timeline && (
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{bidData.timeline}</span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* PRICING BREAKDOWN */}
        <div>
          <h4 className="font-semibold mb-2 text-sm uppercase tracking-wide">Pricing Breakdown</h4>
          <div className="space-y-3">
            {Object.entries(itemsByCategory).map(([category, items]) => (
              <div key={category} className="border-l-2 border-primary/20 pl-3">
                <h5 className="font-medium text-sm text-primary mb-1">{category}</h5>
                <div className="space-y-1">
                  {items.map((item) => (
                    <div key={item.id} className="flex justify-between items-center text-sm">
                      <div className="flex-1">
                        <span className="font-mono text-xs text-muted-foreground mr-2">
                          {item.costCode}
                        </span>
                        <span>{item.description}</span>
                      </div>
                      <span className="font-medium min-w-0 ml-2">
                        {formatCurrency(item.totalPrice)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          <div className="border-t pt-2 mt-2">
            <div className="flex justify-between items-center font-bold">
              <span>TOTAL</span>
              <span className="text-green-600">{formatCurrency(displayTotal)}</span>
            </div>
          </div>
        </div>

        {/* INCLUDED ITEMS */}
        {inclusions.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2 text-sm uppercase tracking-wide flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Included Items
            </h4>
            <ul className="space-y-1">
              {inclusions.map((item, index) => (
                <li key={`inclusion-${index}-${item.slice(0, 10)}`} className="flex items-start gap-2 text-sm">
                  <CheckCircle className="h-3 w-3 text-green-600 mt-1 flex-shrink-0" />
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* NOT INCLUDED ITEMS */}
        {exclusions.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2 text-sm uppercase tracking-wide flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              Not Included Items
            </h4>
            <ul className="space-y-1">
              {exclusions.map((item, index) => (
                <li key={`exclusion-${index}-${item.slice(0, 10)}`} className="flex items-start gap-2 text-sm">
                  <XCircle className="h-3 w-3 text-red-600 mt-1 flex-shrink-0" />
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* CONTRACTOR INFO */}
        <div>
          <h4 className="font-semibold mb-2 text-sm uppercase tracking-wide">Contractor Information</h4>
          <div className="grid grid-cols-1 gap-2 text-sm">
            <div>
              <span className="font-medium">Contact:</span>{' '}
              <span>{bidData.contractorInfo.primaryContactName}</span>
            </div>
            <div>
              <span className="font-medium">Experience:</span>{' '}
              <span>{bidData.contractorInfo.yearsInBusiness} years in business</span>
            </div>
            {bidData.contractorInfo.generalLiability && (
              <div>
                <span className="font-medium">Insurance:</span>{' '}
                <span>{bidData.contractorInfo.generalLiability}</span>
              </div>
            )}
            {bidData.contractorInfo.bondingSingle && (
              <div>
                <span className="font-medium">Bonding:</span>{' '}
                <span>{bidData.contractorInfo.bondingSingle}</span>
              </div>
            )}
            {bidData.timeline && (
              <div>
                <span className="font-medium">Completion Time:</span>{' '}
                <span>{bidData.timeline}</span>
              </div>
            )}
          </div>
        </div>

        {/* ACTION BUTTONS */}
        {(onAccept || onReject) && (
          <div className="flex gap-2 pt-2">
            {onAccept && (
              <Button 
                onClick={() => onAccept(bidData.bidId)}
                className="flex-1"
                variant={bidData.status === 'accepted' ? 'default' : 'outline'}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                {bidData.status === 'accepted' ? 'Accepted' : 'Accept Bid'}
              </Button>
            )}
            {onReject && (
              <Button 
                onClick={() => onReject(bidData.bidId)}
                variant="outline"
                className="flex-1"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}