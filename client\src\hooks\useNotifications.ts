import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "./useAuth";
import { apiRequest, getQueryFn } from "@/lib/queryClient";
import { toast } from "react-toastify";

export interface Notification {
  id: string;
  userId: string;
  organizationId?: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority: "low" | "medium" | "high" | "urgent";
  readAt?: string;
  deliveredAt?: string;
  createdAt: string;
  expiresAt?: string;
}

export interface NotificationsResponse {
  notifications: Notification[];
  unreadCount: number;
  hasMore: boolean;
  total: number;
}

export function useNotifications(options?: {
  page?: number;
  limit?: number;
  unreadOnly?: boolean;
}) {
  const { user, isAuthenticated } = useAuth();
  const { page = 1, limit = 20, unreadOnly = false } = options || {};

  return useQuery<NotificationsResponse>({
    queryKey: ["/api/notifications", { page, limit, unreadOnly }],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: isAuthenticated && !!user,
    staleTime: 30 * 1000, // 30 seconds for real-time feel
    refetchInterval: 60 * 1000, // Poll every 60 seconds
    refetchOnWindowFocus: true,
    retry: false,
  });
}

export function useUnreadNotifications() {
  const { user, isAuthenticated } = useAuth();

  return useQuery<{ notifications: Notification[]; count: number }>({
    queryKey: ["/api/notifications/unread"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: isAuthenticated && !!user,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Poll every 30 seconds for unread count
    refetchOnWindowFocus: true,
    retry: false,
  });
}

export function useMarkNotificationRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (notificationId: string) => {
      const response = await apiRequest("PATCH", `/api/notifications/${notificationId}/read`);
      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ["/api/notifications"] });
      queryClient.invalidateQueries({ queryKey: ["/api/notifications/unread"] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to mark notification as read: ${error.message}`);
    },
  });
}

export function useMarkAllNotificationsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/notifications/mark-all-read");
      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ["/api/notifications"] });
      queryClient.invalidateQueries({ queryKey: ["/api/notifications/unread"] });
      toast.success("All notifications marked as read");
    },
    onError: (error: Error) => {
      toast.error(`Failed to mark all notifications as read: ${error.message}`);
    },
  });
}
