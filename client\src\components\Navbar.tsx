import { User, MessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { NotificationDropdown } from "@/components/NotificationDropdown";
import { UserFeedbackModal } from "@/components/UserFeedbackModal";
import { useAuth } from "@/hooks/useAuth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ProfileModal } from "@/components/ProfileModal";
import { useState } from "react";
import { useLocation } from "wouter";
import { useClerk } from "@clerk/clerk-react";
import bidaibleLogo from "@assets/logo_1752612470296.png";

export function Navbar() {
  const { user, clerkUser, isAuthenticated } = useAuth();
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [, navigate] = useLocation();
  const { signOut } = useClerk();

  // Use clerkUser as fallback if dbUser is not available
  const displayUser = user || clerkUser;



  const handleProfileClick = () => {
    setShowProfileModal(true);
  };

  const handleSettingsClick = () => {
    navigate("/settings");
  };

  const handleLogout = async () => {
    try {
      await signOut();
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <>
      <nav className="fixed top-0 left-64 right-0 h-16 bg-background border-b border-border flex items-center justify-between px-6 z-50">
        {/* Logo */}
        <div className="flex items-center space-x-3">
          <img 
            src={bidaibleLogo} 
            alt="Bidaible Logo" 
            className="w-8 h-8 object-contain"
          />
          <span className="text-lg font-semibold text-foreground">Bidaible</span>
        </div>

        {/* User Actions */}
        <div className="flex items-center space-x-4">
          {/* User Feedback Button */}
          {isAuthenticated && (
            <Button 
              size="sm" 
              onClick={() => setShowFeedbackModal(true)}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              User Feedback
            </Button>
          )}
          
          <ThemeToggle />
          
          {/* Authenticated user features */}
          {isAuthenticated && (
            <>
              {/* Notifications */}
              <NotificationDropdown />
              
              {/* User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={(displayUser as any)?.profileImageUrl || (displayUser as any)?.imageUrl || ""} />
                      <AvatarFallback>
                        {((displayUser as any)?.firstName || (displayUser as any)?.first_name || "U")[0]}
                        {((displayUser as any)?.lastName || (displayUser as any)?.last_name || "U")[0]}
                      </AvatarFallback>
                    </Avatar>
                    <span className="hidden md:block text-sm font-medium">
                      {(displayUser as any)?.firstName || (displayUser as any)?.first_name} {(displayUser as any)?.lastName || (displayUser as any)?.last_name}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleProfileClick}>Profile</DropdownMenuItem>
                  <DropdownMenuItem onClick={handleSettingsClick}>Settings</DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLogout}>
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}
        </div>
      </nav>
      
      {/* Profile Modal */}
      <ProfileModal 
        open={showProfileModal} 
        onOpenChange={setShowProfileModal}
        user={displayUser}
      />
      
      {/* User Feedback Modal */}
      <UserFeedbackModal
        open={showFeedbackModal}
        onOpenChange={setShowFeedbackModal}
      />
    </>
  );
}
