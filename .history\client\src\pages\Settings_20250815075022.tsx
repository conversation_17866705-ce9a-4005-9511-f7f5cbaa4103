import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useAuth } from "@/hooks/useAuth";
import { useOrganization, useOrganizationList, UserProfile, CreateOrganization } from "@clerk/clerk-react";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import OnboardingOrganizationSetup from "@/components/OnboardingOrganizationSetup";
import { queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Save, User, Key, Bell, Activity, Users, Building } from "lucide-react";
import { useUserRole } from "@/hooks/useUserRole";

const contractorFormSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  tradeTypes: z.array(z.string()).min(1, "At least one trade type is required"),
});

const tradeOptions = [
  { value: "General Contractor", label: "General Contractor" },
  { value: "electrical", label: "Electrical" },
  { value: "plumbing", label: "Plumbing" },
  { value: "hvac", label: "HVAC" },
  { value: "concrete", label: "Concrete" },
  { value: "sitework", label: "Site Work/Excavation" },
  { value: "masonry", label: "Masonry" },
  { value: "carpentry", label: "Carpentry" },
  { value: "roofing", label: "Roofing" },
  { value: "painting", label: "Painting" },
];

export default function Settings() {
  const { user } = useAuth();
  const { organization, membership, isLoaded } = useOrganization();
  const { userMemberships } = useOrganizationList();
  const { toast } = useToast();
  const { role: userRole } = useUserRole();
  const [location] = useLocation();
  
  const isOnboardingMode = location.includes('onboarding=true');
  const [activeTab, setActiveTab] = useState("user-settings");
  
  const isOrgAdmin = membership?.role === 'org:admin';
  const hasOrganizations = userMemberships?.data && userMemberships.data.length > 0;
  const isSuperUserOrAdmin = userRole === 'general_contractor' || userRole === 'contractor';

  const { data: contractorProfile } = useQuery({
    queryKey: ["/api/contractors/profile"],
    enabled: !!user,
  });

  const contractorForm = useForm<z.infer<typeof contractorFormSchema>>({
    resolver: zodResolver(contractorFormSchema),
    defaultValues: {
      companyName: "",
      tradeTypes: [],
    },
  });

  useEffect(() => {
    if (contractorProfile) {
      const profile = contractorProfile as any;
      contractorForm.reset({
        companyName: profile?.companyName || "",
        tradeTypes: profile?.tradeTypes || [],
      });
    }
  }, [contractorProfile, contractorForm]);

  const updateContractorMutation = useMutation({
    mutationFn: (data: any) => fetch("/api/contractors/profile", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    }).then(res => res.json()),
    onSuccess: () => {
      toast({ title: "Profile updated successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/contractors/profile"] });
      
      if (isOnboardingMode) {
        toast({ title: "Onboarding Complete!", description: "Welcome to Bidaible!" });
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      }
    },
  });

  const onContractorSubmit = (data: any) => {
  { value: "elevator", label: "Elevator/Escalator" },
  { value: "architectural_millwork", label: "Architectural Millwork" },
];

const certificationOptions = [
  { value: "mbe", label: "MBE (Minority Business Enterprise)" },
  { value: "wbe", label: "WBE (Women Business Enterprise)" },
  { value: "vbe", label: "VBE (Veteran Business Enterprise)" },
  { value: "dbe", label: "DBE (Disadvantaged Business Enterprise)" },
  { value: "sbe", label: "SBE (Small Business Enterprise)" },
];

const specializationOptions = [
  { value: "design_build", label: "Design/Build" },
  { value: "bim", label: "BIM" },
  { value: "prefab", label: "Prefabrication" },
  { value: "sustainable", label: "Sustainable Construction" },
  { value: "heavy_civil", label: "Heavy Civil" },
  { value: "emergency", label: "Emergency Work" },
];

const projectTypeOptions = [
  { value: "residential", label: "Residential" },
  { value: "commercial", label: "Commercial" },
  { value: "infrastructure", label: "Infrastructure" },
  { value: "industrial", label: "Industrial" },
  { value: "institutional", label: "Institutional" },
  { value: "healthcare", label: "Healthcare" },
  { value: "education", label: "Education" },
];

export default function Settings() {
  const { user } = useAuth();
  const { organization, membership, isLoaded } = useOrganization();
  const { userMemberships } = useOrganizationList();
  const { toast } = useToast();
  const { role: userRole } = useUserRole();
  const [location] = useLocation();
  
  // Check if user is in onboarding mode
  const isOnboardingMode = location.includes('onboarding=true');
  
  // State
  const [activeTab, setActiveTab] = useState(isOnboardingMode ? "user-settings" : "user-settings");
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({});
  
  // Check permissions
  const isOrgAdmin = membership?.role === 'org:admin';
  const hasOrganizations = userMemberships?.data && userMemberships.data.length > 0;
  const isSuperUserOrAdmin = userRole === 'general_contractor' || userRole === 'contractor';

  // Data queries
  const { data: contractorProfile } = useQuery({
    queryKey: ["/api/contractors/profile"],
    enabled: !!user,
  });

  // Form initialization with all fields
  const contractorForm = useForm<z.infer<typeof contractorFormSchema>>({
    resolver: zodResolver(contractorFormSchema),
    defaultValues: {
      companyName: "",
      companyWebsite: "",
      legalStructure: "",
      taxId: "",
      dba: "",
      primaryAddress: "",
      mailingAddress: "",
      primaryContactName: "",
      primaryContactEmail: "",
      primaryContactPhone: "",
      primaryContactTitle: "",
      tradeTypes: [],
      unionStatus: "",
      unionAffiliations: "",
      certifications: [],
      serviceAreas: "",
      licenseNumber: "",
      licenseState: "",
      licenseExpiration: "",
      generalLiability: "",
      workersComp: "",
      autoInsurance: "",
      bondingSingle: undefined,
      bondingAggregate: undefined,
      emr: undefined,
      bankReference: "",
      suretyReference: "",
      creditRating: "",
      paymentTerms: "",
      litigationHistory: "",
      projectReferences: "",
      yearsInBusiness: undefined,
      specializations: [],
      awards: "",
      environmentalPrograms: "",
      workforceSize: undefined,
      workforceBreakdown: "",
      equipment: "",
      availability: "",
      keywordTags: [],
      preferredProjectTypes: [],
    },
  });

  // Update form values when contractor profile data loads
  useEffect(() => {
    if (contractorProfile) {
      const profile = contractorProfile as any;
      contractorForm.reset({
        companyName: profile?.companyName || "",
        companyWebsite: profile?.companyWebsite || "",
        legalStructure: profile?.legalStructure || "",
        taxId: profile?.taxId || "",
        dba: profile?.dba || "",
        primaryAddress: profile?.primaryAddress || "",
        mailingAddress: profile?.mailingAddress || "",
        primaryContactName: profile?.primaryContactName || "",
        primaryContactEmail: profile?.primaryContactEmail || "",
        primaryContactPhone: profile?.primaryContactPhone || "",
        primaryContactTitle: profile?.primaryContactTitle || "",
        tradeTypes: profile?.tradeTypes || [],
        unionStatus: profile?.unionStatus || "",
        unionAffiliations: profile?.unionAffiliations || "",
        certifications: profile?.certifications || [],
        serviceAreas: profile?.serviceAreas || "",
        licenseNumber: profile?.licenseNumber || "",
        licenseState: profile?.licenseState || "",
        licenseExpiration: profile?.licenseExpiration || "",
        generalLiability: profile?.generalLiability || "",
        workersComp: profile?.workersComp || "",
        autoInsurance: profile?.autoInsurance || "",
        bondingSingle: profile?.bondingSingle,
        bondingAggregate: profile?.bondingAggregate,
        emr: profile?.emr,
        bankReference: profile?.bankReference || "",
        suretyReference: profile?.suretyReference || "",
        creditRating: profile?.creditRating || "",
        paymentTerms: profile?.paymentTerms || "",
        litigationHistory: profile?.litigationHistory || "",
        projectReferences: profile?.projectReferences || "",
        yearsInBusiness: profile?.yearsInBusiness,
        specializations: profile?.specializations || [],
        awards: profile?.awards || "",
        environmentalPrograms: profile?.environmentalPrograms || "",
        workforceSize: profile?.workforceSize,
        workforceBreakdown: profile?.workforceBreakdown || "",
        equipment: profile?.equipment || "",
        availability: profile?.availability || "",
        keywordTags: profile?.keywordTags || [],
        preferredProjectTypes: profile?.preferredProjectTypes || [],
      });
    }
  }, [contractorProfile, contractorForm]);

  const toggleCollapse = (section: string) => {
    setCollapsedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  // Mutations
  const updateContractorMutation = useMutation({
    mutationFn: (data: any) => fetch("/api/contractors/profile", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    }).then(res => res.json()),
    onSuccess: () => {
      toast({ title: "Profile updated successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/contractors/profile"] });
      
      if (isOnboardingMode) {
        // Mark onboarding as complete and redirect to dashboard
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      }
    },
  });

  // Handlers
  const onContractorSubmit = (data: any) => {
    updateContractorMutation.mutate(data);
  };

  if (!isLoaded) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account, profile, and system preferences
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="user-settings">
            <User className="h-4 w-4 mr-2" />
            User Settings
          </TabsTrigger>
          <TabsTrigger value="api-keys">
            <Key className="h-4 w-4 mr-2" />
            API Keys
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          {isSuperUserOrAdmin && (
            <TabsTrigger value="audit-logs">
              <Activity className="h-4 w-4 mr-2" />
              Audit Logs
            </TabsTrigger>
          )}
        </TabsList>

        {/* User Settings Tab - Enhanced with Onboarding */}
        <TabsContent value="user-settings" className="space-y-6">
          {isOnboardingMode && !hasOrganizations && (
            <OnboardingOrganizationSetup 
              onComplete={() => {
                queryClient.invalidateQueries({ queryKey: ['/api/auth/user'] });
                toast({
                  title: "Organization Created!",
                  description: "Now let's complete your profile setup.",
                });
              }}
            />
          )}

          {!isOnboardingMode && !hasOrganizations && (
            <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <CardContent className="pt-6">
                <div className="flex items-start gap-4">
                  <Users className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="font-semibold text-blue-900">Create Your Organization</h3>
                    <p className="text-sm text-blue-700 mb-4">
                      Create an organization to access team features and user management.
                    </p>
                    <CreateOrganization 
                      appearance={{
                        elements: {
                          rootBox: "w-full",
                          card: "shadow-none border-0 bg-transparent",
                        }
                      }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* User Profile Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Account
              </CardTitle>
              <CardDescription>
                Manage your personal account settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserProfile 
                routing="hash"
                appearance={{
                  elements: {
                    rootBox: "w-full",
                    card: "shadow-none border border-gray-200 rounded-lg",
                    headerTitle: "text-lg font-semibold",
                    headerSubtitle: "text-sm text-muted-foreground",
                  }
                }}
              />
            </CardContent>
          </Card>

          {/* Contractor Profile Section - ALL Profile content moved here */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {isOnboardingMode && (
                  <div className="flex items-center gap-2 mr-4">
                    <Badge variant="outline">Step 3 of 3</Badge>
                    <div className="h-2 w-2 bg-primary rounded-full animate-pulse" />
                  </div>
                )}
                <Building className="h-5 w-5" />
                Contractor Profile
              </CardTitle>
              <CardDescription>
                {isOnboardingMode 
                  ? "Complete your profile to finish onboarding - Company Name and Trade Types are required"
                  : "Complete your contractor profile to participate in RFQ processes"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...contractorForm}>
                <form onSubmit={contractorForm.handleSubmit(onContractorSubmit)} className="space-y-6">
                  
                  {/* Business Identity & Contact - 10 fields */}
                  <Collapsible defaultOpen={isOnboardingMode}>
                    <CollapsibleTrigger
                      className={`flex items-center justify-between w-full p-4 rounded-lg hover:bg-gray-100 ${
                        isOnboardingMode ? 'bg-blue-50 border-l-4 border-l-blue-500' : 'bg-gray-50'
                      }`}
                      onClick={() => toggleCollapse('business')}
                    >
                      <h3 className="text-lg font-semibold">Business Identity & Contact</h3>
                      <div className="flex items-center gap-2">
                        {isOnboardingMode && (
                          <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                            * Required Fields
                          </span>
                        )}
                        <span className="text-sm text-gray-500">10 fields</span>
                        {collapsedSections.business ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="companyName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={isOnboardingMode ? "text-red-600 font-semibold" : ""}>
                                Company Name *
                                {isOnboardingMode && <span className="ml-1 text-red-500">★</span>}
                              </FormLabel>
                              <FormControl>
                                <Input 
                                  {...field} 
                                  placeholder="Enter company name" 
                                  className={isOnboardingMode && !field.value ? "border-red-300" : ""}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="companyWebsite"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Company Website</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="https://example.com" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="legalStructure"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Legal Structure</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select structure" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="llc">LLC</SelectItem>
                                  <SelectItem value="corporation">Corporation</SelectItem>
                                  <SelectItem value="partnership">Partnership</SelectItem>
                                  <SelectItem value="sole_proprietorship">Sole Proprietorship</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="taxId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Tax ID / EIN</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="XX-XXXXXXX" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="dba"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>DBA (Doing Business As)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Enter DBA name" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="primaryContactName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Contact Name</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Contact person name" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="primaryContactEmail"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Contact Email</FormLabel>
                              <FormControl>
                                <Input {...field} type="email" placeholder="<EMAIL>" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="primaryContactPhone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Contact Phone</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="(*************" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="space-y-4">
                        <FormField
                          control={contractorForm.control}
                          name="primaryAddress"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Business Address</FormLabel>
                              <FormControl>
                                <Textarea {...field} placeholder="Full business address" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="mailingAddress"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Mailing Address (if different)</FormLabel>
                              <FormControl>
                                <Textarea {...field} placeholder="Mailing address" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Classification & Capability - 5 fields */}
                  <Collapsible defaultOpen={isOnboardingMode}>
                    <CollapsibleTrigger
                      className={`flex items-center justify-between w-full p-4 rounded-lg hover:bg-gray-100 ${
                        isOnboardingMode ? 'bg-blue-50 border-l-4 border-l-blue-500' : 'bg-gray-50'
                      }`}
                      onClick={() => toggleCollapse('classification')}
                    >
                      <h3 className="text-lg font-semibold">Classification & Capability</h3>
                      <div className="flex items-center gap-2">
                        {isOnboardingMode && (
                          <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                            * Required Fields
                          </span>
                        )}
                        <span className="text-sm text-gray-500">5 fields</span>
                        {collapsedSections.classification ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <FormField
                        control={contractorForm.control}
                        name="tradeTypes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={isOnboardingMode ? "text-red-600 font-semibold" : ""}>
                              Trade Types *
                              {isOnboardingMode && <span className="ml-1 text-red-500">★</span>}
                            </FormLabel>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                              {tradeOptions.map((trade) => (
                                <div key={trade.value} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={trade.value}
                                    checked={field.value?.includes(trade.value)}
                                    onCheckedChange={(checked) => {
                                      const currentValue = field.value || [];
                                      if (checked) {
                                        field.onChange([...currentValue, trade.value]);
                                      } else {
                                        field.onChange(currentValue.filter((v: string) => v !== trade.value));
                                      }
                                    }}
                                  />
                                  <Label 
                                    htmlFor={trade.value} 
                                    className={`text-sm ${isOnboardingMode && field.value?.length === 0 ? 'text-red-600' : ''}`}
                                  >
                                    {trade.label}
                                  </Label>
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                            {isOnboardingMode && field.value?.length === 0 && (
                              <p className="text-red-500 text-xs">Please select at least one trade type to continue</p>
                            )}
                          </FormItem>
                        )}
                      />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="unionStatus"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Union Status</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="union">Union</SelectItem>
                                  <SelectItem value="non_union">Non-Union</SelectItem>
                                  <SelectItem value="mixed">Mixed</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="unionAffiliations"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Union Affiliations</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Union names/local numbers" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={contractorForm.control}
                        name="certifications"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Certifications</FormLabel>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {certificationOptions.map((cert) => (
                                <div key={cert.value} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={cert.value}
                                    checked={field.value?.includes(cert.value)}
                                    onCheckedChange={(checked) => {
                                      const currentValue = field.value || [];
                                      if (checked) {
                                        field.onChange([...currentValue, cert.value]);
                                      } else {
                                        field.onChange(currentValue.filter((v: string) => v !== cert.value));
                                      }
                                    }}
                                  />
                                  <Label htmlFor={cert.value} className="text-sm">
                                    {cert.label}
                                  </Label>
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={contractorForm.control}
                        name="serviceAreas"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Service Areas</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Geographic areas you serve (cities, counties, states)" />
                            </FormControl>
                            <FormMessage />
                          </FormItem
