/**
 * Admin Routes Denial Test (non-admin should be denied)
 * Note: These routes rely on Clerk org membership; in local dev without <PERSON>, they may return 403 or 500.
 */
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const { attachDebugInterceptors, getApiKey, whoAmI } = require('./test-utils');

const BASE_URL = process.env.BASE_URL || 'http://localhost:5000';

async function createUserWithKey({ userId, classification, organizationId }) {
  await axios.post(`${BASE_URL}/api/test/user`, { id: userId, organizationId, userClassification: classification });
  const apiKey = await getApiKey(BASE_URL, userId);
  const client = axios.create({ baseURL: BASE_URL, headers: { Authorization: `Bearer ${apiKey}` }, timeout: 30000 });
  attachDebugInterceptors(client, `client:${userId}`);
  await whoAmI(BASE_URL, apiKey);
  return { userId, client };
}

async function run() {
  console.log('🧪 Admin Routes Denial Test');
  const { data: orgA } = await axios.post(`${BASE_URL}/api/test/org`, { id: uuidv4(), name: 'Org A', slug: `org-a-${Date.now()}` });
  const user = await createUserWithKey({ userId: `user_${Date.now()}`, classification: 'general_contractor', organizationId: orgA.id });

  // Without Clerk admin membership, should not be able to list org users
  try {
    await user.client.get(`/api/organizations/${orgA.id}/users`);
    throw new Error('Expected denial for non-admin on org users');
  } catch (e) {
    const status = e.response && e.response.status;
    if (status === 403 || status === 500) console.log('✅ Non-admin denied (403/500) on org users route');
    else throw e;
  }
  console.log('\n🎉 Admin Routes Denial Test COMPLETED');
}

if (require.main === module) {
  run().catch(err => { console.error('💥 Test failed:', err && err.response ? err.response.data || err.response.status : err); process.exit(1); });
}

module.exports = { run };
