import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { formatDistanceToNow } from "date-fns";
import { TrendingUp, Clock, FileText, CheckCircle, AlertCircle, Sparkles, Package, User } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useStructuredBidData } from "@/hooks/useStructuredBidData";
import { BidLineItemsDisplay } from "./BidLineItemsDisplay";
import { BidScopeDisplay } from "./BidScopeDisplay";
import { BidContactInfoDisplay } from "./BidContactInfoDisplay";
import { BidDataQualityIndicator } from "./BidDataQualityIndicator";

interface BidDetailsModalProps {
  bid: {
    id: string;
    bidAmount: number | null;
    extractedAmount: number | null;
    timeline: string | null;
    extractedTimeline: string | null;
    scope: string | null;
    extractedScope: string | null;
    conditions: string | null;
    extractedConditions: string | null;
    extractionConfidence: number | null;
    status: string;
    submittedAt: string;
    aiSummary: string | null;
    aiAnalysis: any;
    notes: string | null;
  };
  rfq: {
    title: string;
    projectLocation: string;
    dueDate: string;
  };
  isOpen: boolean;
  onClose: () => void;
}

export default function BidDetailsModal({ bid, rfq, isOpen, onClose }: BidDetailsModalProps) {
  // Fetch structured bid data
  const { data: structuredData, isLoading: isLoadingStructured } = useStructuredBidData(bid.id);

  const formatCurrency = (amount: number | null) => {
    if (!amount) return "Not specified";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'submitted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'accepted':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const confidenceScore = bid.extractionConfidence || 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto" aria-describedby="bid-details-description">
        <div id="bid-details-description" className="sr-only">
          View detailed bid information including AI-extracted data, amounts, timeline, and scope
        </div>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Bid Details - {rfq.title}
            {structuredData?.summary.hasStructuredData && (
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                <Package className="h-3 w-3 mr-1" />
                Structured Data
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="line-items" className="flex items-center gap-1">
              <Package className="h-3 w-3" />
              Line Items
              {structuredData?.lineItems.count && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {structuredData.lineItems.count}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="scope" className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              Scope
              {structuredData?.scopeDefinition && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {structuredData.scopeDefinition.included.count + structuredData.scopeDefinition.excluded.count}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="contact" className="flex items-center gap-1">
              <User className="h-3 w-3" />
              Contact
            </TabsTrigger>
            <TabsTrigger value="ai-analysis" className="flex items-center gap-1">
              <Sparkles className="h-3 w-3" />
              AI Analysis
            </TabsTrigger>
            <TabsTrigger value="quality" className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              Quality
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
          {/* Project Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Project Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Project</p>
                  <p className="font-medium">{rfq.title}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Location</p>
                  <p className="font-medium">{rfq.projectLocation}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <Badge className={getStatusColor(bid.status)}>
                    {bid.status.replace('_', ' ')}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Submitted</p>
                  <p className="font-medium">
                    {formatDistanceToNow(new Date(bid.submittedAt), { addSuffix: true })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI Extraction Results */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                AI Extraction Results
                <Badge variant="outline" className={getConfidenceColor(confidenceScore)}>
                  {Math.round(confidenceScore * 100)}% Confidence
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="font-medium">Bid Amount</span>
                  </div>
                  <div className="bg-muted p-3 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(bid.extractedAmount || bid.bidAmount)}
                    </p>
                    {bid.bidAmount && bid.extractedAmount && bid.bidAmount !== bid.extractedAmount && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Original: {formatCurrency(bid.bidAmount)}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">Timeline</span>
                  </div>
                  <div className="bg-muted p-3 rounded-lg">
                    <p className="text-lg font-semibold">
                      {bid.extractedTimeline || bid.timeline || "Not specified"}
                    </p>
                    {bid.timeline && bid.extractedTimeline && bid.timeline !== bid.extractedTimeline && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Original: {bid.timeline}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Scope of Work */}
              {(bid.extractedScope || bid.scope) && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">Scope of Work</span>
                  </div>
                  <div className="bg-muted p-3 rounded-lg">
                    <p className="whitespace-pre-wrap">{bid.extractedScope || bid.scope}</p>
                  </div>
                </div>
              )}

              {/* Conditions */}
              {(bid.extractedConditions || bid.conditions) && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <span className="font-medium">Conditions & Terms</span>
                  </div>
                  <div className="bg-muted p-3 rounded-lg">
                    <p className="whitespace-pre-wrap">{bid.extractedConditions || bid.conditions}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* AI Summary */}
          {bid.aiSummary && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  AI Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <ReactMarkdown 
                    remarkPlugins={[remarkGfm]}
                    components={{
                      table: ({ children }) => (
                        <div className="overflow-x-auto">
                          <table className="min-w-full border border-border rounded-lg">{children}</table>
                        </div>
                      ),
                      th: ({ children }) => (
                        <th className="border border-border bg-muted px-2 py-1 text-left font-medium">{children}</th>
                      ),
                      td: ({ children }) => (
                        <td className="border border-border px-2 py-1">{children}</td>
                      ),
                      h1: ({ children }) => (
                        <h1 className="text-xl font-bold mb-3 text-foreground">{children}</h1>
                      ),
                      h2: ({ children }) => (
                        <h2 className="text-lg font-semibold mb-2 text-foreground">{children}</h2>
                      ),
                      h3: ({ children }) => (
                        <h3 className="text-base font-medium mb-2 text-foreground">{children}</h3>
                      ),
                      ul: ({ children }) => (
                        <ul className="list-disc list-inside space-y-1 mb-3">{children}</ul>
                      ),
                      ol: ({ children }) => (
                        <ol className="list-decimal list-inside space-y-1 mb-3">{children}</ol>
                      ),
                      blockquote: ({ children }) => (
                        <blockquote className="border-l-4 border-primary pl-4 italic bg-muted p-3 rounded-r-lg mb-3">{children}</blockquote>
                      ),
                      strong: ({ children }) => (
                        <strong className="font-semibold text-foreground">{children}</strong>
                      )
                    }}
                  >
                    {bid.aiSummary}
                  </ReactMarkdown>
                </div>
              </CardContent>
            </Card>
          )}

          {/* AI Analysis */}
          {bid.aiAnalysis && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">AI Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg">
                  <pre className="text-sm whitespace-pre-wrap">
                    {JSON.stringify(bid.aiAnalysis, null, 2)}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {bid.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Additional Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">{bid.notes}</p>
              </CardContent>
            </Card>
          )}
          </TabsContent>

          {/* Line Items Tab */}
          <TabsContent value="line-items">
            {isLoadingStructured ? (
              <div className="animate-pulse space-y-4">
                <div className="h-8 bg-muted rounded w-64" />
                <div className="h-40 bg-muted rounded" />
              </div>
            ) : structuredData ? (
              <BidLineItemsDisplay
                lineItems={structuredData.lineItems.items}
                total={structuredData.lineItems.total}
                categories={structuredData.lineItems.categories}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center text-muted-foreground">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Unable to load structured line item data</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Scope Definition Tab */}
          <TabsContent value="scope">
            {isLoadingStructured ? (
              <div className="animate-pulse space-y-4">
                <div className="h-8 bg-muted rounded w-64" />
                <div className="h-40 bg-muted rounded" />
              </div>
            ) : structuredData ? (
              <BidScopeDisplay
                includedItems={structuredData.scopeDefinition.included.items}
                excludedItems={structuredData.scopeDefinition.excluded.items}
                includedCategories={structuredData.scopeDefinition.included.categories}
                excludedCategories={structuredData.scopeDefinition.excluded.categories}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center text-muted-foreground">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Unable to load structured scope definition data</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Contact Information Tab */}
          <TabsContent value="contact">
            {isLoadingStructured ? (
              <div className="animate-pulse space-y-4">
                <div className="h-8 bg-muted rounded w-64" />
                <div className="h-40 bg-muted rounded" />
              </div>
            ) : structuredData ? (
              <BidContactInfoDisplay
                contactInfo={structuredData.bid.contactInfo}
                hasContactInfo={structuredData.summary.dataCompleteness.hasContactInfo}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center text-muted-foreground">
                  <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Unable to load contact information</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* AI Analysis Tab (existing content) */}
          <TabsContent value="ai-analysis" className="space-y-6">
            {/* AI Summary */}
            {bid.aiSummary && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-600" />
                    AI Summary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm dark:prose-invert max-w-none">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {bid.aiSummary}
                    </ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* AI Analysis */}
            {bid.aiAnalysis && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">AI Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg">
                    <pre className="text-sm whitespace-pre-wrap">
                      {JSON.stringify(bid.aiAnalysis, null, 2)}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Notes */}
            {bid.notes && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Additional Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm whitespace-pre-wrap">{bid.notes}</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Data Quality Tab */}
          <TabsContent value="quality">
            {isLoadingStructured ? (
              <div className="animate-pulse space-y-4">
                <div className="h-8 bg-muted rounded w-64" />
                <div className="h-40 bg-muted rounded" />
              </div>
            ) : structuredData ? (
              <BidDataQualityIndicator
                dataCompleteness={structuredData.summary.dataCompleteness}
                hasStructuredData={structuredData.summary.hasStructuredData}
                lineItemsCount={structuredData.lineItems.count}
                scopeItemsCount={structuredData.scopeDefinition.included.count + structuredData.scopeDefinition.excluded.count}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center text-muted-foreground">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Unable to load data quality assessment</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}