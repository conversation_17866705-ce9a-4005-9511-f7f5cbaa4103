import type { Express } from "express";
import { protectedRoute, addUserToRequest } from "../clerkAuth";
import { getProcessingStats } from "../services/aiOptimizedService";
import { getFileProcessingStats } from "../services/fileProcessingService";

export function registerProcessingStatsRoutes(app: Express) {
  // Get real-time processing statistics
  app.get('/api/processing/stats', protectedRoute, addUserToRequest, async (req, res) => {
    try {
      const aiStats = getProcessingStats();
      const fileStats = getFileProcessingStats();
      
      res.json({
        ai: aiStats,
        file: fileStats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error("Error fetching processing stats:", error);
      res.status(500).json({ message: "Failed to fetch processing stats" });
    }
  });
}