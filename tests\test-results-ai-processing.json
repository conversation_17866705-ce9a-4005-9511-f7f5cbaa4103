{"passed": 2, "failed": 4, "total": 6, "details": [{"test": "PDF.js Primary Extraction", "status": "FAIL", "duration": 1722, "notes": "Error: Request failed with status code 401", "timestamp": "2025-08-15T16:32:32.419Z"}, {"test": "AI Processing - Groq Integration", "status": "PASS", "duration": 1987, "notes": "AI processing successful - Summary: true, Data: true", "timestamp": "2025-08-15T16:32:34.406Z"}, {"test": "Multi-file AI Processing with Priorities", "status": "FAIL", "duration": 9916, "notes": "Error: Request failed with status code 401", "timestamp": "2025-08-15T16:32:44.322Z"}, {"test": "AI Processing Fallback Mechanisms", "status": "PASS", "duration": 1772, "notes": "System handled challenging content gracefully", "timestamp": "2025-08-15T16:32:46.094Z"}, {"test": "Large File AI Processing Performance", "status": "FAIL", "duration": 2397, "notes": "Large file processing failed: 200", "timestamp": "2025-08-15T16:32:48.492Z"}, {"test": "PDF Extraction Confidence Scoring", "status": "FAIL", "duration": 3281, "notes": "Processing failed: 200", "timestamp": "2025-08-15T16:32:51.773Z"}], "performance": [{"test": "AI Processing - Groq Integration", "aiProcessingTime": 1987, "aiSummaryLength": 1050, "extractedDataFields": 10, "hasStructuredExtraction": true, "timestamp": "2025-08-15T16:32:34.406Z"}]}