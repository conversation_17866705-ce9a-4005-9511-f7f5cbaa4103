import { useQuery } from "@tanstack/react-query";
import { getQueryFn } from "@/lib/queryClient";
import { useAuth } from "@/hooks/useAuth";

export type UserRoleType = 'general_contractor' | 'contractor' | 'loading';

export function useUserRole() {
  const { user, isAuthenticated } = useAuth();
  
  // Fetch contractor profile to determine contractor-specific role
  const { data: contractorProfile, isLoading: isProfileLoading } = useQuery({
    queryKey: ["/api/contractors/profile"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: isAuthenticated,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Determine user role based on contractor classification
  const getUserRole = (): UserRoleType => {
    if (isProfileLoading || !isAuthenticated) {
      return 'loading';
    }

    // If no contractor profile exists, default to General Contractor
    if (!contractorProfile) {
      return 'general_contractor';
    }

    try {
      const profile = contractorProfile as any;
      
      // Check trade types to determine role
      const tradeTypes = profile.tradeTypes || [];
      if (Array.isArray(tradeTypes) && tradeTypes.length > 0) {
        // If they have "General Contractor" in their trade types, they're a GC
        const hasGeneralContractor = tradeTypes.some(trade => 
          trade === 'General Contractor' || trade === 'general_contractor'
        );
        
        if (hasGeneralContractor) {
          return 'general_contractor';
        } else {
          // If they have other trades but no "General Contractor", they're a specialty contractor
          return 'contractor';
        }
      }

      // Default to General Contractor if no trade types are set
      return 'general_contractor';
    } catch (error) {
      console.warn('Error determining user role:', error);
      return 'general_contractor';
    }
  };

  const role = getUserRole();

  return {
    role,
    isLoading: isProfileLoading || !isAuthenticated,
    contractorProfile,
    isGeneralContractor: role === 'general_contractor',
    isContractor: role === 'contractor',
  };
}