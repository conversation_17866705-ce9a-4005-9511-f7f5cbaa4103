{"startTime": 1755275547385, "endTime": 1755275571779, "totalDuration": 24394, "suites": [{"name": "Wasabi Storage Integration", "status": "FAIL", "duration": 2955, "totalTests": 6, "passed": 1, "failed": 5, "successRate": 16.666666666666664, "details": [{"test": "Basic File Upload to Wasabi", "status": "FAIL", "duration": 2858, "notes": "Unexpected response: 200", "timestamp": "2025-08-15T16:32:30.592Z"}, {"test": "Large File Chunked Upload", "status": "FAIL", "duration": 18, "notes": "Error: Request failed with status code 401", "timestamp": "2025-08-15T16:32:30.610Z"}, {"test": "Multi-file Batch Upload", "status": "FAIL", "duration": 9, "notes": "Error: Request failed with status code 401", "timestamp": "2025-08-15T16:32:30.620Z"}, {"test": "SSE Progress Tracking", "status": "FAIL", "duration": 3, "notes": "Error: Request failed with status code 401", "timestamp": "2025-08-15T16:32:30.623Z"}, {"test": "File Validation & Security", "status": "FAIL", "duration": 4, "notes": "Unexpected error: Request failed with status code 401", "timestamp": "2025-08-15T16:32:30.627Z"}, {"test": "Wasabi Storage Operations", "status": "PASS", "duration": 8, "notes": "Storage operations accessible", "timestamp": "2025-08-15T16:32:30.635Z"}], "performance": []}, {"name": "AI Processing & PDF Extraction", "status": "FAIL", "duration": 21139, "totalTests": 6, "passed": 2, "failed": 4, "successRate": 33.33333333333333, "details": [{"test": "PDF.js Primary Extraction", "status": "FAIL", "duration": 1722, "notes": "Error: Request failed with status code 401", "timestamp": "2025-08-15T16:32:32.419Z"}, {"test": "AI Processing - Groq Integration", "status": "PASS", "duration": 1987, "notes": "AI processing successful - Summary: true, Data: true", "timestamp": "2025-08-15T16:32:34.406Z"}, {"test": "Multi-file AI Processing with Priorities", "status": "FAIL", "duration": 9916, "notes": "Error: Request failed with status code 401", "timestamp": "2025-08-15T16:32:44.322Z"}, {"test": "AI Processing Fallback Mechanisms", "status": "PASS", "duration": 1772, "notes": "System handled challenging content gracefully", "timestamp": "2025-08-15T16:32:46.094Z"}, {"test": "Large File AI Processing Performance", "status": "FAIL", "duration": 2397, "notes": "Large file processing failed: 200", "timestamp": "2025-08-15T16:32:48.492Z"}, {"test": "PDF Extraction Confidence Scoring", "status": "FAIL", "duration": 3281, "notes": "Processing failed: 200", "timestamp": "2025-08-15T16:32:51.773Z"}], "performance": [{"test": "AI Processing - Groq Integration", "aiProcessingTime": 1987, "aiSummaryLength": 1050, "extractedDataFields": 10, "hasStructuredExtraction": true, "timestamp": "2025-08-15T16:32:34.406Z"}]}], "summary": {"totalTests": 12, "totalPassed": 3, "totalFailed": 9, "successRate": 25}, "environment": {"nodeVersion": "v22.15.0", "platform": "win32", "timestamp": "2025-08-15T16:32:27.383Z"}}