// Dynamic imports for environment-specific libraries
let pdfjsLib: any = null;
let pdfParseLib: any = null;

// Initialize PDF.js conditionally - NEVER in production due to DOM API dependencies
async function initializePDFJS(): Promise<void> {
  const isProduction = process.env.NODE_ENV === 'production';
  const hasDOM = typeof window !== 'undefined' && typeof document !== 'undefined';
  
  // Completely skip PDF.js in production environments to avoid DOMMatrix errors
  if (isProduction) {
    console.log('🔧 Production environment detected, PDF.js disabled (DOM APIs not available)');
    return;
  }
  
  // Only attempt PDF.js initialization in development or browser environments
  if (!isProduction && (process.env.NODE_ENV === 'development' || hasDOM)) {
    try {
      pdfjsLib = await import("pdfjs-dist/legacy/build/pdf.mjs");
      console.log('✅ PDF.js initialized successfully');
    } catch (error) {
      console.warn('⚠️ PDF.js not available in this environment, using fallback methods');
    }
  } else {
    console.log('🔧 Non-development environment detected, skipping PDF.js initialization');
  }
}

// Initialize pdf-parse
async function initializePDFParse(): Promise<void> {
  try {
    pdfParseLib = await import("pdf-parse");
    console.log('✅ pdf-parse initialized successfully');
  } catch (error) {
    console.warn('⚠️ pdf-parse not available:', error);
  }
}

/**
 * Unified PDF Extraction Service
 * Consolidates enhanced PDF text extraction logic from multiple services
 * Provides consistent, reliable PDF processing for both RFQ and Bid documents
 */

interface ExtractionOptions {
  useStreaming?: boolean;
  timeout?: number;
  onProgress?: (progress: { stage: string; percentage: number; message: string }) => void;
}

interface ExtractionResult {
  text: string;
  success: boolean;
  extractionMethod: 'pdfjs' | 'pdf-parse' | 'gemini-vision';
  processingTime: number;
  pageCount: number;
  confidence: number;
}

export class UnifiedPDFExtractor {
  private readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private readonly PDF_CONFIG = {
    disableFontFace: true,
    disableRange: true,
    disableStream: true
  };

  /**
   * Main extraction method with automatic fallback strategy
   * Prioritizes pdf-parse in production environments to avoid DOM API issues
   */
  async extractText(fileBuffer: Buffer, options: ExtractionOptions = {}): Promise<ExtractionResult> {
    console.log('🔧 UnifiedPDFExtractor: Starting PDF text extraction');
    console.log('🔧 Buffer size:', fileBuffer.length, 'bytes');
    console.log('🔧 Environment:', process.env.NODE_ENV);

    const startTime = Date.now();
    const isProduction = process.env.NODE_ENV === 'production';

    // Initialize libraries if not already done
    if (!pdfParseLib) {
      await initializePDFParse();
    }
    
    // Only initialize PDF.js in non-production environments
    if (!isProduction && !pdfjsLib) {
      await initializePDFJS();
    }

    // In production, prioritize pdf-parse to avoid DOM API issues
    if (isProduction) {
      try {
        // Primary: pdf-parse library (Node.js compatible)
        const result = await this.fallbackPdfParse(fileBuffer);
        const processingTime = Date.now() - startTime;
        
        if (result.text.length >= 50) {
          console.log('✅ pdf-parse extraction successful:', result.text.length, 'characters');
          return {
            ...result,
            success: true,
            extractionMethod: 'pdf-parse',
            processingTime,
            confidence: this.calculateConfidence(result.text, result.pageCount)
          };
        }
        
        throw new Error('pdf-parse extraction yielded insufficient text');
        
      } catch (pdfParseError: any) {
        console.log('❌ pdf-parse extraction failed:', pdfParseError?.message || pdfParseError);
        
        // Fallback: Gemini Vision (if API key available)
        if (process.env.GEMINI_API_KEY) {
          try {
            const result = await this.fallbackGeminiVision(fileBuffer);
            const processingTime = Date.now() - startTime;
            
            console.log('✅ Gemini Vision fallback successful:', result.text.length, 'characters');
            return {
              ...result,
              success: true,
              extractionMethod: 'gemini-vision',
              processingTime,
              confidence: this.calculateConfidence(result.text, 1)
            };
            
          } catch (geminiError: any) {
            console.log('❌ Gemini Vision fallback failed:', geminiError?.message || geminiError);
          }
        }
        
        // All extraction methods failed
        const processingTime = Date.now() - startTime;
        return {
          text: '',
          success: false,
          extractionMethod: 'pdf-parse',
          processingTime,
          pageCount: 0,
          confidence: 0
        };
      }
    } else {
      // Development: Try PDF.js first, then fallback to pdf-parse
      try {
        // Primary: Enhanced PDF.js extraction
        const result = await this.enhancedPDFJsExtraction(fileBuffer, options);
        const processingTime = Date.now() - startTime;

        if (result.text.length >= 50) {
          console.log('✅ Enhanced PDF.js extraction successful:', result.text.length, 'characters');
          return {
            ...result,
            success: true,
            extractionMethod: 'pdfjs',
            processingTime,
            confidence: this.calculateConfidence(result.text, result.pageCount)
          };
        }

        console.log('⚠️ PDF.js extraction insufficient, trying fallbacks...');
        throw new Error('PDF.js extraction yielded insufficient text');

      } catch (pdfJsError: any) {
        console.log('❌ PDF.js extraction failed:', pdfJsError?.message || pdfJsError);
        
        try {
          // Fallback 1: pdf-parse library
          const result = await this.fallbackPdfParse(fileBuffer);
          const processingTime = Date.now() - startTime;
          
          if (result.text.length >= 50) {
            console.log('✅ pdf-parse fallback successful:', result.text.length, 'characters');
            return {
              ...result,
              success: true,
              extractionMethod: 'pdf-parse',
              processingTime,
              confidence: this.calculateConfidence(result.text, result.pageCount)
            };
          }
          
          throw new Error('pdf-parse extraction yielded insufficient text');
          
        } catch (pdfParseError: any) {
          console.log('❌ pdf-parse fallback failed:', pdfParseError?.message || pdfParseError);
          
          // Fallback 2: Gemini Vision (if API key available)
          if (process.env.GEMINI_API_KEY) {
            try {
              const result = await this.fallbackGeminiVision(fileBuffer);
              const processingTime = Date.now() - startTime;
              
              console.log('✅ Gemini Vision fallback successful:', result.text.length, 'characters');
              return {
                ...result,
                success: true,
                extractionMethod: 'gemini-vision',
                processingTime,
                confidence: this.calculateConfidence(result.text, 1)
              };
              
            } catch (geminiError: any) {
              console.log('❌ Gemini Vision fallback failed:', geminiError?.message || geminiError);
            }
          }
          
          // All extraction methods failed
          const processingTime = Date.now() - startTime;
          return {
            text: '',
            success: false,
            extractionMethod: 'pdfjs',
            processingTime,
            pageCount: 0,
            confidence: 0
          };
        }
      }
    }
  }

  /**
   * Enhanced PDF.js extraction with robust text item processing
   */
  private async enhancedPDFJsExtraction(fileBuffer: Buffer, options: ExtractionOptions): Promise<{ text: string; pageCount: number }> {
    console.log('🔧 Starting enhanced PDF.js extraction...');
    
    // Check if PDF.js is available
    if (!pdfjsLib) {
      throw new Error('PDF.js not available in this environment');
    }
    
    // Buffer integrity validation
    this.validateBuffer(fileBuffer);
    
    const loadingTask = pdfjsLib.getDocument({
      data: new Uint8Array(fileBuffer),
      ...this.PDF_CONFIG
    });

    const pdf = await loadingTask.promise;
    console.log(`📄 PDF loaded successfully: ${pdf.numPages} pages`);

    let fullText = '';
    const totalPages = pdf.numPages;

    // Process pages with progress tracking
    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();

      console.log(`📄 Page ${pageNum} - Processing ${textContent.items?.length || 0} text items`);

      // Use enhanced text extraction
      const pageText = this.extractTextFromItems(textContent.items, pageNum);
      
      if (pageText.length > 0) {
        fullText += `\n--- Page ${pageNum} ---\n${pageText}`;
      } else {
        console.log(`⚠️ Page ${pageNum} - No text extracted, trying alternative methods`);
        
        // Alternative extraction for problematic pages
        const altText = await this.alternativePageExtraction(page, pageNum);
        if (altText.length > 0) {
          fullText += `\n--- Page ${pageNum} ---\n${altText}`;
        } else {
          fullText += `\n--- Page ${pageNum} ---\n`;
        }
      }

      // Progress callback
      if (options.onProgress) {
        const percentage = Math.round((pageNum / totalPages) * 70); // Reserve 30% for post-processing
        options.onProgress({
          stage: 'text_extraction',
          percentage,
          message: `Extracted text from page ${pageNum} of ${totalPages}`
        });
      }
    }

    // Clean up extracted text
    const cleanedText = this.cleanExtractedText(fullText);
    console.log(`✅ PDF.js extraction complete: ${cleanedText.length} characters extracted`);

    return {
      text: cleanedText,
      pageCount: totalPages
    };
  }

  /**
   * Consolidated enhanced text extraction from PDF.js text items
   * Combines the best logic from both aiService.ts and aiStreamProcessor.ts
   */
  private extractTextFromItems(items: any[], pageNum?: number): string {
    if (!items || !Array.isArray(items)) {
      return '';
    }

    const textParts: string[] = [];
    
    for (const item of items) {
      try {
        let text = '';
        
        // Handle different PDF.js text item structures
        if (typeof item === 'string') {
          text = item;
        } else if (item && typeof item === 'object') {
          // Primary extraction properties (in order of reliability)
          if (typeof item.str === 'string') {
            text = item.str;
          } else if (typeof item.text === 'string') {
            text = item.text;
          } else if (typeof item.textContent === 'string') {
            text = item.textContent;
          } else if (typeof item.unicode === 'string') {
            text = item.unicode;
          } else if (typeof item.chars === 'string') {
            text = item.chars;
          } else if (typeof item.content === 'string') {
            text = item.content;
          } else if (typeof item.value === 'string') {
            text = item.value;
          }
          // Handle nested items
          else if (Array.isArray(item.items)) {
            text = this.extractTextFromItems(item.items, pageNum);
          }
        }
        
        // Include text with custom font encoding support
        if (text) {
          if (text.trim()) {
            textParts.push(text.trim());
          } else if (text.length > 0) {
            // Include control characters for custom font encoding
            textParts.push(text);
          }
        }
        
      } catch (itemError) {
        console.warn(`Page ${pageNum || '?'} - Skipping malformed text item:`, itemError);
        continue;
      }
    }
    
    return textParts.join(' ');
  }

  /**
   * Alternative page extraction for problematic pages
   */
  private async alternativePageExtraction(page: any, pageNum: number): Promise<string> {
    try {
      // Try with different textContent options
      const altTextContent = await page.getTextContent({
        includeMarkedContent: true,
        disableCombineTextItems: false,
        normalizeWhitespace: false
      });
      
      if (altTextContent.items?.length > 0) {
        return altTextContent.items.map((item: any) => {
          return item?.str || item?.unicode || item?.chars || item?.text || '';
        }).join(' ');
      }
      
    } catch (error) {
      console.warn(`Alternative extraction failed for page ${pageNum}:`, error);
    }
    
    return '';
  }

  /**
   * pdf-parse fallback extraction
   */
  private async fallbackPdfParse(fileBuffer: Buffer): Promise<{ text: string; pageCount: number }> {
    console.log('🔧 Attempting pdf-parse fallback...');
    
    try {
      // Dynamic import with error handling for initialization issues
      const pdfParse = await import("pdf-parse");
      const parseFunction = pdfParse.default || pdfParse;
      
      // Call the parser with the buffer
      const pdfData = await parseFunction(fileBuffer);
      
      return {
        text: pdfData.text || '',
        pageCount: pdfData.numpages || 0
      };
    } catch (error: any) {
      console.log('❌ pdf-parse import/execution failed:', error.message);
      
      // If pdf-parse fails, try a simple text extraction approach
      return this.simpleFallbackExtraction(fileBuffer);
    }
  }

  /**
   * Simple fallback extraction when all libraries fail
   */
  private async simpleFallbackExtraction(fileBuffer: Buffer): Promise<{ text: string; pageCount: number }> {
    console.log('🔧 Attempting simple fallback extraction...');
    
    try {
      // Convert buffer to string and look for readable text patterns
      const bufferString = fileBuffer.toString('binary');
      
      // Extract text between common PDF text markers
      const textMatches = bufferString.match(/\(([^)]+)\)/g) || [];
      const extractedTexts = textMatches
        .map(match => match.slice(1, -1)) // Remove parentheses
        .filter(text => text.length > 2 && /[a-zA-Z]/.test(text)) // Filter meaningful text
        .join(' ');
      
      // Also try to extract text using stream patterns
      const streamMatches = bufferString.match(/stream\s*([\s\S]*?)\s*endstream/g) || [];
      const streamTexts = streamMatches
        .map(match => match.replace(/stream|endstream/g, '').trim())
        .filter(text => text.length > 10)
        .join(' ');
      
      const combinedText = (extractedTexts + ' ' + streamTexts).trim();
      
      return {
        text: combinedText,
        pageCount: 1 // Estimate
      };
    } catch (error) {
      console.log('❌ Simple fallback extraction failed:', error);
      return {
        text: '',
        pageCount: 0
      };
    }
  }

  /**
   * Gemini Vision fallback extraction
   */
  private async fallbackGeminiVision(fileBuffer: Buffer): Promise<{ text: string; pageCount: number }> {
    console.log('🔧 Attempting Gemini Vision fallback...');
    
    const { GoogleGenerativeAI } = await import("@google/generative-ai");
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

    const prompt = "Extract all text content from this document. Focus on preserving the structure and all readable text.";

    const result = await model.generateContent([
      prompt,
      {
        inlineData: {
          data: fileBuffer.toString('base64'),
          mimeType: 'application/pdf'
        }
      }
    ]);

    const response = await result.response;
    const text = response.text() || '';
    
    return {
      text,
      pageCount: 1 // Vision doesn't provide accurate page count
    };
  }

  /**
   * Validate buffer integrity
   */
  private validateBuffer(fileBuffer: Buffer): void {
    if (!Buffer.isBuffer(fileBuffer)) {
      throw new Error('Invalid buffer provided');
    }
    
    if (fileBuffer.length === 0) {
      throw new Error('Empty buffer provided');
    }
    
    // Validate PDF header
    const pdfHeader = fileBuffer.slice(0, 4).toString();
    if (pdfHeader !== '%PDF') {
      console.warn('⚠️ Buffer may not be a valid PDF file (missing %PDF header)');
    }
    
    console.log('✅ Buffer validation passed:', {
      size: fileBuffer.length,
      isPDF: pdfHeader === '%PDF'
    });
  }

  /**
   * Clean and normalize extracted text
   */
  private cleanExtractedText(text: string): string {
    return text
      .replace(/\s+/g, ' ')           // Normalize whitespace
      .replace(/\n\s*\n/g, '\n')      // Remove extra line breaks
      .replace(/^\s+|\s+$/g, '')      // Trim start and end
      .trim();
  }


  /**
   * Calculate extraction confidence score
   */
  private calculateConfidence(text: string, pageCount: number): number {
    if (!text || text.length === 0) return 0;
    
    const factors = {
      length: Math.min(text.length / 1000, 1) * 0.4,      // Text length factor
      structure: (text.match(/\n/g)?.length || 0) > 0 ? 0.3 : 0, // Structure presence
      pages: pageCount > 0 ? 0.3 : 0                      // Page detection
    };
    
    return Math.round((factors.length + factors.structure + factors.pages) * 100);
  }
}

// Export singleton instance
export const unifiedPDFExtractor = new UnifiedPDFExtractor();
