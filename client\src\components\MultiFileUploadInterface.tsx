import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Upload, FileText, X, GripVertical, AlertTriangle, CheckCircle2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

// File type classifications for multi-file RFQ processing
export type FileType = 'main' | 'drawings' | 'specifications' | 'addendum' | 'supporting';

export interface FileWithMetadata {
  file: File;
  id: string;
  fileType: FileType;
  sequence: number;
  validated: boolean;
  validationErrors: string[];
}

interface MultiFileUploadInterfaceProps {
  files: FileWithMetadata[];
  onFilesChange: (files: FileWithMetadata[]) => void;
  onUpload: (files: FileWithMetadata[]) => void;
  isProcessing: boolean;
  maxFiles?: number;
}

const FILE_TYPE_LABELS: Record<FileType, string> = {
  main: 'Main RFQ Document',
  drawings: 'Drawings/Plans',
  specifications: 'Specifications',
  addendum: 'Addendum/Changes',
  supporting: 'Supporting Documents'
};

const FILE_TYPE_DESCRIPTIONS: Record<FileType, string> = {
  main: 'Primary RFQ document with project details',
  drawings: 'Architectural or engineering drawings',
  specifications: 'Technical specifications and requirements',
  addendum: 'Changes, updates, or clarifications',
  supporting: 'Additional reference materials'
};

export function MultiFileUploadInterface({
  files,
  onFilesChange,
  onUpload,
  isProcessing,
  maxFiles = 8
}: MultiFileUploadInterfaceProps) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  // Auto-detect file type based on filename patterns
  const detectFileType = useCallback((fileName: string): FileType => {
    const name = fileName.toLowerCase();
    
    if (name.includes('rfq') || name.includes('request') || name.includes('quote')) {
      return 'main';
    }
    if (name.includes('drawing') || name.includes('plan') || name.includes('dwg') || name.includes('blueprint')) {
      return 'drawings';
    }
    if (name.includes('spec') || name.includes('requirement') || name.includes('technical')) {
      return 'specifications';
    }
    if (name.includes('addendum') || name.includes('change') || name.includes('update')) {
      return 'addendum';
    }
    
    return 'supporting';
  }, []);

  // Validate file
  const validateFile = useCallback((file: File): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    // File size validation (250MB limit)
    if (file.size > 250 * 1024 * 1024) {
      errors.push('File size exceeds 250MB limit');
    }
    
    // File type validation
    const allowedTypes = ['.pdf', '.txt', '.csv'];
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
      errors.push('Only PDF, TXT, and CSV files are supported');
    }
    
    return { valid: errors.length === 0, errors };
  }, []);

  // Handle file selection
  const handleFileSelect = useCallback((selectedFiles: FileList) => {
    const newFiles: FileWithMetadata[] = [];
    
    Array.from(selectedFiles).forEach((file, index) => {
      if (files.length + newFiles.length >= maxFiles) return;
      
      const validation = validateFile(file);
      const fileType = detectFileType(file.name);
      
      newFiles.push({
        file,
        id: `${Date.now()}-${index}`,
        fileType,
        sequence: files.length + newFiles.length + 1,
        validated: validation.valid,
        validationErrors: validation.errors
      });
    });
    
    onFilesChange([...files, ...newFiles]);
  }, [files, maxFiles, validateFile, detectFileType, onFilesChange]);

  // Handle drag and drop reordering
  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    
    if (draggedIndex === null) return;
    
    const reorderedFiles = [...files];
    const [draggedFile] = reorderedFiles.splice(draggedIndex, 1);
    reorderedFiles.splice(dropIndex, 0, draggedFile);
    
    // Update sequence numbers
    const updatedFiles = reorderedFiles.map((file, index) => ({
      ...file,
      sequence: index + 1
    }));
    
    onFilesChange(updatedFiles);
    setDraggedIndex(null);
  };

  // Update file type
  const updateFileType = (fileId: string, newType: FileType) => {
    const updatedFiles = files.map(file =>
      file.id === fileId ? { ...file, fileType: newType } : file
    );
    onFilesChange(updatedFiles);
  };

  // Remove file
  const removeFile = (fileId: string) => {
    const updatedFiles = files
      .filter(file => file.id !== fileId)
      .map((file, index) => ({ ...file, sequence: index + 1 }));
    onFilesChange(updatedFiles);
  };

  // Check if upload is ready
  const isUploadReady = files.length > 0 && files.every(f => f.validated) && !isProcessing;
  const hasMainDocument = files.some(f => f.fileType === 'main');

  return (
    <div className="space-y-6">
      {/* File Drop Zone */}
      <Card className="border-2 border-dashed border-muted-foreground/25 hover:border-primary/50 transition-colors">
        <CardContent className="p-8">
          <div className="text-center">
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">Upload Multiple RFQ Documents</p>
            <p className="text-sm text-muted-foreground mb-4">
              Select up to {maxFiles} files (PDF, TXT, CSV - up to 250MB each). Files will be processed in priority order.
            </p>
            
            <input
              type="file"
              multiple
              accept=".pdf,.csv,.txt"
              onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
              className="hidden"
              id="multi-file-upload"
              disabled={isProcessing || files.length >= maxFiles}
            />
            
            <Button
              type="button"
              variant="outline"
              onClick={() => document.getElementById('multi-file-upload')?.click()}
              disabled={isProcessing || files.length >= maxFiles}
            >
              {files.length === 0 ? 'Choose Files' : 'Add More Files'}
            </Button>
            
            {files.length >= maxFiles && (
              <p className="text-sm text-amber-600 mt-2">
                Maximum of {maxFiles} files reached
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* File List with Reordering */}
      {files.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Selected Files ({files.length})</h3>
              {!hasMainDocument && (
                <div className="flex items-center gap-2 text-amber-600">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">No main RFQ document identified</span>
                </div>
              )}
            </div>
            
            <div className="space-y-3">
              {files.map((fileItem, index) => (
                <div
                  key={fileItem.id}
                  className={`flex items-center gap-3 p-3 border rounded-lg transition-colors ${
                    draggedIndex === index ? 'bg-muted/50' : 'hover:bg-muted/25'
                  }`}
                  draggable
                  onDragStart={() => handleDragStart(index)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, index)}
                >
                  {/* Drag Handle */}
                  <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                  
                  {/* Sequence Number */}
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center font-medium">
                    {fileItem.sequence}
                  </div>
                  
                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 flex-shrink-0" />
                      <span className="font-medium truncate">{fileItem.file.name}</span>
                      <span className="text-sm text-muted-foreground">
                        ({(fileItem.file.size / (1024 * 1024)).toFixed(1)} MB)
                      </span>
                    </div>
                    
                    {!fileItem.validated && (
                      <div className="flex items-center gap-1 mt-1 text-sm text-red-600">
                        <AlertTriangle className="h-3 w-3" />
                        <span>{fileItem.validationErrors.join(', ')}</span>
                      </div>
                    )}
                  </div>
                  
                  {/* File Type Selector */}
                  <div className="flex-shrink-0 w-48">
                    <Select
                      value={fileItem.fileType}
                      onValueChange={(value: FileType) => updateFileType(fileItem.id, value)}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(FILE_TYPE_LABELS).map(([value, label]) => (
                          <SelectItem key={value} value={value}>
                            <div>
                              <div className="font-medium">{label}</div>
                              <div className="text-xs text-muted-foreground">
                                {FILE_TYPE_DESCRIPTIONS[value as FileType]}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Status Badge */}
                  <div className="flex-shrink-0">
                    {fileItem.validated ? (
                      <Badge variant="outline" className="text-green-600 border-green-200">
                        <CheckCircle2 className="h-3 w-3 mr-1" />
                        Valid
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-red-600 border-red-200">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Invalid
                      </Badge>
                    )}
                  </div>
                  
                  {/* Remove Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(fileItem.id)}
                    className="flex-shrink-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
            
            {/* Processing Order Info */}
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-950/20 dark:border-blue-800">
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                Processing Order
              </p>
              <p className="text-xs text-blue-700 dark:text-blue-300">
                Files will be processed in sequence: Main RFQ → Specifications → Drawings → Addendums → Supporting.
                Drag files to reorder within each category.
              </p>
            </div>
            
            {/* Upload Button */}
            <div className="mt-4 flex justify-end">
              <Button
                onClick={() => onUpload(files)}
                disabled={!isUploadReady}
                className="px-6"
              >
                {isProcessing ? 'Processing...' : `Upload ${files.length} Files`}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}