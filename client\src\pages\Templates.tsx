
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Download, 
  Eye, 
  CheckCircle, 
  AlertTriangle, 
  Info,
  BookOpen,
  Target,
  Clock,
  DollarSign,
  Shield
} from "lucide-react";
import { useState } from "react";
import { StaticPDFViewer } from "@/components/StaticPDFViewer";

export default function Templates() {
  const [showPDFViewer, setShowPDFViewer] = useState(false);
  
  const templateFile = {
    name: "RFQ #2021-301 Response - ACCENT ELECTRICAL.pdf",
    path: "/attached_assets/" + encodeURIComponent("RFQ #2021-301 Response - ACCENT ELECTRICAL_1753707304170.pdf"),
    description: "Professional electrical contractor bid response template",
    category: "Electrical"
  };

  const bestPractices = [
    {
      icon: Target,
      title: "Clear Project Understanding",
      description: "Demonstrate thorough comprehension of the project scope, requirements, and specifications.",
      tips: [
        "Reference specific RFQ sections in your response",
        "Ask clarifying questions if anything is unclear",
        "Highlight any assumptions you're making"
      ]
    },
    {
      icon: DollarSign,
      title: "Detailed Cost Breakdown",
      description: "Provide transparent, itemized pricing that justifies your bid amount.",
      tips: [
        "Break down costs by labor, materials, and equipment",
        "Include any applicable taxes and fees",
        "Include a table that shows what is included and excluded",
        "Explain any contingency allowances",
        "Use MASTER COST CODES or Procore codes if applicable (provided below)"
      ]
    },
    {
      icon: Clock,
      title: "Realistic Timeline",
      description: "Present a detailed schedule that accounts for all project phases and potential delays.",
      tips: [
        "Include mobilization and demobilization time",
        "Account for material delivery times",
        "Consider weather and seasonal factors",
        "Build in buffer time for inspections and approvals"
      ]
    },
    {
      icon: Shield,
      title: "Compliance & Qualifications",
      description: "Demonstrate your ability to meet all regulatory and safety requirements.",
      tips: [
        "Include all required licenses and certifications",
        "Provide insurance documentation",
        "Reference relevant safety protocols",
        "Highlight past experience with similar projects"
      ]
    },
    {
      icon: FileText,
      title: "Professional Documentation",
      description: "Submit a well-organized, professional bid package that's easy to evaluate.",
      tips: [
        "Use consistent formatting and branding",
        "Include a clear executive summary",
        "Organize documents in logical order",
        "Proofread for errors and clarity"
      ]
    }
  ];

  const commonMistakes = [
    "Failing to address all RFQ requirements",
    "Submitting incomplete or missing documentation",
    "Unrealistic pricing (too high or suspiciously low)",
    "Vague or generic project descriptions",
    "Missing required certifications or licenses",
    "Poor presentation and formatting",
    "Late submission after deadline"
  ];

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = templateFile.path;
    link.download = templateFile.name;
    link.click();
  };

  const handleViewPDF = () => {
    // Open PDF in new tab for better viewing experience
    window.open(templateFile.path, '_blank');
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-foreground">RFQ Bid Templates & Best Practices</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Improve your odds of successful bidding with our guide and templates. 
          Review industry best practices and access templates to increase your win rate.
        </p>
      </div>

      <Separator />

      {/* Best Practices Section */}
      <section className="space-y-6">
        <div className="flex items-center gap-2">
          <BookOpen className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">Best Practices for RFQ Responses</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {bestPractices.map((practice, index) => (
            <Card key={index} className="h-full">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <practice.icon className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">{practice.title}</CardTitle>
                </div>
                <CardDescription>{practice.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {practice.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{tip}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      <Separator />

      {/* Common Mistakes Section */}
      <section className="space-y-6">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-6 w-6 text-amber-500" />
          <h2 className="text-2xl font-bold">Common Mistakes to Avoid</h2>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Pitfalls That Can Cost You the Contract</CardTitle>
            <CardDescription>
              Avoid these common mistakes that lead to rejected bids or missed opportunities.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {commonMistakes.map((mistake, index) => (
                <div key={index} className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{mistake}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </section>

      <Separator />

      {/* Templates Section */}
      <section className="space-y-6">
        <div className="flex items-center gap-2">
          <FileText className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">Sample Templates</h2>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Professional Bid Response Template
            </CardTitle>
            <CardDescription>
              Download or preview our sample electrical contractor bid response to see best practices in action.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-red-500" />
                <div>
                  <h3 className="font-medium">{templateFile.name}</h3>
                  <p className="text-sm text-muted-foreground">{templateFile.description}</p>
                  <Badge variant="secondary" className="mt-1">{templateFile.category}</Badge>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleViewPDF}>
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
            
            <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">Template Features</h4>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 mt-2 space-y-1">
                    <li>• Professional letterhead and formatting</li>
                    <li>• Detailed cost breakdown and labor estimates</li>
                    <li>• Timeline with milestones and deliverables</li>
                    <li>• Required certifications and insurance documentation</li>
                    <li>• Terms and conditions clearly outlined</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Master Cost Codes Reference
            </CardTitle>
            <CardDescription>
              Comprehensive Master Cost Code reference including Procore codes for accurate bid formatting.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-green-500" />
                <div>
                  <h3 className="font-medium">consolidated_cost_codes.csv</h3>
                  <p className="text-sm text-muted-foreground">Master construction cost codes with Procore mapping</p>
                  <Badge variant="secondary" className="mt-1">Cost Reference</Badge>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => window.open('/attached_assets/consolidated_cost_codes_1753708871258.csv', '_blank')}>
                  <Eye className="h-4 w-4 mr-2" />
                  View CSV
                </Button>
                <Button onClick={() => {
                  const link = document.createElement('a');
                  link.href = '/attached_assets/consolidated_cost_codes_1753708871258.csv';
                  link.download = 'consolidated_cost_codes.csv';
                  link.click();
                }}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
            
            <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900 dark:text-green-100">Cost Code Features</h4>
                  <ul className="text-sm text-green-800 dark:text-green-200 mt-2 space-y-1">
                    <li>• Master construction cost codes organized by trade</li>
                    <li>• Procore codes included</li>
                    <li>• Detailed descriptions for accurate cost categorization</li>
                    <li>• Compatible with most construction management systems</li>
                    </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      <Separator />

      {/* Success Tips Section */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold">Keys to Winning Bids</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Before You Bid</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">Research the client and project thoroughly</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">Visit the site if possible for accurate estimates</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">Verify you meet all qualification requirements</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">Plan your pricing strategy carefully</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">During Submission</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">Submit well before the deadline</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">Double-check all requirements are met</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">Include all required supporting documents</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">Follow up to confirm receipt</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      
    </div>
  );
}
