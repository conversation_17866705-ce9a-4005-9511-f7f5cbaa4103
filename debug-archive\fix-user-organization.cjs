// Quick fix to create organization and associate user
console.log('🔧 Creating organization and associating user...');
console.log('');
console.log('📋 From the screenshot, I can see:');
console.log('• User: <PERSON> (<EMAIL>)');
console.log('• User ID: user_319LPVLDMzru79fF2TZXTX5xZ6i');
console.log('• Status: Authenticated but no organization');
console.log('');
console.log('✅ SOLUTION:');
console.log('1. Fill out the "Create organization" form in your browser');
console.log('2. Name: "Roy Gatling Construction" (or your preferred name)');
console.log('3. Slug: "roy-gatling-construction" (or leave default)');
console.log('4. Click "Create organization"');
console.log('');
console.log('🎉 After creating the organization:');
console.log('• Your user will be associated with the new organization');
console.log('• The organizationId will be set in the database');
console.log('• /api/rfqs will return your uploaded RFQs');
console.log('• RFQs will appear in Dashboard and My RFQs pages');
console.log('');
console.log('🔍 The fix I made to the middleware was correct, but the real issue');
console.log('   was the missing organizationId. Once you create an organization,');
console.log('   everything should work perfectly!');
