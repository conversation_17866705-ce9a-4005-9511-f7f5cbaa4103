# Multi-tenant Privacy Test Plan (Clerk Orgs + GC/Contractor)

Goals
- Verify that General Contractors (GCs) can only see RFQs within their organization, and bids for those RFQs.
- Verify that Contractors can see all active RFQs but only their own bids.
- Verify ID enumeration is blocked for GCs across endpoints: rfq-by-id, rfq documents, file downloads, distributions, analytics.

Prerequisites
- Two Clerk users in two different organizations: GC_A (Org A) and GC_B (Org B)
- One Contractor user (Contractor_X) with a contractor profile (server will require it to submit bids)
- API keys created for each user (or use browser session cookies). You can use `tests/create-test-api-key.js` pattern if available per user.

Environment
- Base URL: http://localhost:5000

## 1) GC RFQ visibility is org-scoped

As GC_A:
- GET /api/rfqs → should only contain RFQs where `organizationId === OrgA`

As GC_B:
- GET /api/rfqs → should only contain RFQs where `organizationId === OrgB`

## 2) GC RFQ access by id is org-scoped

Create RFQ as GC_A (UI or POST /api/rfqs). Capture `rfqA.id` and an `rfqA.documentId` from `/api/rfqs/:id/documents`.

As GC_B:
- GET /api/rfqs/:id (using `rfqA.id`) → 403
- GET /api/rfqs/:id/documents → 403
- GET /api/files/:documentId → 403

## 3) RFQ distributions are owner-only

As GC_A (owner):
- POST /api/rfqs/:rfqId/distribute with a list of contractorIds → 201
- GET /api/rfqs/:rfqId/distributions → 200

As GC_B:
- POST /api/rfqs/:rfqId/distribute (rfq from Org A) → 403
- GET /api/rfqs/:rfqId/distributions (rfq from Org A) → 403

## 4) Analytics endpoints enforce RFQ access

As GC_A:
- GET /api/analytics/competitive-intelligence/:rfqId (Org A RFQ) → 200
- GET /api/analytics/competitive-intelligence/:rfqId (Org B RFQ) → 403
- GET /api/analytics/predictive-analytics/:rfqId same behavior
- GET /api/analytics/bid-analysis/:rfqId same behavior

## 5) Contractor RFQ and bid access

As Contractor_X:
- GET /api/contractors/rfqs/all → returns active RFQs from all orgs
- POST /api/rfqs/:rfqId/bids (submit a bid) → 201 (requires contractor profile)

Bid privacy:
- GET /api/rfqs/:rfqId/bids (not RFQ owner) → returns only Contractor_X bids for that RFQ
- GET /api/bids/:bidId, /api/bids/:bidId/documents, /api/bids/:bidId/structured-data → 200 for own bids; 403 for other contractors’ bids

## 6) Onboarding classification persists

- PATCH /api/auth/user { userClassification: 'contractor' } → 200 and value persists on GET /api/auth/user
- Switch to 'general_contractor' and verify /api/rfqs scoping flips to org-only

## cURL examples

Replace TOKEN with a valid API key or use browser session.

List RFQs (GC):
```
curl -H "Authorization: Bearer TOKEN" http://localhost:5000/api/rfqs
```

RFQ by id (should be 403 for cross-org GC access):
```
curl -i -H "Authorization: Bearer TOKEN" http://localhost:5000/api/rfqs/RFQ_ID
```

RFQ documents (403 for cross-org GC access):
```
curl -i -H "Authorization: Bearer TOKEN" http://localhost:5000/api/rfqs/RFQ_ID/documents
```

File download (403 for cross-org GC access):
```
curl -i -H "Authorization: Bearer TOKEN" "http://localhost:5000/api/files/DOC_ID?view=true"
```

Distribute (owner-only):
```
curl -i -X POST -H "Authorization: Bearer TOKEN" -H "Content-Type: application/json" \
  -d '{"contractorIds":["..."],"method":"broadcast"}' \
  http://localhost:5000/api/rfqs/RFQ_ID/distribute
```

Contractor all RFQs:
```
curl -H "Authorization: Bearer TOKEN" http://localhost:5000/api/contractors/rfqs/all
```

Set classification:
```
curl -X PATCH -H "Authorization: Bearer TOKEN" -H "Content-Type: application/json" \
  -d '{"userClassification":"contractor"}' \
  http://localhost:5000/api/auth/user
```
