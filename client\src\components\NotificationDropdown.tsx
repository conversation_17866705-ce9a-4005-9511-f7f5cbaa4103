import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Bell, FileText, Users, AlertCircle, CheckCircle2, Clock, Upload, Calendar, Settings, Trash2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useLocation } from "wouter";
import { useUnreadNotifications, useMarkNotificationRead, useMarkAllNotificationsRead, type Notification } from "@/hooks/useNotifications";

export function NotificationDropdown() {
  const [, setLocation] = useLocation();
  const { data: unreadData, isLoading } = useUnreadNotifications();
  const markAsReadMutation = useMarkNotificationRead();
  const markAllAsReadMutation = useMarkAllNotificationsRead();
  
  const notifications = unreadData?.notifications || [];
  const unreadCount = unreadData?.count || 0;
  
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "bid_submitted":
        return <FileText className="h-4 w-4 text-green-600" />;
      case "bid_accepted":
        return <CheckCircle2 className="h-4 w-4 text-green-700" />;
      case "bid_rejected":
        return <Trash2 className="h-4 w-4 text-red-600" />;
      case "bid_request_info":
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case "rfq_uploaded":
        return <Upload className="h-4 w-4 text-blue-600" />;
      case "rfq_distributed":
        return <Users className="h-4 w-4 text-purple-600" />;
      case "rfq_closed":
        return <Clock className="h-4 w-4 text-gray-600" />;
      case "rfq_deadline_reminder":
        return <Calendar className="h-4 w-4 text-orange-600" />;
      case "system_maintenance":
        return <Settings className="h-4 w-4 text-blue-500" />;
      case "account_update":
        return <Settings className="h-4 w-4 text-purple-500" />;
      // Legacy support for old mock types
      case "bid_received":
        return <FileText className="h-4 w-4 text-green-600" />;
      case "rfq_due_soon":
        return <Clock className="h-4 w-4 text-orange-600" />;
      case "document_uploaded":
        return <FileText className="h-4 w-4 text-blue-600" />;
      case "contractor_approved":
        return <Users className="h-4 w-4 text-purple-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };
  
  const getPriorityColor = (priority: "low" | "medium" | "high" | "urgent") => {
    switch (priority) {
      case "urgent":
        return "border-l-red-600";
      case "high":
        return "border-l-red-500";
      case "medium":
        return "border-l-orange-500";
      case "low":
        return "border-l-blue-500";
      default:
        return "border-l-gray-500";
    }
  };
  
  const handleNotificationClick = (notification: Notification) => {
    // Mark as read
    if (!notification.readAt) {
      markAsReadMutation.mutate(notification.id);
    }
    
    // Navigate to relevant page based on notification data
    if (notification.data) {
      const { rfqId, bidId } = notification.data;
      
      if (rfqId && bidId) {
        // Navigate to bid details
        setLocation(`/rfqs/${rfqId}/bids`);
      } else if (rfqId) {
        // Navigate to RFQ details
        setLocation(`/rfq/${rfqId}`);
      }
    }
  };
  
  const handleMarkAllAsRead = () => {
    markAllAsReadMutation.mutate();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative" disabled={isLoading}>
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80 max-h-96 overflow-y-auto">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleMarkAllAsRead}
              disabled={markAllAsReadMutation.isPending}
              className="text-xs"
            >
              {markAllAsReadMutation.isPending ? "Marking..." : "Mark all read"}
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {isLoading ? (
          <div className="p-4 text-center text-muted-foreground">
            <Bell className="h-8 w-8 mx-auto mb-2 opacity-50 animate-pulse" />
            <p className="text-sm">Loading notifications...</p>
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No unread notifications</p>
          </div>
        ) : (
          <div className="space-y-1">
            {notifications.map((notification) => (
              <DropdownMenuItem 
                key={notification.id}
                className="p-0 focus:bg-transparent cursor-pointer"
                onClick={() => handleNotificationClick(notification)}
              >
                <Card className={`w-full border-l-4 ${getPriorityColor(notification.priority)} ${!notification.readAt ? 'bg-muted/50' : ''}`}>
                  <CardContent className="p-3">
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">
                            {notification.title}
                          </p>
                          {!notification.readAt && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full" />
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {notification.message}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </DropdownMenuItem>
            ))}
          </div>
        )}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          className="text-center text-sm text-muted-foreground cursor-pointer"
          onClick={() => setLocation("/notifications")}
        >
          <Button variant="ghost" size="sm" className="w-full">
            View All Notifications
          </Button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}