import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { FileText, Users, TrendingUp, Shield, Mail, Phone, MessageCircle, Code, Key, Database, ChevronDown, ExternalLink, Copy, Check, Info, Send, Clock } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

export default function HelpSupport() {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [sendingEmail, setSendingEmail] = useState(false);
  const { toast } = useToast();

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text);
    setCopiedCode(id);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const sendTestEmail = async () => {
    setSendingEmail(true);
    try {
      const response = await apiRequest(
        'POST',
        '/api/notifications/send-custom-email',
        {
          message: "hello from Bidaible, the best way to manage construction bidding"
        }
      );

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Test Email Sent!",
          description: `Email successfully sent to ${result.email}`,
        });
      } else {
        throw new Error(result.error || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      toast({
        title: "Email Failed",
        description: "Failed to send test email. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSendingEmail(false);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-foreground">Help & Support</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Welcome to Bidaible's help center. Find answers to common questions, learn how to use our platform effectively, and discover tips to maximize your bidding success.
        </p>
      </div>

      {/* Quick Contact */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Need Immediate Help?
          </CardTitle>
          <CardDescription>
            Contact our support team for personalized assistance
          </CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-primary" />
            <span><EMAIL></span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-primary" />
            <span>1-800-BIDAIBLE</span>
          </div>
          <div className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4 text-primary" />
            <span>Live Chat (9 AM - 6 PM EST)</span>
          </div>
        </CardContent>
      </Card>

      {/* Test Email System */}
      <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-300">
            <Send className="h-5 w-5" />
            Test Email System
          </CardTitle>
          <CardDescription className="text-green-600 dark:text-green-400">
            Test the Bidaible notification system by sending yourself a test email
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <p className="text-green-800 dark:text-green-200 mb-3">
                <strong>Test Message:</strong> "hello from Bidaible, the best way to manage construction bidding"
              </p>
              <Button 
                onClick={sendTestEmail} 
                disabled={sendingEmail}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {sendingEmail ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Send Test Email
                  </>
                )}
              </Button>
            </div>
            <p className="text-sm text-green-700 dark:text-green-300">
              This will send a test email to your registered email address to verify the notification system is working properly.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Critical: User Classification */}
      <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-700 dark:text-orange-300">
            <Info className="h-5 w-5" />
            IMPORTANT: User Classification Required
          </CardTitle>
          <CardDescription className="text-orange-600 dark:text-orange-400">
            Your sidebar navigation is customized based on your role. Proper classification is essential for optimal platform experience.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
            <h3 className="font-semibold text-orange-800 dark:text-orange-200 mb-2">
              How Classification Affects Your Experience:
            </h3>
            <ul className="list-disc list-inside space-y-2 text-orange-700 dark:text-orange-300">
              <li><strong>General Contractors:</strong> Get access to RFQ creation, contractor management, and project oversight tools</li>
              <li><strong>All Other Classifications:</strong> See contractor-focused interface with bid opportunities and submission tools</li>
              <li><strong>Shared Access:</strong> Both roles access Resources (Templates) and Administration (Settings, Support)</li>
            </ul>
          </div>
          <div className="flex items-start gap-3 p-4 bg-white dark:bg-gray-900 rounded-lg border border-orange-200 dark:border-orange-800">
            <Shield className="h-5 w-5 text-orange-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-semibold text-orange-800 dark:text-orange-200">To Set Your Classification:</h4>
              <p className="text-orange-700 dark:text-orange-300 text-sm mt-1">
                Navigate to <Badge variant="outline" className="mx-1">Settings</Badge> → <Badge variant="outline" className="mx-1">Profile Information</Badge> 
                → <Badge variant="outline" className="mx-1">Classification & Capabilities</Badge> and select your primary business role.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Getting Started */}
      <Card>
        <CardHeader>
          <CardTitle>Getting Started</CardTitle>
          <CardDescription>Learn the basics of using Bidaible effectively</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">Creating Your First RFQ</h3>
            <ol className="list-decimal list-inside space-y-2 text-muted-foreground">
              <li>Click the <span className="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground">Create RFQ</span> button in the sidebar</li>
              <li>Fill in the basic project information (project name, location)</li>
              <li>Set dual deadlines: bid proposal deadline and RFQ project deadline (with validation)</li>
              <li>Upload your project documents (plans, specifications, etc.) - up to 8 files, 250MB each</li>
              <li>Our enhanced AI will automatically extract key details and generate comprehensive summaries</li>
              <li>Review and edit the extracted information</li>
              <li>Select contractors to invite or broadcast to all qualified contractors</li>
              <li>Click <span className="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground">Submit RFQ</span> to send invitations (notifications automatically scheduled)</li>
            </ol>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">RFQ Deadline Management System</h3>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400 mb-2">🕒 Dual Deadline System</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Bidaible manages two crucial deadlines with automatic validation and countdown timers.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>Bid Proposal Deadline:</strong> When contractors must submit their bids</li>
                  <li><strong>RFQ Project Deadline:</strong> Overall project completion deadline</li>
                  <li><strong>Smart Validation:</strong> System ensures bid deadline precedes project deadline</li>
                </ul>
              </div>

              <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">📅 Automated Notifications</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Automatic reminder system keeps everyone informed about approaching deadlines.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>1 Week Before:</strong> Advance notice to prepare bids</li>
                  <li><strong>3 Days Before:</strong> Urgent reminder for final preparations</li>
                  <li><strong>1 Day Before:</strong> Last chance notification</li>
                  <li><strong>Real-time Processing:</strong> Background service checks every minute</li>
                </ul>
              </div>

              <div className="bg-purple-50 dark:bg-purple-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-700 dark:text-purple-400 mb-2">🎨 Visual Countdown Timers</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Color-coded countdown displays provide instant deadline awareness throughout the platform.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>Green (Safe):</strong> More than 3 days remaining</li>
                  <li><strong>Yellow (Warning):</strong> 1-3 days remaining</li>
                  <li><strong>Red (Urgent):</strong> Less than 24 hours remaining</li>
                  <li><strong>Gray (Expired):</strong> Deadline has passed</li>
                </ul>
              </div>

              <div className="bg-indigo-50 dark:bg-indigo-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-indigo-700 dark:text-indigo-400 mb-2">🔔 Real-Time Notification Bell</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Live notification system keeps you informed with instant updates and easy navigation.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>Always Visible:</strong> Bell icon in main navigation with unread count badge</li>
                  <li><strong>Live Updates:</strong> Polls for new notifications every 30-60 seconds</li>
                  <li><strong>Click Navigation:</strong> Click any notification to go directly to relevant RFQ/bid</li>
                  <li><strong>Mark as Read:</strong> Individual and batch operations with instant UI updates</li>
                  <li><strong>Full Management:</strong> Dedicated notifications page with tabbed interface</li>
                </ul>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Enhanced AI Document Processing</h3>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400 mb-2">🎯 Main File Priority Processing</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Our AI system focuses on your main RFQ document for comprehensive extraction while supporting files contribute supplemental data.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>Main RFQ Files:</strong> Get complete AI analysis with comprehensive summaries</li>
                  <li><strong>Supporting Files:</strong> Drawings, specifications, and addenda contribute additional requirements</li>
                  <li><strong>File Classification:</strong> Automatic detection of file types for intelligent processing</li>
                </ul>
              </div>

              <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">🏗️ Construction-Specific AI Analysis</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Professional construction project analyst with industry-specific prompts and terminology.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>Project Scope Analysis:</strong> Detailed construction scope, trade types, and complexity assessment</li>
                  <li><strong>Requirements Extraction:</strong> Critical requirements, specifications, and qualifications</li>
                  <li><strong>Timeline Intelligence:</strong> Project phases, milestones, and completion requirements</li>
                  <li><strong>Professional Insights:</strong> Risk assessment, challenges, and contractor opportunities</li>
                </ul>
              </div>

              <div className="bg-purple-50 dark:bg-purple-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-700 dark:text-purple-400 mb-2">📋 Comprehensive AI Summaries</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Professional markdown-formatted summaries with detailed project analysis and insights.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>Project Overview:</strong> Complete project details, location, and key dates</li>
                  <li><strong>Scope of Work:</strong> Detailed construction scope and trade categories involved</li>
                  <li><strong>Submission Requirements:</strong> Bid submission details, documents, and deadlines</li>
                  <li><strong>Contact Information:</strong> Primary contacts and organization details</li>
                  <li><strong>Professional Analysis:</strong> Project complexity, potential challenges, and strategic insights</li>
                </ul>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Managing Contractor Profiles</h3>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
              <li>Navigate to the <span className="inline-flex items-center rounded-md border px-2 py-1 text-xs font-semibold">Contractors</span> section</li>
              <li>Review contractor profiles, licenses, and experience</li>
              <li>Add contractors to your favorites for easy access</li>
              <li>Manage contractor verification status and notes</li>
            </ul>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">AI-Powered Bid Analysis</h3>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
              <li>Access the <span className="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground">AI Analysis</span> tab in the Bid Management Dashboard</li>
              <li>Get instant AI-generated executive summaries of your bidding landscape</li>
              <li>Review competitive scoring and ranking with detailed reasoning</li>
              <li>Identify key insights, recommendations, and risk factors automatically</li>
              <li>View market analysis with price spreads and competitive positioning</li>
              <li>Analysis powered by leading AI models for rapid 3-second generation</li>
            </ul>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Enhanced Bid Submission Process</h3>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
              <li><strong>5-Tab Structured Interface:</strong> Contact Info → Line Items → Scope Definition → Documents → Summary</li>
              <li><strong>Consolidated Cost Codes:</strong> Access to 760+ unique cost codes from your consolidated master list</li>
              <li><strong>Simplified Pricing:</strong> Total Bid Amount equals Line Items Total (no markup calculations)</li>
              <li><strong>Detailed Line Items:</strong> Cost code breakdown with quantities, unit prices, and categories</li>
              <li><strong>Scope Definition:</strong> Clearly specify included and excluded items with descriptions</li>
              <li><strong>Document Upload:</strong> Attach supporting documents with your bid submission</li>
              <li><strong>Real-time Totals:</strong> Automatic calculation as you enter line item data</li>
            </ul>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Contractor Performance Analytics</h3>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
              <li><strong>Performance Dashboard:</strong> Navigate to <span className="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground">Analytics</span> to view your contractor performance metrics</li>
              <li><strong>Key Performance Indicators:</strong> Track total bids submitted, win rate percentage, and average bid amounts</li>
              <li><strong>Real-time Calculations:</strong> Average bid amount calculated from ALL submitted bids for accurate performance tracking</li>
              <li><strong>Monthly Activity Trends:</strong> 6-month historical data showing bid submissions, wins, and earnings over time</li>
              <li><strong>Status Distribution:</strong> Visual breakdown of your bid statuses (Accepted, Pending, Rejected) with color coding</li>
              <li><strong>Recent Bid Activity:</strong> Latest bid information with actual amounts, status, and submission dates</li>
              <li><strong>Earnings Tracking:</strong> Total confirmed earnings from accepted bids and potential revenue calculations</li>
              <li><strong>Interactive Charts:</strong> Bar charts, line graphs, and pie charts powered by Recharts for professional visualization</li>
            </ul>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Tracking Bids and Responses</h3>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
              <li>Monitor bid submissions in real-time through the dashboard</li>
              <li>View detailed bid responses and extracted pricing information</li>
              <li>Compare bids side-by-side with our analysis tools</li>
              <li>Download bid documents and contractor submissions</li>
            </ul>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">RFQ Archive Management</h3>
            <div className="space-y-4">
              <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-700 dark:text-orange-400 mb-2">📁 Project Archiving</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Archive completed RFQs to organize your projects and create comprehensive project bundles.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>Manual Control:</strong> Archive RFQs when you decide - no automatic archiving</li>
                  <li><strong>Individual Archive:</strong> Click the <Badge variant="outline" className="text-xs mx-1">Archive</Badge> badge in the top-right of any RFQ card</li>
                  <li><strong>Bulk Archive:</strong> Enable bulk select mode to archive multiple RFQs at once</li>
                  <li><strong>Organization Security:</strong> Only your organization can access your archived RFQs</li>
                </ul>
              </div>

              <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400 mb-2">📦 Complete Project Bundles</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Download complete project archives with all RFQ documents and submitted bids organized in folders.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>ZIP Downloads:</strong> Streamlined archive files with structured folder hierarchy</li>
                  <li><strong>RFQ Documents:</strong> All original project documents in `rfq-documents/original/` folder</li>
                  <li><strong>Bid Organization:</strong> Each contractor's bids in separate `bids/contractor-name/` folders</li>
                  <li><strong>Metadata Included:</strong> JSON file with bid summaries, contractor details, and project timeline</li>
                </ul>
              </div>

              <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
                <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">🔒 Archive Security & Privacy</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Archive system maintains security and privacy with comprehensive access controls.
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li><strong>Contractor Protection:</strong> Archived RFQs are completely hidden from contractor views</li>
                  <li><strong>Access Control:</strong> Only RFQ creators and organization admins can archive</li>
                  <li><strong>Audit Trail:</strong> All archive actions logged for compliance and tracking</li>
                  <li><strong>Easy Recovery:</strong> Unarchive RFQs to restore them to active project list</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role-Based Access Control */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            User Roles & Permissions
          </CardTitle>
          <CardDescription>
            Understanding user roles and how they affect your platform experience
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">User Roles</h3>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400">Admin</h4>
                <p className="text-sm text-muted-foreground">Can manage users within your organization, invite new team members, and access all company RFQs and bids.</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-green-700 dark:text-green-400">Standard User</h4>
                <p className="text-sm text-muted-foreground">Create and manage RFQs, submit bids, view company data, and access all core platform features.</p>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">User Management</h3>
            <p className="text-muted-foreground mb-3">Admins can manage team members through the Settings page:</p>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
              <li>Navigate to Settings → User Management</li>
              <li>View all users in your organization</li>
              <li>Invite new team members to join your organization</li>
              <li>Change user roles between Admin and Standard User</li>
              <li>Monitor team size (maximum 15 users per organization)</li>
            </ul>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Data Security</h3>
            <p className="text-muted-foreground mb-3">Your data is protected with enterprise-grade security:</p>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
              <li><strong>Organization Privacy:</strong> You only see data from your own organization</li>
              <li><strong>Secure Access:</strong> All activities are logged for security monitoring</li>
              <li><strong>Data Protection:</strong> Files and information are encrypted and securely stored</li>
              <li><strong>User Activity:</strong> Admins can review user activity through audit logs</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Features Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FileText className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-lg">AI Document Processing</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Automatic data extraction</li>
              <li>• Project detail recognition</li>
              <li>• Deadline identification</li>
              <li>• Specification parsing</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <CardTitle className="text-lg">Contractor Management</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Profile verification</li>
              <li>• Trade categorization</li>
              <li>• Favorites management</li>
              <li>• Performance tracking</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle className="text-lg">Enhanced Bid Submission</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 5-tab structured interface</li>
              <li>• 760+ consolidated cost codes</li>
              <li>• Simplified pricing calculations</li>
              <li>• Real-time total calculations</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <CardTitle className="text-lg">Contractor Performance Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Real-time bidding metrics</li>
              <li>• Win rate and earnings tracking</li>
              <li>• Monthly activity trends</li>
              <li>• Interactive performance charts</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Shield className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <CardTitle className="text-lg">Security & Compliance</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Your data is private and secure</li>
              <li>• Only your team can see your information</li>
              <li>• Files are encrypted and protected</li>
              <li>• Activity is logged for security</li>
              <li>• Clear terms and conditions</li>
              <li>• Industry-standard data protection</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Terms & Conditions System */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Terms & Conditions
          </CardTitle>
          <CardDescription>
            Simple and transparent terms that protect both contractors and project owners
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">What You Need to Know</h3>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400">Clear & Fair Terms</h4>
                <p className="text-sm text-muted-foreground">Our terms are written in plain language and protect both contractors and general contractors equally.</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-green-700 dark:text-green-400">Simple Acceptance</h4>
                <p className="text-sm text-muted-foreground">New users accept terms when signing up. Your acceptance is tracked for legal compliance.</p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-semibold text-purple-700 dark:text-purple-400">Existing Users Protected</h4>
                <p className="text-sm text-muted-foreground">If you were already using Bidaible, you're automatically covered under our updated terms.</p>
              </div>
              <div className="border-l-4 border-orange-500 pl-4">
                <h4 className="font-semibold text-orange-700 dark:text-orange-400">Always Accessible</h4>
                <p className="text-sm text-muted-foreground">You can review our complete terms and conditions anytime from your account settings.</p>
              </div>
            </div>
          </div>
          </CardContent>
      </Card>

      {/* Real-Time Notification System */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Real-Time Notification System
          </CardTitle>
          <CardDescription>
            Comprehensive notification system with email and in-app delivery options
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">Business Workflow Notifications</h3>
            <p className="text-muted-foreground mb-3">Automatic notifications are sent for key business events:</p>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400">RFQ Upload Processing</h4>
                <p className="text-sm text-muted-foreground">Receive confirmation when RFQ documents are processed and AI extraction is complete</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-green-700 dark:text-green-400">New Bid Submissions</h4>
                <p className="text-sm text-muted-foreground">Instant notifications when contractors submit bids on your RFQs</p>
              </div>
              <div className="border-l-4 border-orange-500 pl-4">
                <h4 className="font-semibold text-orange-700 dark:text-orange-400">Bid Status Changes</h4>
                <p className="text-sm text-muted-foreground">Automatic updates when bids are accepted, rejected, or require additional information</p>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Notification Preferences</h3>
            <p className="text-muted-foreground mb-3">Manage your notification settings through Settings → Notification Preferences:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Mail className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="font-semibold">Email Notifications</h4>
                <p className="text-sm text-muted-foreground">Professional email delivery with verified bidaible.com domain</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <MessageCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <h4 className="font-semibold">In-App Notifications</h4>
                <p className="text-sm text-muted-foreground">Real-time alerts within the Bidaible platform</p>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Email System Features</h3>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
              <li><strong>Resend Integration:</strong> Professional email service with enterprise-grade delivery</li>
              <li><strong>Domain Verification:</strong> Authenticated bidaible.com domain for trusted delivery</li>
              <li><strong>Message Tracking:</strong> Complete audit trail with message IDs and delivery status</li>
              <li><strong>Template System:</strong> Professional email templates with personalized content</li>
              <li><strong>Delivery Analytics:</strong> Track email opens, clicks, and engagement metrics</li>
              <li><strong>Error Handling:</strong> Automatic retries and comprehensive error logging</li>
            </ul>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Test Email System</h3>
            <p className="text-muted-foreground mb-3">
              Verify your notification system is working properly:
            </p>
            <div className="flex items-center gap-3">
              <Button 
                onClick={sendTestEmail}
                disabled={sendingEmail}
                className="flex items-center gap-2"
              >
                {sendingEmail ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    Send Test Email
                  </>
                )}
              </Button>
              <div className="text-sm text-muted-foreground">
                This will send a test email to your registered email address to verify the notification system is working properly.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Frequently Asked Questions
          </CardTitle>
          <CardDescription>
            Quick answers to common questions about using Bidaible
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Collapsible>
            <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
              <h3 className="text-lg font-semibold">How do I reset my password?</h3>
              <ChevronDown className="h-4 w-4" />
            </CollapsibleTrigger>
            <CollapsibleContent className="pt-2 text-muted-foreground">
              Password resets are handled through your Replit account. Click the "Forgot Password" link on the login page <NAME_EMAIL> for assistance.
            </CollapsibleContent>
          </Collapsible>

          <Separator />

          <Collapsible>
            <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
              <h3 className="text-lg font-semibold">Why can't I see certain RFQs or contractors?</h3>
              <ChevronDown className="h-4 w-4" />
            </CollapsibleTrigger>
            <CollapsibleContent className="pt-2 text-muted-foreground">
              Bidaible uses organization-based data isolation. You can only see RFQs, contractors, and bids within your own organization. This ensures data privacy and security.
            </CollapsibleContent>
          </Collapsible>

          <Separator />

          <Collapsible>
            <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
              <h3 className="text-lg font-semibold">How do I change my company classification?</h3>
              <ChevronDown className="h-4 w-4" />
            </CollapsibleTrigger>
            <CollapsibleContent className="pt-2 text-muted-foreground">
              Navigate to Settings → Profile Information → Classification & Capabilities. Select "General Contractor" for GC access or any other trade type for contractor access. This affects your sidebar navigation and available features.
            </CollapsibleContent>
          </Collapsible>

          <Separator />

          <Collapsible>
            <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
              <h3 className="text-lg font-semibold">What file types can I upload and what are the limits?</h3>
              <ChevronDown className="h-4 w-4" />
            </CollapsibleTrigger>
            <CollapsibleContent className="pt-2 text-muted-foreground">
              You can upload PDF, TXT, and CSV files. Maximum file size is 250MB per file, and you can upload up to 8 files per RFQ. Our AI system works best with clear, text-based documents.
            </CollapsibleContent>
          </Collapsible>

          <Separator />

          <Collapsible>
            <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
              <h3 className="text-lg font-semibold">How long does AI document processing take?</h3>
              <ChevronDown className="h-4 w-4" />
            </CollapsibleTrigger>
            <CollapsibleContent className="pt-2 text-muted-foreground">
              Most documents are processed within 30-60 seconds. Larger files or multiple files may take up to 3-5 minutes. You'll see real-time progress updates during processing.
            </CollapsibleContent>
          </Collapsible>

          <Separator />

          <Collapsible>
            <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
              <h3 className="text-lg font-semibold">How do I invite contractors to bid on my RFQ?</h3>
              <ChevronDown className="h-4 w-4" />
            </CollapsibleTrigger>
            <CollapsibleContent className="pt-2 text-muted-foreground">
              During RFQ creation, you can either select specific contractors from your favorites list or choose "Broadcast to All Qualified Contractors" to send invitations to all relevant contractors in the system.
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>

      {/* Troubleshooting Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Troubleshooting Common Issues
          </CardTitle>
          <CardDescription>
            Solutions for frequently encountered problems
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">Upload Issues</h3>
            <div className="space-y-3">
              <div className="border-l-4 border-orange-500 pl-4">
                <h4 className="font-semibold text-orange-700 dark:text-orange-400">File Too Large</h4>
                <p className="text-sm text-muted-foreground">Maximum file size is 250MB. Try compressing PDFs or splitting large documents into smaller files.</p>
              </div>
              <div className="border-l-4 border-red-500 pl-4">
                <h4 className="font-semibold text-red-700 dark:text-red-400">Upload Failed</h4>
                <p className="text-sm text-muted-foreground">Check your internet connection and try again. Ensure files are not corrupted and in supported formats (PDF, TXT, CSV).</p>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Browser Issues</h3>
            <div className="space-y-3">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400">Browser Compatibility</h4>
                <p className="text-sm text-muted-foreground">Bidaible works best with Chrome, Firefox, Safari, and Edge. Ensure JavaScript is enabled and your browser is up to date.</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-green-700 dark:text-green-400">Clear Cache</h4>
                <p className="text-sm text-muted-foreground">If experiencing loading issues, try clearing your browser cache and cookies, then refresh the page.</p>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Feature Access Issues</h3>
            <div className="space-y-3">
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-semibold text-purple-700 dark:text-purple-400">Missing Features</h4>
                <p className="text-sm text-muted-foreground">Feature access depends on your user role and company classification. Check Settings → Profile Information to verify your settings.</p>
              </div>
              <div className="border-l-4 border-indigo-500 pl-4">
                <h4 className="font-semibold text-indigo-700 dark:text-indigo-400">Can't Create RFQs</h4>
                <p className="text-sm text-muted-foreground">Only users classified as "General Contractor" can create RFQs. Update your classification in Settings if this is incorrect.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Best Practices Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Best Practices & Templates
          </CardTitle>
          <CardDescription>
            Tips for success and access to helpful templates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-semibold mb-3 text-blue-700 dark:text-blue-300">Professional Templates Available</h3>
            <p className="text-muted-foreground mb-3">
              Access professional templates and best practice guides through the Templates section in your sidebar.
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>RFQ creation templates with industry-standard formats</li>
              <li>Bid submission templates with proper cost code structure</li>
              <li>Contractor profile optimization guides</li>
              <li>Project documentation best practices</li>
              <li>Email templates for contractor communications</li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">Quick Tips for Success</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <h4 className="font-semibold text-green-700 dark:text-green-400">For General Contractors:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>Include detailed project specifications in RFQ uploads</li>
                  <li>Set realistic deadlines to attract quality bids</li>
                  <li>Use contractor favorites to build trusted networks</li>
                  <li>Review AI analysis for comprehensive bid insights</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400">For Contractors:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>Complete your profile with certifications and experience</li>
                  <li>Submit detailed line-item bids with proper cost codes</li>
                  <li>Clearly define scope inclusions and exclusions</li>
                  <li>Monitor analytics to improve win rates</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API Documentation Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            API Documentation
          </CardTitle>
          <CardDescription>
            Comprehensive guide for integrating with Bidaible's API for external systems and programmatic access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="authentication">Authentication</TabsTrigger>
              <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
              <TabsTrigger value="integrations">Integrations</TabsTrigger>
              <TabsTrigger value="examples">Examples</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="text-center">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <Key className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-lg">API Keys</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Secure JWT-based authentication with configurable permissions and rate limiting
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="text-center">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <Database className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <CardTitle className="text-lg">RESTful API</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Comprehensive REST endpoints for RFQ management, bidding, and data export
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="text-center">
                    <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <ExternalLink className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <CardTitle className="text-lg">Integrations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      QuickBooks export, Sage ERP sync, and CSV/JSON data export capabilities
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="bg-muted/50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">API Features</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Three-tier permission system (read-only, upload-only, full-access)</li>
                  <li>• Configurable rate limiting with usage analytics</li>
                  <li>• Dual authentication support (session + API key)</li>
                  <li>• Comprehensive error handling and response codes</li>
                  <li>• File upload support with document processing</li>
                </ul>
              </div>
            </TabsContent>

            <TabsContent value="authentication" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Creating API Keys</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    API keys are created through the web interface and provide programmatic access to your Bidaible account.
                  </p>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">POST /api/auth/api-keys</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard('POST /api/auth/api-keys', 'create-key')}
                      >
                        {copiedCode === 'create-key' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <pre className="text-sm text-muted-foreground">
{`{
  "name": "My Integration Key",
  "permissions": "read-only",
  "rateLimit": 100
}`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Permission Levels</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">read-only</Badge>
                      <span className="text-sm text-muted-foreground">View data and export reports</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">upload-only</Badge>
                      <span className="text-sm text-muted-foreground">Create RFQs and submit bids</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="destructive">full-access</Badge>
                      <span className="text-sm text-muted-foreground">Complete API access including updates</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Using API Keys</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Include your API key in the Authorization header of all requests:
                  </p>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">Authorization Header</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard('Authorization: Bearer bda_your-api-key-here', 'auth-header')}
                      >
                        {copiedCode === 'auth-header' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <pre className="text-sm text-muted-foreground">
Authorization: Bearer bda_your-api-key-here
                    </pre>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="endpoints" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Core API Endpoints</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">GET /api/rfqs</span>
                        <p className="text-xs text-muted-foreground">List all RFQs</p>
                      </div>
                      <Badge variant="secondary">read-only</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">POST /api/rfqs</span>
                        <p className="text-xs text-muted-foreground">Create new RFQ</p>
                      </div>
                      <Badge variant="outline">upload-only</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">GET /api/rfqs/:id</span>
                        <p className="text-xs text-muted-foreground">Get RFQ details</p>
                      </div>
                      <Badge variant="secondary">read-only</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">POST /api/rfqs/:rfqId/bids</span>
                        <p className="text-xs text-muted-foreground">Submit bid</p>
                      </div>
                      <Badge variant="outline">upload-only</Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">API Key Management</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">GET /api/auth/api-keys</span>
                        <p className="text-xs text-muted-foreground">List your API keys</p>
                      </div>
                      <Badge variant="secondary">session</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">GET /api/auth/api-keys/:id/stats</span>
                        <p className="text-xs text-muted-foreground">Get usage statistics</p>
                      </div>
                      <Badge variant="secondary">session</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">DELETE /api/auth/api-keys/:id</span>
                        <p className="text-xs text-muted-foreground">Revoke API key</p>
                      </div>
                      <Badge variant="destructive">session</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="integrations" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Notification System API</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Manage notification preferences and delivery settings programmatically
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">GET /api/notifications/preferences</span>
                        <p className="text-xs text-muted-foreground">Get user notification preferences</p>
                      </div>
                      <Badge variant="secondary">read-only</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">PATCH /api/notifications/preferences</span>
                        <p className="text-xs text-muted-foreground">Update notification settings</p>
                      </div>
                      <Badge variant="outline">full-access</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <span className="font-mono text-sm">POST /api/notifications/send-custom-email</span>
                        <p className="text-xs text-muted-foreground">Send custom notification emails</p>
                      </div>
                      <Badge variant="outline">upload-only</Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">QuickBooks Integration</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Export financial data formatted for QuickBooks import
                  </p>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">GET /api/integrations/quickbooks</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard('GET /api/integrations/quickbooks', 'quickbooks')}
                      >
                        {copiedCode === 'quickbooks' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Returns project summaries, bid data, and financial calculations ready for QuickBooks import
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Sage ERP Integration</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Bidirectional sync with Sage ERP systems
                  </p>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">POST /api/integrations/sage</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard('POST /api/integrations/sage', 'sage')}
                      >
                        {copiedCode === 'sage' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Sync project data between Sage ERP and Bidaible with automatic RFQ creation
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Project Budget Sync - Multi-System Integration</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Comprehensive cost code synchronization with ERP/CRM systems including 760+ consolidated cost codes from bid line items
                  </p>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">GET /api/integrations/project-budget/:rfqId/:bidId</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard('GET /api/integrations/project-budget/:rfqId/:bidId?system=quickbooks', 'budget-sync')}
                      >
                        {copiedCode === 'budget-sync' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">
                      System-specific formatting for: QuickBooks, Sage, Salesforce, HubSpot
                    </p>
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div>• Complete cost code breakdown with descriptions, quantities, unit prices</div>
                      <div>• Category-based budget summaries and analysis</div>
                      <div>• System-specific field mapping (QuickBooks items, Sage job codes, etc.)</div>
                      <div>• Query parameter: system (quickbooks, sage, salesforce, hubspot)</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Data Export</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Export RFQ data in CSV or JSON format with date filtering
                  </p>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">GET /api/integrations/export/rfqs</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard('GET /api/integrations/export/rfqs?format=csv', 'export')}
                      >
                        {copiedCode === 'export' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Query parameters: format (csv/json), startDate, endDate
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="examples" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Fetch RFQs (JavaScript)</h4>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">JavaScript Example</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(`const response = await fetch('/api/rfqs', {
  headers: {
    'Authorization': 'Bearer bda_your-api-key-here'
  }
});
const rfqs = await response.json();`, 'js-fetch')}
                      >
                        {copiedCode === 'js-fetch' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <pre className="text-sm text-muted-foreground">
{`const response = await fetch('/api/rfqs', {
  headers: {
    'Authorization': 'Bearer bda_your-api-key-here'
  }
});
const rfqs = await response.json();`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Create RFQ (curl)</h4>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">curl Example</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(`curl -X POST http://localhost:5000/api/rfqs \\
  -H "Authorization: Bearer bda_your-api-key-here" \\
  -F "projectName=Office Renovation" \\
  -F "projectLocation=Los Angeles, CA" \\
  -F "tradeCategory=general" \\
  -F "documents=@/path/to/document.pdf"`, 'curl-create')}
                      >
                        {copiedCode === 'curl-create' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <pre className="text-sm text-muted-foreground">
{`curl -X POST http://localhost:5000/api/rfqs \\
  -H "Authorization: Bearer bda_your-api-key-here" \\
  -F "projectName=Office Renovation" \\
  -F "projectLocation=Los Angeles, CA" \\
  -F "tradeCategory=general" \\
  -F "documents=@/path/to/document.pdf"`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Python Example</h4>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono">Python Example</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(`import requests

headers = {
    'Authorization': 'Bearer bda_your-api-key-here'
}

# Fetch RFQs
response = requests.get('/api/rfqs', headers=headers)
rfqs = response.json()

# Export to QuickBooks
qb_response = requests.get('/api/integrations/quickbooks', headers=headers)
quickbooks_data = qb_response.json()`, 'python-example')}
                      >
                        {copiedCode === 'python-example' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <pre className="text-sm text-muted-foreground">
{`import requests

headers = {
    'Authorization': 'Bearer bda_your-api-key-here'
}

# Fetch RFQs
response = requests.get('/api/rfqs', headers=headers)
rfqs = response.json()

# Export to QuickBooks
qb_response = requests.get('/api/integrations/quickbooks', headers=headers)
quickbooks_data = qb_response.json()`}
                    </pre>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-semibold mb-2 text-blue-900 dark:text-blue-100">Rate Limiting</h4>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              All API keys have configurable rate limits (default: 100 requests/hour). 
              Monitor the X-RateLimit-* headers in responses to track your usage.
            </p>
          </div>

          <div className="mt-4 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
            <h4 className="font-semibold mb-2 text-amber-900 dark:text-amber-100">Security Best Practices</h4>
            <ul className="text-sm text-amber-800 dark:text-amber-200 space-y-1">
              <li>• Store API keys securely, never in client-side code</li>
              <li>• Use environment variables for API keys in production</li>
              <li>• RotateAPI keys regularly</li>
              <li>• Use the minimum required permission level</li>
              <li>• Monitor API key usage for suspicious activity</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
          <CardDescription>Common questions and their answers</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-semibold mb-2">What file types can I upload for RFQs?</h4>
            <p className="text-muted-foreground">
              Bidaible supports PDF, TXT, and CSV files up to 50MB in size. 
              Our AI works best with PDF documents containing project specifications and plans.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">How does the AI document extraction work?</h4>
            <p className="text-muted-foreground">
              Our AI analyzes your uploaded documents to automatically extract key information such as project details, 
              deadlines, locations, and specifications. You can always review and edit this information before sending your RFQ.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">How does the enhanced bid submission system work?</h4>
            <p className="text-muted-foreground">
              The new bid submission system uses a 5-tab interface: Contact Info, Line Items, Scope Definition, Documents, and Summary. 
              Contractors can select from 760+ consolidated cost codes, enter detailed line items with quantities and prices, 
              and the system automatically calculates totals with simplified pricing (no markup calculations).
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">What are consolidated cost codes and how do I use them?</h4>
            <p className="text-muted-foreground">
              Consolidated cost codes are standardized construction cost categories (760+ unique codes) from your master list. 
              When submitting bids, simply select the appropriate cost code from the dropdown menu for each line item. 
              The system will automatically categorize and organize your pricing breakdown for easy comparison.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">Can contractors see each other's bids?</h4>
            <p className="text-muted-foreground">
              No, all bid submissions are private and confidential. Only the RFQ creator (general contractor) 
              can view all submitted bids. Contractors can only see their own submissions.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">How do I manage my contractor favorites?</h4>
            <p className="text-muted-foreground">
              Navigate to the Contractors section and use the star icon to add contractors to your favorites. 
              When creating an RFQ, you can choose to send it only to your favorite contractors or broadcast to all qualified contractors.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">What happens after the RFQ deadline?</h4>
            <p className="text-muted-foreground">
              After the deadline passes, no new bids can be submitted. You can review all received bids, 
              download contractor submissions, and use our analysis tools to compare proposals and make your selection.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">How do RFQ deadline notifications work?</h4>
            <p className="text-muted-foreground">
              The system automatically schedules deadline reminders when you create an RFQ. Notifications are sent 1 week, 
              3 days, and 1 day before the bid proposal deadline. You can customize notification preferences in your Settings.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">Why can't I set a bid deadline after the project deadline?</h4>
            <p className="text-muted-foreground">
              The system validates that bid proposal deadlines must come before RFQ project deadlines. This ensures 
              contractors have submitted their bids before the project needs to begin. The countdown timers and validation 
              help prevent scheduling conflicts.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">What do the countdown timer colors mean?</h4>
            <p className="text-muted-foreground">
              Countdown timers use color coding for quick deadline awareness: Green (safe - more than 3 days), 
              Yellow (warning - 1-3 days), Red (urgent - less than 24 hours), and Gray (expired - deadline passed).
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">How does the notification bell work?</h4>
            <p className="text-muted-foreground">
              The notification bell in the top navigation provides real-time updates. It shows an unread count badge, 
              polls for new notifications every 30-60 seconds, and lets you click notifications to navigate directly 
              to relevant RFQs or bids. You can mark notifications as read individually or in batch.
            </p>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">Where can I manage all my notifications?</h4>
            <p className="text-muted-foreground">
              Click "View All Notifications" at the bottom of the notification dropdown, or navigate to the dedicated 
              Notifications page. There you'll find tabbed interface (All/Unread), filtering options, and comprehensive 
              notification management with priority indicators and delivery status.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Additional Resources */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Resources</CardTitle>
          <CardDescription>Helpful links and documentation</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Platform Guides</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Best practices for RFQ creation</li>
                <li>• Contractor onboarding guide</li>
                <li>• Document preparation tips</li>
                <li>• Bid evaluation strategies</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Account & Billing</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Account settings management</li>
                <li>• User permissions and roles</li>
                <li>• Billing and subscription info</li>
                <li>• Data export and backup</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* <Card>
        <CardHeader>
            <CardTitle>MatIQ</CardTitle>
            <CardDescription>View material intelligence and analytics</CardDescription>
        </CardHeader>
        <CardContent>
            <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Material market trends</li>
                <li>• Price forecasting</li>
                <li>• Construction cost projections</li>
            </ul>
        </CardContent>
    </Card> */}

      {/* MatIQ Integration Section - COMMENTED OUT PER USER REQUEST */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            MatIQ Material Intelligence
          </CardTitle>
          <CardDescription>
            Advanced material intelligence and market forecasting capabilities integrated into the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Market Intelligence</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Real-time material price tracking and trends</li>
                <li>• Regional market analysis and comparisons</li>
                <li>• Supply chain risk assessment and alerts</li>
                <li>• Seasonal pricing pattern analysis</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Forecasting & Analytics</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• AI-powered price forecasting models</li>
                <li>• Construction cost projections and budgeting</li>
                <li>• Material availability predictions</li>
                <li>• Impact analysis for project planning</li>
              </ul>
            </div>
          </div>

          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 dark:text-blue-100">Integration Benefits</h4>
                <p className="text-sm text-blue-800 dark:text-blue-200 mt-1">
                  MatIQ data is automatically integrated into bid analysis, providing contractors and project owners 
                  with real-time material cost insights to make more informed bidding and procurement decisions.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card> */}
    </div>
  );
}