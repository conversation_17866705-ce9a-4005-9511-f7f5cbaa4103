/**
 * Organization Sync Service
 * 
 * Handles bidirectional sync between Clerk and Bidaible organizations
 * Ensures all organization operations maintain consistency between both systems
 */

import { clerkClient } from '@clerk/express';
import { storage } from '../storage';
import { asInsert } from '../utils/typing';
import type { InsertOrganization, InsertUser } from '@shared/schema';

interface ClerkOrganization {
  id: string;
  name: string;
  slug?: string;
  createdAt: number;
}

interface BidaibleOrganization {
  id: string;
  name: string;
  slug: string;
  createdAt: Date;
  updatedAt: Date;
}

export class OrganizationSyncService {
  /**
   * Create organization in both Clerk and Bidaible
   * This is the primary method for new organization creation
   */
  static async createOrganization(
    userId: string, 
    name: string, 
    slug: string
  ): Promise<{ clerkOrg: ClerkOrganization; bidaibleOrg: BidaibleOrganization }> {
    try {
      console.log(`🔄 Creating organization: ${name} (${slug}) for user: ${userId}`);

      // Step 1: Create in Clerk first
      const clerkOrg = await clerkClient.organizations.createOrganization({
        name: name.trim(),
        slug: slug.toLowerCase().replace(/[^a-z0-9-]/g, "-"),
        createdBy: userId,
      });

      console.log(`✅ Created Clerk organization: ${clerkOrg.id}`);

      // Step 2: Create in Bidaible with Clerk metadata
      const organizationData = asInsert<InsertOrganization>({
        name: name.trim(),
        slug: slug.toLowerCase().replace(/[^a-z0-9-]/g, "-"),
        description: `Organization synced from Clerk: ${name} (Clerk ID: ${clerkOrg.id})`,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      const bidaibleOrg = await storage.createOrganization(organizationData);
      console.log(`✅ Created Bidaible organization: ${bidaibleOrg.id}`);

      // Step 3: Associate user with organization in both systems
      await this.associateUserWithOrganization(userId, clerkOrg.id, bidaibleOrg.id);

      return {
        clerkOrg: {
          id: clerkOrg.id,
          name: clerkOrg.name,
          slug: clerkOrg.slug,
          createdAt: clerkOrg.createdAt
        },
        bidaibleOrg
      };

    } catch (error) {
      console.error('❌ Error creating organization:', error);
      throw new Error(`Failed to create organization: ${error.message}`);
    }
  }

  /**
   * Associate a user with an organization in both systems
   */
  static async associateUserWithOrganization(
    userId: string,
    clerkOrgId: string,
    bidaibleOrgId: string
  ): Promise<void> {
    try {
      // Add user to Clerk organization as admin (since they created it)
      await clerkClient.organizations.createOrganizationMembership({
        organizationId: clerkOrgId,
        userId: userId,
        role: 'org:admin'
      });

      console.log(`✅ Added user ${userId} as admin to Clerk org ${clerkOrgId}`);

      // Update user in Bidaible database
      const user = await storage.getUser(userId);
      if (user) {
        await storage.upsertUser(
          asInsert<InsertUser>({
            ...user,
            organizationId: bidaibleOrgId,
          })
        );
        console.log(`✅ Associated user ${userId} with Bidaible org ${bidaibleOrgId}`);
      }

    } catch (error) {
      console.error('❌ Error associating user with organization:', error);
      throw new Error(`Failed to associate user with organization: ${error.message}`);
    }
  }

  /**
   * Sync a Clerk organization to Bidaible (for existing Clerk orgs)
   */
  static async syncClerkOrgToBidaible(clerkOrgId: string): Promise<BidaibleOrganization | null> {
    try {
      // Get organization from Clerk
      const clerkOrg = await clerkClient.organizations.getOrganization({
        organizationId: clerkOrgId
      });

      // Check if it already exists in Bidaible
      const existingOrg = await storage.getOrganizationBySlug(clerkOrg.slug || `org-${clerkOrgId.toLowerCase()}`);
      if (existingOrg) {
        console.log(`✅ Organization already exists in Bidaible: ${existingOrg.name}`);
        return existingOrg;
      }

      // Create in Bidaible
      const organizationData = asInsert<InsertOrganization>({
        name: clerkOrg.name,
        slug: clerkOrg.slug || `org-${clerkOrgId.toLowerCase()}`,
        description: `Organization synced from Clerk: ${clerkOrg.name} (Clerk ID: ${clerkOrg.id})`,
        isActive: true,
        createdAt: new Date(clerkOrg.createdAt),
        updatedAt: new Date()
      });

      const bidaibleOrg = await storage.createOrganization(organizationData);
      console.log(`✅ Synced Clerk org to Bidaible: ${bidaibleOrg.id}`);

      return bidaibleOrg;

    } catch (error) {
      console.error(`❌ Error syncing Clerk org ${clerkOrgId} to Bidaible:`, error);
      return null;
    }
  }

  /**
   * Sync user's Clerk organizations to Bidaible and update their primary org
   */
  static async syncUserOrganizations(userId: string): Promise<void> {
    try {
      console.log(`🔄 Syncing organizations for user: ${userId}`);

      // Get user's Clerk organization memberships
      const memberships = await clerkClient.users.getOrganizationMembershipList({ userId });
      
      if (memberships.data.length === 0) {
        console.log(`ℹ️  User ${userId} has no Clerk organizations`);
        return;
      }

      // Find primary organization (admin role preferred, or first one)
      const primaryMembership = memberships.data.find(m => m.role === 'org:admin') || memberships.data[0];
      const primaryClerkOrg = primaryMembership.organization;

      console.log(`🏢 Primary Clerk org: ${primaryClerkOrg.name} (${primaryClerkOrg.id})`);

      // Sync primary organization to Bidaible
      const bidaibleOrg = await this.syncClerkOrgToBidaible(primaryClerkOrg.id);
      
      if (bidaibleOrg) {
        // Update user's organization in Bidaible
        const user = await storage.getUser(userId);
        if (user && user.organizationId !== bidaibleOrg.id) {
          await storage.upsertUser(
            asInsert<InsertUser>({
              ...user,
              organizationId: bidaibleOrg.id,
            })
          );
          console.log(`✅ Updated user ${userId} organization to ${bidaibleOrg.id}`);
        }
      }

      // Sync other organizations (but don't set as primary)
      for (const membership of memberships.data) {
        if (membership.organization.id !== primaryClerkOrg.id) {
          await this.syncClerkOrgToBidaible(membership.organization.id);
        }
      }

    } catch (error) {
      console.error(`❌ Error syncing organizations for user ${userId}:`, error);
      throw new Error(`Failed to sync user organizations: ${error.message}`);
    }
  }

  /**
   * Middleware to ensure user organizations are synced on login
   */
  static async ensureUserOrganizationSync(userId: string): Promise<void> {
    try {
      const user = await storage.getUser(userId);
      
      // If user has no organization, try to sync from Clerk
      if (!user?.organizationId) {
        console.log(`🔄 User ${userId} has no organization, attempting sync...`);
        await this.syncUserOrganizations(userId);
      }

    } catch (error) {
      console.error(`❌ Error ensuring user organization sync for ${userId}:`, error);
      // Don't throw here - let the user continue even if sync fails
    }
  }
}

export default OrganizationSyncService;
