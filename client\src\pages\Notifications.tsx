import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Bell, FileText, Users, AlertCircle, CheckCircle2, Clock, Upload, Calendar, Settings, Trash2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useLocation } from "wouter";
import { 
  useNotifications, 
  useUnreadNotifications, 
  useMarkNotificationRead, 
  useMarkAllNotificationsRead,
  type Notification 
} from "@/hooks/useNotifications";
import { toast } from "react-toastify";

export default function NotificationsPage() {
  const [, setLocation] = useLocation();
  const [currentTab, setCurrentTab] = useState("all");
  
  // Fetch all notifications with pagination
  const { 
    data: allNotifications, 
    isLoading: isLoadingAll, 
    refetch: refetchAll 
  } = useNotifications({ limit: 50 });
  
  // Fetch unread notifications
  const { 
    data: unreadData, 
    isLoading: isLoadingUnread 
  } = useUnreadNotifications();
  
  const markAsReadMutation = useMarkNotificationRead();
  const markAllAsReadMutation = useMarkAllNotificationsRead();
  
  const notifications = allNotifications?.notifications || [];
  const unreadNotifications = unreadData?.notifications || [];
  const unreadCount = unreadData?.count || 0;
  
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "bid_submitted":
        return <FileText className="h-5 w-5 text-green-600" />;
      case "bid_accepted":
        return <CheckCircle2 className="h-5 w-5 text-green-700" />;
      case "bid_rejected":
        return <Trash2 className="h-5 w-5 text-red-600" />;
      case "bid_request_info":
        return <AlertCircle className="h-5 w-5 text-orange-600" />;
      case "rfq_uploaded":
        return <Upload className="h-5 w-5 text-blue-600" />;
      case "rfq_distributed":
        return <Users className="h-5 w-5 text-purple-600" />;
      case "rfq_closed":
        return <Clock className="h-5 w-5 text-gray-600" />;
      case "rfq_deadline_reminder":
        return <Calendar className="h-5 w-5 text-orange-600" />;
      case "system_maintenance":
        return <Settings className="h-5 w-5 text-blue-500" />;
      case "account_update":
        return <Settings className="h-5 w-5 text-purple-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };
  
  const getPriorityColor = (priority: "low" | "medium" | "high" | "urgent") => {
    switch (priority) {
      case "urgent":
        return "border-l-red-600 bg-red-50/50";
      case "high":
        return "border-l-red-500 bg-red-50/30";
      case "medium":
        return "border-l-orange-500 bg-orange-50/30";
      case "low":
        return "border-l-blue-500 bg-blue-50/30";
      default:
        return "border-l-gray-500";
    }
  };
  
  const getPriorityBadgeVariant = (priority: "low" | "medium" | "high" | "urgent") => {
    switch (priority) {
      case "urgent":
        return "destructive";
      case "high":
        return "destructive";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      default:
        return "secondary";
    }
  };
  
  const handleNotificationClick = (notification: Notification) => {
    // Mark as read
    if (!notification.readAt) {
      markAsReadMutation.mutate(notification.id);
    }
    
    // Navigate to relevant page based on notification data
    if (notification.data) {
      const { rfqId, bidId } = notification.data;
      
      if (rfqId && bidId) {
        // Navigate to bid details
        setLocation(`/rfqs/${rfqId}/bids`);
      } else if (rfqId) {
        // Navigate to RFQ details
        setLocation(`/rfq/${rfqId}`);
      }
    }
  };
  
  const handleMarkAllAsRead = () => {
    markAllAsReadMutation.mutate();
  };
  
  const renderNotificationList = (notificationList: Notification[]) => {
    if (notificationList.length === 0) {
      return (
        <div className="text-center py-12 text-muted-foreground">
          <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No notifications</h3>
          <p className="text-sm">You're all caught up!</p>
        </div>
      );
    }
    
    return (
      <div className="space-y-4">
        {notificationList.map((notification) => (
          <Card 
            key={notification.id}
            className={`
              cursor-pointer transition-all duration-200 hover:shadow-md border-l-4 
              ${getPriorityColor(notification.priority)}
              ${!notification.readAt ? 'shadow-sm' : 'opacity-75'}
            `}
            onClick={() => handleNotificationClick(notification)}
          >
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                <div className="mt-1">
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="flex-1 space-y-2">
                  <div className="flex items-start justify-between gap-2">
                    <div>
                      <h3 className="font-medium text-sm leading-5">
                        {notification.title}
                      </h3>
                      {!notification.readAt && (
                        <div className="w-2 h-2 bg-blue-600 rounded-full mt-1" />
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getPriorityBadgeVariant(notification.priority)} className="text-xs">
                        {notification.priority}
                      </Badge>
                      {!notification.readAt && (
                        <Badge variant="default" className="text-xs">
                          New
                        </Badge>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {notification.message}
                  </p>
                  <div className="flex items-center justify-between">
                    <p className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                    </p>
                    {notification.data?.projectName && (
                      <p className="text-xs text-muted-foreground font-medium">
                        {notification.data.projectName}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Notifications</h1>
            <p className="text-muted-foreground mt-1">
              Stay updated on your RFQs, bids, and system updates
            </p>
          </div>
          {unreadCount > 0 && (
            <Button 
              onClick={handleMarkAllAsRead}
              disabled={markAllAsReadMutation.isPending}
              variant="outline"
            >
              {markAllAsReadMutation.isPending ? "Marking..." : `Mark all as read (${unreadCount})`}
            </Button>
          )}
        </div>
      </div>

      <Tabs value={currentTab} onValueChange={setCurrentTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="all" className="relative">
            All Notifications
            {notifications.length > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {notifications.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread" className="relative">
            Unread
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          {isLoadingAll ? (
            <div className="text-center py-12 text-muted-foreground">
              <Bell className="h-12 w-12 mx-auto mb-4 opacity-50 animate-pulse" />
              <p>Loading notifications...</p>
            </div>
          ) : (
            renderNotificationList(notifications)
          )}
        </TabsContent>

        <TabsContent value="unread" className="mt-6">
          {isLoadingUnread ? (
            <div className="text-center py-12 text-muted-foreground">
              <Bell className="h-12 w-12 mx-auto mb-4 opacity-50 animate-pulse" />
              <p>Loading unread notifications...</p>
            </div>
          ) : (
            renderNotificationList(unreadNotifications)
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
