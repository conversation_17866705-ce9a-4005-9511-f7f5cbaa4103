// GitHub service for creating issues from user feedback

interface GitHubIssueData {
  title: string;
  body: string;
  labels: string[];
  assignee: string;
}

interface GitHubIssueResponse {
  number: number;
  html_url: string;
  id: number;
  title: string;
}

class GitHubService {
  private readonly token: string;
  private readonly owner: string;
  private readonly repo: string;
  private readonly assignee: string;
  private readonly baseUrl = 'https://api.github.com';

  constructor() {
    this.token = process.env.GITHUB_TOKEN || '';
    this.owner = process.env.GITHUB_REPO_OWNER || 'roygatling';
    this.repo = process.env.GITHUB_REPO_NAME || 'Bidaible';
    this.assignee = process.env.GITHUB_ASSIGNEE || 'roygatling';

    if (!this.token) {
      console.warn('GitHub token not configured - GitHub issue creation will be disabled');
    }
  }

  async createIssue(feedbackData: {
    type: 'bug' | 'suggestion';
    message: string;
    priority: string;
    userId: string;
    userEmail?: string;
    organizationName?: string;
    feedbackId: string;
  }): Promise<{ issueNumber: number; issueUrl: string } | null> {
    if (!this.token) {
      console.warn('GitHub token not configured, skipping issue creation');
      return null;
    }

    try {
      const issueData = this.formatIssueData(feedbackData);
      
      const response = await fetch(`${this.baseUrl}/repos/${this.owner}/${this.repo}/issues`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Accept': 'application/vnd.github+json',
          'X-GitHub-Api-Version': '2022-11-28',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(issueData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('GitHub API error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
        });
        return null;
      }

      const issue = await response.json() as GitHubIssueResponse;
      
      console.log('GitHub issue created successfully:', {
        issueNumber: issue.number,
        issueUrl: issue.html_url,
        feedbackId: feedbackData.feedbackId,
      });

      return {
        issueNumber: issue.number,
        issueUrl: issue.html_url,
      };
    } catch (error) {
    console.error('Failed to create GitHub issue:', error);
      return null;
    }
  }

  private formatIssueData(feedbackData: {
    type: 'bug' | 'suggestion';
    message: string;
    priority: string;
    userId: string;
    userEmail?: string;
    organizationName?: string;
    feedbackId: string;
  }): GitHubIssueData {
    const { type, message, priority, userId, userEmail, organizationName, feedbackId } = feedbackData;
    
    const issueTitlePrefix = type === 'bug' ? '🐛 Bug Report' : '💡 Feature Request';
    const issueTitle = `${issueTitlePrefix}: ${message.substring(0, 80)}${message.length > 80 ? '...' : ''}`;
    
    const labels = ['user-feedback'];
    if (type === 'bug') {
      labels.push('bug');
    } else {
      labels.push('enhancement');
    }

    const issueBody = `**Feedback Type**: ${type === 'bug' ? 'Bug Report' : 'Feature Request'}
**Priority**: ${priority}
**User ID**: ${userId}
${userEmail ? `**User Email**: ${userEmail}` : ''}
${organizationName ? `**Organization**: ${organizationName}` : ''}
**Submitted**: ${new Date().toISOString()}

---

${message}

---
*Internal Reference: bidaible-feedback-${feedbackId}*`;

    return {
      title: issueTitle,
      body: issueBody,
      labels,
      assignee: this.assignee,
    };
  }

  isConfigured(): boolean {
    return !!this.token;
  }
}

export const githubService = new GitHubService();
