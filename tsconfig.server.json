{"include": ["server/**/*", "shared/**/*"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo.server", "noEmit": true, "module": "ESNext", "target": "ES2020", "strict": false, "lib": ["esnext"], "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "bundler", "downlevelIteration": true, "useUnknownInCatchVariables": false, "baseUrl": ".", "types": ["node"], "typeRoots": ["./node_modules/@types", "./server/types"], "paths": {"@shared/*": ["./shared/*"]}}}