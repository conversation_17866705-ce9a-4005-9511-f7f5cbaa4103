
-- Add master summary fields to RFQs table
ALTER TABLE rfqs ADD COLUMN IF NOT EXISTS master_summary TEXT;
ALTER TABLE rfqs ADD COLUMN IF NOT EXISTS conflict_flags JSONB;
ALTER TABLE rfqs ADD COLUMN IF NOT EXISTS summary_generated_at TIMESTAMP;

--> statement-breakpoint

-- Add comparison fields to bids table  
ALTER TABLE bids ADD COLUMN IF NOT EXISTS inclusions_exclusions JSONB;
ALTER TABLE bids ADD COLUMN IF NOT EXISTS comparison_data JSONB;
ALTER TABLE bids ADD COLUMN IF NOT EXISTS cost_code_breakdown JSONB;

--> statement-breakpoint

-- Create waitlist table
CREATE TABLE IF NOT EXISTS "waitlist" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "email" varchar(255) NOT NULL UNIQUE,
  "company_name" varchar(255),
  "role" varchar(100),
  "industry" varchar(100),
  "referral_source" varchar(100),
  "created_at" timestamp DEFAULT now(),
  "status" varchar(50) DEFAULT 'pending'
);

--> statement-breakpoint

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "IDX_rfqs_master_summary" ON "rfqs" ("master_summary");
CREATE INDEX IF NOT EXISTS "IDX_rfqs_summary_generated_at" ON "rfqs" ("summary_generated_at");
CREATE INDEX IF NOT EXISTS "IDX_bids_cost_code_breakdown" ON "bids" ("cost_code_breakdown");
CREATE INDEX IF NOT EXISTS "IDX_waitlist_email" ON "waitlist" ("email");
CREATE INDEX IF NOT EXISTS "IDX_waitlist_created_at" ON "waitlist" ("created_at");
