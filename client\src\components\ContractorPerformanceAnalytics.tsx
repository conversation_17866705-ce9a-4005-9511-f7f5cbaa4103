import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Line, <PERSON>hart, Pie, Cell } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, Target, Calendar, Award, AlertTriangle, FileText } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";

interface ContractorStats {
  totalBidsSubmitted: number;
  totalBidsWon: number;
  winRate: number;
  averageBidAmount: number;
  totalEarnings: number;
  monthlyActivity: Array<{
    month: string;
    bidsSubmitted: number;
    bidsWon: number;
    earnings: number;
  }>;
  bidsByStatus: Array<{
    status: string;
    count: number;
    color: string;
  }>;
  recentPerformance: {
    lastMonthBids: number;
    lastMonthWins: number;
    trend: 'up' | 'down' | 'stable';
    trendPercentage: number;
  };
  latestBid?: {
    amount: number;
    status: string;
    submittedAt: string;
  } | null;
}

export function ContractorPerformanceAnalytics() {
  const { user } = useAuth();

  // Default stats for loading/error states to show page structure
  const defaultStats = {
    totalBidsSubmitted: 0,
    totalBidsWon: 0,
    winRate: 0,
    averageBidAmount: 0,
    totalEarnings: 0,
    monthlyActivity: Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (5 - i));
      return {
        month: date.toLocaleDateString('en-US', { month: 'short' }),
        bidsSubmitted: 0,
        bidsWon: 0,
        earnings: 0
      };
    }),
    bidsByStatus: [
      { status: 'Pending', count: 0, color: '#f59e0b' },
      { status: 'Accepted', count: 0, color: '#10b981' },
      { status: 'Rejected', count: 0, color: '#ef4444' }
    ],
    recentPerformance: {
      lastMonthBids: 0,
      lastMonthWins: 0,
      trend: 'stable' as const,
      trendPercentage: 0
    }
  };

  // Fetch contractor performance data
  const { data: stats, isLoading } = useQuery<ContractorStats>({
    queryKey: ["/api/contractors/performance-stats"],
    enabled: !!user,
  });

  // Use actual stats if available, otherwise use defaults
  const displayStats = stats || defaultStats;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Performance Analytics</h1>
          <p className="text-muted-foreground">Track your bidding performance and success metrics</p>
        </div>
        <div className="flex items-center gap-2">
          {isLoading && (
            <Badge variant="outline" className="bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-400">
              Loading data...
            </Badge>
          )}
          {!isLoading && (
            <Badge 
              variant="outline" 
              className={`${
                displayStats.recentPerformance.trend === 'up' 
                  ? 'bg-green-50 dark:bg-green-950 text-green-700 dark:text-green-400'
                  : displayStats.recentPerformance.trend === 'down'
                  ? 'bg-red-50 dark:bg-red-950 text-red-700 dark:text-red-400'
                  : 'bg-gray-50 dark:bg-gray-950 text-gray-700 dark:text-gray-400'
              }`}
            >
              {displayStats.recentPerformance.trend === 'up' ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : displayStats.recentPerformance.trend === 'down' ? (
                <TrendingDown className="h-3 w-3 mr-1" />
              ) : null}
              {displayStats.recentPerformance.trend === 'stable' 
                ? 'Stable performance' 
                : `${displayStats.recentPerformance.trend === 'up' ? '+' : ''}${displayStats.recentPerformance.trendPercentage}% vs last month`
              }
            </Badge>
          )}
        </div>
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bids Submitted</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{displayStats.totalBidsSubmitted}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-blue-600">{displayStats.recentPerformance.lastMonthBids}</span> last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bids Won</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{displayStats.totalBidsWon}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">{displayStats.winRate.toFixed(1)}%</span> win rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Bid Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(displayStats.averageBidAmount)}</div>
            <p className="text-xs text-muted-foreground">
              Per successful bid
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(displayStats.totalEarnings)}</div>
            <p className="text-xs text-muted-foreground">
              From won projects
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Activity Chart */}
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Monthly Bidding Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={displayStats.monthlyActivity}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis 
                    dataKey="month" 
                    className="text-xs fill-muted-foreground"
                  />
                  <YAxis className="text-xs fill-muted-foreground" />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '6px',
                    }}
                  />
                  <Bar 
                    dataKey="bidsSubmitted" 
                    fill="#3b82f6" 
                    name="Bids Submitted"
                    radius={[2, 2, 0, 0]}
                  />
                  <Bar 
                    dataKey="bidsWon" 
                    fill="#10b981" 
                    name="Bids Won"
                    radius={[2, 2, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Bid Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Bid Status Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={displayStats.bidsByStatus}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                  >
                    {displayStats.bidsByStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Earnings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Monthly Earnings Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={displayStats.monthlyActivity}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis 
                    dataKey="month" 
                    className="text-xs fill-muted-foreground"
                  />
                  <YAxis 
                    className="text-xs fill-muted-foreground"
                    tickFormatter={(value) => formatCurrency(value)}
                  />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '6px',
                    }}
                    formatter={(value) => [formatCurrency(value as number), 'Earnings']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="earnings" 
                    stroke="#f59e0b" 
                    strokeWidth={3}
                    dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Bid Activity */}
      {displayStats.totalBidsSubmitted > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Recent Bid Activity
            </CardTitle>
            <CardDescription>Your latest bid submissions and their details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-400">
                      Latest Submission
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {displayStats.totalBidsSubmitted === 1 ? 'Your current bid' : `1 of ${displayStats.totalBidsSubmitted} bids`}
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Bid Amount</p>
                      <p className="text-lg font-semibold">
                        {stats?.latestBid?.amount ? formatCurrency(stats.latestBid.amount) : formatCurrency(displayStats.averageBidAmount)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Status</p>
                      <Badge className={`${
                        stats?.latestBid?.status === 'accept' || stats?.latestBid?.status === 'accepted'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : stats?.latestBid?.status === 'reject' || stats?.latestBid?.status === 'rejected'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      }`}>
                        {stats?.latestBid?.status === 'accept' ? 'Accepted' : 
                         stats?.latestBid?.status === 'reject' ? 'Rejected' : 
                         stats?.latestBid?.status || 'Submitted'}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {stats?.latestBid?.status === 'accept' ? 'Confirmed Earnings' : 'Potential Earnings'}
                      </p>
                      <p className="text-lg font-semibold text-green-600">
                        {stats?.latestBid?.status === 'accept' && stats?.latestBid?.amount 
                          ? formatCurrency(stats.latestBid.amount) 
                          : formatCurrency(displayStats.totalEarnings)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Performance Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 dark:text-blue-100">Bidding Activity</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  You've submitted {displayStats.recentPerformance.lastMonthBids} bids in the last month{displayStats.totalBidsSubmitted === 0 ? '. Start bidding to see your performance trends!' : displayStats.recentPerformance.trend === 'up' 
                    ? ', showing increased activity.' 
                    : displayStats.recentPerformance.trend === 'down'
                    ? ', which is below your recent average.'
                    : ', maintaining steady activity levels.'
                  }
                </p>
              </div>
              
              <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
                <h4 className="font-semibold text-green-900 dark:text-green-100">Win Rate Analysis</h4>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  Your current win rate of {displayStats.winRate.toFixed(1)}%{displayStats.totalBidsSubmitted === 0 ? '. Submit bids to track your win rate performance!' : displayStats.winRate > 25 
                    ? ' is performing well above industry average.'
                    : displayStats.winRate > 15
                    ? ' is within the typical industry range.'
                    : displayStats.winRate > 0 ? ' suggests room for improvement in bid competitiveness.' : ' will be calculated once you win your first bid.'
                  }
                </p>
              </div>
              
              <div className="bg-orange-50 dark:bg-orange-950 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-900 dark:text-orange-100">Earnings Potential</h4>
                <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                  Average bid amount of {formatCurrency(displayStats.averageBidAmount)}{displayStats.totalBidsWon === 0 ? '. Win bids to see your average project value!' : displayStats.averageBidAmount > 100000 
                    ? ' shows engagement in substantial projects.'
                    : displayStats.averageBidAmount > 50000
                    ? ' shows focus on mid-range projects.' 
                    : ' shows involvement in smaller-scale work.'
                  }
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}