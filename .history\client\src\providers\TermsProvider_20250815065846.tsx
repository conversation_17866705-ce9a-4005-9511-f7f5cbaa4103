import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import TermsModal from "@/components/TermsModal";

interface TermsContextType {
  needsTermsAcceptance: boolean;
  showTermsModal: boolean;
  acceptTerms: () => void;
}

const TermsContext = createContext<TermsContextType | undefined>(undefined);

export const useTerms = () => {
  const context = useContext(TermsContext);
  if (!context) {
    throw new Error("useTerms must be used within a TermsProvider");
  }
  return context;
};

interface TermsProviderProps {
  children: ReactNode;
}

export default function TermsProvider({ children }: TermsProviderProps) {
  const [showTermsModal, setShowTermsModal] = useState(false);
  const queryClient = useQueryClient();

  // Get current user data
  const { data: user, isLoading } = useQuery({
    queryKey: ['/api/auth/user'],
  });

  // Check if terms acceptance is needed (safely access the property)
  const needsTermsAcceptance = user && !(user as any).termsAccepted;

  // Show modal when user needs to accept terms
  useEffect(() => {
    if (!isLoading && needsTermsAcceptance) {
      setShowTermsModal(true);
    }
  }, [isLoading, needsTermsAcceptance]);

  const acceptTerms = () => {
    setShowTermsModal(false);
    // Invalidate user query to refresh user data
    queryClient.invalidateQueries({ queryKey: ['/api/auth/user'] });
  };

  const handleDecline = () => {
    // For now, just keep the modal open
    // In a real app, you might redirect to a goodbye page
    console.log("User declined terms");
  };

  return (
    <TermsContext.Provider 
      value={{ 
        needsTermsAcceptance: !!needsTermsAcceptance,
        showTermsModal,
        acceptTerms 
      }}
    >
      {children}
      
      {/* Terms Modal */}
      <TermsModal
        isOpen={showTermsModal}
        onAccept={acceptTerms}
        onDecline={handleDecline}
      />
    </TermsContext.Provider>
  );
}
