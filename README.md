# Bidaible - AI-Powered Construction Bidding Platform

[![CI](https://github.com/roygatling/Bidaible/actions/workflows/ci.yml/badge.svg)](https://github.com/roygatling/Bidaible/actions/workflows/ci.yml)

Modern construction RFQ platform with AI-driven bid analysis, competitive intelligence, and enterprise-grade security for construction professionals.

## 🚀 Core Platform Features

### AI-Powered Bid Analysis
- **Three-Tab Dashboard**: Overview, Bid Analysis, and AI Analysis with comprehensive intelligence
- **Executive Summaries**: AI-powered insights, recommendations, and risk assessments
- **Competitive Ranking**: Automated scoring with detailed reasoning and market positioning
- **Real-time Intelligence**: Sub-3-second analysis with robust fallback system (Groq → OpenAI → Gemini)

### Document Processing & Management
- **Multi-File Support**: 5-8 files per RFQ with intelligent classification and priority processing
- **Construction AI**: Industry-specific analysis with professional terminology and insights
- **Large File Processing**: Up to 250MB files with smart storage and extended timeouts
- **Smart Extraction**: Automated extraction of project details, contacts, requirements, and deadlines
- **PDF Integration**: Direct viewing with optimized Wasabi Object Storage

### Business Management & Security
- **Multi-Tenant Support**: Clerk authentication with organizational data isolation
- **Role-Based Access**: 3-tier permissions (Owner/Admin/User) with audit trails
- **Contractor Database**: 29 trade specialties with verification and profiles
- **Dashboard Analytics**: Performance metrics and AI-powered bid analysis

### Enterprise-Grade Security & API Management
- **API Key Management**: Full CRUD operations with JWT-based authentication and scoped permissions
- **Security Hardening**: CSP, HSTS, rate limiting, and threat protection
- **Usage Monitoring**: Real-time analytics, audit trails, and key rotation system

### Comprehensive Bid Comparison & Export System
- **✅ SIDE-BY-SIDE BID ANALYSIS**: Complete comparative analysis with detailed cost code breakdowns using CSI MasterFormat standards
- **✅ DETAILED CSV EXPORT**: Individual cost code line items showing Contractor → Major Cost Code → Cost Code Detail → Description → Price format
- **✅ SMART EXPORT FUNCTIONALITY**: "Export Accepted Bids CSV" button in ComprehensiveBidComparison component for detailed budget planning
- **Enhanced QuickBooks Integration**: Complete cost code breakdown export with 760+ consolidated cost codes from bid line items
- **Advanced Sage ERP Sync**: Bidirectional project synchronization with comprehensive cost code data exchange
- **Multi-System Project Budget Sync**: Dedicated endpoint supporting QuickBooks, Sage, Salesforce, and HubSpot with system-specific formatting
- **Comprehensive Cost Code Data**: Detailed line items including cost codes, descriptions, quantities, unit prices, totals, and categories
- **Category-Based Analysis**: Automatic grouping and budget summaries by cost code categories for enhanced ERP workflow integration
- **System-Specific Formatting**: Custom data transformation for each ERP/CRM system (QuickBooks items, Sage job cost codes, Salesforce products, HubSpot line items)
- **Production-Ready Endpoints**: Enterprise-grade authentication, ownership verification, and comprehensive error handling
- **Data Export**: CSV and JSON export formats with date filtering for RFQ data

### RFQ Archive Management & Project Bundling
- **Complete Project Archives**: Download RFQ documents + all submitted bids in organized ZIP bundles
- **Organization Security**: Only RFQ owner's organization can access their archived projects
- **Structured Storage**: Clear hierarchy with `rfq-documents/org-{orgId}/rfq-{rfqId}/original/` + `bids/contractor-{name}/`
- **Bulk Operations**: Archive multiple RFQs with confirmation dialogs and audit trails
- **Contractor Privacy**: Archived RFQs completely hidden from contractor views
- **Metadata Inclusion**: JSON files with bid summaries, contractor info, and project dates

### RFQ Deadline Management & Real-Time Notifications
- **Dual Deadline System**: Bid proposal deadlines and RFQ project deadlines with database-level validation
- **Automated Reminders**: Scheduled notifications 1 week, 3 days, and 1 day before bid deadline
- **Real-time Countdown Timers**: Live countdown displays with urgency color coding (green/yellow/red/gray)
- **Background Processing**: Automated notification scheduler running every minute
- **Live Notification Bell**: Real-time notification dropdown in navigation with unread count badge
- **Interactive Notifications**: Click-to-navigate functionality directing users to relevant RFQs/bids
- **Multi-Channel Delivery**: Email, in-app notifications with preference management and audit tracking
- **Comprehensive Management**: Full notifications page with tabbed interface, filtering, and mark-as-read operations
- **Responsive Design**: Mobile-first with light/dark themes and drag-and-drop file upload
- **Legal Compliance**: Terms & conditions system with acceptance tracking and audit trails

## 🛠 Technology Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side navigation
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query for server state
- **Build Tool**: Vite with hot module replacement
- **Forms**: React Hook Form with Zod validation
- **Markdown Rendering**: React Markdown with custom component styling for terms and documentation

### Backend
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Authentication**: Clerk Authentication with JWT-based sessions
- **File Processing**: Multer middleware with Wasabi Object Storage integration
- **API Security**: Rate limiting, audit logging, and comprehensive middleware

### Database & AI
- **Database**: PostgreSQL (Neon) with Drizzle ORM and 30+ strategic indexes  
- **AI Models**: Groq (primary), OpenAI GPT-4.1, Gemini with construction-specific analysis
- **Performance**: In-memory caching, connection pooling, and intelligent processing

### Storage & Deployment
- **File Storage**: Wasabi S3-compatible storage with presigned URLs and enhanced error handling
- **Deployment**: Railway platform with automated scaling and health monitoring
- **CDN**: Optimized file delivery through Wasabi's global network
- **Backup**: Automated backup strategies for both database and file storage

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL database (Neon recommended)
- Clerk account for authentication
- Wasabi account for file storage
- AI service API keys (Groq, OpenAI, Gemini)

## 🚀 Quick Start

### 1. Clone and Install
```bash
git clone <repository-url>
cd bidaible
npm install
```

### 2. Environment Setup
Create a `.env` file based on `.env.example`:

```env
# Database
DATABASE_URL=postgresql://username:password@host:port/database

# Clerk Authentication
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key

# Wasabi Object Storage
WASABI_ACCESS_KEY_ID=your_wasabi_access_key_id
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_access_key
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com
WASABI_REGION=us-east-1

# AI Services
GROQ_API_KEY=gsk_your_groq_api_key
OPENAI_API_KEY=sk-your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# Application Configuration
NODE_ENV=development
PRIMARY_MODEL=groq
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here
```

### 3. Database Setup
```bash
# Push schema to database
npm run db:push

# Generate database client (if needed)
npm run db:generate
```

### 4. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:5000`

### API-only mode (for integration/security tests)
Run the API without the Vite client so /api/* endpoints are never shadowed by the SPA:

```bash
npm run dev:api            # NODE_ENV=development BIDAIBLE_TEST=1
# optional: run on alternate port
PORT=5001 npm run dev:api
```

Dev test fixtures are mounted under `/api/test/*` in API-only mode (development only). These helpers exist for test setup (orgs/users/contractors/RFQs) and are never enabled in production.

## 🤖 AI Analysis & Processing

### Intelligence Features
- **Multi-factor Evaluation**: Competitive scoring, risk assessment, and strategic recommendations
- **Document Processing**: PDF/TXT/CSV support up to 250MB with automatic data extraction
- **Real-time Analysis**: Sub-3-second bid intelligence with market positioning insights

## 📁 Project Structure

```
bidaible/
├── client/                 # Frontend React application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Application pages
│   │   ├── hooks/         # Custom React hooks
│   │   └── lib/           # Utility functions
├── server/                # Backend Express application
│   ├── services/          # Business logic services
│   │   ├── aiService.ts   # AI document processing
│   │   ├── bidAnalysisService.ts # Bid intelligence & competitive analysis
│   │   ├── objectStorageService.ts # Wasabi S3 storage integration
│   │   └── cacheService.ts # In-memory caching with TTL
│   ├── middleware/        # Security and authentication middleware
│   ├── routes.ts          # API route definitions with dual auth
│   ├── db.ts              # Database operations with Drizzle ORM
│   └── clerkAuth.ts       # Clerk authentication setup
├── shared/                # Shared code between client/server
│   └── schema.ts          # Database schema and types
├── memory-bank/           # Project documentation and context
├── docs/                  # Technical documentation
└── uploads/               # File upload directory
```

## 🔧 Configuration

### AI Model Configuration
Set the `PRIMARY_MODEL` environment variable to choose the primary AI service:
- `groq`: Use Groq as primary (recommended for speed)
- `openai`: Use OpenAI GPT-4.1-mini as primary
- `gemini`: Use Google Gemini 2.5 Pro as primary

All models have intelligent fallback to other providers for reliability.

### Database Schema
Comprehensive relational schema with 30+ strategic indexes including organizations, users, contractors (29 trade categories), RFQs with dual deadline management, bids, scheduled notifications, API keys, and notifications with full audit trails.

### File Upload
- Maximum: 250MB per file
- Formats: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, CSV, TXT
- Storage: Wasabi S3-compatible with presigned URLs

## 🔒 Authentication, Security & Testing

### Clerk Authentication System
- **Modern Authentication**: Clerk provides secure, scalable authentication with JWT-based sessions
- **Multi-Tenant Support**: Organization-based user management with complete data isolation
- **Session Management**: JWT tokens with automatic refresh and secure cookie handling
- **Social Login**: Support for Google, GitHub, and other OAuth providers
- **User Management**: Built-in user profile management and verification

### Dual Authentication System
- **Session-Based Authentication**: Standard web interface using Clerk Auth with JWT sessions
- **API Key Authentication**: Programmatic access using JWT tokens with comprehensive management interface
- **IP Restrictions**: API key-level IP whitelisting for enhanced security
- **Failed Attempt Tracking**: Automatic lockout after 5 failed attempts with 15-minute cooldown

### User Roles
- **Organization Owner**: Full organization access and management
- **Admin**: User management and system configuration within organization
- **User**: Standard access to create and manage RFQs and bids within organization

### Enhanced API Key Management Features
- **Create API Keys**: Generate new keys with custom names, scoped permissions, and security tokens
- **Advanced Security Controls**: Masked key display, visibility toggle, copy functionality, and IP restrictions
- **Permission Scoping**: read-only, upload-only, or full-access permissions with environment targeting
- **Usage Monitoring**: Real-time rate limiting, usage analytics, and comprehensive audit trails
- **Key Lifecycle**: Edit metadata, deactivate keys, secure deletion, and automatic rotation warnings
- **Security Features**: Failed attempt tracking, automatic lockout, key rotation system, and compromise detection
- **Administrative Controls**: Security statistics, rotation monitoring, and session invalidation capabilities
- **Audit Trail**: Complete tracking of key creation, usage, modifications, security events, and rotation history

### Enterprise Security Implementation
- **Content Security Policy**: Comprehensive directives preventing XSS attacks with AI service integration
- **HTTP Strict Transport Security**: 1-year max-age with subdomain inclusion and preloading
- **CORS Configuration**: Environment-based origin validation with credentials support
- **Input Sanitization**: Automatic removal of control characters and null byte injection
- **Rate Limiting**: Security endpoint protection with suspicious activity logging
- **Session Security**: Enhanced cookie security with sameSite: 'strict' for CSRF protection

## 📊 Dashboard Features

### AI-Powered Bid Management Dashboard
- **Three-Tab Interface**: Overview, Bid Analysis, and AI Analysis for comprehensive bid evaluation
- **Executive Summary Panel**: AI-generated insights, recommendations, and risk factors
- **Competitive Bid Ranking**: Automated scoring with detailed reasoning and market positioning
- **Market Analysis**: Price spreads, competitive positioning, and risk assessment
- **Real-time Generation**: Sub-3-second AI analysis powered by Groq with comprehensive fallback system

### Statistics Overview
- Active RFQs count
- Total bids submitted
- Average response time
- Success rate metrics

### MatIQ (Material Intelligence)
- Real-time construction material pricing
- 30-day trend indicators with directional arrows
- CSI standard cost codes integration
- Market trend predictions

## 🎨 UI/UX Design

### Design System
- **Light Theme**: Warm beige background (#e7e5e4) with orange primary (#f75100)
- **Dark Theme**: Dark brown background (#1e1b18) with orange accent (#f38620)
- **Typography**: Plus Jakarta Sans font family
- **Components**: shadcn/ui component library

### Responsive Design
- Mobile-first approach
- Adaptive layouts for all screen sizes
- Touch-friendly interface elements
- Optimized for construction industry workflows

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

### Railway Deployment
The application is configured for Railway deployment with `railway.toml`:

```toml
[build]
builder = "nixpacks"
buildCommand = "npm run build"

[deploy]
startCommand = "npm start"
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10
```

### Environment Variables for Production
Ensure all required environment variables are set in Railway:
- Database connection string (Neon PostgreSQL)
- Clerk authentication keys
- Wasabi storage credentials
- AI service API keys
- Application configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Code Style
- TypeScript for all new code
- Follow existing naming conventions
- Use proper type definitions
- Add JSDoc comments for functions

## 📝 API Documentation

### Core RFQ & Bid Endpoints
- `POST /api/rfqs` - Create RFQ with AI document processing
- `GET /api/rfqs` - List all RFQs with filtering and pagination
- `GET /api/rfqs/:id` - Get specific RFQ with AI summary and analysis
- `GET /api/rfqs/:rfqId/bids` - List all bids for an RFQ
- `POST /api/rfqs/:rfqId/bids` - Submit bid with AI analysis
- `POST /api/bids/:bidId/action` - Accept/reject/request info for bids

### AI Analysis & Intelligence
- `GET /api/analytics/bid-analysis/:rfqId` - Comprehensive bid analysis with competitive insights
- `POST /api/bids/:bidId/analysis` - Generate deep AI analysis for specific bid
- `GET /api/bids/:bidId/summary` - Get bid summary with risk assessment

### API Key Management (JWT Authentication)
- `POST /api/auth/api-keys` - Generate new API key with scoped permissions
- `GET /api/auth/api-keys` - List user's API keys (metadata only)
- `GET /api/auth/api-keys/:id/stats` - Get API key usage statistics
- `PATCH /api/auth/api-keys/:id` - Update API key settings
- `DELETE /api/auth/api-keys/:id` - Revoke API key

### User Management & Security
- `GET /api/admin/users` - List users (Admin only)
- `PATCH /api/admin/users/:userId/role` - Update user roles with audit logging
- `GET /api/admin/audit/roles` - Role change audit logs
- `GET /api/admin/audit/access` - Access attempt audit logs

### ERP & Integration Endpoints with Cost Code Synchronization
- `GET /api/integrations/quickbooks` - Export financial data with detailed cost code breakdown for QuickBooks integration
- `POST /api/integrations/sage` - Bidirectional sync with Sage ERP including comprehensive cost code data export
- `GET /api/integrations/project-budget/:rfqId/:bidId?system=quickbooks` - Comprehensive project budget sync with cost codes for multiple ERP/CRM systems
- `GET /api/integrations/export/rfqs` - Export RFQ data (CSV/JSON)

### Notification System Endpoints
- `GET /api/notifications` - Get user notifications with filtering (all/unread)
- `GET /api/notifications/unread` - Get unread notifications with count for real-time bell display
- `PATCH /api/notifications/:id/read` - Mark individual notification as read
- `PATCH /api/notifications/mark-all-read` - Mark all user notifications as read (batch operation)
- `GET /api/notifications/preferences` - Get user notification preferences and delivery settings
- `PATCH /api/notifications/preferences` - Update notification preferences with granular controls
- `POST /api/notifications/send-custom-email` - Send custom notification emails for testing and communication
- `GET /api/notifications/history` - Retrieve notification delivery history and status tracking
- `GET /api/notifications/scheduled` - Get scheduled deadline notifications for RFQs

### Contractor Management
- `GET /api/contractors` - List contractors with trade filtering
- `GET /api/contractors/rfqs/all` - Get all active RFQs for contractors
- `POST /api/contractors/favorites` - Manage favorite contractor relationships

### Authentication & Permissions
The platform supports comprehensive multi-level authentication and authorization:
- **Clerk Authentication**: Modern authentication with JWT-based sessions and automatic token refresh
- **API Key Authentication**: JWT tokens for programmatic access with scoped permissions:
  - `read-only`: GET requests for data retrieval
  - `upload-only`: POST requests for data creation and file uploads  
  - `full-access`: All operations (GET, POST, PUT, PATCH, DELETE)
- **Role-Based Access Control**: 3-tier permission system with organization-scoped data access:
  - `Organization Owner`: Full organization access and management
  - `Admin`: Complete organization management and user administration
  - `User`: Standard access to create and manage RFQs and bids

### AI Processing Features
All document uploads and bid submissions automatically trigger comprehensive AI processing:
- **Document Analysis**: Project details, specifications, contact information, timelines
- **Bid Analysis Dashboard**: Complete AI-powered analysis with Executive Summary, Bid Ranking, and Market Analysis panels
- **Rapid AI Generation**: Sub-3-second analysis powered by Groq with comprehensive fallback system
- **Competitive Intelligence**: Market positioning, pricing analysis, risk assessment, and strategic recommendations
- **Real-time Insights**: Instant bid evaluation with competitive scoring, reasoning, and actionable recommendations
- **Risk Assessment**: Timeline feasibility, contractor reliability, project complexity
- **Strategic Recommendations**: Actionable insights for bid evaluation and decision making

## 🧪 Security Testing

### Local (API-only mode)
1) Start API-only server so the SPA never intercepts /api/*:
```bash
npm run dev:api
# or on a custom port
PORT=5001 npm run dev:api
```
2) In another shell, run tests (set BASE_URL if not using 5000):
```bash
node tests/test-rfq-update-access.js
node tests/test-docs-files-access.js
node tests/test-rate-limits.js
node tests/test-admin-routes.js
```
3) Optional: generate an endpoint inventory
```bash
node scripts/generate-route-map.cjs
# outputs docs/route-map.md and docs/route-map.json
```

Notes
- Denial semantics: non-owner access may yield 404 to avoid leaking resource existence (tests accept 403 or 404).
- File streaming may be skipped in local/dev if object storage isn’t configured; the access checks are still validated.

### CI Security Tests
A dedicated job (security-tests) runs in CI:
- Starts the API-only server on port 5001 (BIDAIBLE_TEST=1)
- Requires TEST_DATABASE_URL secret (Neon/PG) to run end-to-end
- Executes the four security tests and uploads the route map as an artifact

See [.github/workflows/ci.yml].

## 📞 Support

For technical support or questions:
- Review the Help and Support page
- Check the project issues on GitHub

## 📄 License

This project is proprietary software. All rights reserved.

---

