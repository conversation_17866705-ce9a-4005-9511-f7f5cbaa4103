# API Documentation

## Overview

The Bidaible API provides RESTful endpoints for managing construction RFQs, contractor profiles, bids, and advanced AI-powered bid analysis. The platform features dual authentication supporting both session-based and API key-based access with enterprise-level security and comprehensive analytics. The system includes a complete AI Bid Analysis system with executive summaries, competitive ranking, and market analysis powered by Groq Kimi K2 model for sub-3-second generation.

## Authentication

### Dual Authentication System
The platform supports two authentication methods:

#### 1. Session-Based Authentication (Web Interface)
- **Provider**: Replit Auth with OpenID Connect
- **Storage**: PostgreSQL-backed sessions with connect-pg-simple
- **Flow**: OAuth 2.0 with automatic session management

#### 2. API Key Authentication (Programmatic Access)
- **Technology**: JWT-based tokens with SHA-256 hashing
- **Permissions**: Scoped access control (read-only, upload-only, full-access)
- **Security**: Rate limiting, usage tracking, and comprehensive audit trails

### API Key Management

#### Generate API Key
```http
POST /api/auth/api-keys
Content-Type: application/json

{
  "name": "Integration API Key",
  "permissions": "full-access",
  "rateLimit": 1000
}
```

**Response:**
```json
{
  "id": "string",
  "name": "string", 
  "permissions": "read-only|upload-only|full-access",
  "rateLimit": "number",
  "apiKey": "string",
  "createdAt": "timestamp",
  "expiresAt": "timestamp",
  "isActive": "boolean"
}
```

#### AI Bid Analysis Endpoint
```http
GET /api/rfqs/:rfqId/bids/analysis
```

**Response:**
```json
{
  "executiveSummary": {
    "overview": "string",
    "keyInsights": ["string"],
    "recommendations": ["string"],
    "riskFactors": ["string"]
  },
  "bidRanking": [
    {
      "bidId": "string",
      "contractorName": "string",
      "score": "number",
      "reasoning": "string",
      "riskLevel": "Low|Medium|High",
      "competitivePosition": "string"
    }
  ],
  "marketAnalysis": {
    "priceSpread": {
      "min": "number",
      "max": "number",
      "average": "number",
      "median": "number"
    },
    "competitivePositioning": "string",
    "riskAssessment": "string"
  },
  "generatedAt": "timestamp",
  "model": "groq|gemini|openai",
  "processingTime": "number"
}
```

### List API Keys
```http
GET /api/auth/api-keys
```

**Response:**
```json
[
  {
    "id": "string",
    "name": "string",
    "permissions": "string", 
    "rateLimit": "number",
    "createdAt": "timestamp",
    "lastUsedAt": "timestamp",
    "expiresAt": "timestamp",
    "isActive": "boolean"
  }
]
```

#### Update API Key
```http
PATCH /api/auth/api-keys/:id
Content-Type: application/json

{
  "name": "Updated Name",
  "isActive": false,
  "rateLimit": 500
}
```

#### Delete API Key
```http
DELETE /api/auth/api-keys/:id
```

#### Get Usage Statistics
```http
GET /api/auth/api-keys/:id/stats
```

**Response:**
```json
{
  "totalRequests": "number",
  "requestsToday": "number",
  "requestsThisMonth": "number",
  "lastUsedAt": "timestamp",
  "rateLimit": "number",
  "remainingRequests": "number"
}
```

### Authentication Headers
```http
# Session-based (automatic with cookies)
Cookie: connect.sid=...

# API Key-based
Authorization: Bearer your-jwt-api-key
```

### Permission Scopes
- **read-only**: GET requests for data retrieval
- **upload-only**: POST requests for data creation and file uploads
- **full-access**: All operations (GET, POST, PUT, PATCH, DELETE)

### Rate Limiting
- Configurable per-key limits (default: 100 requests/hour)
- Daily usage tracking with detailed analytics
- Automatic throttling with 429 status codes

## User Management

### Get Current User
```http
GET /api/auth/user
```

**Response:**
```json
{
  "id": "string",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "profileImageUrl": "string",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

## AI-Powered Bid Analysis

### Get Comprehensive Bid Analysis
```http
GET /api/analytics/bid-analysis/:rfqId
```

**Response:**
```json
{
  "rfq": {
    "id": "string",
    "projectName": "string",
    "status": "string"
  },
  "bidSummary": {
    "totalBids": "number",
    "acceptedBids": "number",
    "rejectedBids": "number",
    "pendingBids": "number",
    "baseTotal": "number",
    "bufferAmount": "number",
    "totalWithBuffer": "number"
  },
  "bids": [
    {
      "bid": {
        "id": "string",
        "bidAmount": "number",
        "extractedAmount": "number",
        "timeline": "string",
        "extractedTimeline": "string",
        "extractedScope": "string",
        "extractedConditions": "string",
        "status": "string",
        "submittedAt": "timestamp",
        "aiSummary": "string",
        "competitiveScore": "number",
        "aiAnalysis": {
          "strengths": ["string"],
          "concerns": ["string"],
          "competitivePosition": "string",
          "priceAnalysis": "string",
          "timelineAssessment": "string",
          "contractorProfile": "string",
          "riskFactors": ["string"],
          "recommendations": ["string"]
        }
      },
      "contractor": {
        "id": "string",
        "companyName": "string",
        "primaryContactName": "string",
        "tradeTypes": "object",
        "yearsInBusiness": "number",
        "workforceSize": "number"
      }
    }
  ]
}
```

### Generate Individual Bid Analysis
```http
POST /api/bids/:bidId/analysis
```

**Response:**
```json
{
  "aiSummary": "string",
  "competitiveScore": "number",
  "aiAnalysis": {
    "strengths": ["string"],
    "concerns": ["string"],
    "competitivePosition": "string",
    "priceAnalysis": "string",
    "timelineAssessment": "string",
    "contractorProfile": "string",
    "riskFactors": ["string"],
    "recommendations": ["string"]
  }
}
```

## RFQ Management

### List RFQs
```http
GET /api/rfqs
```

**Response:**
```json
[
  {
    "id": "string",
    "projectName": "string",
    "projectLocation": "string",
    "description": "string",
    "tradeCategory": "electrical|plumbing|hvac|concrete|general|site_work",
    "status": "Draft|Active|Review|Closed|Awarded",
    "dueDate": "timestamp",
    "createdAt": "timestamp",
    "createdBy": "string",
    "contactName": "string",
    "contactEmail": "string",
    "projectSummary": "string",
    "requirements": "string",
    "finalAwardDate": "string",
    "extractedData": "object",
    "aiSummary": "string",
    "bufferPercentage": "number"
  }
]
```

### Submit Bid with AI Analysis
```http
POST /api/rfqs/:rfqId/bids
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
- `files`: Bid proposal documents
- `bidAmount`: Proposed bid amount (optional - AI can extract)
- `timeline`: Project timeline (optional - AI can extract)
- `proposalText`: Additional proposal text

**Response:**
```json
{
  "id": "string",
  "rfqId": "string",
  "contractorId": "string",
  "bidAmount": "number",
  "timeline": "string", 
  "extractedAmount": "number",
  "extractedTimeline": "string",
  "extractedScope": "string",
  "extractedConditions": "string",
  "status": "submitted",
  "submittedAt": "timestamp",
  "aiSummary": "string",
  "competitiveScore": "number"
}
```

### Manage Bid Actions
```http
POST /api/bids/:bidId/action
Content-Type: application/json
```

**Request Body:**
```json
{
  "action": "accept|reject|request_info",
  "notes": "string",
  "message": "string"
}
```

## ERP & Integration Endpoints

### QuickBooks Export
```http
GET /api/integrations/quickbooks?startDate=2025-01-01&endDate=2025-12-31
```

**Response:**
```json
{
  "exportData": {
    "customers": [...],
    "items": [...],
    "estimates": [...],
    "invoices": [...]
  },
  "totalProjects": "number",
  "totalValue": "number",
  "exportedAt": "timestamp"
}
```

### Sage ERP Synchronization
```http
POST /api/integrations/sage
Content-Type: application/json
```

**Request Body:**
```json
{
  "operation": "sync|export|import",
  "data": "object",
  "options": {
    "includeCompleted": "boolean",
    "dateRange": {
      "start": "date",
      "end": "date"
    }
  }
}
```

### Export RFQ Data
```http
GET /api/integrations/export/rfqs?format=csv&startDate=2025-01-01&endDate=2025-12-31
```

**Query Parameters:**
- `format`: csv | json
- `startDate`: ISO date string
- `endDate`: ISO date string
- `status`: Filter by RFQ status

## Contractor Management

### List Contractors
```http
GET /api/contractors?tradeType=electrical&verified=true
```

**Response:**
```json
[
  {
    "id": "string",
    "companyName": "string",
    "primaryContactName": "string",
    "primaryContactEmail": "string",
    "primaryContactPhone": "string",
    "tradeTypes": ["electrical", "hvac"],
    "yearsInBusiness": "number",
    "workforceSize": "number",
    "certifications": ["NECA", "IBEW"],
    "licenseNumber": "string",
    "licenseState": "string",
    "verificationStatus": "pending|verified|rejected",
    "businessAddress": "string",
    "projectRadius": "number"
  }
]
```

### Get RFQs for Contractors
```http
GET /api/contractors/rfqs/all
```

**Response:**
```json
[
  {
    "id": "string",
    "projectName": "string",
    "projectLocation": "string",
    "tradeCategory": "string",
    "dueDate": "timestamp",
    "status": "Active",
    "description": "string",
    "canBid": "boolean"
  }
]
```

### Create RFQ with AI Processing
```http
POST /api/rfqs
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
- `files`: One or more document files
  - Supported formats: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, CSV, TXT
  - Maximum file size: 50MB per file
  - Upload-only interface - AI automatically extracts all RFQ data

**Advanced AI Processing Pipeline:**
1. **Document Analysis**: PDF.js extracts clean text from PDFs (24K+ characters typical)
2. **Multi-Provider AI**: Configurable primary model (OpenAI GPT-4o-mini, Google Gemini 2.0 Flash, or Groq Llama) with intelligent fallback
3. **Redundancy**: Multiple AI providers ensure high availability and processing reliability
4. **Data Validation**: Extracted data cleaned and mapped to database schema
5. **Auto-Save**: Complete RFQ created automatically without manual input
6. **AI Summary**: Comprehensive markdown summary generated automatically

**AI-Extracted Fields:**
- `fileName`: Original document name
- `projectName`: Extracted project name
- `projectDescription`: Comprehensive project details
- `projectLocation`: Full address and location information
- `contactName`: Primary contact person
- `contactEmail`: Contact email address  
- `projectSummary`: Executive project summary
- `requirements`: Detailed project requirements array
- `finalAwardDate`: Award deadline date
- `scope`: Detailed scope of work
- `owner`: Project owner/client
- `aiSummary`: Comprehensive markdown-formatted summary

**Location Extraction:**
The system intelligently extracts and parses location data:
- Full addresses with city, state, zip codes
- Partial addresses with cross-streets
- Project site descriptions
- Fallback to available location text

**Response:**
```json
{
  "id": "uuid",
  "projectName": "string",
  "projectLocation": "string", 
  "description": "string",
  "tradeCategory": "general",
  "status": "Draft",
  "dueDate": "timestamp",
  "createdAt": "timestamp",
  "createdBy": "string",
  "aiSummary": "string",
  "extractedData": {
    "fileName": "string",
    "projectName": "string",
    "projectDescription": "string",
    "projectLocation": "string",
    "contactName": "string",
    "contactEmail": "string",
    "projectSummary": "string",
    "requirements": ["string"],
    "finalAwardDate": "string",
    "scope": "string",
    "owner": "string",
    "aiSummary": "string"
  }
}
```

### Get RFQ by ID
```http
GET /api/rfqs/:id
```

**Response:**
Same as Create RFQ response, includes full extracted data and AI summary

### Update RFQ
```http
PUT /api/rfqs/:id
Content-Type: application/json
```

**Request Body:**
```json
{
  "projectName": "string",
  "projectLocation": "string", 
  "description": "string",
  "tradeCategory": "electrical|plumbing|hvac|concrete|general|site_work",
  "status": "Draft|Active|Review|Closed|Awarded",
  "dueDate": "timestamp"
}
```

### Get RFQ Documents
```http
GET /api/rfqs/:id/documents
```

**Response:**
```json
[
  {
    "id": "string",
    "rfqId": "string",
    "fileName": "string",
    "fileUrl": "string",
    "uploadedAt": "timestamp",
    "extractedText": "string"
  }
]
```

## Contractor Management

### List Contractors
```http
GET /api/contractors
```

**Response:**
```json
[
  {
    "id": "string",
    "userId": "string",
    "companyName": "string",
    "contactName": "string",
    "email": "string",
    "phone": "string",
    "address": "string",
    "licenseNumber": "string",
    "insuranceInfo": "string",
    "tradeTypes": ["general_contractor", "electrician", "plumber", "hvac", "sitework", "concrete", "masonry", "structural_steel", "carpentry", "roofing", "waterproofing", "insulation", "drywall", "flooring", "painting", "fire_protection", "security_systems", "landscaping", "asphalt_paving", "surveying", "environmental", "demolition", "utilities", "telecommunications", "glazing", "metal_fabrication", "elevator", "architectural_millwork", "specialty_other"],
    "yearsExperience": "number",
    "certifications": ["string"],
    "portfolio": "string",
    "verified": "boolean",
    "createdAt": "timestamp"
  }
]
```

### Create Contractor Profile
```http
POST /api/contractors
Content-Type: application/json
```

**Request Body:**
```json
{
  "companyName": "string",
  "contactName": "string",
  "email": "string",
  "phone": "string",
  "address": "string",
  "licenseNumber": "string",
  "insuranceInfo": "string",
  "tradeTypes": ["string"], // From 29 available trade categories
  "yearsExperience": "number",
  "certifications": ["string"],
  "portfolio": "string"
}
```

### Get Contractor by ID
```http
GET /api/contractors/:id
```

### Update Contractor
```http
PUT /api/contractors/:id
Content-Type: application/json
```

## Bid Management

### List Bids for RFQ
```http
GET /api/rfqs/:rfqId/bids
```

**Response:**
```json
[
  {
    "id": "string",
    "rfqId": "string",
    "contractorId": "string",
    "amount": "number",
    "proposal": "string",
    "timeline": "string",
    "notes": "string",
    "status": "Draft|Submitted|Under Review|Accepted|Rejected",
    "submittedAt": "timestamp"
  }
]
```

### Create Bid
```http
POST /api/bids
Content-Type: application/json
```

**Request Body:**
```json
{
  "rfqId": "string",
  "amount": "number",
  "proposal": "string",
  "timeline": "string",
  "notes": "string"
}
```

### Update Bid
```http
PUT /api/bids/:id
Content-Type: application/json
```

## Material Forecasting

### Get Material Forecasts
```http
GET /api/forecast-materials
```

**Response:**
```json
[
  {
    "id": "string",
    "name": "string",
    "description": "string",
    "currentPrice": "number",
    "unit": "string",
    "trend30Day": "up|down|stable",
    "priceChange": "number",
    "marketAnalysis": "string",
    "lastUpdated": "timestamp",
    "costCode": "string"
  }
]
```

### Create Material Forecast
```http
POST /api/forecast-materials
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "string",
  "description": "string",
  "currentPrice": "number",
  "unit": "string",
  "costCode": "string"
}
```

### Update Material Forecast
```http
PUT /api/forecast-materials/:id
Content-Type: application/json
```

### Delete Material Forecast
```http
DELETE /api/forecast-materials/:id
```

## Dashboard Statistics

### Get Dashboard Stats
```http
GET /api/dashboard/stats
```

**Response:**
```json
{
  "activeRfqs": "number",
  "totalBids": "number",
  "avgResponseTime": "number",
  "successRate": "number"
}
```

## Error Handling

### Standard Error Response
```json
{
  "message": "Error description"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### AI Processing Errors
```json
{
  "message": "AI processing failed",
  "details": {
    "primaryProvider": "openai",
    "fallbackUsed": "gemini",
    "processingTime": "5.2s",
    "extractedFields": ["fileName", "projectDescription"]
  }
}
```

## Rate Limiting

API endpoints are subject to rate limiting to ensure fair usage:
- 100 requests per minute per user
- 1000 requests per hour per user
- File upload endpoints: 10 requests per minute

## File Upload Specifications

### Supported File Types
- **PDF**: `.pdf` (Primary support with PDF.js)
- **Microsoft Word**: `.doc`, `.docx`
- **PowerPoint**: `.ppt`, `.pptx`
- **Excel**: `.xls`, `.xlsx`
- **CSV**: `.csv`
- **Plain Text**: `.txt`

### File Size Limits
- Maximum file size: 50MB
- Maximum files per request: 5

### Upload Process
1. Files are processed by multi-provider AI for data extraction
2. PDF.js extracts text from PDF files for optimal processing
3. Extracted data is automatically mapped to RFQ fields
4. AI generates comprehensive markdown summaries
5. Original files are stored and referenced in the database
6. File URLs are provided for download access

## AI Processing Details

### Multi-Provider Architecture
The system uses three AI providers with intelligent fallback:

1. **OpenAI GPT-4o-mini**: Fast, reliable, structured output
2. **Google Gemini 2.0 Flash**: High accuracy, excellent reasoning
3. **Groq Llama**: Ultra-fast inference, cost-effective

### Document Analysis
When files are uploaded to `/api/rfqs`, they are automatically processed by AI to extract:
- Project information and specifications
- Contact details and communication information
- Timeline, deadlines, and important dates
- Requirements, qualifications, and evaluation criteria
- Scope of work and deliverables
- Budget and pricing information
- Comprehensive project summaries in markdown format

### Processing Pipeline
1. **File Validation**: Check file type and size
2. **Text Extraction**: Extract readable text from documents (PDF.js for PDFs)
3. **AI Analysis**: Process text with configured primary AI model
4. **Fallback Processing**: Retry with alternative AI providers if needed
5. **Data Mapping**: Map extracted data to database schema
6. **Validation**: Clean and validate extracted information
7. **Storage**: Save both raw and processed data
8. **Summary Generation**: Create comprehensive AI summary

### AI Model Configuration
The API supports configurable AI models via environment variables:
- Primary model can be OpenAI, Gemini, or Groq
- Intelligent fallback processing across multiple providers ensures reliability
- Processing results include provider information and performance metrics

## Recent API Updates

### Latest Enhancements
- **AI Summary**: Added `aiSummary` field to RFQ responses with markdown-formatted summaries
- **Enhanced Location Extraction**: Improved address parsing and location data extraction
- **Multi-Provider Support**: Added Groq Llama with intelligent fallback
- **Better Error Handling**: Enhanced error responses with AI processing details
- **Performance Optimization**: Faster processing with intelligent provider selection

### Breaking Changes
- None - All updates are backward compatible

## Support

For API support:
- Check the troubleshooting section in the main documentation
- Review error response details for AI processing issues
- Verify API key configuration for all enabled providers
- Monitor request logs for debugging information

---

### Database Schema
The application uses a comprehensive relational schema with 30+ strategic indexes:
- `users`: User accounts and profiles with role-based permissions
- `contractors`: Contractor information, verification status, and 29 trade categories
- `contractor_favorites`: GC-managed favorite contractor relationships
- `rfqs`: Request for Quote records with AI-extracted data and analysis
- `rfq_documents`: Uploaded document references with Object Storage links
- `rfq_distribution`: Smart distribution tracking with favorites/broadcast modes
- `bids`: Bid submissions with AI analysis, competitive scoring, and risk assessment
- `api_keys`: JWT-based API authentication with scoped permissions and usage tracking
- `api_key_usage`: Comprehensive usage analytics and rate limiting data
- `waitlist`: Pre-launch user registration with contact information and company details

---

Built with multi-provider AI for maximum reliability and performance.