/**
 * Sync Clerk Organizations Script
 * 
 * This script syncs organizations from Clerk to the Bidaible database
 * for the legitimate users.
 */

import { Pool, neonConfig } from '@neondatabase/serverless';
import { clerkClient } from '@clerk/express';
import ws from 'ws';
import * as dotenv from 'dotenv';

dotenv.config();
neonConfig.webSocketConstructor = ws;

const pool = new Pool({ connectionString: process.env.DATABASE_URL });

const LEGITIMATE_USERS = [
  '<EMAIL>',
  '<EMAIL>'
];

async function getUserByEmail(email) {
  try {
    const users = await clerkClient.users.getUserList({ emailAddress: [email] });
    return users.data.length > 0 ? users.data[0] : null;
  } catch (error) {
    console.error(`Error fetching user ${email} from Clerk:`, error.message);
    return null;
  }
}

async function getUserOrganizations(userId) {
  try {
    const memberships = await clerkClient.users.getOrganizationMembershipList({ userId });
    return memberships.data;
  } catch (error) {
    console.error(`Error fetching organizations for user ${userId}:`, error.message);
    return [];
  }
}

async function createOrganizationInBidaible(clerkOrg, clerkUserId) {
  // Generate a new UUID for Bidaible (since Clerk IDs aren't valid UUIDs)
  const insertQuery = `
    INSERT INTO organizations (name, slug, description, created_at, updated_at)
    VALUES ($1, $2, $3, $4, $5)
    ON CONFLICT (slug) DO UPDATE SET
      name = EXCLUDED.name,
      description = EXCLUDED.description,
      updated_at = NOW()
    RETURNING id, name, slug
  `;
  
  const values = [
    clerkOrg.name,
    clerkOrg.slug || `org-${clerkOrg.id.toLowerCase()}`,
    `Organization synced from Clerk: ${clerkOrg.name} (Clerk ID: ${clerkOrg.id})`,
    new Date(clerkOrg.createdAt),
    new Date()
  ];
  
  try {
    const result = await pool.query(insertQuery, values);
    return result.rows[0];
  } catch (error) {
    console.error(`Error creating organization ${clerkOrg.name}:`, error.message);
    return null;
  }
}

async function updateUserOrganization(userId, organizationId) {
  const updateQuery = `
    UPDATE users 
    SET organization_id = $1, updated_at = NOW()
    WHERE id = $2
    RETURNING id, email, organization_id
  `;
  
  try {
    const result = await pool.query(updateQuery, [organizationId, userId]);
    return result.rows[0];
  } catch (error) {
    console.error(`Error updating user ${userId} organization:`, error.message);
    return null;
  }
}

async function syncClerkOrganizations(dryRun = true) {
  console.log(`🔄 ${dryRun ? 'DRY RUN - ' : ''}Syncing Clerk organizations...\n`);
  
  let totalOrgsCreated = 0;
  let totalUsersUpdated = 0;
  
  for (const email of LEGITIMATE_USERS) {
    console.log(`👤 Processing user: ${email}`);
    
    // Get user from Clerk
    const clerkUser = await getUserByEmail(email);
    if (!clerkUser) {
      console.log('  ❌ User not found in Clerk\n');
      continue;
    }
    
    // Get user from Bidaible database
    const dbUserQuery = `SELECT id, organization_id FROM users WHERE email = $1`;
    const dbUserResult = await pool.query(dbUserQuery, [email]);
    const dbUser = dbUserResult.rows[0];
    
    if (!dbUser) {
      console.log('  ❌ User not found in Bidaible database\n');
      continue;
    }
    
    // Get Clerk organizations
    const memberships = await getUserOrganizations(clerkUser.id);
    
    if (memberships.length === 0) {
      console.log('  🏢 No Clerk organizations to sync\n');
      continue;
    }
    
    // Find the primary organization (usually the first one, or admin role)
    const primaryMembership = memberships.find(m => m.role === 'org:admin') || memberships[0];
    const primaryOrg = primaryMembership.organization;
    
    console.log(`  🏢 Primary organization: ${primaryOrg.name} (${primaryOrg.id})`);
    console.log(`  👔 Role: ${primaryMembership.role}`);
    
    // Check if organization exists in Bidaible (by slug since Clerk IDs aren't UUIDs)
    const dbOrgQuery = `SELECT id, name, slug FROM organizations WHERE slug = $1 OR name = $2`;
    const dbOrgResult = await pool.query(dbOrgQuery, [primaryOrg.slug, primaryOrg.name]);
    
    let bidaibleOrg;
    
    if (dbOrgResult.rows.length === 0) {
      console.log(`  📝 Creating organization in Bidaible...`);
      
      if (!dryRun) {
        bidaibleOrg = await createOrganizationInBidaible(primaryOrg, clerkUser.id);
        if (bidaibleOrg) {
          console.log(`  ✅ Created: ${bidaibleOrg.name} (${bidaibleOrg.slug})`);
          totalOrgsCreated++;
        } else {
          console.log(`  ❌ Failed to create organization`);
          continue;
        }
      } else {
        console.log(`  🧪 Would create: ${primaryOrg.name}`);
      }
    } else {
      bidaibleOrg = dbOrgResult.rows[0];
      console.log(`  ✅ Organization already exists: ${bidaibleOrg.name}`);
    }
    
    // Update user's organization if needed (use Bidaible UUID, not Clerk ID)
    const targetOrgId = bidaibleOrg ? bidaibleOrg.id : (dbOrgResult.rows[0] ? dbOrgResult.rows[0].id : null);
    
    if (!dbUser.organization_id || dbUser.organization_id !== targetOrgId) {
      console.log(`  🔄 Updating user organization...`);
      
      if (!dryRun && targetOrgId) {
        const updatedUser = await updateUserOrganization(dbUser.id, targetOrgId);
        if (updatedUser) {
          console.log(`  ✅ User organization updated to: ${targetOrgId}`);
          totalUsersUpdated++;
        } else {
          console.log(`  ❌ Failed to update user organization`);
        }
      } else {
        console.log(`  🧪 Would update user organization to: ${targetOrgId}`);
      }
    } else {
      console.log(`  ✅ User already in correct organization`);
    }
    
    console.log('');
  }
  
  return { totalOrgsCreated, totalUsersUpdated };
}

async function main() {
  console.log('🔄 Clerk Organization Sync Script');
  console.log('==================================\n');

  if (!process.env.CLERK_SECRET_KEY) {
    console.error('❌ CLERK_SECRET_KEY environment variable is required');
    process.exit(1);
  }

  const isDryRun = process.argv.includes('--dry-run') || process.argv.includes('-n');
  const isConfirmed = process.argv.includes('--confirm') || process.argv.includes('-y');
  
  if (!isDryRun && !isConfirmed) {
    console.log('🛡️  Safety check: Add --confirm to execute, or --dry-run to preview');
    console.log('   Example: node scripts/sync-clerk-orgs.js --confirm');
    console.log('   Example: node scripts/sync-clerk-orgs.js --dry-run');
    return;
  }

  try {
    const { totalOrgsCreated, totalUsersUpdated } = await syncClerkOrganizations(isDryRun);
    
    if (!isDryRun) {
      console.log(`✅ Sync completed!`);
      console.log(`📊 Organizations created: ${totalOrgsCreated}`);
      console.log(`👥 Users updated: ${totalUsersUpdated}`);
    } else {
      console.log(`🧪 Dry run completed - no changes made`);
    }
    
  } catch (error) {
    console.error('❌ Sync failed:', error);
    console.error('Stack:', error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

main().catch(console.error);
