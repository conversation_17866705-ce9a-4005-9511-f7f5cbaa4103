/**
 * Backup Service for Database and File Storage
 * Provides automated backup strategies and recovery utilities
 */

import { exec } from "child_process";
import { promisify } from "util";
import fs from "fs/promises";
import path from "path";
import { storage } from "../storage";

const execAsync = promisify(exec);

// Parse DATABASE_URL to extract components safely for backup operations
function parseDatabaseUrl(url: string) {
  try {
    const parsed = new URL(url);
    return {
      user: parsed.username,
      password: parsed.password,
      host: parsed.hostname,
      port: parseInt(parsed.port) || 5432,
      database: parsed.pathname.slice(1), // Remove leading slash
      ssl: parsed.searchParams.get('sslmode') === 'require'
    };
  } catch (error) {
    throw new Error("Invalid DATABASE_URL format for backup operations");
  }
}

// Backup configuration
const BACKUP_CONFIG = {
  DATABASE_URL: process.env.DATABASE_URL,
  BACKUP_RETENTION_DAYS: 30,
  BACKUP_DIRECTORY: "./backups",
  MAX_BACKUP_SIZE: 1024 * 1024 * 1024, // 1GB
};

interface BackupMetadata {
  id: string;
  type: "database" | "files" | "full";
  timestamp: Date;
  size: number;
  path: string;
  checksum?: string;
  status: "pending" | "completed" | "failed";
}

/**
 * Generate backup ID
 */
function generateBackupId(): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  return `backup_${timestamp}_${Math.random().toString(36).substr(2, 8)}`;
}

/**
 * Ensure backup directory exists
 */
async function ensureBackupDirectory(): Promise<void> {
  try {
    await fs.mkdir(BACKUP_CONFIG.BACKUP_DIRECTORY, { recursive: true });
  } catch (error) {
    console.error("Failed to create backup directory:", error);
    throw error;
  }
}

/**
 * Calculate file checksum (simplified MD5)
 */
async function calculateChecksum(filePath: string): Promise<string> {
  try {
    const { stdout } = await execAsync(`md5sum "${filePath}"`);
    return stdout.split(' ')[0];
  } catch (error) {
    console.warn("Failed to calculate checksum:", error);
    return '';
  }
}

/**
 * Create database backup using pg_dump
 */
export async function createDatabaseBackup(): Promise<BackupMetadata> {
  const backupId = generateBackupId();
  const backupPath = path.join(BACKUP_CONFIG.BACKUP_DIRECTORY, `${backupId}_database.sql`);
  
  await ensureBackupDirectory();
  
  const metadata: BackupMetadata = {
    id: backupId,
    type: "database",
    timestamp: new Date(),
    size: 0,
    path: backupPath,
    status: "pending",
  };

  try {
    // Create database backup using pg_dump
    if (!BACKUP_CONFIG.DATABASE_URL) {
      throw new Error("DATABASE_URL environment variable not found");
    }

    // Parse database URL to use individual components for security
    const dbConfig = parseDatabaseUrl(BACKUP_CONFIG.DATABASE_URL);
    
    // Use environment variables to pass credentials securely to pg_dump
    const pgDumpEnv = {
      ...process.env,
      PGHOST: dbConfig.host,
      PGPORT: dbConfig.port.toString(),
      PGDATABASE: dbConfig.database,
      PGUSER: dbConfig.user,
      PGPASSWORD: dbConfig.password,
    };
    
    const pgDumpCommand = `pg_dump > "${backupPath}"`;
    await execAsync(pgDumpCommand, { env: pgDumpEnv });

    // Get file size
    const stats = await fs.stat(backupPath);
    metadata.size = stats.size;
    metadata.checksum = await calculateChecksum(backupPath);
    metadata.status = "completed";

    console.log(`Database backup created: ${backupId} (${metadata.size} bytes)`);
    return metadata;
  } catch (error) {
    metadata.status = "failed";
    console.error("Database backup failed:", error);
    throw error;
  }
}

/**
 * Create file storage backup
 * Note: In production, this would integrate with Object Storage backup APIs
 */
export async function createFileStorageBackup(): Promise<BackupMetadata> {
  const backupId = generateBackupId();
  const backupPath = path.join(BACKUP_CONFIG.BACKUP_DIRECTORY, `${backupId}_files.tar.gz`);
  
  await ensureBackupDirectory();
  
  const metadata: BackupMetadata = {
    id: backupId,
    type: "files",
    timestamp: new Date(),
    size: 0,
    path: backupPath,
    status: "pending",
  };

  try {
    // In a real implementation, this would backup Object Storage files
    // For now, we create a manifest of file metadata
    const rfqDocuments = await storage.getRfqs();
    const fileManifest = {
      timestamp: new Date(),
      totalRfqs: rfqDocuments.length,
      files: [], // Would contain file metadata
    };

    const manifestPath = path.join(BACKUP_CONFIG.BACKUP_DIRECTORY, `${backupId}_file_manifest.json`);
    await fs.writeFile(manifestPath, JSON.stringify(fileManifest, null, 2));

    const stats = await fs.stat(manifestPath);
    metadata.size = stats.size;
    metadata.checksum = await calculateChecksum(manifestPath);
    metadata.status = "completed";
    metadata.path = manifestPath;

    console.log(`File storage backup manifest created: ${backupId}`);
    return metadata;
  } catch (error) {
    metadata.status = "failed";
    console.error("File storage backup failed:", error);
    throw error;
  }
}

/**
 * Create full backup (database + files)
 */
export async function createFullBackup(): Promise<{
  database: BackupMetadata;
  files: BackupMetadata;
}> {
  console.log("Starting full backup...");
  
  const [databaseBackup, filesBackup] = await Promise.all([
    createDatabaseBackup(),
    createFileStorageBackup(),
  ]);

  console.log("Full backup completed successfully");
  return {
    database: databaseBackup,
    files: filesBackup,
  };
}

/**
 * List available backups
 */
export async function listBackups(): Promise<BackupMetadata[]> {
  try {
    await ensureBackupDirectory();
    const files = await fs.readdir(BACKUP_CONFIG.BACKUP_DIRECTORY);
    
    const backups: BackupMetadata[] = [];
    
    for (const file of files) {
      if (file.endsWith('.sql') || file.endsWith('.json') || file.endsWith('.tar.gz')) {
        const filePath = path.join(BACKUP_CONFIG.BACKUP_DIRECTORY, file);
        const stats = await fs.stat(filePath);
        
        const [, timestamp, type] = file.split('_');
        const backupType = file.includes('database') ? 'database' : 
                          file.includes('files') ? 'files' : 'full';
        
        backups.push({
          id: file.replace(/\.(sql|json|tar\.gz)$/, ''),
          type: backupType as "database" | "files" | "full",
          timestamp: stats.birthtime,
          size: stats.size,
          path: filePath,
          status: "completed",
        });
      }
    }
    
    return backups.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  } catch (error) {
    console.error("Failed to list backups:", error);
    return [];
  }
}

/**
 * Clean up old backups based on retention policy
 */
export async function cleanupOldBackups(): Promise<number> {
  const backups = await listBackups();
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - BACKUP_CONFIG.BACKUP_RETENTION_DAYS);
  
  let deletedCount = 0;
  
  for (const backup of backups) {
    if (backup.timestamp < cutoffDate) {
      try {
        await fs.unlink(backup.path);
        deletedCount++;
        console.log(`Deleted old backup: ${backup.id}`);
      } catch (error) {
        console.error(`Failed to delete backup ${backup.id}:`, error);
      }
    }
  }
  
  return deletedCount;
}

/**
 * Restore database from backup
 */
export async function restoreDatabase(backupId: string): Promise<void> {
  const backups = await listBackups();
  const backup = backups.find(b => b.id === backupId && b.type === 'database');
  
  if (!backup) {
    throw new Error(`Database backup not found: ${backupId}`);
  }

  try {
    if (!BACKUP_CONFIG.DATABASE_URL) {
      throw new Error("DATABASE_URL environment variable not found");
    }

    // Parse database URL to use individual components for security
    const dbConfig = parseDatabaseUrl(BACKUP_CONFIG.DATABASE_URL);
    
    // Use environment variables to pass credentials securely to psql
    const psqlEnv = {
      ...process.env,
      PGHOST: dbConfig.host,
      PGPORT: dbConfig.port.toString(),
      PGDATABASE: dbConfig.database,
      PGUSER: dbConfig.user,
      PGPASSWORD: dbConfig.password,
    };
    
    const restoreCommand = `psql < "${backup.path}"`;
    await execAsync(restoreCommand, { env: psqlEnv });
    
    console.log(`Database restored from backup: ${backupId}`);
  } catch (error) {
    console.error("Database restore failed:", error);
    throw error;
  }
}

/**
 * Get backup statistics
 */
export async function getBackupStats(): Promise<{
  totalBackups: number;
  totalSize: number;
  oldestBackup: Date | null;
  newestBackup: Date | null;
  byType: Record<string, number>;
}> {
  const backups = await listBackups();
  
  const stats = {
    totalBackups: backups.length,
    totalSize: backups.reduce((sum, b) => sum + b.size, 0),
    oldestBackup: backups.length > 0 ? new Date(Math.min(...backups.map(b => b.timestamp.getTime()))) : null,
    newestBackup: backups.length > 0 ? new Date(Math.max(...backups.map(b => b.timestamp.getTime()))) : null,
    byType: {} as Record<string, number>,
  };
  
  for (const backup of backups) {
    stats.byType[backup.type] = (stats.byType[backup.type] || 0) + 1;
  }
  
  return stats;
}

/**
 * Schedule automatic backups (in production, this would use a proper job scheduler)
 */
export function scheduleAutomaticBackups(): void {
  // Daily database backup at 2 AM
  const databaseBackupInterval = 24 * 60 * 60 * 1000; // 24 hours
  setInterval(async () => {
    try {
      await createDatabaseBackup();
      await cleanupOldBackups();
    } catch (error) {
      console.error("Scheduled database backup failed:", error);
    }
  }, databaseBackupInterval);

  // Weekly file backup on Sundays at 3 AM
  const fileBackupInterval = 7 * 24 * 60 * 60 * 1000; // 7 days
  setInterval(async () => {
    try {
      await createFileStorageBackup();
    } catch (error) {
      console.error("Scheduled file backup failed:", error);
    }
  }, fileBackupInterval);

  console.log("Automatic backup scheduling enabled");
}