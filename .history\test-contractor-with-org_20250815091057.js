// Test contractor creation and update with proper organization setup
import { storage } from './server/storage.ts';

async function testWithOrganization() {
  try {
    console.log('🧪 Testing contractor creation with organization...');
    
    // First, create a test organization
    console.log('🏢 Creating test organization...');
    const newOrg = await storage.createOrganization({
      name: 'Test Construction Company',
      slug: 'test-construction-' + Date.now(),
      description: 'Test organization for contractor testing',
      userLimit: 15,
      isActive: true
    });
    
    if (newOrg) {
      console.log('✅ Organization created successfully:', newOrg.id, newOrg.name);
      
      // Now create a contractor with the organization ID
      console.log('📝 Creating test contractor...');
      const newContractor = await storage.createContractor({
        organizationId: newOrg.id, // Required field
        companyName: 'Test Contractor Inc.',
        primaryContactName: '<PERSON>',
        primaryContactEmail: '<EMAIL>',
        primaryContactPhone: '555-0123',
        primaryAddress: '123 Test St, Test City, TS 12345',
        licenseNumber: 'LIC123456',
        yearsInBusiness: 10,
        tradeTypes: ['General Construction', 'Electrical'],
        isApproved: false
      });
      
      if (newContractor) {
        console.log('✅ Contractor created successfully:', newContractor.id, newContractor.companyName);
        
        // Now test the update
        console.log('🔄 Testing update functionality...');
        const updatedName = 'Updated Test Contractor - ' + Date.now();
        const result = await storage.updateContractor(newContractor.id, {
          companyName: updatedName,
          primaryContactName: 'Jane Doe Updated',
          yearsInBusiness: 15
        });
        
        if (result) {
          console.log('✅ Update successful!');
          console.log('   - New company name:', result.companyName);
          console.log('   - New contact name:', result.primaryContactName);
          console.log('   - New years in business:', result.yearsInBusiness);
          console.log('   - ID remains:', result.id);
        } else {
          console.log('❌ Update returned null/undefined');
        }
        
        // Verify the update by fetching the contractor again
        console.log('🔍 Verifying update by fetching contractor...');
        const fetchedContractor = await storage.getContractorById(newContractor.id);
        if (fetchedContractor) {
          console.log('✅ Verification successful:');
          console.log('   - Fetched company name:', fetchedContractor.companyName);
          console.log('   - Fetched contact name:', fetchedContractor.primaryContactName);
          console.log('   - Fetched years in business:', fetchedContractor.yearsInBusiness);
        } else {
          console.log('❌ Could not fetch updated contractor');
        }
        
      } else {
        console.log('❌ Failed to create contractor');
      }
      
    } else {
      console.log('❌ Failed to create organization');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
  process.exit(0);
}

testWithOrganization();
