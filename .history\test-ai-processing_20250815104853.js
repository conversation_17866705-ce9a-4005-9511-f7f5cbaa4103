/**
 * Simple AI Processing Test
 * Tests the AI processing pipeline to identify where it stops
 */

import { processRfqDocumentOptimized } from './server/services/aiOptimizedService.js';
import fs from 'fs';
import path from 'path';

const TEST_FILE = './attached_assets/RFQ #2021-301 Response - ACCENT ELECTRICAL_1753707304170.pdf';

async function testAIProcessing() {
  console.log('🧪 Testing AI Processing Pipeline...');
  
  try {
    // Check if test file exists
    if (!fs.existsSync(TEST_FILE)) {
      console.log('❌ Test file not found:', TEST_FILE);
      return;
    }
    
    console.log('✅ Test file found:', TEST_FILE);
    const fileStats = fs.statSync(TEST_FILE);
    console.log(`📊 File size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);
    
    // Test AI processing with progress tracking
    console.log('🤖 Starting AI processing...');
    const result = await processRfqDocumentOptimized(TEST_FILE, 'test-rfq.pdf', {
      onProgress: (progress) => {
        console.log(`📊 Progress: ${progress.stage} - ${progress.percentage}% - ${progress.message}`);
      },
      timeout: 60000,
      retries: 1
    });
    
    console.log('🎯 AI Processing Result:');
    console.log('- Success:', result.success);
    console.log('- Model:', result.model);
    console.log('- Processing Time:', result.processingTime, 'ms');
    console.log('- Retry Count:', result.retryCount);
    console.log('- Has Structured Data:', !!result.structuredData);
    console.log('- Has Extracted Text:', !!result.extractedText);
    
    if (result.structuredData) {
      console.log('📋 Structured Data Keys:', Object.keys(result.structuredData));
      if (result.structuredData.aiSummary) {
        console.log('📝 AI Summary Preview:', result.structuredData.aiSummary.substring(0, 200) + '...');
      }
    }
    
    if (result.success) {
      console.log('✅ AI Processing completed successfully!');
    } else {
      console.log('❌ AI Processing failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testAIProcessing().catch(console.error);
