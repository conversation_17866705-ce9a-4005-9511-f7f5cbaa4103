{"id": "3346c7c5-1711-4d0d-9297-11b64e837cb3", "prevId": "1838dc2e-17db-4dd8-9f1a-d86ba818f742", "version": "7", "dialect": "postgresql", "tables": {"public.bid_documents": {"name": "bid_documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "bid_id": {"name": "bid_id", "type": "uuid", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"bid_documents_bid_id_bids_id_fk": {"name": "bid_documents_bid_id_bids_id_fk", "tableFrom": "bid_documents", "tableTo": "bids", "columnsFrom": ["bid_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bids": {"name": "bids", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "rfq_id": {"name": "rfq_id", "type": "uuid", "primaryKey": false, "notNull": false}, "contractor_id": {"name": "contractor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "bid_amount": {"name": "bid_amount", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "proposal_text": {"name": "proposal_text", "type": "text", "primaryKey": false, "notNull": false}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'Pending'"}}, "indexes": {}, "foreignKeys": {"bids_rfq_id_rfqs_id_fk": {"name": "bids_rfq_id_rfqs_id_fk", "tableFrom": "bids", "tableTo": "rfqs", "columnsFrom": ["rfq_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bids_contractor_id_contractors_id_fk": {"name": "bids_contractor_id_contractors_id_fk", "tableFrom": "bids", "tableTo": "contractors", "columnsFrom": ["contractor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contractors": {"name": "contractors", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "company_website": {"name": "company_website", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "legal_structure": {"name": "legal_structure", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "dba": {"name": "dba", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "primary_address": {"name": "primary_address", "type": "text", "primaryKey": false, "notNull": false}, "mailing_address": {"name": "mailing_address", "type": "text", "primaryKey": false, "notNull": false}, "primary_contact_name": {"name": "primary_contact_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "primary_contact_email": {"name": "primary_contact_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "primary_contact_phone": {"name": "primary_contact_phone", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "primary_contact_title": {"name": "primary_contact_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "trade_types": {"name": "trade_types", "type": "jsonb", "primaryKey": false, "notNull": false}, "union_status": {"name": "union_status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "union_affiliations": {"name": "union_affiliations", "type": "text", "primaryKey": false, "notNull": false}, "certifications": {"name": "certifications", "type": "jsonb", "primaryKey": false, "notNull": false}, "service_areas": {"name": "service_areas", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "license_state": {"name": "license_state", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "license_expiration": {"name": "license_expiration", "type": "timestamp", "primaryKey": false, "notNull": false}, "general_liability": {"name": "general_liability", "type": "text", "primaryKey": false, "notNull": false}, "workers_comp": {"name": "workers_comp", "type": "text", "primaryKey": false, "notNull": false}, "auto_insurance": {"name": "auto_insurance", "type": "text", "primaryKey": false, "notNull": false}, "bonding_single": {"name": "bonding_single", "type": "integer", "primaryKey": false, "notNull": false}, "bonding_aggregate": {"name": "bonding_aggregate", "type": "integer", "primaryKey": false, "notNull": false}, "emr": {"name": "emr", "type": "numeric(4, 2)", "primaryKey": false, "notNull": false}, "bank_reference": {"name": "bank_reference", "type": "text", "primaryKey": false, "notNull": false}, "surety_reference": {"name": "surety_reference", "type": "text", "primaryKey": false, "notNull": false}, "credit_rating": {"name": "credit_rating", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "payment_terms": {"name": "payment_terms", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "litigation_history": {"name": "litigation_history", "type": "text", "primaryKey": false, "notNull": false}, "project_references": {"name": "project_references", "type": "text", "primaryKey": false, "notNull": false}, "years_in_business": {"name": "years_in_business", "type": "integer", "primaryKey": false, "notNull": false}, "specializations": {"name": "specializations", "type": "jsonb", "primaryKey": false, "notNull": false}, "awards": {"name": "awards", "type": "text", "primaryKey": false, "notNull": false}, "environmental_programs": {"name": "environmental_programs", "type": "text", "primaryKey": false, "notNull": false}, "workforce_size": {"name": "workforce_size", "type": "integer", "primaryKey": false, "notNull": false}, "workforce_breakdown": {"name": "workforce_breakdown", "type": "text", "primaryKey": false, "notNull": false}, "equipment": {"name": "equipment", "type": "text", "primaryKey": false, "notNull": false}, "availability": {"name": "availability", "type": "text", "primaryKey": false, "notNull": false}, "keyword_tags": {"name": "keyword_tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "preferred_project_types": {"name": "preferred_project_types", "type": "jsonb", "primaryKey": false, "notNull": false}, "business_address": {"name": "business_address", "type": "text", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "is_approved": {"name": "is_approved", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"contractors_user_id_users_id_fk": {"name": "contractors_user_id_users_id_fk", "tableFrom": "contractors", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forecast_materials": {"name": "forecast_materials", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "cost_code": {"name": "cost_code", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "current_price": {"name": "current_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "previous_price": {"name": "previous_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "change_percent": {"name": "change_percent", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "volume": {"name": "volume", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time": {"name": "time", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "ytd_change": {"name": "ytd_change", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "ytd_change_percent": {"name": "ytd_change_percent", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rfq_documents": {"name": "rfq_documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "rfq_id": {"name": "rfq_id", "type": "uuid", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "object_key": {"name": "object_key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "extracted_text": {"name": "extracted_text", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"rfq_documents_rfq_id_rfqs_id_fk": {"name": "rfq_documents_rfq_id_rfqs_id_fk", "tableFrom": "rfq_documents", "tableTo": "rfqs", "columnsFrom": ["rfq_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rfqs": {"name": "rfqs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "project_name": {"name": "project_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "project_location": {"name": "project_location", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "trade_category": {"name": "trade_category", "type": "trade", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "rfq_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'Draft'"}, "extracted_data": {"name": "extracted_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"rfqs_created_by_users_id_fk": {"name": "rfqs_created_by_users_id_fk", "tableFrom": "rfqs", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sid": {"name": "sid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "sess": {"name": "sess", "type": "jsonb", "primaryKey": false, "notNull": true}, "expire": {"name": "expire", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"IDX_session_expire": {"name": "IDX_session_expire", "columns": [{"expression": "expire", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "profile_image_url": {"name": "profile_image_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'Viewer'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.rfq_status": {"name": "rfq_status", "schema": "public", "values": ["Draft", "Active", "Review", "Closed", "Awarded"]}, "public.trade": {"name": "trade", "schema": "public", "values": ["electrical", "plumbing", "hvac", "concrete", "general", "site_work"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["SuperUser", "Admin", "Editor", "Viewer"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}