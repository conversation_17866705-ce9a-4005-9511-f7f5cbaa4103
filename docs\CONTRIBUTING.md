# Contributing Guide

## Welcome Contributors

Thank you for your interest in contributing to Bidaible! This guide will help you understand our development process, coding standards, and how to submit contributions effectively.

## Table of Contents

- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Code Standards](#code-standards)
- [Development Workflow](#development-workflow)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Guidelines](#issue-guidelines)
- [Documentation](#documentation)
- [Community](#community)

## Getting Started

### Prerequisites

Before contributing, ensure you have:
- Node.js 18+ installed
- PostgreSQL database access (Neon recommended)
- Git configured with your name and email
- Code editor with TypeScript support (VS Code recommended)

### Repository Setup

1. **Fork the repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/your-username/bidaible.git
   cd bidaible
   ```

2. **Add upstream remote**
   ```bash
   git remote add upstream https://github.com/original-repo/bidaible.git
   ```

3. **Install dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   # Fill in your environment variables
   ```

5. **Database setup**
   ```bash
   npm run db:push
   ```

## Development Setup

### Local Development

1. **Start development server**
   ```bash
   npm run dev
   ```

2. **Access the application**
   - Frontend: http://localhost:5000
   - API: http://localhost:5000/api

### Environment Variables

Create a `.env` file with required variables:

```env
# Required for development
DATABASE_URL=your_database_url
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
GROQ_API_KEY=your_groq_key
SESSION_SECRET=your_session_secret

# Optional for development
PERPLEXITY_API_KEY=your_perplexity_key
PRIMARY_MODEL=openai
```

### VS Code Setup

Recommended extensions:
- TypeScript and JavaScript Language Features
- Tailwind CSS IntelliSense
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint

## Code Standards

### TypeScript Guidelines

1. **Use strict typing**
   ```typescript
   // Good
   interface User {
     id: string;
     email: string;
     name?: string;
   }

   // Avoid
   const user: any = getData();
   ```

2. **Prefer interfaces over types for object shapes**
   ```typescript
   // Good
   interface ComponentProps {
     title: string;
     onClick: () => void;
   }

   // Good for unions
   type Status = 'loading' | 'success' | 'error';
   ```

3. **Use proper return types**
   ```typescript
   // Good
   async function fetchUser(id: string): Promise<User | null> {
     // implementation
   }
   ```

### React Guidelines

1. **Functional components with hooks**
   ```typescript
   // Good
   function UserProfile({ userId }: { userId: string }) {
     const [user, setUser] = useState<User | null>(null);
     
     useEffect(() => {
       fetchUser(userId).then(setUser);
     }, [userId]);
     
     return <div>{user?.name}</div>;
   }
   ```

2. **Custom hooks for reusable logic**
   ```typescript
   // Good
   function useUser(userId: string) {
     const [user, setUser] = useState<User | null>(null);
     const [loading, setLoading] = useState(true);
     
     useEffect(() => {
       setLoading(true);
       fetchUser(userId)
         .then(setUser)
         .finally(() => setLoading(false));
     }, [userId]);
     
     return { user, loading };
   }
   ```

3. **Proper prop interfaces**
   ```typescript
   interface ButtonProps {
     children: React.ReactNode;
     variant?: 'primary' | 'secondary';
     onClick?: () => void;
     disabled?: boolean;
   }

   function Button({ children, variant = 'primary', ...props }: ButtonProps) {
     return <button className={cn(buttonVariants({ variant }))} {...props}>{children}</button>;
   }
   ```

### Backend Guidelines

1. **Use async/await over promises**
   ```typescript
   // Good
   async function createRfq(data: InsertRfq): Promise<Rfq> {
     try {
       const rfq = await storage.createRfq(data);
       return rfq;
     } catch (error) {
       throw new Error(`Failed to create RFQ: ${error.message}`);
     }
   }
   ```

2. **Proper error handling**
   ```typescript
   // Good
   app.post('/api/rfqs', asyncHandler(async (req, res) => {
     const data = insertRfqSchema.parse(req.body);
     const rfq = await storage.createRfq(data);
     res.status(201).json(rfq);
   }));
   ```

3. **Input validation with Zod**
   ```typescript
   // Good
   const createRfqSchema = z.object({
     projectName: z.string().min(1),
     projectLocation: z.string().optional(),
     tradeCategory: z.enum(['electrical', 'plumbing', 'hvac', 'concrete', 'general', 'site_work'])
   });
   ```

### Database Guidelines

1. **Use Drizzle ORM properly**
   ```typescript
   // Good
   async function getUserRfqs(userId: string): Promise<Rfq[]> {
     return await db
       .select()
       .from(rfqs)
       .where(eq(rfqs.createdBy, userId))
       .orderBy(desc(rfqs.createdAt));
   }
   ```

2. **Proper schema definitions**
   ```typescript
   // Good
   export const rfqs = pgTable("rfqs", {
     id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
     projectName: varchar("project_name").notNull(),
     createdAt: timestamp("created_at").defaultNow()
   });
   ```

### Styling Guidelines

1. **Use Tailwind CSS classes**
   ```typescript
   // Good
   <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md">
     Submit
   </button>
   ```

2. **Use shadcn/ui components**
   ```typescript
   // Good
   import { Button } from "@/components/ui/button";
   import { Input } from "@/components/ui/input";

   <Button variant="outline" size="sm">
     Cancel
   </Button>
   ```

3. **Custom CSS only when necessary**
   ```css
   /* Only for complex animations or unavoidable custom styling */
   .custom-animation {
     animation: slideIn 0.3s ease-out;
   }
   ```

## Development Workflow

### Branch Naming

Use descriptive branch names:
- `feature/rfq-bulk-upload` - New features
- `fix/pdf-extraction-error` - Bug fixes
- `docs/api-documentation` - Documentation updates
- `refactor/auth-service` - Code refactoring
- `chore/update-dependencies` - Maintenance tasks

### Commit Messages

Follow conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
```
feat(rfq): add AI-powered document extraction

- Implement PDF text extraction with OpenAI
- Add structured data mapping
- Include error handling for unsupported formats

Closes #123
```

```
fix(auth): resolve session expiration issue

- Fix token refresh logic
- Add proper error handling for expired tokens
- Update authentication middleware

Fixes #456
```

### Development Process

1. **Create feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make changes with frequent commits**
   ```bash
   git add .
   git commit -m "feat(component): add new functionality"
   ```

3. **Keep branch updated**
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

4. **Test your changes**
   ```bash
   npm run test
   npm run type-check
   npm run lint
   ```

5. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## Testing Guidelines

### Frontend Testing

1. **Component testing with React Testing Library**
   ```typescript
   import { render, screen } from '@testing-library/react';
   import { Button } from './Button';

   test('renders button with text', () => {
     render(<Button>Click me</Button>);
     expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
   });
   ```

2. **Hook testing**
   ```typescript
   import { renderHook } from '@testing-library/react';
   import { useUser } from './useUser';

   test('fetches user data', async () => {
     const { result } = renderHook(() => useUser('123'));
     expect(result.current.loading).toBe(true);
   });
   ```

### Backend Testing

1. **API endpoint testing**
   ```typescript
   import request from 'supertest';
   import { app } from '../app';

   describe('POST /api/rfqs', () => {
     test('creates new RFQ', async () => {
       const response = await request(app)
         .post('/api/rfqs')
         .send({ projectName: 'Test Project' })
         .expect(201);
       
       expect(response.body.projectName).toBe('Test Project');
     });
   });
   ```

2. **Service testing**
   ```typescript
   import { DatabaseStorage } from '../storage';

   describe('DatabaseStorage', () => {
     test('creates RFQ successfully', async () => {
       const storage = new DatabaseStorage();
       const rfq = await storage.createRfq({
         projectName: 'Test',
         createdBy: 'user123'
       });
       
       expect(rfq.id).toBeDefined();
     });
   });
   ```

### Testing Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix
```

## Pull Request Process

### Before Submitting

1. **Ensure tests pass**
   ```bash
   npm run test
   npm run type-check
   npm run lint
   ```

2. **Update documentation** if needed

3. **Test manually** in development environment

4. **Rebase on latest main**
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

### PR Template

Use this template for pull requests:

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Checklist
- [ ] My code follows the style guidelines
- [ ] I have performed a self-review
- [ ] I have commented my code where necessary
- [ ] I have made corresponding changes to documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
```

### Review Process

1. **Automated checks** must pass
2. **Code review** by maintainer
3. **Testing** in review environment
4. **Approval** and merge by maintainer

## Issue Guidelines

### Bug Reports

Use this template for bug reports:

```markdown
## Bug Description
A clear description of what the bug is.

## Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
What you expected to happen.

## Actual Behavior
What actually happened.

## Environment
- OS: [e.g. macOS, Windows, Linux]
- Browser: [e.g. Chrome, Firefox, Safari]
- Version: [e.g. 1.0.0]

## Additional Context
Add any other context about the problem here.
```

### Feature Requests

Use this template for feature requests:

```markdown
## Feature Description
A clear description of what you want to happen.

## Problem Statement
What problem does this feature solve?

## Proposed Solution
Describe the solution you'd like.

## Alternatives Considered
Describe any alternative solutions you've considered.

## Additional Context
Add any other context or screenshots about the feature request here.
```

## Documentation

### Code Documentation

1. **JSDoc comments for functions**
   ```typescript
   /**
    * Extracts structured data from RFQ documents
    * @param filePath - Path to the uploaded document
    * @param originalName - Original filename for type detection
    * @returns Promise containing extracted RFQ data
    */
   export async function processRfqDocument(
     filePath: string, 
     originalName?: string
   ): Promise<ExtractedRfqData> {
     // implementation
   }
   ```

2. **README updates** for new features

3. **API documentation** for new endpoints

4. **Architecture documentation** for significant changes

### Documentation Standards

- Use clear, concise language
- Include code examples
- Keep documentation up-to-date with changes
- Follow markdown formatting standards

## Community

### Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please be respectful and professional in all interactions.

### Getting Help

- Check existing documentation
- Search existing issues
- Ask questions in discussions
- Contact maintainers for urgent issues

### Recognition

Contributors will be recognized in:
- README contributors section
- Release notes for significant contributions
- Special recognition for major features

## Release Process

### Versioning

We follow semantic versioning (SemVer):
- `MAJOR.MINOR.PATCH`
- Major: Breaking changes
- Minor: New features (backward compatible)
- Patch: Bug fixes (backward compatible)

### Release Schedule

- Regular releases on a monthly basis
- Hotfix releases for critical bugs
- Feature releases when significant functionality is ready

Thank you for contributing to Bidaible! Your contributions help make construction procurement more efficient and intelligent.