import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CheckCircle, XCircle, Package, AlertCircle } from "lucide-react";
import { ScopeItem } from "@/hooks/useStructuredBidData";

interface BidScopeDisplayProps {
  includedItems: ScopeItem[];
  excludedItems: ScopeItem[];
  includedCategories: string[];
  excludedCategories: string[];
}

export function BidScopeDisplay({ 
  includedItems, 
  excludedItems, 
  includedCategories, 
  excludedCategories 
}: BidScopeDisplayProps) {
  const getCategoryColor = (category: string, isIncluded: boolean) => {
    if (isIncluded) {
      const colors = [
        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      ];
      const index = category.charCodeAt(0) % colors.length;
      return colors[index];
    } else {
      const colors = [
        'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      ];
      const index = category.charCodeAt(0) % colors.length;
      return colors[index];
    }
  };

  const groupByCategory = (items: ScopeItem[]) => {
    return items.reduce((acc, item) => {
      const category = item.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(item);
      return acc;
    }, {} as Record<string, ScopeItem[]>);
  };

  const includedByCategory = groupByCategory(includedItems);
  const excludedByCategory = groupByCategory(excludedItems);

  if (includedItems.length === 0 && excludedItems.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Scope Definition
          </CardTitle>
          <CardDescription>
            No structured scope definition available. This bid may have been submitted with documents only.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No scope definition available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Included Items</p>
                <p className="text-2xl font-bold text-green-600">{includedItems.length}</p>
                <p className="text-xs text-muted-foreground">
                  {includedCategories.length} categories
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">Excluded Items</p>
                <p className="text-2xl font-bold text-red-600">{excludedItems.length}</p>
                <p className="text-xs text-muted-foreground">
                  {excludedCategories.length} categories
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Scope Comparison */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Included Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-5 w-5" />
              What's Included
            </CardTitle>
            <CardDescription>
              Items and services covered by this bid
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.keys(includedByCategory).length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No included items specified</p>
              </div>
            ) : (
              Object.entries(includedByCategory).map(([category, items]) => (
                <div key={category} className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge className={getCategoryColor(category, true)}>
                      {category}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      ({items.length} items)
                    </span>
                  </div>
                  <div className="space-y-1 ml-4">
                    {items.map((item) => (
                      <div key={item.id} className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <p className="text-sm">{item.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Excluded Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <XCircle className="h-5 w-5" />
              What's Excluded
            </CardTitle>
            <CardDescription>
              Items and services NOT covered by this bid
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.keys(excludedByCategory).length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No excluded items specified</p>
              </div>
            ) : (
              Object.entries(excludedByCategory).map(([category, items]) => (
                <div key={category} className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge className={getCategoryColor(category, false)}>
                      {category}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      ({items.length} items)
                    </span>
                  </div>
                  <div className="space-y-1 ml-4">
                    {items.map((item) => (
                      <div key={item.id} className="flex items-start gap-2">
                        <XCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                        <p className="text-sm">{item.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>

      {/* Category Overview */}
      {(includedCategories.length > 0 || excludedCategories.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle>Category Overview</CardTitle>
            <CardDescription>
              Quick overview of all categories covered in this scope definition
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {includedCategories.length > 0 && (
                <div>
                  <h4 className="font-medium text-green-600 mb-2 flex items-center gap-1">
                    <CheckCircle className="h-4 w-4" />
                    Included Categories
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {includedCategories.map((category) => (
                      <Badge key={category} className={getCategoryColor(category, true)}>
                        {category}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {excludedCategories.length > 0 && (
                <div>
                  <h4 className="font-medium text-red-600 mb-2 flex items-center gap-1">
                    <XCircle className="h-4 w-4" />
                    Excluded Categories
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {excludedCategories.map((category) => (
                      <Badge key={category} className={getCategoryColor(category, false)}>
                        {category}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}