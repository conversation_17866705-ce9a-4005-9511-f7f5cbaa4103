-- Simple SQL script to identify test data
-- Run with: psql $DATABASE_URL -f scripts/check-test-data-simple.sql

\echo '🔍 Checking for test data in production database...'
\echo ''

-- Check test organizations
\echo '🏢 Test Organizations:'
SELECT name, slug, created_at 
FROM organizations 
WHERE name ILIKE '%test%' 
   OR name <PERSON>IKE '%demo%' 
   OR name ILIKE '%example%'
   OR slug ILIKE '%test%'
   OR slug ILIKE '%demo%';

\echo ''

-- Check test users  
\echo '👥 Test Users:'
SELECT email, first_name, last_name, role, organization_id, created_at
FROM users 
WHERE email ILIKE '%test%' 
   OR email ILIKE '%demo%' 
   OR email ILIKE '%example%'
   OR email ILIKE '%@test.com'
   OR email ILIKE '%@example.com'
   OR first_name ILIKE 'test%'
   OR first_name ILIKE 'demo%';

\echo ''

-- Check API keys for test users
\echo '🔑 Test API Keys:'
SELECT ak.name, ak.permissions, ak.is_active, ak.last_used_at, ak.created_at, u.email
FROM api_keys ak
JOIN users u ON ak.user_id = u.id
WHERE u.email ILIKE '%test%' 
   OR u.email ILIKE '%demo%' 
   OR u.email ILIKE '%example%'
   OR u.first_name ILIKE 'test%';

\echo ''

-- Count summary
\echo '📊 Summary Counts:'
SELECT 
    'Organizations' as type,
    COUNT(*) as count
FROM organizations 
WHERE name ILIKE '%test%' 
   OR name ILIKE '%demo%' 
   OR name ILIKE '%example%'
   OR slug ILIKE '%test%'
   OR slug ILIKE '%demo%'

UNION ALL

SELECT 
    'Users' as type,
    COUNT(*) as count
FROM users 
WHERE email ILIKE '%test%' 
   OR email ILIKE '%demo%' 
   OR email ILIKE '%example%'
   OR email ILIKE '%@test.com'
   OR email ILIKE '%@example.com'
   OR first_name ILIKE 'test%'
   OR first_name ILIKE 'demo%'

UNION ALL

SELECT 
    'API Keys' as type,
    COUNT(*) as count
FROM api_keys ak
JOIN users u ON ak.user_id = u.id
WHERE u.email ILIKE '%test%' 
   OR u.email ILIKE '%demo%' 
   OR u.email ILIKE '%example%'
   OR u.first_name ILIKE 'test%';

\echo ''
\echo '⚠️  To clean up test data, run: node scripts/cleanup-test-data.js --dry-run'
