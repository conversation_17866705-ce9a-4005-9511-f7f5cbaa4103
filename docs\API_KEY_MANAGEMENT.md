
# API Key Management Documentation

## Overview

The Bidaible platform provides comprehensive API key management functionality through an intuitive Settings interface, enabling users to create, manage, and monitor programmatic access to their accounts with enterprise-level security controls.

## Features

### Core API Key Management
- **Create API Keys**: Generate new keys with custom names and scoped permissions
- **View API Keys**: Display existing keys with masked values for security
- **Edit API Keys**: Modify key metadata including name, status, and rate limits
- **Delete API Keys**: Secure deletion with confirmation dialogs

### Security Features
- **Key Masking**: API keys are masked by default showing only partial values (e.g., sk-...xyz123)
- **Visibility Controls**: Toggle visibility with eye icon to reveal full key values
- **Copy to Clipboard**: One-click copying of API keys with visual feedback
- **Confirmation Dialogs**: Destructive actions require explicit confirmation

### Permission Scoping
- **read-only**: View data only (GET requests)
- **upload-only**: Create RFQs and upload documents (POST requests for creation)
- **full-access**: Complete access to account (all HTTP methods)

### Monitoring & Analytics
- **Creation Date**: Track when each key was created
- **Last Used**: Monitor recent key activity
- **Rate Limiting**: Configurable requests per hour (1-10,000)
- **Expiration**: Automatic key expiration for enhanced security

## User Interface

### Settings Page Layout
The API key management is accessible through the Settings page (`/settings`) with a dedicated "API Keys" tab:

```
Settings Page
├── API Keys Tab
│   ├── Header Section
│   │   ├── Title and Description
│   │   └── Create API Key Button
│   ├── API Key List
│   │   ├── Key Cards with Metadata
│   │   ├── Visibility Controls
│   │   ├── Copy Functionality
│   │   └── Action Buttons (Edit/Delete)
│   └── Empty State (when no keys exist)
```

### Create API Key Dialog
- **Name Field**: Custom identifier for the API key
- **Permissions Dropdown**: Select access level (read-only, upload-only, full-access)
- **Rate Limit Input**: Configure requests per hour (default: 100)
- **Create Button**: Generate new key with form validation

### API Key Display
Each API key is displayed in a card format showing:
- **Masked Key Value**: Partial key display with visibility toggle
- **Metadata Grid**: Creation date, last used, rate limit, expiration
- **Status Indicators**: Visual feedback for active/inactive keys
- **Action Controls**: Edit and delete buttons with appropriate permissions

## Technical Implementation

### Form Validation
- **Zod Schema**: Type-safe form validation with React Hook Form
- **Required Fields**: Name and permissions are mandatory
- **Rate Limit Validation**: Numeric input between 1-10,000 requests/hour
- **Real-time Validation**: Immediate feedback on form errors

### State Management
- **TanStack Query**: Server state management with caching
- **Optimistic Updates**: Immediate UI feedback with rollback on errors
- **Cache Invalidation**: Automatic refresh after mutations
- **Error Handling**: Comprehensive error states with user-friendly messages

### Security Measures
- **Session-Only Creation**: API keys can only be created through authenticated sessions
- **User Ownership**: Users can only manage their own API keys
- **Masked Display**: Keys are never stored in full on the client side
- **Secure Deletion**: Immediate revocation with confirmation requirements

## API Integration

### Backend Endpoints
- `POST /api/auth/api-keys` - Create new API key
- `GET /api/auth/api-keys` - List user's API keys (metadata only)
- `PATCH /api/auth/api-keys/:id` - Update API key settings
- `DELETE /api/auth/api-keys/:id` - Revoke API key
- `GET /api/auth/api-keys/:id/stats` - Get usage statistics

### Database Schema
```sql
api_keys
├── id (UUID, Primary Key)
├── user_id (String, Foreign Key)
├── name (String)
├── api_key (String, Hashed)
├── permissions (Enum)
├── rate_limit (Integer)
├── is_active (Boolean)
├── created_at (Timestamp)
├── last_used_at (Timestamp)
├── expires_at (Timestamp)
└── updated_at (Timestamp)
```

## Usage Examples

### Creating an API Key
1. Navigate to Settings → API Keys tab
2. Click "Create API Key" button
3. Fill in name (e.g., "QuickBooks Integration")
4. Select permissions level
5. Set rate limit (optional, defaults to 100/hour)
6. Click "Create" to generate the key
7. Copy the displayed key immediately (won't be shown again)

### Managing Existing Keys
1. View all keys in the main list
2. Toggle visibility to see full key values
3. Copy keys using the copy button
4. Edit metadata using the edit button
5. Delete keys with confirmation dialog

### Using API Keys
Once created, API keys can be used for programmatic access:

```bash
curl -H "Authorization: Bearer your-api-key" \
     https://your-domain/api/rfqs
```

## Best Practices

### Security
- **Rotate Keys Regularly**: Create new keys and delete old ones periodically
- **Principle of Least Privilege**: Use minimal permissions required for each integration
- **Monitor Usage**: Regular review of key activity and usage patterns
- **Secure Storage**: Store API keys securely in environment variables or secure vaults

### Management
- **Descriptive Names**: Use clear, descriptive names for easy identification
- **Appropriate Rate Limits**: Set realistic rate limits based on usage patterns
- **Regular Audits**: Periodically review and clean up unused keys
- **Documentation**: Maintain records of key purposes and integrations

## Troubleshooting

### Common Issues
- **Key Not Working**: Verify the key is active and hasn't expired
- **Permission Denied**: Check that the key has appropriate permissions for the operation
- **Rate Limit Exceeded**: Monitor usage and increase rate limits if needed
- **Key Not Found**: Ensure the key hasn't been deleted or revoked

### Error Messages
- "API key not found": Key was deleted or doesn't exist
- "Insufficient permissions": Key lacks required access level
- "Rate limit exceeded": Too many requests in the time window
- "Invalid API key format": Malformed or corrupted key

## Support

For additional support with API key management:
- Review the main API documentation
- Check the troubleshooting section
- Contact support through the Help & Support page
- Monitor the application logs for detailed error information
