import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import {
  authMiddleware,
  protectedRoute,
  getCurrentUser,
  getAuth,
  clerkClient,
  addUserToRequest,
} from "./clerkAuth";
import {
  insertContractorSchema,
  insertRfqSchema,
  insertBidSchema,
  insertApiKeySchema,
  insertUserFeedbackSchema,
  type InsertRfq,
  type InsertRfqDocument,
  type InsertBidLineItem,
  type InsertBidInclusionsExclusions,
  type InsertBidDocument,
  type InsertForecastMaterial,
  type InsertUserFeedback,
  type InsertUser,
  type InsertContractor,
  type InsertScheduledNotification,
} from "@shared/schema";
import {
  processRfqDocument,
  processBidDocument,
  generateBidAnalysis,
  BidAnalysisResult,
} from "./services/aiService";
import {
  processRfqDocumentOptimized,
  getProcessingStats,
} from "./services/aiOptimizedService";
import {
  uploadFile,
  downloadFile,
  downloadFileStream,
} from "./services/objectStorageService";
import {
  optimizedUpload,
  largeFileUpload,
  fileProcessingMiddleware,
  getFileProcessingStats,
} from "./services/fileProcessingService";
import { progressTracker } from "./services/progressService";
import { AuditService } from "./services/auditService";
import { NotificationService } from "./services/notificationService";
import { cache } from "./services/cacheService";
import { getAuditLogs, getSecurityStats, sensitiveRateLimit } from "./middleware/security";
import {
  getBackupStats,
  createDatabaseBackup,
  createFileStorageBackup,
} from "./services/backupService";
import {
  generateApiKey,
  getApiKeyStats,
  rotateApiKey,
  getKeysNeedingRotation,
  getAdvancedApiKeyStats,
} from "./services/apiKeyService";
import {
  apiAuthentication,
  readOnlyApiAccess,
  writeApiAccess,
  uploadApiAccess,
} from "./middleware/apiAuth";
import analyticsRoutes from "./routes/analytics";
import { registerProcessingStatsRoutes } from "./routes/processingStats";
import { addUnifiedExtractorTestRoute } from "./routes/testUnifiedExtractor";
import { requireOrgAdmin } from "./middleware/orgAuth";
import multer from "multer";
import path from "path";
import fs from "fs";
import os from "os";
import { nanoid } from "nanoid";
import { asInsert } from "./utils/typing";
import { githubService } from "./services/githubService";

// Use optimized upload configuration
const upload = optimizedUpload;

/**
 * Schedule deadline reminder notifications for an RFQ
 * Creates reminders at 1 week, 3 days, and 1 day before bid proposal deadline
 */
async function scheduleRfqDeadlineReminders(
  rfqId: string, 
  userId: string, 
  bidProposalDeadlineAt: Date, 
  projectName: string
): Promise<void> {
  const now = new Date();
  const deadlineDate = new Date(bidProposalDeadlineAt);
  
  // Define reminder intervals: 1 week, 3 days, 1 day before deadline
  const reminderIntervals = [
    { days: 7, label: "1 week" },
    { days: 3, label: "3 days" },
    { days: 1, label: "1 day" }
  ];

  // First, cancel any existing reminders for this RFQ
  try {
    await storage.cancelScheduledNotifications(rfqId, NotificationService.NotificationTypes.RFQ_DEADLINE_REMINDER);
  } catch (error) {
    console.warn("Failed to cancel existing reminders:", error);
  }

  // Schedule new reminders
  for (const interval of reminderIntervals) {
    const reminderDate = new Date(deadlineDate.getTime() - (interval.days * 24 * 60 * 60 * 1000));
    
    // Only schedule if the reminder date is in the future
    if (reminderDate > now) {
      const scheduledNotification = asInsert<InsertScheduledNotification>({
        notificationType: NotificationService.NotificationTypes.RFQ_DEADLINE_REMINDER,
        userId,
        rfqId,
        scheduledFor: reminderDate,
        payload: {
          projectName,
          deadlineDate: deadlineDate.toISOString(),
          reminderType: interval.label,
          rfqId
        }
      });
      
      await storage.createScheduledNotification(scheduledNotification);
      console.log(`📅 Scheduled ${interval.label} reminder for RFQ ${rfqId} at ${reminderDate.toISOString()}`);
    } else {
      console.log(`⏰ Skipping ${interval.label} reminder for RFQ ${rfqId} - date in the past`);
    }
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Clerk auth middleware
  app.use(authMiddleware);

  // Development test endpoint for bypassing authentication
  app.post(
    "/api/test/rfqs",
    upload.array("documents"),
    async (req: any, res) => {
      try {
        // Simple test endpoint that bypasses authentication for development testing
        if (process.env.NODE_ENV !== "development") {
          return res
            .status(403)
            .json({ message: "Test endpoint only available in development" });
        }

        console.log(
          "🧪 Test RFQ endpoint called with files:",
          req.files?.length || 0
        );

        // Mock user for testing
        req.user = {
          claims: {
            sub: "test-user-" + Date.now(),
          },
        };

        // Test with REAL AI processing if files are provided
        if (req.files && req.files.length > 0) {
          console.log("🤖 Testing REAL AI processing with uploaded files...");

          let allExtractedData: any = {};

          for (const file of req.files) {
            try {
              console.log(`🔍 Processing file: ${file.originalname}`);

              // Write buffer to temporary file for AI processing
              const tempFilePath = path.join(
                os.tmpdir(),
                `${Date.now()}-${file.originalname}`
              );
              await fs.promises.writeFile(tempFilePath, file.buffer);

              // Use the real AI processing service
              const { processRfqDocumentOptimized } = await import(
                "./services/aiOptimizedService"
              );
              const result = await processRfqDocumentOptimized(
                tempFilePath,
                file.originalname,
                {
                  onProgress: (progress) => {
                    console.log(
                      `📊 AI Progress: ${progress.stage} - ${progress.percentage}% - ${progress.message}`
                    );
                  },
                  timeout: 60000,
                  retries: 1,
                }
              );

              console.log("🎯 AI Processing Result:", {
                success: result.success,
                model: result.model,
                processingTime: result.processingTime,
                hasStructuredData: !!result.structuredData,
                hasExtractedText: !!result.extractedText,
              });

              if (result.success && result.structuredData) {
                Object.assign(allExtractedData, result.structuredData);
                console.log(
                  "✅ AI processing successful for",
                  file.originalname
                );
              } else {
                console.log("⚠️ AI processing failed for", file.originalname);
              }

              // Clean up temporary file
              await fs.promises.unlink(tempFilePath);
            } catch (aiError) {
              console.error("❌ AI processing error:", aiError);
            }
          }

          // Create test RFQ with real AI data
          const testRfq = {
            id: "test-rfq-" + Date.now(),
            projectName:
              allExtractedData.projectName ||
              req.body.projectName ||
              "Test Project",
            projectLocation:
              allExtractedData.projectLocation ||
              req.body.projectLocation ||
              "Test Location",
            description:
              allExtractedData.projectDescription ||
              req.body.description ||
              "Test RFQ for development",
            tradeCategory: "general",
            status: "Draft",
            createdBy: req.user.claims.sub,
            createdAt: new Date(),
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            extractedData: allExtractedData,
            aiSummary:
              allExtractedData.aiSummary ||
              "AI processing completed - check extractedData for details",
          };

          console.log("✅ Test RFQ created with REAL AI data:", testRfq.id);
          res.status(200).json(testRfq);
        } else {
          // No files - return mock data
          const testRfq = {
            id: "test-rfq-" + Date.now(),
            projectName: req.body.projectName || "Test Project",
            projectLocation: req.body.projectLocation || "Test Location",
            description: req.body.description || "Test RFQ for development",
            tradeCategory: "general",
            status: "Draft",
            createdBy: req.user.claims.sub,
            createdAt: new Date(),
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            extractedData: {
              projectName: req.body.projectName || "Test Project",
              projectLocation: req.body.projectLocation || "Test Location",
              projectDescription:
                req.body.description || "Test RFQ for development",
              tradeTypes: ["general"],
              requirements: ["Test requirement 1", "Test requirement 2"],
              dueDate: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
            },
            aiSummary:
              "This is a test RFQ created for development and testing purposes. The project involves general construction work with standard requirements.",
          };

          console.log("✅ Test RFQ created (no files):", testRfq.id);
          res.status(200).json(testRfq);
        }
      } catch (error) {
        console.error("❌ Test RFQ endpoint error:", error);
        res
          .status(500)
          .json({ message: "Test endpoint failed", error: error.message });
      }
    }
  );

  // Development test fixtures (dev-only)
  app.post("/api/test/org", async (req: any, res) => {
    try {
      if (process.env.NODE_ENV !== "development") {
        return res.status(403).json({ message: "Test endpoint only available in development" });
      }
      const { name, slug } = req.body || {};
      if (!name) return res.status(400).json({ message: "name required" });
      const org = await storage.createOrganization({ name, slug: slug || name.toLowerCase().replace(/[^a-z0-9-]/g, '-') } as any);
      res.json(org);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });
  app.post("/api/test/user", async (req: any, res) => {
    try {
      if (process.env.NODE_ENV !== "development") {
        return res.status(403).json({ message: "Test endpoint only available in development" });
      }
      const { id, email, organizationId, userClassification } = req.body || {};
      if (!id) return res.status(400).json({ message: "id required" });
      const user = await storage.upsertUser({ id, email: email || `${id}@example.com`, organizationId, userClassification } as any);
      res.json(user);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });
  app.post("/api/test/contractor", async (req: any, res) => {
    try {
      if (process.env.NODE_ENV !== "development") {
        return res.status(403).json({ message: "Test endpoint only available in development" });
      }
      const contractor = await storage.createContractor(req.body);
      res.json(contractor);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });
  app.post("/api/test/rfq", async (req: any, res) => {
    try {
      if (process.env.NODE_ENV !== "development") {
        return res.status(403).json({ message: "Test endpoint only available in development" });
      }
      const rfq = await storage.createRfq(req.body);
      res.json(rfq);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });
  app.post("/api/test/rfq-document", async (req: any, res) => {
    try {
      if (process.env.NODE_ENV !== "development") {
        return res.status(403).json({ message: "Test endpoint only available in development" });
      }
      const doc = await storage.createRfqDocument(req.body);
      res.json(doc);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });
  app.post("/api/test/api-key", async (req: any, res) => {
    try {
      if (process.env.NODE_ENV !== "development") {
        return res.status(403).json({ message: "Test endpoint only available in development" });
      }
      const { userId, permissions = 'full-access', rateLimit = 1000 } = req.body || {};
      if (!userId) return res.status(400).json({ message: "userId required" });
      const result = await generateApiKey(userId, `Test Key ${Date.now()}`, permissions as any, rateLimit, { expirationDays: 365, environment: 'development' } as any);
      res.json(result);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });
  app.post("/api/test/distribute", async (req: any, res) => {
    try {
      if (process.env.NODE_ENV !== "development") {
        return res.status(403).json({ message: "Test endpoint only available in development" });
      }
      const { rfqId, contractorIds, method } = req.body || {};
      const rows = await storage.distributeRfq(rfqId, contractorIds || [], method || 'favorites');
      res.json(rows);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });
  app.get("/api/test/whoami", ...readOnlyApiAccess, (req: any, res) => {
    try {
      res.json({ user: req.user, ok: true });
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });

  // Test GitHub integration endpoint
  app.post("/api/test/github-feedback", async (req: any, res) => {
    try {
      const { type, message, priority } = req.body;
      
      // Test GitHub issue creation
      const githubResult = await githubService.createIssue({
        type: type || 'bug',
        message: message || 'Test feedback message',
        priority: priority || 'medium',
        userId: 'test-user-123',
        userEmail: '<EMAIL>',
        organizationName: 'Test Organization',
        feedbackId: 'test-' + Date.now(),
      });

      res.json({
        success: !!githubResult,
        githubConfigured: githubService.isConfigured(),
        result: githubResult,
      });
    } catch (error: any) {
      res.status(500).json({ 
        success: false,
        error: error.message,
        githubConfigured: githubService.isConfigured(),
      });
    }
  });

  // Cost codes endpoint
  app.get("/api/cost-codes", async (req, res) => {
    try {
      const filePath = path.join(
        process.cwd(),
        "attached_assets",
        "consolidated_cost_codes_1753708871258.csv"
      );

      // Check if file exists (async)
      try {
        await fs.promises.access(filePath);
      } catch {
        return res.status(404).json({ error: "Cost codes file not found" });
      }

      // Read and serve the CSV content (async)
      const csvContent = await fs.promises.readFile(filePath, "utf-8");
      res.setHeader("Content-Type", "text/csv");
      res.send(csvContent);
    } catch (error) {
      console.error("Error serving cost codes:", error);
      res.status(500).json({ error: "Failed to load cost codes" });
    }
  });

  // Cost codes stats endpoint for verification
  app.get("/api/cost-codes/stats", async (req, res) => {
    try {
      const filePath = path.join(
        process.cwd(),
        "attached_assets",
        "consolidated_cost_codes_1753708871258.csv"
      );

      try {
        await fs.promises.access(filePath);
      } catch {
        return res.status(404).json({ error: "Cost codes file not found" });
      }

      const csvContent = await fs.promises.readFile(filePath, "utf-8");
      const lines = csvContent.split("\n");
      const totalLines = lines.length;
      const masterCodes = lines
        .slice(1)
        .filter((line) => line.trim())
        .map((line) => line.split(",")[0]?.trim())
        .filter(Boolean);
      const uniqueCodes = Array.from(new Set(masterCodes));
      const duplicates = masterCodes.filter(
        (code, index) => masterCodes.indexOf(code) !== index
      );
      const uniqueDuplicates = Array.from(new Set(duplicates));

      res.json({
        totalLines,
        totalMasterCodes: masterCodes.length,
        uniqueMasterCodes: uniqueCodes.length,
        duplicateCount: uniqueDuplicates.length,
        duplicates: uniqueDuplicates,
        sampleCodes: uniqueCodes.slice(0, 10),
      });
    } catch (error) {
      console.error("Error analyzing cost codes:", error);
      res.status(500).json({ error: "Failed to analyze cost codes" });
    }
  });

  // File serving endpoint
  // Serve attached assets (static files)
  app.get("/attached_assets/:fileName", async (req, res) => {
    try {
      const fileName = req.params.fileName;
      const filePath = path.join(process.cwd(), "attached_assets", fileName);

      // Check if file exists (async)
      try {
        await fs.promises.access(filePath);
      } catch {
        return res.status(404).json({ error: "File not found" });
      }

      // Stat for size
      const stat = await fs.promises.stat(filePath);
      const fileSize = stat.size;

      // Determine content type
      if (fileName.toLowerCase().endsWith(".pdf")) {
        res.setHeader("Content-Type", "application/pdf");
      } else {
        res.setHeader("Content-Type", "application/octet-stream");
      }

      const range = req.headers.range;
      if (range) {
        // bytes=start-end
        const match = /^bytes=(\d+)-(\d+)?$/.exec(range);
        if (!match) {
          return res.status(416).end();
        }
        const start = parseInt(match[1], 10);
        const end = match[2] ? Math.min(parseInt(match[2], 10), fileSize - 1) : fileSize - 1;
        if (start >= fileSize || end < start) {
          return res.status(416).end();
        }
        const chunkSize = end - start + 1;
        res.status(206);
        res.setHeader("Content-Range", `bytes ${start}-${end}/${fileSize}`);
        res.setHeader("Accept-Ranges", "bytes");
        res.setHeader("Content-Length", String(chunkSize));
        // For PDFs, inline; else leave disposition unspecified
        if (fileName.toLowerCase().endsWith(".pdf")) {
          res.setHeader("Content-Disposition", "inline");
        }
        const fileStream = fs.createReadStream(filePath, { start, end });
        fileStream.on("error", (err) => {
          console.error("Attached asset stream error:", err);
          if (!res.headersSent) res.status(500).end();
        });
        return fileStream.pipe(res);
      }

      // No range: stream full file
      res.setHeader("Content-Length", String(fileSize));
      if (fileName.toLowerCase().endsWith(".pdf")) {
        res.setHeader("Content-Disposition", "inline");
      }
      const fileStream = fs.createReadStream(filePath);
      fileStream.on("error", (err) => {
        console.error("Attached asset stream error:", err);
        if (!res.headersSent) res.status(500).end();
      });
      fileStream.pipe(res);
    } catch (error) {
      console.error("Error serving attached asset:", error);
      res.status(500).json({ error: "Failed to serve file" });
    }
  });


  // Serve uploaded files
  app.get(
    "/api/files/:documentId",
    sensitiveRateLimit,
    ...readOnlyApiAccess,
    async (req, res) => {
      try {
        const { documentId } = req.params;

        // Get document metadata from database
        const document = await storage.getRfqDocument(documentId);
        if (!document) {
          return res.status(404).json({ message: "File not found" });
        }

        // Enforce org scoping for General Contractors
        try {
          const rfq = await storage.getRfq(document.rfqId);
          const userId = (req as any).user?.claims?.sub;
          if (rfq && userId) {
            const user = await storage.getUser(userId);
            if (user && user.userClassification === "general_contractor") {
              if (
                !user.organizationId ||
                rfq.organizationId !== user.organizationId
              ) {
                return res.status(403).json({ message: "Access denied" });
              }
            }
          }
        } catch (e) {
          console.warn("File access check failed");
        }

        // Contractor access control: must be distributed or bid owner
        try {
          const userId = (req as any).user?.claims?.sub;
          if (userId) {
            const u = await storage.getUser(userId);
            if (u && u.userClassification === "contractor") {
              const contractor = await storage.getContractorByUserId(userId);
              let allowed = false;
              if (contractor) {
                try {
                  const dists = await storage.getContractorRfqDistributions(contractor.id);
                  allowed = dists.some((d: any) => d.rfqId === document.rfqId);
                } catch {}
                if (!allowed) {
                  const bids = await storage.getBidsByRfq(document.rfqId);
                  allowed = bids.some((b: any) => b.contractorId === contractor.id);
                }
              }
              if (!allowed) {
                return res.status(403).json({ message: "Access denied" });
              }
            }
          }
        } catch {}

        // Stream file from Object Storage
        const rangeHeader = req.headers["range"] as string | undefined;
        const { stream, contentLength, contentType, etag, lastModified, contentRange, acceptRanges } = await downloadFileStream(
          document.objectKey,
          rangeHeader ? { range: rangeHeader } : undefined
        );

        // Check if this is a view request (for PDFs)
        const isView = req.query.view === "true";

        // Set proper headers
        const finalMime =
          document.mimeType || contentType || "application/octet-stream";
        res.setHeader("Content-Type", finalMime);

        if (isView && finalMime === "application/pdf") {
          // For PDF viewing, set inline disposition and iframe-friendly headers
          res.setHeader(
            "Content-Disposition",
            `inline; filename="${document.fileName}"`
          );
          res.setHeader("X-Frame-Options", "SAMEORIGIN");
          res.setHeader("Cache-Control", "no-cache");
        } else {
          // For downloads, set attachment disposition
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${document.fileName}"`
          );
        }

        if (etag) {
          res.setHeader("ETag", etag);
        }
        if (lastModified) {
          res.setHeader("Last-Modified", new Date(lastModified).toUTCString());
        }

        // Conditional requests
        const ifNoneMatch = req.headers["if-none-match"];
        const ifModifiedSince = req.headers["if-modified-since"];
        if (ifNoneMatch && etag && ifNoneMatch.replace(/"/g, "") === etag) {
          return res.status(304).end();
        }
        if (
          ifModifiedSince &&
          lastModified &&
          new Date(ifModifiedSince).getTime() >= new Date(lastModified).getTime()
        ) {
          return res.status(304).end();
        }

        if (contentLength) {
          res.setHeader("Content-Length", contentLength.toString());
        }

        stream.on("error", (err) => {
          console.error("Stream error:", err);
          if (!res.headersSent) {
            res.status(500).json({ message: "File stream failed" });
          } else {
            res.end();
          }
        });

        stream.pipe(res);
      } catch (error) {
        console.error("Error serving file:", error);
        res.status(500).json({ message: "Failed to serve file" });
      }
    }
  );

  // Clerk User endpoint - Create or get user from database when authenticated via Clerk
  app.get("/api/clerk/user", protectedRoute, async (req: any, res) => {
    try {
      const { userId } = getAuth(req);
      if (!userId) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      // Get user from Clerk
      const clerkUser = await clerkClient.users.getUser(userId);

      // Get or create user in our database
      const dbUser = await storage.upsertUser(
        asInsert<InsertUser>({
          id: userId,
          email: clerkUser.emailAddresses[0]?.emailAddress,
          firstName: clerkUser.firstName,
          lastName: clerkUser.lastName,
          profileImageUrl: clerkUser.imageUrl,
        })
      );

      // Ensure user's organizations are synced from Clerk
      const { default: OrganizationSyncService } = await import('./services/organizationSyncService');
      await OrganizationSyncService.ensureUserOrganizationSync(userId);

      // Get updated user (might have new organization)
      const updatedUser = await storage.getUser(userId);
      res.json(updatedUser || dbUser);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Terms acceptance endpoint
  app.patch(
    "/api/clerk/accept-terms",
    protectedRoute,
    async (req: any, res) => {
      try {
        const { userId } = getAuth(req);
        if (!userId) {
          return res.status(401).json({ message: "Unauthorized" });
        }

        // Update user's terms acceptance
        const updatedUser = await storage.acceptUserTerms(userId);

        if (!updatedUser) {
          return res.status(404).json({ message: "User not found" });
        }

        // Log terms acceptance for audit trail
        console.log(
          `✅ Terms accepted by user ${userId} at ${new Date().toISOString()}`
        );

        res.json({
          success: true,
          message: "Terms accepted successfully",
          termsAcceptedAt: updatedUser.termsAcceptedAt,
        });
      } catch (error) {
        console.error("Error accepting terms:", error);
        res.status(500).json({ message: "Failed to accept terms" });
      }
    }
  );

  // Organization admin access control is provided by middleware/orgAuth.ts

  // User management routes
  app.get(
    "/api/admin/users",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const currentUser = req.currentUser;
        if (!currentUser.organizationId) {
          return res.status(403).json({ message: "No organization access" });
        }
        const limit = req.query.limit ? parseInt(String(req.query.limit)) : undefined;
        const offset = req.query.offset ? parseInt(String(req.query.offset)) : undefined;
        const users = await storage.getUsersByOrganization(
          currentUser.organizationId,
          limit || offset ? { limit, offset } : undefined
        );
        res.json(users);
      } catch (error) {
        console.error("Error fetching users:", error);
        res.status(500).json({ message: "Failed to fetch users" });
      }
    }
  );

  app.patch(
    "/api/admin/users/:userId/role",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const { userId } = req.params;
        const { role, reason } = req.body;
        const currentUser = req.currentUser;

        // Allowed roles in DB (legacy field) – limited to org scope
        const allowedRoles = ["OrganizationAdmin", "StandardUser"];
        if (!allowedRoles.includes(role)) {
          return res
            .status(400)
            .json({ message: "Invalid role", allowedRoles });
        }

        // Target user must be in same organization
        const targetUser = await storage.getUser(userId);
        if (
          !targetUser ||
          targetUser.organizationId !== currentUser.organizationId
        ) {
          return res
            .status(403)
            .json({ message: "Cannot modify users outside your organization" });
        }

        const updatedUser = await storage.updateUserRole(
          userId,
          role,
          currentUser.id,
          reason,
          req
        );

        if (!updatedUser) {
          return res.status(404).json({ message: "User not found" });
        }

        console.log(
          `🔒 Security: Role changed for user ${userId} to ${role}. Clerk will handle session management.`
        );

        res.json(updatedUser);
      } catch (error) {
        console.error("Error updating user role:", error);
        res.status(500).json({ message: "Failed to update user role" });
      }
    }
  );

  app.get(
    "/api/admin/audit/roles",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const currentUser = req.currentUser;
        if (!currentUser.organizationId) {
          return res.status(403).json({ message: "No organization access" });
        }
        const limit = req.query.limit ? parseInt(String(req.query.limit)) : undefined;
        const offset = req.query.offset ? parseInt(String(req.query.offset)) : undefined;
        const auditLogs = await storage.getRoleAuditLogs(
          currentUser.organizationId,
          limit || offset ? { limit, offset } : undefined
        );
        res.json(auditLogs);
      } catch (error) {
        console.error("Error fetching role audit logs:", error);
        res.status(500).json({ message: "Failed to fetch audit logs" });
      }
    }
  );

  app.get(
    "/api/admin/audit/access",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const currentUser = req.currentUser;
        if (!currentUser.organizationId) {
          return res.status(403).json({ message: "No organization access" });
        }
        const limit = req.query.limit ? parseInt(String(req.query.limit)) : undefined;
        const offset = req.query.offset ? parseInt(String(req.query.offset)) : undefined;
        const auditLogs = await storage.getAccessAuditLogs(
          currentUser.organizationId,
          limit || offset ? { limit, offset } : undefined
        );
        res.json(auditLogs);
      } catch (error) {
        console.error("Error fetching access audit logs:", error);
        res.status(500).json({ message: "Failed to fetch audit logs" });
      }
    }
  );

  // User activity logs (accessible to all authenticated users for their own activity)
  app.get(
    "/api/user/activity",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;

        // Get user's own business activity logs
        const userActivityLogs = await storage.getBusinessAuditLogs({
          userId,
          limit: 50,
        });

        res.json(userActivityLogs);
      } catch (error) {
        console.error("Error fetching user activity logs:", error);
        res.status(500).json({ message: "Failed to fetch activity logs" });
      }
    }
  );

  // Business audit logs (SuperUser and OrganizationAdmin only)
  app.get(
    "/api/admin/audit/business",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const currentUser = req.currentUser;
        if (!currentUser.organizationId) {
          return res.status(403).json({ message: "No organization access" });
        }
        const businessLogs = await storage.getBusinessAuditLogs(
          currentUser.organizationId
        );
        res.json(businessLogs);
      } catch (error) {
        console.error("Error fetching business audit logs:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch business audit logs" });
      }
    }
  );

  // Backfill business audit logs (SuperUser only)
  app.post(
    "/api/admin/audit/business/backfill",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        console.log("🚀 Starting business audit log backfill...");

        let totalEntries = 0;

        // 1. Backfill RFQ Creation Events
        const rfqs = await storage.getAllRfqs();
        for (const rfq of rfqs) {
          await storage.createBusinessAuditLog({
            userId: rfq.createdBy || "system",
            eventType: "rfq_creation",
            eventData: {
              rfqId: rfq.id,
              projectName: rfq.projectName || "Unnamed Project",
              tradeCategory: rfq.tradeCategory,
              dueDate: rfq.dueDate,
            },
            resourceId: rfq.id,
            ipAddress: "127.0.0.1",
            userAgent: "Backfill Migration",
          });
          totalEntries++;
        }

        // 2. Backfill File Upload Events
        const documents = await storage.getAllRfqDocuments();
        for (const doc of documents) {
          await storage.createBusinessAuditLog({
            userId: "system",
            eventType: "file_upload",
            eventData: {
              fileName: doc.fileName,
              fileSize: doc.fileSize || 0,
              fileType: doc.fileType || "application/pdf",
              objectKey: doc.objectKey,
              rfqId: doc.rfqId,
            },
            resourceId: doc.rfqId,
            ipAddress: "127.0.0.1",
            userAgent: "Backfill Migration",
          });
          totalEntries++;
        }

        // 3. Backfill Bid Submission Events
        const bids = await storage.getAllBids();
        for (const bid of bids) {
          await storage.createBusinessAuditLog({
            userId: bid.contractorId || "unknown",
            eventType: "file_upload", // Using file_upload since bid_submission isn't defined
            eventData: {
              fileName: `Bid Submission - ${bid.id}`,
              fileSize: 0,
              fileType: "application/json",
              bidId: bid.id,
              rfqId: bid.rfqId,
            },
            resourceId: bid.id,
            ipAddress: "127.0.0.1",
            userAgent: "Backfill Migration",
          });
          totalEntries++;

          // Add bid action if status indicates acceptance/rejection
          if (
            bid.status &&
            ["accept", "rejected", "Accepted", "Rejected"].includes(bid.status)
          ) {
            await storage.createBusinessAuditLog({
              userId: "system",
              eventType: "bid_action",
              eventData: {
                bidId: bid.id,
                contractorId: bid.contractorId || "unknown",
                contractorName: "Historical Contractor",
                bidAmount:
                  typeof bid.bidAmount === "string"
                    ? parseFloat(bid.bidAmount)
                    : bid.bidAmount,
                action: bid.status,
                previousStatus: "submitted",
                newStatus: bid.status,
              },
              resourceId: bid.id,
              ipAddress: "127.0.0.1",
              userAgent: "Backfill Migration",
            });
            totalEntries++;
          }
        }

        console.log(
          `🎉 Backfill complete! Created ${totalEntries} audit log entries.`
        );

        res.json({
          success: true,
          message: `Successfully backfilled ${totalEntries} audit log entries`,
          summary: {
            rfqs: rfqs.length,
            documents: documents.length,
            bids: bids.length,
            totalEntries,
          },
        });
      } catch (error) {
        console.error("❌ Error during backfill:", error);
        res.status(500).json({
          message: "Failed to backfill audit logs",
          error: (error as Error).message,
        });
      }
    }
  );

  // API Key Management Routes
  app.post(
    "/api/auth/api-keys",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { name, permissions = "read-only", rateLimit = 100 } = req.body;
        const userId = req.user.claims.sub;

        if (!name || typeof name !== "string") {
          return res.status(400).json({ message: "API key name is required" });
        }

        const validPermissions = ["read-only", "upload-only", "full-access"];
        if (!validPermissions.includes(permissions)) {
          return res.status(400).json({
            message:
              "Invalid permissions. Must be one of: read-only, upload-only, full-access",
          });
        }

        if (
          typeof rateLimit !== "number" ||
          rateLimit < 1 ||
          rateLimit > 10000
        ) {
          return res
            .status(400)
            .json({ message: "Rate limit must be between 1 and 10000" });
        }

        const { apiKey, keyRecord } = await generateApiKey(
          userId,
          name,
          permissions,
          rateLimit
        );

        res.status(201).json({
          id: keyRecord.id,
          name: keyRecord.name,
          apiKey, // This is the only time we return the raw API key
          permissions: keyRecord.permissions,
          rateLimit: keyRecord.rateLimit,
          createdAt: keyRecord.createdAt,
          expiresAt: keyRecord.expiresAt,
        });
      } catch (error) {
        console.error("Error creating API key:", error);
        res.status(500).json({ message: "Failed to create API key" });
      }
    }
  );

  app.get(
    "/api/auth/api-keys",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        const apiKeys = await storage.getApiKeysByUserId(userId);

        // Never return the actual key hash, only metadata
        const safeApiKeys = apiKeys.map((key) => ({
          id: key.id,
          name: key.name,
          permissions: key.permissions,
          rateLimit: key.rateLimit,
          isActive: key.isActive,
          lastUsedAt: key.lastUsedAt,
          expiresAt: key.expiresAt,
          createdAt: key.createdAt,
        }));

        res.json(safeApiKeys);
      } catch (error) {
        console.error("Error fetching API keys:", error);
        res.status(500).json({ message: "Failed to fetch API keys" });
      }
    }
  );

  app.get(
    "/api/auth/api-keys/:id/stats",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { id } = req.params;
        const userId = req.user.claims.sub;

        // Verify the API key belongs to the user
        const apiKey = await storage.getApiKey(id);
        if (!apiKey || apiKey.userId !== userId) {
          return res.status(404).json({ message: "API key not found" });
        }

        const stats = await getApiKeyStats(id);
        res.json(stats);
      } catch (error) {
        console.error("Error fetching API key stats:", error);
        res.status(500).json({ message: "Failed to fetch API key stats" });
      }
    }
  );

  app.patch(
    "/api/auth/api-keys/:id",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { id } = req.params;
        const { name, isActive, rateLimit } = req.body;
        const userId = req.user.claims.sub;

        // Verify the API key belongs to the user
        const apiKey = await storage.getApiKey(id);
        if (!apiKey || apiKey.userId !== userId) {
          return res.status(404).json({ message: "API key not found" });
        }

        const updates: any = {};
        if (name !== undefined) updates.name = name;
        if (isActive !== undefined) updates.isActive = isActive;
        if (rateLimit !== undefined) {
          if (
            typeof rateLimit !== "number" ||
            rateLimit < 1 ||
            rateLimit > 10000
          ) {
            return res
              .status(400)
              .json({ message: "Rate limit must be between 1 and 10000" });
          }
          updates.rateLimit = rateLimit;
        }

        const updatedKey = await storage.updateApiKey(id, updates);
        if (!updatedKey) {
          return res.status(404).json({ message: "API key not found" });
        }

        res.json({
          id: updatedKey.id,
          name: updatedKey.name,
          permissions: updatedKey.permissions,
          rateLimit: updatedKey.rateLimit,
          isActive: updatedKey.isActive,
          lastUsedAt: updatedKey.lastUsedAt,
          expiresAt: updatedKey.expiresAt,
          createdAt: updatedKey.createdAt,
        });
      } catch (error) {
        console.error("Error updating API key:", error);
        res.status(500).json({ message: "Failed to update API key" });
      }
    }
  );

  app.delete(
    "/api/auth/api-keys/:id",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { id } = req.params;
        const userId = req.user.claims.sub;

        // Verify the API key belongs to the user
        const apiKey = await storage.getApiKey(id);
        if (!apiKey || apiKey.userId !== userId) {
          return res.status(404).json({ message: "API key not found" });
        }

        const success = await storage.deleteApiKey(id);
        if (!success) {
          return res.status(404).json({ message: "API key not found" });
        }

        res.json({ message: "API key deleted successfully" });
      } catch (error) {
        console.error("Error deleting API key:", error);
        res.status(500).json({ message: "Failed to delete API key" });
      }
    }
  );

  // Integration Endpoints
  app.get(
    "/api/integrations/quickbooks",
    ...readOnlyApiAccess,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;

        // Get all RFQs created by the user
        const rfqs = await storage.getRfqsByUser(userId);

        // Get all bids with detailed cost code breakdown for those RFQs
        const rfqsWithBids = await Promise.all(
          rfqs.map(async (rfq) => {
            const bids = await storage.getBidsByRfq(rfq.id);

            // **CRITICAL ENHANCEMENT**: Include bid line items (cost codes) for each bid
            const bidsWithLineItems = await Promise.all(
              bids.map(async (bid) => {
                const lineItems = await storage.getBidLineItems(bid.id);
                return { ...bid, lineItems };
              })
            );

            return { ...rfq, bids: bidsWithLineItems };
          })
        );

        // Format data for QuickBooks integration
        const quickbooksData = {
          projectSummary: {
            totalProjects: rfqs.length,
            activeProjects: rfqs.filter((r) => r.status === "Active").length,
            completedProjects: rfqs.filter((r) => r.status === "Closed").length,
          },
          financialData: rfqsWithBids.map((rfq) => ({
            projectId: rfq.id,
            projectName: rfq.projectName,
            location: rfq.projectLocation,
            totalBids: rfq.bids.length,
            lowestBid:
              rfq.bids.length > 0
                ? Math.min(
                    ...rfq.bids.map((b) => parseFloat(b.bidAmount || "0"))
                  )
                : 0,
            highestBid:
              rfq.bids.length > 0
                ? Math.max(
                    ...rfq.bids.map((b) => parseFloat(b.bidAmount || "0"))
                  )
                : 0,
            averageBid:
              rfq.bids.length > 0
                ? rfq.bids.reduce(
                    (sum, b) => sum + parseFloat(b.bidAmount || "0"),
                    0
                  ) / rfq.bids.length
                : 0,
            dueDate: rfq.dueDate,
            status: rfq.status,
            tradeCategory: rfq.tradeCategory,
            bufferPercentage: rfq.bufferPercentage,
            estimatedTotal:
              rfq.bids.length > 0
                ? Math.min(
                    ...rfq.bids.map((b) => parseFloat(b.bidAmount || "0"))
                  ) *
                  (1 + parseFloat(rfq.bufferPercentage || "10") / 100)
                : 0,

            // **CRITICAL ENHANCEMENT**: Detailed cost code breakdown for QuickBooks project budgets
            costCodeBreakdown: rfq.bids
              .filter((bid) => bid.lineItems && bid.lineItems.length > 0)
              .map((bid) => ({
                bidId: bid.id,
                contractorName: bid.contractorId, // Will be resolved to company name in production
                bidAmount: parseFloat(bid.bidAmount || "0"),
                lineItems: bid.lineItems.map((item) => ({
                  costCode: item.costCode,
                  description: item.description,
                  quantity: parseFloat(item.quantity || "0"),
                  unitPrice: parseFloat(item.unitPrice || "0"),
                  totalPrice: parseFloat(item.totalPrice || "0"),
                  unitOfMeasure: item.unitOfMeasure || "EA",
                  category: item.category || "General",

                  // QuickBooks-specific formatting
                  quickbooksItem: `${item.costCode} - ${item.description}`,
                  quickbooksClass: item.category,
                  quickbooksRate: parseFloat(item.unitPrice || "0"),
                  quickbooksAmount: parseFloat(item.totalPrice || "0"),
                })),
                categoryTotals: bid.lineItems.reduce(
                  (acc, item) => {
                    const category = item.category || "General";
                    if (!acc[category]) {
                      acc[category] = 0;
                    }
                    acc[category] += parseFloat(item.totalPrice || "0");
                    return acc;
                  },
                  {} as Record<string, number>
                ),
              })),

            // Aggregate cost code summary across all bids for this project
            projectBudgetSummary: rfq.bids
              .filter((bid) => bid.lineItems && bid.lineItems.length > 0)
              .reduce(
                (summary, bid) => {
                  bid.lineItems.forEach((item) => {
                    const key = `${item.costCode}_${item.category}`;
                    if (!summary[key]) {
                      summary[key] = {
                        costCode: item.costCode,
                        description: item.description,
                        category: item.category || "General",
                        totalQuantity: 0,
                        averageUnitPrice: 0,
                        totalAmount: 0,
                        bidCount: 0,
                      };
                    }
                    summary[key].totalQuantity += parseFloat(
                      item.quantity || "0"
                    );
                    summary[key].totalAmount += parseFloat(
                      item.totalPrice || "0"
                    );
                    summary[key].bidCount += 1;
                    summary[key].averageUnitPrice =
                      summary[key].totalAmount / summary[key].totalQuantity ||
                      0;
                  });
                  return summary;
                },
                {} as Record<string, any>
              ),
          })),
          exportFormat: "quickbooks",
          exportDate: new Date().toISOString(),
          metadata: {
            userId,
            generatedBy: "Bidaible API",
            version: "1.0",
          },
        };

        res.json(quickbooksData);
      } catch (error) {
        console.error("Error generating QuickBooks export:", error);
        res
          .status(500)
          .json({ message: "Failed to generate QuickBooks export" });
      }
    }
  );

  app.post(
    "/api/integrations/sage",
    ...writeApiAccess,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        const { projectData, syncOptions = {} } = req.body;

        if (!projectData || !Array.isArray(projectData)) {
          return res
            .status(400)
            .json({ message: "Project data array is required" });
        }

        // Validate project data format
        const validatedProjects = projectData.map((project) => {
          if (!project.projectName || !project.projectLocation) {
            throw new Error(
              "Each project must have projectName and projectLocation"
            );
          }
          return project;
        });

        const syncResults = [];

        // Get user organization for multi-tenant isolation
        const user = await storage.getUser(userId);
        if (!user || !user.organizationId) {
          return res
            .status(403)
            .json({ message: "User not associated with organization" });
        }

        // Process each project for Sage ERP integration
        for (const project of validatedProjects) {
          try {
            // Create or update RFQ based on Sage data
            const dueDate = project.dueDate
              ? new Date(project.dueDate)
              : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
            const bidProposalDeadlineAt = project.bidProposalDeadlineAt
              ? new Date(project.bidProposalDeadlineAt)
              : new Date(dueDate.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days before due date by default

            const rfqData = {
              projectName: project.projectName,
              projectLocation: project.projectLocation,
              tradeCategory: project.tradeCategory || "general",
              description:
                project.description || `Project synced from Sage ERP`,
              dueDate,
              bidProposalDeadlineAt,
              status: project.status || "Draft",
              createdBy: userId,
              organizationId: user.organizationId, // Multi-tenant isolation
              extractedData: {
                ...project,
                syncedFromSage: true,
                sageProjectId: project.sageProjectId,
                syncDate: new Date().toISOString(),
              },
            };

            let rfq;
            if (project.existingRfqId) {
              // Update existing RFQ
              rfq = await storage.updateRfq(project.existingRfqId, rfqData);
            } else {
              // Create new RFQ
              rfq = await storage.createRfq(rfqData);
            }

            // **ENHANCED**: Include bid cost code data for Sage ERP integration
            let costCodeData = null;
            if (project.includeBidData && rfq) {
              // Get bids with line items for this RFQ to sync cost codes back to Sage
              const bids = await storage.getBidsByRfq(rfq.id);
              const bidsWithLineItems = await Promise.all(
                bids.map(async (bid) => {
                  const lineItems = await storage.getBidLineItems(bid.id);
                  return { ...bid, lineItems };
                })
              );

              // Format cost code data for Sage ERP
              if (bidsWithLineItems.length > 0) {
                costCodeData = {
                  projectBudgets: bidsWithLineItems.map((bid) => ({
                    bidId: bid.id,
                    contractorId: bid.contractorId,
                    bidAmount: parseFloat(bid.bidAmount || "0"),
                    costCodes: bid.lineItems.map((item) => ({
                      sageJobCostCode: item.costCode,
                      description: item.description,
                      budgetQuantity: parseFloat(item.quantity || "0"),
                      budgetUnitCost: parseFloat(item.unitPrice || "0"),
                      budgetTotalCost: parseFloat(item.totalPrice || "0"),
                      costCategory: item.category || "General",
                      unitOfMeasure: item.unitOfMeasure || "EA",
                    })),
                  })),
                  totalBudgetAmount: bidsWithLineItems.reduce(
                    (sum, bid) =>
                      sum +
                      bid.lineItems.reduce(
                        (bidSum, item) =>
                          bidSum + parseFloat(item.totalPrice || "0"),
                        0
                      ),
                    0
                  ),
                };
              }
            }

            if (rfq) {
              syncResults.push({
                sageProjectId: project.sageProjectId,
                rfqId: rfq.id,
                status: "success",
                action: project.existingRfqId ? "updated" : "created",
                projectName: project.projectName,
                ...(costCodeData && { costCodeData }), // Include cost code data if available
              });
            }
          } catch (projectError) {
            console.error(
              `Error syncing project ${project.projectName}:`,
              projectError
            );
            const errorMessage =
              projectError instanceof Error
                ? projectError.message
                : "Unknown error";
            syncResults.push({
              sageProjectId: project.sageProjectId,
              status: "error",
              error: errorMessage,
              projectName: project.projectName,
            });
          }
        }

        // Generate sync report
        const syncReport = {
          syncDate: new Date().toISOString(),
          totalProjects: validatedProjects.length,
          successCount: syncResults.filter((r) => r.status === "success")
            .length,
          errorCount: syncResults.filter((r) => r.status === "error").length,
          results: syncResults,
          syncOptions,
          metadata: {
            userId,
            syncedBy: "Bidaible API",
            version: "1.0",
          },
        };

        res.json(syncReport);
      } catch (error) {
        console.error("Error syncing with Sage ERP:", error);
        res.status(500).json({ message: "Failed to sync with Sage ERP" });
      }
    }
  );

  // **NEW ENDPOINT**: Project Budget Sync with Cost Codes (Critical Requirement)
  app.get(
    "/api/integrations/project-budget/:rfqId/:bidId",
    ...readOnlyApiAccess,
    async (req: any, res) => {
      try {
        const { rfqId, bidId } = req.params;
        const { system = "quickbooks" } = req.query; // quickbooks, sage, salesforce, hubspot
        const userId = req.user.claims.sub;

        // Verify ownership
        const rfq = await storage.getRfq(rfqId);
        if (!rfq || rfq.createdBy !== userId) {
          return res.status(403).json({ message: "Access denied" });
        }

        // Get bid and its line items (cost codes)
        const bid = await storage.getBid(bidId);
        if (!bid || bid.rfqId !== rfqId) {
          return res.status(404).json({ message: "Bid not found" });
        }

        // **CRITICAL**: Get bid line items with cost codes, descriptions, and prices
        const bidLineItems = await storage.getBidLineItems(bidId);

        // Get contractor details
        const contractor = await storage.getContractor(bid.contractorId!);

        // Transform to ERP/CRM specific format
        const projectBudget = {
          // Project Information
          projectId: rfq.id,
          projectName: rfq.projectName,
          projectLocation: rfq.projectLocation,
          customer: contractor?.companyName || "Unknown Contractor",
          contractorId: contractor?.id,

          // Bid Information
          bidId: bid.id,
          bidAmount: parseFloat(bid.bidAmount || "0"),
          bidDate: bid.submittedAt,
          bidStatus: bid.status,

          // **CRITICAL**: Detailed cost code breakdown (760+ consolidated cost codes)
          costCodeBreakdown: bidLineItems.map((item) => ({
            costCode: item.costCode,
            description: item.description,
            quantity: parseFloat(item.quantity || "0"),
            unitPrice: parseFloat(item.unitPrice || "0"),
            totalPrice: parseFloat(item.totalPrice || "0"),
            unitOfMeasure: item.unitOfMeasure || "EA",
            category: item.category || "General",

            // System-specific formatting
            ...(system === "quickbooks" && {
              quickbooksItem: `${item.costCode} - ${item.description}`,
              quickbooksClass: item.category,
              quickbooksRate: parseFloat(item.unitPrice || "0"),
              quickbooksAmount: parseFloat(item.totalPrice || "0"),
            }),
            ...(system === "sage" && {
              sageJobCostCode: item.costCode,
              sageDescription: item.description,
              sageCostCategory: item.category,
              sageUnitCost: parseFloat(item.unitPrice || "0"),
              sageTotalCost: parseFloat(item.totalPrice || "0"),
            }),
            ...(system === "salesforce" && {
              salesforceProductCode: item.costCode,
              salesforceLineDescription: item.description,
              salesforceUnitPrice: parseFloat(item.unitPrice || "0"),
              salesforceTotalPrice: parseFloat(item.totalPrice || "0"),
            }),
            ...(system === "hubspot" && {
              hubspotLineItem: `${item.costCode} - ${item.description}`,
              hubspotUnitAmount: parseFloat(item.unitPrice || "0"),
              hubspotAmount: parseFloat(item.totalPrice || "0"),
            }),
          })),

          // Category summaries for better ERP integration
          categoryTotals: bidLineItems.reduce(
            (acc, item) => {
              const category = item.category || "General";
              if (!acc[category]) {
                acc[category] = 0;
              }
              acc[category] += parseFloat(item.totalPrice || "0");
              return acc;
            },
            {} as Record<string, number>
          ),

          // Summary information
          totalLineItems: bidLineItems.length,
          totalProjectBudget: bidLineItems.reduce(
            (sum, item) => sum + parseFloat(item.totalPrice || "0"),
            0
          ),
          budgetByCategory: Object.entries(
            bidLineItems.reduce(
              (acc, item) => {
                const category = item.category || "General";
                if (!acc[category]) {
                  acc[category] = { count: 0, total: 0 };
                }
                acc[category].count += 1;
                acc[category].total += parseFloat(item.totalPrice || "0");
                return acc;
              },
              {} as Record<string, { count: number; total: number }>
            )
          ).map(([category, data]) => ({
            category,
            lineItemCount: data.count,
            totalAmount: data.total,
          })),

          // Export metadata
          exportDate: new Date().toISOString(),
          exportSystem: system,
          generatedBy: "Bidaible API v1.0",
        };

        res.json(projectBudget);
      } catch (error) {
        console.error("Error generating project budget:", error);
        res.status(500).json({ message: "Failed to generate project budget" });
      }
    }
  );

  app.get(
    "/api/integrations/export/rfqs",
    ...readOnlyApiAccess,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        const format = req.query.format || "json";
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;

        let rfqs = await storage.getRfqsByUser(userId);

        // Filter by date range if provided
        if (startDate) {
          rfqs = rfqs.filter(
            (rfq) =>
              rfq.createdAt && new Date(rfq.createdAt) >= new Date(startDate)
          );
        }
        if (endDate) {
          rfqs = rfqs.filter(
            (rfq) =>
              rfq.createdAt && new Date(rfq.createdAt) <= new Date(endDate)
          );
        }

        if (format === "csv") {
          // Generate CSV format
          const csvHeaders = [
            "Project Name",
            "Location",
            "Trade Category",
            "Status",
            "Due Date",
            "Created Date",
            "Buffer Percentage",
            "Description",
          ];

          const csvRows = rfqs.map((rfq) => [
            rfq.projectName,
            rfq.projectLocation,
            rfq.tradeCategory,
            rfq.status,
            rfq.dueDate?.toISOString().split("T")[0] || "",
            rfq.createdAt?.toISOString().split("T")[0] || "",
            rfq.bufferPercentage || "10.00",
            rfq.description?.replace(/"/g, '""') || "",
          ]);

          const csvContent = [csvHeaders, ...csvRows]
            .map((row) => row.map((cell) => `"${cell}"`).join(","))
            .join("\n");

          res.setHeader("Content-Type", "text/csv");
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="rfqs_export_${new Date().toISOString().split("T")[0]}.csv"`
          );
          res.send(csvContent);
        } else {
          // JSON format (default)
          res.json({
            exportDate: new Date().toISOString(),
            totalRecords: rfqs.length,
            format,
            data: rfqs,
            metadata: {
              userId,
              dateRange: {
                startDate: startDate || null,
                endDate: endDate || null,
              },
            },
          });
        }
      } catch (error) {
        console.error("Error exporting RFQs:", error);
        res.status(500).json({ message: "Failed to export RFQs" });
      }
    }
  );

  // Dashboard routes
  app.get(
    "/api/dashboard/stats",
    protectedRoute,
    addUserToRequest,
    async (req, res) => {
      try {
        const stats = await storage.getDashboardStats();
        res.json(stats);
      } catch (error) {
        console.error("Error fetching dashboard stats:", error);
        res.status(500).json({ message: "Failed to fetch dashboard stats" });
      }
    }
  );

  // RFQ routes - Organization-scoped for General Contractors
  app.get(
    "/api/rfqs",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        const user = await storage.getUser(userId);

        // Temporarily allow users without organization for debugging
        if (!user) {
          return res.status(403).json({ message: "User not found" });
        }

        // TODO: Re-enable organization check after testing
        // if (!user.organizationId) {
        //   return res.status(403).json({ message: "User not associated with organization" });
        // }

        // GC vs Contractor scoping using userClassification
        // General contractors only see RFQs from their organization
        // Contractors can see all RFQs (cross-organization access for bidding)
        const limit = req.query.limit ? parseInt(String(req.query.limit)) : undefined;
        const offset = req.query.offset ? parseInt(String(req.query.offset)) : undefined;

        const rfqs =
                 user.userClassification === "contractor"
            ? await storage.getRfqs(limit || offset ? { limit, offset } : undefined)
            : await storage.getRfqsByOrganization(
                user.organizationId,
                limit || offset ? { limit, offset } : undefined
              );

        res.json(rfqs);
      } catch (error) {
        console.error("Error fetching RFQs:", error);
        res.status(500).json({ message: "Failed to fetch RFQs" });
      }
    }
  );

  app.get("/api/rfqs/:id", sensitiveRateLimit, ...readOnlyApiAccess, async (req, res) => {
    try {
      const rfq = await storage.getRfq(req.params.id);
      if (!rfq) {
        return res.status(404).json({ message: "RFQ not found" });
      }

      // Enforce org scoping for General Contractors
      try {
        const userId = (req as any).user?.claims?.sub;
        if (userId) {
          const user = await storage.getUser(userId);
          if (user && user.userClassification === "general_contractor") {
            if (
              !user.organizationId ||
              rfq.organizationId !== user.organizationId
            ) {
              return res.status(403).json({ message: "Access denied" });
            }
          }
        }
      } catch (e) {
        console.warn("RFQ access check failed, defaulting to deny on error");
      }

      res.json(rfq);
    } catch (error) {
      console.error("Error fetching RFQ:", error);
      res.status(500).json({ message: "Failed to fetch RFQ" });
    }
  });

  app.get(
    "/api/rfqs/:id/documents",
    ...readOnlyApiAccess,
    async (req: any, res) => {
      try {
        // Enforce org scoping for General Contractors
        const rfq = await storage.getRfq(req.params.id);
        if (!rfq) {
          return res.status(404).json({ message: "RFQ not found" });
        }
        const userId = req.user?.claims?.sub;
        if (userId) {
          const user = await storage.getUser(userId);
          if (user && user.userClassification === "general_contractor") {
            if (
              !user.organizationId ||
              rfq.organizationId !== user.organizationId
            ) {
              return res.status(403).json({ message: "Access denied" });
            }
          }
        }

        // Contractor access control: must be distributed or bid owner
        if (userId) {
          const u = await storage.getUser(userId);
          if (u && u.userClassification === "contractor") {
            const contractor = await storage.getContractorByUserId(userId);
            let allowed = false;
            if (contractor) {
              try {
                const dists = await storage.getContractorRfqDistributions(contractor.id);
                allowed = dists.some((d: any) => d.rfqId === rfq.id);
              } catch {}
              if (!allowed) {
                const bids = await storage.getBidsByRfq(rfq.id);
                allowed = bids.some((b: any) => b.contractorId === contractor.id);
              }
            }
            if (!allowed) {
              return res.status(403).json({ message: "Access denied" });
            }
          }
        }

        const limit = req.query.limit ? parseInt(String(req.query.limit)) : undefined;
        const offset = req.query.offset ? parseInt(String(req.query.offset)) : undefined;
        const documents = await storage.getRfqDocuments(req.params.id, limit || offset ? { limit, offset } : undefined);
        res.json(documents);
      } catch (error) {
        console.error("Error fetching RFQ documents:", error);
        res.status(500).json({ message: "Failed to fetch RFQ documents" });
      }
    }
  );

  // Progress tracking routes
  app.post(
    "/api/upload/start-session",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const sessionId = `${Date.now()}-${nanoid(8)}`;
        const userId = req.user.claims.sub;

        progressTracker.createSession(sessionId, userId);
        res.json({ sessionId });
      } catch (error) {
        console.error("Error creating upload session:", error);
        res.status(500).json({ message: "Failed to create upload session" });
      }
    }
  );

  app.get(
    "/api/upload/progress/:sessionId",
    protectedRoute,
    addUserToRequest,
    (req: any, res) => {
      try {
        const { sessionId } = req.params;
        progressTracker.setupSSE(sessionId, res);
      } catch (error) {
        console.error("Error setting up progress tracking:", error);
        res.status(500).json({ message: "Failed to setup progress tracking" });
      }
    }
  );

  // Process documents with AI (preview endpoint)
  app.post(
    "/api/rfqs/process-documents",
    protectedRoute,
    addUserToRequest,
    upload.array("documents"),
    async (req: any, res) => {
      try {
        if (!req.files || req.files.length === 0) {
          return res.status(400).json({ message: "No documents provided" });
        }

        const extractedData: any = {};

        for (const file of req.files) {
          try {
            const result = await processRfqDocument(file.path);
            if (result.structuredData) {
              // Merge extracted data from all files
              Object.assign(extractedData, result.structuredData);
            }
          } catch (aiError) {
            console.error("AI processing error:", aiError);
            // Continue with next file if one fails
          }
        }

        res.json(extractedData);
      } catch (error) {
        console.error("Error processing documents:", error);
        res.status(500).json({ message: "Failed to process documents" });
      }
    }
  );

  app.put(
    "/api/rfqs/:id",
    ...writeApiAccess,
    async (req: any, res) => {
      try {
        const { id } = req.params;
        const userId = req.user?.claims?.sub;
        if (!userId) {
          return res.status(401).json({ message: "Unauthorized" });
        }
        const rfq = await storage.getRfq(id);
        if (!rfq) {
          return res.status(404).json({ message: "RFQ not found" });
        }
        // Ownership/admin check
        let allowed = rfq.createdBy === userId;
        if (!allowed) {
          const user = await storage.getUser(userId);
          if (user && user.organizationId && user.organizationId === rfq.organizationId) {
            try {
              const memberships = await clerkClient.organizations.getOrganizationMembershipList({ organizationId: rfq.organizationId, userId: [userId] });
              allowed = memberships.data.some((m: any) => m.role === "org:admin");
            } catch {}
          }
        }
        if (!allowed) {
          return res.status(403).json({ message: "Access denied" });
        }

        const updateData = req.body || {};
        if (updateData.dueDate && typeof updateData.dueDate === "string") {
          updateData.dueDate = new Date(updateData.dueDate);
        }
        if (updateData.bidProposalDeadlineAt && typeof updateData.bidProposalDeadlineAt === "string") {
          updateData.bidProposalDeadlineAt = new Date(updateData.bidProposalDeadlineAt);
        }

        // Validate bidProposalDeadlineAt if provided
        if (updateData.bidProposalDeadlineAt) {
          const now = new Date();
          const dueDate = updateData.dueDate || rfq.dueDate;
          
          // Validation: bidProposalDeadlineAt must be in the future
          if (updateData.bidProposalDeadlineAt <= now) {
            return res.status(400).json({ 
              message: "Bid proposal deadline must be in the future",
              field: "bidProposalDeadlineAt"
            });
          }
          
          // Validation: bidProposalDeadlineAt must be before dueDate
          if (updateData.bidProposalDeadlineAt >= dueDate) {
            return res.status(400).json({ 
              message: "Bid proposal deadline must be before project due date",
              field: "bidProposalDeadlineAt"
            });
          }
        }

        const validatedData = insertRfqSchema.partial().parse(updateData);
        // Whitelist allowed fields
        const allowedFields = new Set([
          "projectName","projectLocation","tradeCategory","description","dueDate","bidProposalDeadlineAt","status",
          "extractedData","extractedText","aiSummary","masterSummary","conflictFlags","bufferPercentage","bufferNotes"
        ]);
        const sanitized: any = {};
        Object.keys(validatedData).forEach((k) => { if (allowedFields.has(k)) (sanitized as any)[k] = (validatedData as any)[k]; });

        const updatedRfq = await storage.updateRfq(id, sanitized);
        if (!updatedRfq) {
          return res.status(404).json({ message: "RFQ not found" });
        }

        // Schedule deadline reminder notifications if bidProposalDeadlineAt was updated
        if (updateData.bidProposalDeadlineAt) {
          try {
            await scheduleRfqDeadlineReminders(updatedRfq.id, updatedRfq.createdBy, updatedRfq.bidProposalDeadlineAt as Date, updatedRfq.projectName);
            console.log("✅ RFQ deadline reminders scheduled successfully");
          } catch (schedulingError) {
            console.error("⚠️ Failed to schedule RFQ deadline reminders:", schedulingError);
            // Don't fail the update if scheduling fails
          }
        }

        res.json(updatedRfq);
      } catch (error) {
        console.error("Error updating RFQ:", error);
        res.status(500).json({ message: "Failed to update RFQ" });
      }
    }
  );

  app.post(
    "/api/rfqs",
    protectedRoute,
    addUserToRequest,
    upload.array("documents"),
    async (req: any, res) => {
      const debugId = `rfq-${Date.now()}`;
      console.log(
        `🔍 [${debugId}] RFQ Upload Started - User: ${req.user?.claims?.sub?.slice(0, 8)}`
      );

      try {
        // Get current user for organization isolation
        const currentUser = await storage.getUser(req.user.claims.sub);
        if (!currentUser) {
          return res.status(404).json({ message: "User not found" });
        }

        let allExtractedData: any = {};
        const sessionId = req.body.sessionId; // Optional progress tracking session
        const uploadBatchId = req.body.uploadBatchId; // Batch ID for multi-file uploads

        console.log(`📋 [${debugId}] Request Details:`, {
          filesCount: req.files?.length || 0,
          sessionId: sessionId || "none",
          uploadBatchId: uploadBatchId || "none",
          userAuth: !!req.user?.claims?.sub,
          hasApiKey: !!req.user?.apiKey,
        });

        // Parse file metadata for batch processing
        const fileMetadata: Array<{
          id: string;
          fileType:
            | "main"
            | "drawings"
            | "specifications"
            | "addendum"
            | "supporting";
          sequence: number;
          fileName: string;
        }> = [];

        // Extract file metadata from form data
        if (req.body.fileMetadata) {
          Object.keys(req.body.fileMetadata).forEach((key) => {
            try {
              const metadata = JSON.parse(req.body.fileMetadata[key]);
              fileMetadata.push(metadata);
            } catch (e) {
              console.warn(`Failed to parse metadata for ${key}:`, e);
            }
          });
        }

        // Clean function to prevent encoding issues
        const cleanString = (str: string | null | undefined): string | null => {
          if (!str || typeof str !== "string") return null;
          return (
            str
              .replace(/\0/g, "")
              .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, "")
              .trim() || null
          );
        };

        const cleanArray = (arr: string[] | null | undefined): string[] => {
          if (!Array.isArray(arr)) return [];
          return arr
            .map((item) => cleanString(item))
            .filter(Boolean) as string[];
        };

        const mapToValidTradeCategory = (
          tradeTypes: string[]
        ):
          | "electrical"
          | "plumbing"
          | "hvac"
          | "concrete"
          | "general"
          | "site_work" => {
          if (!tradeTypes || tradeTypes.length === 0) return "general";

          const firstTrade = tradeTypes[0].toLowerCase();

          // Map AI-extracted trade types to our valid enum values
          if (
            firstTrade.includes("electrical") ||
            firstTrade.includes("electric")
          )
            return "electrical";
          if (firstTrade.includes("plumbing") || firstTrade.includes("plumb"))
            return "plumbing";
          if (
            firstTrade.includes("hvac") ||
            firstTrade.includes("heating") ||
            firstTrade.includes("cooling")
          )
            return "hvac";
          if (
            firstTrade.includes("concrete") ||
            firstTrade.includes("structural") ||
            firstTrade.includes("steel")
          )
            return "concrete";
          if (
            firstTrade.includes("site") ||
            firstTrade.includes("landscaping") ||
            firstTrade.includes("excavation")
          )
            return "site_work";

          return "general"; // Default fallback
        };

        // Store results for each file with batch metadata
        const fileResults: { [filename: string]: any } = {};
        const storedFiles: any[] = [];

        // Process uploaded documents with AI in priority order
        if (req.files && req.files.length > 0) {
          let allExtractedText = "";
          if (req.files && Array.isArray(req.files)) {
            console.log(
              `🔄 Processing ${req.files.length} files in batch: ${uploadBatchId || "no-batch-id"}`
            );

            // Sort files by priority: main → specifications → drawings → addendum → supporting
            const filePriorityOrder = [
              "main",
              "specifications",
              "drawings",
              "addendum",
              "supporting",
            ];
            const filesWithMetadata = req.files.map(
              (file: any, index: number) => {
                const metadata = fileMetadata.find(
                  (m) => m.fileName === file.originalname
                ) || {
                  id: `file-${index}`,
                  fileType: "supporting" as const,
                  sequence: index + 1,
                  fileName: file.originalname,
                };
                return { file, metadata };
              }
            );

            // Sort by file type priority and then by sequence
            filesWithMetadata.sort((a: any, b: any) => {
              const aPriority = filePriorityOrder.indexOf(a.metadata.fileType);
              const bPriority = filePriorityOrder.indexOf(b.metadata.fileType);
              if (aPriority !== bPriority) {
                return aPriority - bPriority;
              }
              return a.metadata.sequence - b.metadata.sequence;
            });

            // Process files in priority order
            for (let i = 0; i < filesWithMetadata.length; i++) {
              const { file, metadata } = filesWithMetadata[i];
              console.log(
                `🔍 Processing file ${i + 1}/${filesWithMetadata.length}: ${file.originalname} (${metadata.fileType})`
              );

              try {
                // Update progress: Starting file upload with batch context
                if (sessionId) {
                  progressTracker.updateProgress(sessionId, {
                    fileName: file.originalname,
                    uploadedBytes: 0,
                    totalBytes: file.size,
                    percentage: 0,
                    status: "uploading",
                    stage: "upload",
                  } as any);
                }

                // Upload file to Object Storage with progress tracking
                const storedFile = await uploadFile(
                  file.buffer,
                  file.originalname,
                  file.mimetype,
                  {
                    organizationId: currentUser.organizationId || undefined,
                    ...(sessionId
                      ? {
                          onProgress: (progress) => {
                            progressTracker.updateProgress(sessionId, progress);
                          },
                        }
                      : {})
                  }
                );

                storedFiles.push(storedFile);

                // Update progress: Starting AI processing
                if (sessionId) {
                  progressTracker.updateProgress(sessionId, {
                    fileName: file.originalname,
                    uploadedBytes: file.size,
                    totalBytes: file.size,
                    percentage: 100,
                    status: "processing",
                    stage: "ai_processing",
                  } as any);
                }

                // Process with optimized AI using temporary file approach
                const tempFilePath = path.join(
                  os.tmpdir(),
                  `${Date.now()}-${file.originalname}`
                );
                console.log(
                  `💾 [${debugId}] Writing temp file: ${tempFilePath}`
                );
                await fs.promises.writeFile(tempFilePath, file.buffer);

                console.log(
                  `🤖 [${debugId}] Starting AI processing for ${file.originalname}`
                );
                const result = await processRfqDocumentOptimized(
                  tempFilePath,
                  file.originalname,
                  {
                    onProgress: sessionId
                      ? (progress: any) => {
                          console.log(
                            `📊 [${debugId}] AI Progress: ${progress.stage} - ${progress.percentage}%`
                          );
                          progressTracker.updateProgress(sessionId, {
                            fileName: file.originalname,
                            uploadedBytes: file.size,
                            totalBytes: file.size,
                            percentage: Math.round(
                              50 + progress.percentage * 0.5
                            ), // 50-100% range for AI
                            status: "processing",
                            stage: "ai_processing",
                          } as any);
                        }
                      : undefined,
                    timeout: 90000, // Increased for large file processing
                    retries: 2,
                  }
                );

                console.log(`🎯 [${debugId}] AI Processing Result:`, {
                  success: result.success,
                  model: result.model,
                  processingTime: result.processingTime,
                  retryCount: result.retryCount,
                  hasStructuredData: !!result.structuredData,
                  structuredDataKeys: result.structuredData
                    ? Object.keys(result.structuredData)
                    : [],
                });
                if (result.success && result.structuredData) {
                  // Add the original filename and batch metadata to the extracted data
                  (result.structuredData as any).fileName = file.originalname;
                  (result.structuredData as any).fileType = metadata.fileType;
                  (result.structuredData as any).processingOrder = i + 1;

                  // Store the full result for this file with batch context
                  fileResults[file.originalname] = {
                    ...result,
                    batchMetadata: metadata,
                    processingOrder: i + 1,
                  };

                  // **MAIN FILE PRIORITY**: Focus on main RFQ files for comprehensive extraction
                  if (metadata.fileType === "main") {
                    // Main RFQ files get complete data extraction with AI summary
                    console.log(
                      `🎯 Primary extraction from MAIN RFQ file: ${file.originalname}`
                    );
                    Object.assign(allExtractedData, result.structuredData);

                    // Ensure AI summary is preserved from main file
                    if (result.structuredData.aiSummary) {
                      console.log(
                        `✅ AI Summary generated for main RFQ: ${result.structuredData.aiSummary.substring(0, 100)}...`
                      );
                    }
                  } else if (!allExtractedData.projectName) {
                    // If no main file found, use first available file as fallback
                    console.log(
                      `📄 Using ${metadata.fileType} file as fallback: ${file.originalname}`
                    );
                    Object.assign(allExtractedData, result.structuredData);
                  } else {
                    // Supporting files only contribute specific fields
                    console.log(
                      `📎 Supplemental data from ${metadata.fileType}: ${file.originalname}`
                    );
                    if (
                      result.structuredData.requirements &&
                      Array.isArray(result.structuredData.requirements)
                    ) {
                      const existingRequirements = Array.isArray(
                        allExtractedData.requirements
                      )
                        ? allExtractedData.requirements
                        : [];
                      allExtractedData.requirements = [
                        ...existingRequirements,
                        ...result.structuredData.requirements,
                      ];
                    }
                  }

                  console.log(
                    `✅ Batch processing completed for ${file.originalname} (${metadata.fileType}) using ${result.model} in ${result.processingTime}ms (${result.retryCount} retries)`
                  );
                } else {
                  console.warn(
                    `⚠️ Batch processing failed for ${file.originalname} (${metadata.fileType})`
                  );
                  // Store minimal data so we still track the file
                  fileResults[file.originalname] = {
                    text: "",
                    structuredData: {
                      fileName: file.originalname,
                      fileType: metadata.fileType,
                    },
                    batchMetadata: metadata,
                    processingOrder: i + 1,
                  };
                }

                // Also collect raw extracted text
                if (result.extractedText) {
                  allExtractedText += `\n\n=== ${file.originalname} ===\n${result.extractedText}`;
                }

                // Clean up temporary file
                await fs.promises.unlink(tempFilePath);

                // Update progress: File completed
                if (sessionId) {
                  progressTracker.completeFile(sessionId, file.originalname);
                }
              } catch (aiError) {
                console.error(
                  `❌ Batch processing error for ${file.originalname} (${metadata.fileType}):`,
                  aiError
                );

                // Still store an empty result so we track the file with batch metadata
                fileResults[file.originalname] = {
                  text: "",
                  structuredData: {
                    fileName: file.originalname,
                    fileType: metadata.fileType,
                  },
                  batchMetadata: metadata,
                  processingOrder: i + 1,
                  processingFailed: true,
                };

                // Report error to progress tracker
                if (sessionId) {
                  const errorMessage =
                    aiError instanceof Error
                      ? aiError.message
                      : "Processing failed";
                  progressTracker.failFile(
                    sessionId,
                    file.originalname,
                    errorMessage
                  );
                }
              }
            }
          }
        }

        // Extract location from the extracted text
        const extractLocation = (data: any): string => {
          // First priority: AI-extracted projectLocation
          if (data.projectLocation) {
            const location = cleanString(data.projectLocation);
            if (location && location !== "TBD") return location;
          }

          // Second priority: Look for complete address patterns in extracted text
          const textToSearch = [
            data.projectDescription,
            data.projectSummary,
            data.extractedText,
          ].join(" ");

          // Look for complete address patterns with city and state
          const fullAddressPatterns = [
            /(?:project\s+location[:\s]*|address[:\s]*|located?\s+(?:at|in)[:\s]*)([\d\w\s,-]+),\s*([a-zA-Z\s]+),\s*([A-Z]{2})\b/i,
            /(?:project\s+location[:\s]*|address[:\s]*|located?\s+(?:at|in)[:\s]*)([\d\w\s,-]+)\s+([a-zA-Z\s]+)\s+([A-Z]{2})\b/i,
            /([\d\w\s,-]+),\s*([a-zA-Z\s]+),\s*([A-Z]{2})\s*\d{5}/i,
          ];

          for (const pattern of fullAddressPatterns) {
            const match = textToSearch.match(pattern);
            if (match) {
              const [, street, city, state] = match;
              return (
                cleanString(
                  `${street.trim()}, ${city.trim()}, ${state.trim()}`
                ) || "TBD"
              );
            }
          }

          // Third priority: Look for city, state patterns
          const cityStatePattern = /\b([a-zA-Z\s]+),\s*([A-Z]{2})\b/i;
          const cityStateMatch = textToSearch.match(cityStatePattern);
          if (cityStateMatch) {
            const [, city, state] = cityStateMatch;
            // Validate that this looks like a real city name (more than 2 characters, not all caps unless it's a state)
            if (city.length > 2 && !city.match(/^[A-Z]{2,}$/)) {
              return cleanString(`${city.trim()}, ${state.trim()}`) || "TBD";
            }
          }

          // Fourth priority: Look for basic location clues
          const locationMatches = textToSearch.match(
            /(?:located?\s+(?:in|at)\s+|project\s+location[:\s]+|address[:\s]+|site[:\s]+)([^.]+)/i
          );
          if (locationMatches) {
            const location = locationMatches[1].split(",")[0].trim();
            return cleanString(location) || "TBD";
          }

          // Fifth priority: Look for standalone state patterns
          const stateMatch = textToSearch.match(
            /\b([A-Z]{2}|washington\s+d\.?c\.?|district\s+of\s+columbia)\b/i
          );
          if (stateMatch) {
            return stateMatch[1].toUpperCase() === "DC"
              ? "Washington, DC"
              : stateMatch[1];
          }

          return "TBD";
        };

        // Get user organization for multi-tenant isolation
        const userId = req.user.claims.sub;
        const user = await storage.getUser(userId);

        if (!user || !user.organizationId) {
          return res
            .status(403)
            .json({ message: "User not associated with organization" });
        }

        // Get organization details from Clerk for folder naming
        let organizationSlug = "default";
        try {
          if (user.organizationId) {
            const clerkOrg = await clerkClient.organizations.getOrganization({
              organizationId: user.organizationId,
            });
            organizationSlug =
              clerkOrg.slug ||
              clerkOrg.name?.toLowerCase().replace(/[^a-z0-9]/g, "-") ||
              user.organizationId;
            console.log(
              `📁 Using organization folder: ${organizationSlug} (${clerkOrg.name})`
            );
          }
        } catch (orgError) {
          console.warn(
            `⚠️ Could not fetch organization details, using ID as folder name:`,
            orgError
          );
          organizationSlug = user.organizationId || "default";
        }

        // Create RFQ data with cleaned extracted information
        const dueDate = allExtractedData.finalAwardDate
          ? new Date(allExtractedData.finalAwardDate)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
        const bidProposalDeadlineAt = allExtractedData.bidSubmissionDeadline
          ? new Date(allExtractedData.bidSubmissionDeadline)
          : new Date(dueDate.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days before due date by default

        const rfqData = asInsert<InsertRfq>(
          insertRfqSchema.parse({
            projectName:
              cleanString(allExtractedData.fileName) ||
              cleanString(allExtractedData.projectSummary) ||
              "Untitled Project",
            projectLocation: extractLocation(allExtractedData),
            description:
              cleanString(allExtractedData.projectDescription) ||
              cleanString(allExtractedData.projectSummary) ||
              "No description available",
            dueDate,
            bidProposalDeadlineAt,
            tradeCategory: "general", // Default since not extracted by optimized prompt
            status: "Draft",
            createdBy: userId,
            organizationId: user.organizationId, // Multi-tenant isolation
            extractedData: allExtractedData,
            aiSummary: cleanString(allExtractedData.aiSummary) || null,
          })
        );

        console.log(`📝 [${debugId}] Creating RFQ with data:`, {
          projectName: rfqData.projectName,
          projectLocation: rfqData.projectLocation,
          filesProcessed: storedFiles.length,
          hasExtractedData: Object.keys(allExtractedData).length > 0,
          hasAiSummary: !!cleanString(allExtractedData.aiSummary),
        });

        const rfq = await storage.createRfq(rfqData);
        console.log(
          `✅ [${debugId}] RFQ created successfully with ID: ${rfq.id}`
        );

        // 📝 AUDIT LOG: RFQ Creation
        await AuditService.logRfqCreation({
          userId: req.user.claims.sub,
          rfqId: rfq.id,
          projectName: rfq.projectName,
          tradeCategory: rfq.tradeCategory,
          dueDate: rfq.dueDate,
          req,
        });

        // 🔔 NOTIFICATION: RFQ Upload Complete
        try {
          await NotificationService.createRfqUploadNotification({
            userId: req.user.claims.sub,
            rfqId: rfq.id,
            projectName: rfq.projectName,
            projectLocation: rfq.projectLocation,
            dueDate: rfq.dueDate,
            fileCount: storedFiles.length,
            organizationId: currentUser.organizationId,
          } as any);
          console.log("✅ RFQ upload notification sent successfully");
        } catch (notificationError) {
          console.error(
            "⚠️ Failed to send RFQ upload notification:",
            notificationError
          );
          // Don't fail the RFQ creation if notification fails
        }

        // 📅 SCHEDULE: Deadline reminder notifications
        try {
          await scheduleRfqDeadlineReminders(rfq.id, rfq.createdBy, rfq.bidProposalDeadlineAt as Date, rfq.projectName);
          console.log("✅ RFQ deadline reminders scheduled successfully");
        } catch (schedulingError) {
          console.error("⚠️ Failed to schedule RFQ deadline reminders:", schedulingError);
          // Don't fail the RFQ creation if scheduling fails
        }

        // Store document references with batch metadata and extracted text
        for (
          let i = 0;
          i < storedFiles.length && req.files && i < req.files.length;
          i++
        ) {
          const file = req.files[i];
          const storedFile = storedFiles[i];

          try {
            const fileResult = fileResults[file.originalname];
            const extractedText = fileResult?.extractedText || "";
            const metadata = fileResult?.batchMetadata || {
              id: `file-${i}`,
              fileType: "supporting" as const,
              sequence: i + 1,
              fileName: file.originalname,
            };

            // **ENHANCED**: Store with multi-file batch schema fields
            await storage.createRfqDocument(
              asInsert<InsertRfqDocument>({
                rfqId: rfq.id,
                fileName: storedFile.fileName,
                // New batch processing fields
                uploadBatchId: uploadBatchId || null,
                fileSequence: metadata.sequence,
                fileType: metadata.fileType,
                processingStatus: fileResult?.processingFailed
                  ? "failed"
                  : "complete",
                individualAiData: fileResult?.structuredData
                  ? JSON.stringify(fileResult.structuredData)
                  : null,
                objectKey: storedFile.objectKey,
                fileSize: storedFile.fileSize,
                mimeType: storedFile.mimeType,
                extractedText:
                  cleanString(extractedText) || "Document processed",
              })
            );

            // 📝 AUDIT LOG: File Upload for RFQ
            await AuditService.logFileUpload({
              userId: req.user.claims.sub,
              fileName: storedFile.fileName,
              fileSize: storedFile.fileSize,
              fileType: storedFile.mimeType,
              objectKey: storedFile.objectKey,
              rfqId: rfq.id,
              req,
            });

            console.log(
              `Stored document ${file.originalname} with ${extractedText.length} characters of text`
            );
          } catch (docError) {
            console.error("Error storing document:", docError);
            // Continue even if document storage fails
          }
        }

        console.log(`🎉 [${debugId}] RFQ creation completed successfully`);
        res.json(rfq);
      } catch (error) {
        console.error(`❌ [${debugId}] Error creating RFQ:`, error);

        // Provide specific error messages based on error type
        let errorMessage = "Could not process RFQ document";
        let statusCode = 500;

        if (error instanceof Error) {
          const errorMsg = error.message.toLowerCase();

          if (
            errorMsg.includes("authentication") ||
            errorMsg.includes("unauthorized")
          ) {
            errorMessage =
              "Authentication failed - please check your credentials";
            statusCode = 401;
          } else if (
            errorMsg.includes("file") &&
            errorMsg.includes("processing")
          ) {
            errorMessage =
              "File processing failed - please check file format and try again";
            statusCode = 400;
          } else if (
            errorMsg.includes("ai") ||
            errorMsg.includes("processing")
          ) {
            errorMessage =
              "AI processing failed - document may be corrupted or unreadable";
            statusCode = 422;
          } else if (
            errorMsg.includes("storage") ||
            errorMsg.includes("upload")
          ) {
            errorMessage =
              "File upload failed - please check your connection and try again";
            statusCode = 503;
          } else if (
            errorMsg.includes("database") ||
            errorMsg.includes("sql")
          ) {
            errorMessage = "Database error - please try again later";
            statusCode = 503;
          } else if (errorMsg.includes("timeout")) {
            errorMessage =
              "Processing timeout - file may be too large or complex";
            statusCode = 408;
          }

          console.log(
            `🔍 [${debugId}] Error categorized as: ${errorMessage} (${statusCode})`
          );
        }

        res.status(statusCode).json({
          message: errorMessage,
          debugId: debugId,
          timestamp: new Date().toISOString(),
        });
      }
    }
  );

  // Contractor routes
  app.get(
    "/api/contractors",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        const user = await storage.getUser(userId);

        if (!user || !user.organizationId) {
          return res
            .status(403)
            .json({ message: "User not associated with organization" });
        }

        // Organization-scoped contractor access
        const contractors = await storage.getContractorsByOrganization(
          user.organizationId
        );
        res.json(contractors);
      } catch (error) {
        console.error("Error fetching contractors:", error);
        res.status(500).json({ message: "Failed to fetch contractors" });
      }
    }
  );

  app.get(
    "/api/contractors/profile",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        console.log("🔍 Fetching contractor profile for user:", userId);

        const contractor = await storage.getContractorByUserId(userId);

        if (contractor) {
          console.log(
            "✅ Found contractor profile:",
            contractor.id,
            contractor.companyName
          );
          res.json(contractor);
        } else {
          console.log("ℹ️ No contractor profile found for user:", userId);
          res.json(null);
        }
      } catch (error) {
        console.error("❌ Error fetching contractor profile:", error);
        // Return null instead of error to let the form know there's no existing profile
        res.json(null);
      }
    }
  );

  app.post(
    "/api/contractors",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        console.log("🔧 Creating contractor profile for user:", userId);

        // Try to get user, but be more lenient about organization requirements
        let user = await storage.getUser(userId);

        // If user doesn't exist yet, try to create them from Clerk data
        if (!user) {
          console.log(
            "⚠️ User not found in database, attempting to create from Clerk"
          );
          try {
            const clerkUser = await clerkClient.users.getUser(userId);
            user = await storage.upsertUser({
              id: userId,
              email: clerkUser.emailAddresses[0]?.emailAddress,
              firstName: clerkUser.firstName,
              lastName: clerkUser.lastName,
              profileImageUrl: clerkUser.imageUrl,
            } as any);
            console.log("✅ Created user from Clerk data:", user.id);
          } catch (clerkError) {
            console.error("❌ Failed to create user from Clerk:", clerkError);
            return res
              .status(500)
              .json({ message: "Failed to create user profile" });
          }
        }

        // Ensure user has organization assignment
        if (!user.organizationId) {
          console.log(
            "⚠️ User has no organization, assigning default organization"
          );
          // Assign to the default organization
          user =
            (await storage.updateUser(user.id, {
              organizationId: "7e199b22-b767-44be-9f63-4d9ac6b15f45",
            })) || user;
        }

        const contractorData = insertContractorSchema.parse({
          ...req.body,
          userId: userId,
          organizationId: user.organizationId,
        });

        const contractor = await storage.createContractor(
          contractorData as any
        );
        console.log("✅ Created contractor profile:", contractor.id);
        res.json(contractor);
      } catch (error) {
        console.error("❌ Error creating contractor:", error);
        res.status(500).json({ message: "Failed to create contractor" });
      }
    }
  );

  // PUT endpoint for profile updates (both general contractors and contractors)
  app.put(
    "/api/contractors/profile",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        console.log("🔧 Updating contractor profile for user:", userId);
        console.log("📝 Request body:", JSON.stringify(req.body, null, 2));

        // Get or create user if needed
        let user = await storage.getUser(userId);
        if (!user) {
          console.log(
            "⚠️ User not found in database, attempting to create from Clerk"
          );
          try {
            const clerkUser = await clerkClient.users.getUser(userId);
            user = await storage.upsertUser({
              id: userId,
              email: clerkUser.emailAddresses[0]?.emailAddress,
              firstName: clerkUser.firstName,
              lastName: clerkUser.lastName,
              profileImageUrl: clerkUser.imageUrl,
            } as any);
            console.log("✅ Created user from Clerk data:", user.id);
          } catch (clerkError) {
            console.error("❌ Failed to create user from Clerk:", clerkError);
            return res
              .status(500)
              .json({ message: "Failed to create user profile" });
          }
        }

        // Ensure user has organization assignment
        if (!user.organizationId) {
          console.log(
            "⚠️ User has no organization, assigning default organization"
          );
          user =
            (await storage.updateUser(user.id, {
              organizationId: "7e199b22-b767-44be-9f63-4d9ac6b15f45",
            })) || user;
        }

        // Check if contractor profile already exists
        const existingContractor = await storage.getContractorByUserId(userId);
        console.log(
          "🔍 Existing contractor found:",
          existingContractor ? existingContractor.id : "none"
        );

        // Handle date fields that come as strings from the frontend
        const requestBody = { ...req.body };

        // Convert date strings to Date objects if they exist
        if (
          requestBody.licenseExpiration &&
          typeof requestBody.licenseExpiration === "string"
        ) {
          requestBody.licenseExpiration = new Date(
            requestBody.licenseExpiration
          );
        }

        // Remove any empty string date fields to avoid validation errors
        Object.keys(requestBody).forEach((key) => {
          if (requestBody[key] === "" && key.toLowerCase().includes("date")) {
            delete requestBody[key];
          }
        });

        let contractor;

        if (existingContractor) {
          // Update existing contractor profile
          console.log(
            "✅ Updating existing contractor profile:",
            existingContractor.id
          );

          // Use a more lenient approach for partial updates - bypass schema validation for updates
          const validFields: Record<string, any> = {};
          Object.keys(requestBody).forEach((key) => {
            if (
              requestBody[key] !== undefined &&
              requestBody[key] !== null &&
              requestBody[key] !== ""
            ) {
              validFields[key] = requestBody[key];
            }
          });

          console.log(
            "📝 Valid fields for update:",
            JSON.stringify(validFields, null, 2)
          );

          contractor = await storage.updateContractor(
            existingContractor.id,
            validFields
          );
          console.log("🔄 Update result:", contractor ? "success" : "failed");

          if (!contractor) {
            console.error("❌ Update contractor returned null/undefined");
            return res.status(500).json({
              message:
                "Failed to update contractor profile - database update failed",
            });
          }
        } else {
          // Create new contractor profile
          console.log("🆕 Creating new contractor profile for user:", userId);

          try {
            const contractorData = insertContractorSchema.parse({
              ...requestBody,
              userId: userId,
              organizationId: user.organizationId,
            });

            contractor = await storage.createContractor(contractorData as any);
            console.log("✅ Created new contractor profile:", contractor.id);
          } catch (validationError) {
            console.error(
              "❌ Validation error creating contractor:",
              validationError
            );
            return res.status(400).json({
              message: "Invalid contractor data",
              error: validationError.message,
            });
          }
        }

        // Auto-sync userClassification based on trade types
        try {
          console.log("🔄 Auto-syncing userClassification based on trade types...");
          const tradeTypes = contractor.tradeTypes || [];
          let newClassification: string;
          
          if (Array.isArray(tradeTypes) && tradeTypes.length > 0) {
            // Check if they have "General Contractor" in their trade types
            const hasGeneralContractor = tradeTypes.some(trade => 
              trade === 'General Contractor' || trade === 'general_contractor'
            );
            
            if (hasGeneralContractor) {
              newClassification = 'general_contractor';
            } else {
              // If they have other trades but no "General Contractor", they're a specialty contractor
              newClassification = 'contractor';
            }
          } else {
            // Default to General Contractor if no trade types are set
            newClassification = 'general_contractor';
          }
          
          // Update user classification if it differs
          if (user.userClassification !== newClassification) {
            console.log(`🔄 Updating userClassification: ${user.userClassification} → ${newClassification}`);
            await storage.updateUser(userId, { userClassification: newClassification });
            console.log("✅ UserClassification auto-sync completed");
          } else {
            console.log("✅ UserClassification already correct, no sync needed");
          }
        } catch (syncError) {
          console.error("⚠️ Failed to auto-sync userClassification:", syncError);
          // Don't fail the request if sync fails
        }

        console.log(
          "🎉 Contractor profile operation successful:",
          contractor.id
        );
        res.json(contractor);
      } catch (error) {
        console.error("❌ Error updating contractor profile:", error);
        console.error("❌ Error stack:", error.stack);
        res.status(500).json({
          message: "Failed to update contractor profile",
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }
    }
  );

  app.patch(
    "/api/contractors/:id",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        // Handle date fields that come as strings from the frontend
        const requestBody = { ...req.body };

        // Convert date strings to Date objects if they exist
        if (
          requestBody.licenseExpiration &&
          typeof requestBody.licenseExpiration === "string"
        ) {
          requestBody.licenseExpiration = new Date(
            requestBody.licenseExpiration
          );
        }

        // Remove any empty string date fields to avoid validation errors
        Object.keys(requestBody).forEach((key) => {
          if (requestBody[key] === "" && key.toLowerCase().includes("date")) {
            delete requestBody[key];
          }
        });

        // Use a more lenient approach for partial updates - bypass schema validation for updates
        const validFields: Record<string, any> = {};
        Object.keys(requestBody).forEach((key) => {
          if (
            requestBody[key] !== undefined &&
            requestBody[key] !== null &&
            requestBody[key] !== ""
          ) {
            (validFields as any)[key] = requestBody[key];
          }
        });

        // Skip schema validation for updates to allow more flexible data
        const contractor = await storage.updateContractor(
          req.params.id,
          validFields
        );

        if (!contractor) {
          return res.status(404).json({ message: "Contractor not found" });
        }

        res.json(contractor);
      } catch (error) {
        console.error("Error updating contractor:", error);
        res.status(500).json({ message: "Failed to update contractor" });
      }
    }
  );

  // RFQ Archive Management Routes
  app.patch(
    "/api/rfqs/:id/archive",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { id } = req.params;
        const userId = req.user.claims.sub;
        const user = await storage.getUser(userId);

        if (!user?.organizationId) {
          return res.status(403).json({ message: "User not associated with organization" });
        }

        // Verify RFQ ownership
        const rfq = await storage.getRfq(id);
        if (!rfq || rfq.organizationId !== user.organizationId) {
          return res.status(404).json({ message: "RFQ not found" });
        }

        // Only RFQ creator or org admin can archive
        if (rfq.createdBy !== userId && user.role !== "OrganizationAdmin" && user.role !== "SuperUser") {
          return res.status(403).json({ message: "Only RFQ creator or organization admin can archive RFQs" });
        }

        const result = await storage.updateRfqsArchiveStatus([id], user.organizationId, true);
        
        // Log archive action
        await storage.createBusinessAuditLog({
          userId,
          eventType: 'rfq_archived',
          eventData: { 
            rfqId: id,
            archivedAt: new Date().toISOString(),
            organizationId: user.organizationId 
          },
          resourceId: id,
        });

        res.json({ success: true, archivedCount: result.affectedRows });
      } catch (error) {
        console.error("Error archiving RFQ:", error);
        res.status(500).json({ message: "Failed to archive RFQ" });
      }
    }
  );

  app.patch(
    "/api/rfqs/bulk-archive",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqIds } = req.body;
        const userId = req.user.claims.sub;
        const user = await storage.getUser(userId);

        if (!user?.organizationId) {
          return res.status(403).json({ message: "User not associated with organization" });
        }

        if (!Array.isArray(rfqIds) || rfqIds.length === 0) {
          return res.status(400).json({ message: "Invalid RFQ IDs provided" });
        }

        // Verify all RFQs belong to user's organization and user has access
        const rfqs = await Promise.all(rfqIds.map((id: string) => storage.getRfq(id)));
        const invalidRfqs = rfqs.filter((rfq: any) => !rfq || rfq.organizationId !== user.organizationId);
        
        if (invalidRfqs.length > 0) {
          return res.status(403).json({ message: "Access denied to one or more RFQs" });
        }

        // Check permission for each RFQ
        if (user.role !== "OrganizationAdmin" && user.role !== "SuperUser") {
          const unauthorizedRfqs = rfqs.filter((rfq: any) => rfq?.createdBy !== userId);
          if (unauthorizedRfqs.length > 0) {
            return res.status(403).json({ message: "Can only archive your own RFQs" });
          }
        }

        const result = await storage.updateRfqsArchiveStatus(rfqIds, user.organizationId, true);
        
        // Log bulk archive action
        await storage.createBusinessAuditLog({
          userId,
          eventType: 'rfqs_bulk_archived',
          eventData: { 
            rfqIds, 
            archivedCount: result.affectedRows,
            archivedAt: new Date().toISOString(),
            organizationId: user.organizationId
          },
          resourceId: rfqIds[0], // Use first RFQ ID as resource reference
        });

        res.json({ success: true, archivedCount: result.affectedRows });
      } catch (error) {
        console.error("Error bulk archiving RFQs:", error);
        res.status(500).json({ message: "Failed to archive RFQs" });
      }
    }
  );

  app.patch(
    "/api/rfqs/:id/unarchive",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { id } = req.params;
        const userId = req.user.claims.sub;
        const user = await storage.getUser(userId);

        if (!user?.organizationId) {
          return res.status(403).json({ message: "User not associated with organization" });
        }

        // Verify RFQ ownership
        const rfq = await storage.getRfq(id);
        if (!rfq || rfq.organizationId !== user.organizationId) {
          return res.status(404).json({ message: "RFQ not found" });
        }

        // Only RFQ creator or org admin can unarchive
        if (rfq.createdBy !== userId && user.role !== "OrganizationAdmin" && user.role !== "SuperUser") {
          return res.status(403).json({ message: "Only RFQ creator or organization admin can unarchive RFQs" });
        }

        await storage.updateRfqsArchiveStatus([id], user.organizationId, false);
        
        // Log unarchive action
        await storage.createBusinessAuditLog({
          userId,
          eventType: 'rfq_unarchived',
          eventData: { 
            rfqId: id,
            unarchivedAt: new Date().toISOString(),
            organizationId: user.organizationId 
          },
          resourceId: id,
        });

        res.json({ success: true });
      } catch (error) {
        console.error("Error unarchiving RFQ:", error);
        res.status(500).json({ message: "Failed to unarchive RFQ" });
      }
    }
  );

  app.get(
    "/api/rfqs/archived",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        const user = await storage.getUser(userId);

        if (!user?.organizationId) {
          return res.status(403).json({ message: "User not associated with organization" });
        }

        const limit = req.query.limit ? parseInt(String(req.query.limit)) : undefined;
        const offset = req.query.offset ? parseInt(String(req.query.offset)) : undefined;

        const archivedRfqs = await storage.getArchivedRfqsByOrganization(
          user.organizationId,
          { limit, offset }
        );

        res.json(archivedRfqs);
      } catch (error) {
        console.error("Error fetching archived RFQs:", error);
        res.status(500).json({ message: "Failed to fetch archived RFQs" });
      }
    }
  );

  app.get(
    "/api/rfqs/:id/archive-download",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { id } = req.params;
        const userId = req.user.claims.sub;
        const user = await storage.getUser(userId);

        if (!user?.organizationId) {
          return res.status(403).json({ message: "User not associated with organization" });
        }

        // Verify RFQ ownership and that it's archived
        const rfq = await storage.getRfq(id);
        if (!rfq || rfq.organizationId !== user.organizationId) {
          return res.status(404).json({ message: "RFQ not found" });
        }

        if (!rfq.isArchived) {
          return res.status(400).json({ message: "RFQ must be archived before downloading" });
        }

        // Only RFQ creator or org admin can download archives
        if (rfq.createdBy !== userId && user.role !== "OrganizationAdmin" && user.role !== "SuperUser") {
          return res.status(403).json({ message: "Access denied" });
        }

        // Import ArchiveService here to avoid circular dependencies
        const { ArchiveService } = await import('./services/archiveService');
        const { stream, metadata } = await ArchiveService.createRfqArchiveStream(id, user.organizationId);

        // Set appropriate headers for ZIP download
        const fileName = `RFQ-${rfq.projectName.replace(/[^a-zA-Z0-9-_]/g, '-')}-Archive-${new Date().toISOString().split('T')[0]}.zip`;
        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
        res.setHeader('Cache-Control', 'no-cache');

        // Log archive download
        await storage.createBusinessAuditLog({
          userId,
          eventType: 'rfq_archive_downloaded',
          eventData: { 
            rfqId: id,
            downloadedAt: new Date().toISOString(),
            fileName,
            bidCount: metadata.bids.length,
            documentCount: metadata.documents.length,
            organizationId: user.organizationId
          },
          resourceId: id,
        });

        // Stream the archive
        stream.pipe(res);
      } catch (error) {
        console.error("Error downloading RFQ archive:", error);
        res.status(500).json({ message: "Failed to download archive" });
      }
    }
  );

  // Enhanced bid routes with AI processing and access controls
  app.get(
    "/api/rfqs/:rfqId/bids",
    ...readOnlyApiAccess,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const user = req.user;

        // Get the RFQ to check ownership
        const rfq = await storage.getRfq(rfqId);
        if (!rfq) {
          return res.status(404).json({ message: "RFQ not found" });
        }

        // Check if user is the RFQ owner/submitter
        const isRfqOwner = rfq.createdBy === user.claims.sub;

        if (isRfqOwner) {
          // RFQ owner can see all bids for their RFQ with contractor information
          const limit = req.query.limit ? parseInt(String(req.query.limit)) : undefined;
          const offset = req.query.offset ? parseInt(String(req.query.offset)) : undefined;
          const bidResults = await storage.getBidsByRfqWithContractors(rfqId);
          // Note: owner path currently returns joined data; pagination support can be added in storage join later.

          // Transform to include contractor data in the bid object for frontend compatibility
          const bidsWithContractors = bidResults.map(({ bid, contractor }) => ({
            ...bid,
            contractor: {
              companyName: contractor.companyName,
              primaryContactName: contractor.primaryContactName,
              primaryContactEmail: contractor.primaryContactEmail,
            },
          }));
          res.json(bidsWithContractors);
        } else {
          // Contractors can only see their own bids for this RFQ
          const contractor = await storage.getContractorByUserId(
            user.claims.sub
          );
          if (!contractor) {
            return res
              .status(403)
              .json({ message: "Access denied. Contractor profile required." });
          }

          const contractorBidResults = await storage.getBidsByContractor(
            contractor.id
          );
          const rfqSpecificBids = contractorBidResults.filter(
            ({ bid }) => bid.rfqId === rfqId
          );
          res.json(rfqSpecificBids.map(({ bid }) => bid));
        }
      } catch (error) {
        console.error("Error fetching RFQ bids:", error);
        res.status(500).json({ message: "Failed to fetch bids" });
      }
    }
  );

  // Submit bid with document upload and AI processing
  app.post(
    "/api/rfqs/:rfqId/bids",
    protectedRoute,
    addUserToRequest,
    upload.array("bidDocuments"),
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const sessionId = req.body.sessionId; // Optional progress tracking session
        const user = req.user;

        // Get contractor profile and user organization context
        const contractor = await storage.getContractorByUserId(user.claims.sub);
        if (!contractor) {
          return res
            .status(400)
            .json({ message: "Contractor profile required to submit bids" });
        }

        // Get current user for organization isolation
        const currentUser = await storage.getUser(user.claims.sub);
        if (!currentUser) {
          return res.status(404).json({ message: "User not found" });
        }

        // Clean function for bid data
        const cleanString = (str: string | null | undefined): string | null => {
          if (!str || typeof str !== "string") return null;
          return (
            str
              .replace(/\0/g, "")
              .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, "")
              .trim() || null
          );
        };

        let allExtractedData: any = {};
        const fileResults: { [filename: string]: any } = {};

        // Process uploaded bid documents with AI
        let allExtractedText = "";
        if (req.files && req.files.length > 0) {
          for (const file of req.files) {
            try {
              console.log(`Processing bid document: ${file.originalname}`);

              // Update progress: Starting file upload
              if (sessionId) {
                progressTracker.updateProgress(sessionId, {
                  fileName: file.originalname,
                  uploadedBytes: 0,
                  totalBytes: file.size,
                  percentage: 0,
                  status: "uploading",
                  stage: "upload",
                });
              }

              // Upload file to Object Storage with progress tracking
              const storedFile = await uploadFile(
                file.buffer,
                file.originalname,
                file.mimetype,
                {
                  organizationId: currentUser.organizationId || undefined,
                  ...(sessionId
                    ? {
                        onProgress: (progress) => {
                          progressTracker.updateProgress(sessionId, progress);
                        },
                      }
                    : {})
                }
              );

              // Update progress: Starting AI processing
              if (sessionId) {
                progressTracker.updateProgress(sessionId, {
                  fileName: file.originalname,
                  uploadedBytes: file.size,
                  totalBytes: file.size,
                  percentage: 50,
                  status: "processing",
                  stage: "ai_processing",
                });
              }

              // Write buffer to temporary file for AI processing
              const tempFilePath = path.join(
                os.tmpdir(),
                `${Date.now()}-${file.originalname}`
              );
              await fs.promises.writeFile(tempFilePath, file.buffer);

              const result = await processBidDocument(
                tempFilePath,
                file.originalname
              );
              if (result.structuredData) {
                result.structuredData.fileName = file.originalname;
                fileResults[file.originalname] = { 
                  ...result, 
                  storedFile: storedFile 
                };
                Object.assign(allExtractedData, result.structuredData);
                console.log(
                  `Extracted bid data from ${file.originalname}:`,
                  result.structuredData
                );
              } else {
                // Even if no structured data, save the stored file reference
                fileResults[file.originalname] = { 
                  text: result.text || "",
                  structuredData: {},
                  storedFile: storedFile 
                };
              }

              // Accumulate all extracted text for AI summary
              if (result.text) {
                allExtractedText += `\n\n--- ${file.originalname} ---\n\n${result.text}`;
              }

              // Update progress: Completing file processing
              if (sessionId) {
                progressTracker.updateProgress(sessionId, {
                  fileName: file.originalname,
                  uploadedBytes: file.size,
                  totalBytes: file.size,
                  percentage: 90,
                  status: "processing",
                  stage: "storage",
                });
              }

              // Clean up temporary file
              await fs.promises.unlink(tempFilePath);

              // Complete file progress
              if (sessionId) {
                progressTracker.completeFile(sessionId, file.originalname);
              }
            } catch (aiError) {
              console.error("AI bid processing error:", aiError);
              fileResults[file.originalname] = { text: "", structuredData: {} };
            }
          }
        }

        // Generate AI summary from all extracted text
        let aiSummary = null;
        if (allExtractedText.trim()) {
          try {
            const { generateBidSummary } = await import("./services/aiService");
            aiSummary = await generateBidSummary(
              allExtractedText,
              req.files?.[0]?.originalname
            );
            console.log(
              "🤖 AI Summary generated:",
              aiSummary.substring(0, 200) + "..."
            );
          } catch (summaryError) {
            console.error("AI summary generation error:", summaryError);
            aiSummary =
              "AI summary generation failed - manual review recommended";
          }
        }

        // **ENHANCED BID PROCESSING** - Handle structured form data from 5-tab interface
        console.log("📋 Processing structured bid data from 5-tab form...");

        // Helper function for safe JSON parsing
        const parseJsonSafe = (
          jsonString: string,
          fieldName: string
        ): any[] => {
          try {
            const parsed = JSON.parse(jsonString);
            return Array.isArray(parsed) ? parsed : [];
          } catch (error) {
            console.error(`Failed to parse ${fieldName}:`, error);
            return [];
          }
        };

        // Extract structured form data (Phase 3 Enhancement)
        const formData = {
          // Contact Information
          bidContactName: cleanString(req.body.bidContactName),
          bidContactEmail: cleanString(req.body.bidContactEmail),
          bidContactPhone: cleanString(req.body.bidContactPhone),

          // Pricing Structure
          totalBidAmount: req.body.totalBidAmount
            ? parseFloat(req.body.totalBidAmount)
            : null,
          lineItemsTotal: req.body.lineItemsTotal
            ? parseFloat(req.body.lineItemsTotal)
            : null,

          // Timeline and Notes
          timeline: cleanString(req.body.timeline),
          notes: cleanString(req.body.notes),

          // Structured Data (JSON) with error handling
          lineItems: req.body.lineItems
            ? parseJsonSafe(req.body.lineItems, "lineItems")
            : [],
          includedItems: req.body.includedItems
            ? parseJsonSafe(req.body.includedItems, "includedItems")
            : [],
          excludedItems: req.body.excludedItems
            ? parseJsonSafe(req.body.excludedItems, "excludedItems")
            : [],
        };

        console.log("📊 Structured form data extracted:", {
          contactInfo: `${formData.bidContactName} <${formData.bidContactEmail}>`,
          totalAmount: formData.totalBidAmount,
          lineItemsCount: formData.lineItems.length,
          includedItemsCount: formData.includedItems.length,
          excludedItemsCount: formData.excludedItems.length,
        });

        // Prioritize structured form data over AI-extracted data
        const finalBidAmount = formData.totalBidAmount
          ? String(formData.totalBidAmount)
          : allExtractedData.bidAmount
            ? String(allExtractedData.bidAmount)
            : null;
        const finalTimeline =
          formData.timeline || cleanString(allExtractedData.timeline);

        console.log(
          "🎯 Final bid amount:",
          finalBidAmount,
          "Timeline:",
          finalTimeline
        );

        const bidData = insertBidSchema.parse({
          rfqId,
          contractorId: contractor.id,

          // **ENHANCED BID DATA** - Prioritize structured form data
          bidAmount: finalBidAmount,
          timeline: finalTimeline,

          // Enhanced contact information from form
          bidContactName: formData.bidContactName,
          bidContactEmail: formData.bidContactEmail,
          bidContactPhone: formData.bidContactPhone,

          // Enhanced pricing structure
          totalBidAmount: formData.totalBidAmount,
          lineItemsTotal: formData.lineItemsTotal
            ? String(formData.lineItemsTotal)
            : null,

          // Additional notes and scope from form or AI
          scope: formData.notes || cleanString(allExtractedData.scope),
          conditions: cleanString(allExtractedData.conditions),
          laborBreakdown: cleanString(allExtractedData.laborBreakdown),
          materialBreakdown: cleanString(allExtractedData.materialBreakdown),
          equipmentCosts: cleanString(allExtractedData.equipmentCosts),
          startDate: allExtractedData.startDate
            ? new Date(allExtractedData.startDate)
            : null,
          completionDate: allExtractedData.completionDate
            ? new Date(allExtractedData.completionDate)
            : null,
          warranty: cleanString(allExtractedData.warranty),
          bondingRequired: allExtractedData.bondingRequired || false,
          insuranceDetails: cleanString(allExtractedData.insuranceDetails),

          // Extract confidence from AI processing
          confidenceScore: allExtractedData.confidence
            ? allExtractedData.confidence.toString()
            : "0",

          // Combine all extracted text
          extractedText: Object.values(fileResults)
            .map((r: any) => r.text)
            .join("\n\n"),

          // Store AI summary
          aiSummary: aiSummary || null,

          submittedAt: new Date(),
          status: "submitted",
        });

        const newBid = await storage.createBid(bidData);
        console.log("✅ Bid created with ID:", newBid.id);

        // 📝 AUDIT LOG: Bid Submission (will implement dedicated method later)
        await AuditService.logFileUpload({
          userId: req.user.claims.sub,
          fileName: "Bid Submission",
          fileSize: 0,
          fileType: "application/json",
          bidId: newBid.id,
          req,
        });

        // 🔔 NOTIFICATION: Bid Submission
        try {
          // Get RFQ details for notification
          const rfq = await storage.getRfq(rfqId);
          if (rfq && rfq.createdBy) {
            // Notify the RFQ owner about new bid submission
            await NotificationService.createBidSubmissionNotification({
              rfqOwnerId: rfq.createdBy,
              contractorId: contractor.id,
              contractorName: contractor.companyName || "Contractor",
              bidId: newBid.id,
              rfqId: rfq.id,
              projectName: rfq.projectName,
              bidAmount: finalBidAmount
                ? parseFloat(finalBidAmount)
                : undefined,
              submissionDate: new Date(),
              organizationId: rfq.organizationId,
            });
            console.log("✅ Bid submission notification sent to RFQ owner");
          }
        } catch (notificationError) {
          console.error(
            "⚠️ Failed to send bid submission notification:",
            notificationError
          );
          // Don't fail the bid submission if notification fails
        }

        // **ENHANCED DATA STORAGE** - Store structured line items in bid_line_items table
        if (formData.lineItems && formData.lineItems.length > 0) {
          console.log(`💼 Storing ${formData.lineItems.length} line items...`);
          for (const lineItem of formData.lineItems) {
            try {
              await storage.createBidLineItem(
                asInsert<InsertBidLineItem>({
                  bidId: newBid.id,
                  costCode: lineItem.costCode,
                  description: lineItem.description,
                  quantity: lineItem.quantity,
                  unitPrice: lineItem.unitPrice,
                  totalPrice: lineItem.totalPrice,
                  unitOfMeasure: lineItem.unitOfMeasure || "EA",
                  category: lineItem.category || "General",
                })
              );
              console.log(
                `📝 Line item stored: ${lineItem.costCode} - ${lineItem.description}`
              );
            } catch (lineItemError) {
              console.error("Error storing line item:", lineItemError);
            }
          }
        }

        // **ENHANCED SCOPE STORAGE** - Store inclusions/exclusions in bid_inclusions_exclusions table
        const allScopeItems = [
          ...formData.includedItems.map((item: any) => ({
            ...item,
            type: "included",
          })),
          ...formData.excludedItems.map((item: any) => ({
            ...item,
            type: "excluded",
          })),
        ];

        if (allScopeItems.length > 0) {
          console.log(
            `📋 Storing ${allScopeItems.length} scope definition items...`
          );
          for (const scopeItem of allScopeItems) {
            try {
              await storage.createBidInclusionExclusion(
                asInsert<InsertBidInclusionsExclusions>({
                  bidId: newBid.id,
                  itemType: scopeItem.type, // Fix column name mismatch
                  description: scopeItem.description,
                  category: scopeItem.category || "General",
                })
              );
              console.log(
                `📋 Scope item stored: ${scopeItem.type} - ${scopeItem.description}`
              );
            } catch (scopeError) {
              console.error("Error storing scope item:", scopeError);
            }
          }
        }

        // Store bid documents metadata (files already uploaded during AI processing)
        if (req.files && req.files.length > 0) {
          for (const file of req.files) {
            try {
              // Find the stored file from earlier upload
              const matchingStoredFile = fileResults[file.originalname]?.storedFile;
              if (!matchingStoredFile) {
                console.warn(`No stored file found for ${file.originalname}`);
                continue;
              }

              await storage.createBidDocument(
                asInsert<InsertBidDocument>({
                  bidId: newBid.id,
                  fileName: file.originalname,
                  fileUrl: matchingStoredFile.fileUrl,
                  mimeType: file.mimetype,
                  fileSize: file.buffer.length,
                  objectKey: matchingStoredFile.objectKey,
                  extractedText: fileResults[file.originalname]?.text || null,
                })
              );

              // 📝 AUDIT LOG: File Upload for Bid
              await AuditService.logFileUpload({
                userId: req.user.claims.sub,
                fileName: file.originalname,
                fileSize: file.buffer.length,
                fileType: file.mimetype,
                objectKey: matchingStoredFile.objectKey,
                bidId: newBid.id,
                req,
              });
            } catch (storageError) {
              console.error("Error storing bid document:", storageError);
            }
          }
        }

        // **ENHANCED RESPONSE** - Include structured data summary
        const enhancedBidResponse = {
          ...newBid,
          structuredDataSummary: {
            hasLineItems: formData.lineItems.length > 0,
            lineItemsCount: formData.lineItems.length,
            lineItemsTotal: formData.lineItemsTotal,
            hasScopeDefinition:
              formData.includedItems.length + formData.excludedItems.length > 0,
            includedItemsCount: formData.includedItems.length,
            excludedItemsCount: formData.excludedItems.length,
            hasContactInfo: !!(
              formData.bidContactName && formData.bidContactEmail
            ),
            processingMethod:
              formData.lineItems.length > 0
                ? "structured_form"
                : "ai_extracted",
          },
          aiProcessingSummary: {
            documentsProcessed: req.files?.length || 0,
            aiExtractionPerformed: Object.keys(allExtractedData).length > 0,
            hasAiSummary: !!aiSummary,
          },
        };

        console.log("🎉 Enhanced bid submission completed:", {
          bidId: newBid.id,
          structuredItems: formData.lineItems.length,
          scopeItems:
            formData.includedItems.length + formData.excludedItems.length,
          documentsProcessed: req.files?.length || 0,
        });

        res.status(201).json(enhancedBidResponse);
      } catch (error) {
        console.error("Error submitting bid:", error);
        res.status(500).json({ message: "Failed to submit bid" });
      }
    }
  );

  // **NEW ENHANCED API** - Get structured bid data with line items and scope definitions
  app.get(
    "/api/bids/:bidId/structured-data",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { bidId } = req.params;
        const user = req.user;

        // Get the bid to check access permissions
        const bid = await storage.getBid(bidId);
        if (!bid) {
          return res.status(404).json({ message: "Bid not found" });
        }

        // Get the RFQ to check if user is the owner
        const rfq = await storage.getRfq(bid.rfqId || "");
        if (!rfq) {
          return res.status(404).json({ message: "Associated RFQ not found" });
        }

        const isRfqOwner = rfq.createdBy === user.claims.sub;

        // Check if user is the contractor who submitted this bid
        const contractor = await storage.getContractorByUserId(user.claims.sub);
        const isBidOwner = contractor && bid.contractorId === contractor.id;

        if (!isRfqOwner && !isBidOwner) {
          return res.status(403).json({
            message:
              "Access denied. You can only view your own bids or bids for your RFQs.",
          });
        }

        // **RETRIEVE STRUCTURED DATA** - Get line items and scope definitions
        const [lineItems, scopeDefinitions] = await Promise.all([
          storage.getBidLineItems(bidId),
          storage.getBidInclusionsExclusions(bidId),
        ]);

        // Calculate totals from line items (handle null/undefined arrays)
        const lineItemsTotal =
          lineItems && lineItems.length > 0
            ? lineItems.reduce((sum, item) => {
                return sum + parseFloat(item.totalPrice?.toString() || "0");
              }, 0)
            : 0;

        // Separate inclusions and exclusions (handle null/undefined arrays)
        const includedItems = scopeDefinitions
          ? scopeDefinitions.filter((item) => item.itemType === "included")
          : [];
        const excludedItems = scopeDefinitions
          ? scopeDefinitions.filter((item) => item.itemType === "excluded")
          : [];

        // **ENHANCED BID DATA RESPONSE**
        const structuredBidData = {
          bid: {
            ...bid,
            // Enhanced fields that might be available
            contactInfo: {
              name: bid.bidContactName,
              email: bid.bidContactEmail,
              phone: bid.bidContactPhone,
            },
          },
          lineItems: {
            items: lineItems || [],
            count: lineItems ? lineItems.length : 0,
            total: lineItemsTotal,
            categories: lineItems
              ? Array.from(
                  new Set(
                    lineItems.map((item) => item.category).filter(Boolean)
                  )
                )
              : [],
          },
          scopeDefinition: {
            included: {
              items: includedItems,
              count: includedItems.length,
              categories: Array.from(
                new Set(
                  includedItems.map((item) => item.category).filter(Boolean)
                )
              ),
            },
            excluded: {
              items: excludedItems,
              count: excludedItems.length,
              categories: Array.from(
                new Set(
                  excludedItems.map((item) => item.category).filter(Boolean)
                )
              ),
            },
          },
          summary: {
            hasStructuredData:
              (lineItems && lineItems.length > 0) ||
              (scopeDefinitions && scopeDefinitions.length > 0),
            dataCompleteness: {
              hasLineItems: lineItems ? lineItems.length > 0 : false,
              hasScopeDefinition: scopeDefinitions
                ? scopeDefinitions.length > 0
                : false,
              hasContactInfo: !!(bid.bidContactName && bid.bidContactEmail),
              calculatedTotal: lineItemsTotal,
              bidAmountMatch:
                lineItemsTotal === parseFloat(bid.bidAmount?.toString() || "0"),
            },
          },
        };

        console.log("📊 Retrieved structured bid data:", {
          bidId,
          lineItems: lineItems.length,
          scopeItems: scopeDefinitions.length,
          calculatedTotal: lineItemsTotal,
        });

        res.json(structuredBidData);
      } catch (error) {
        console.error("Error fetching structured bid data:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch structured bid data" });
      }
    }
  );

  // Update bid status (for GC review only)
  app.patch(
    "/api/bids/:bidId",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { bidId } = req.params;
        const { status, notes } = req.body;
        const user = req.user;

        // Get the bid and verify RFQ ownership
        const bid = await storage.getBid(bidId);
        if (!bid) {
          return res.status(404).json({ message: "Bid not found" });
        }

        const rfq = bid.rfqId ? await storage.getRfq(bid.rfqId) : null;
        if (!rfq) {
          return res.status(404).json({ message: "Associated RFQ not found" });
        }

        // Only RFQ owner can update bid status
        if (rfq.createdBy !== user.claims.sub) {
          return res.status(403).json({
            message: "Access denied. Only RFQ owner can update bid status.",
          });
        }

        const updatedBid = await storage.updateBid(bidId, {
          status,
          reviewNotes: notes,
          reviewedAt: new Date(),
        });

        if (!updatedBid) {
          return res.status(404).json({ message: "Bid not found" });
        }

        // 🔔 NOTIFICATION: Bid Action (Accept/Reject)
        try {
          // Get contractor details for notification
          if (bid.contractorId) {
            const contractor = await storage.getContractor(bid.contractorId);
            if (
              contractor &&
              contractor.userId &&
              (status === "accepted" || status === "rejected")
            ) {
              await NotificationService.createBidActionNotification({
                contractorUserId: contractor.userId,
                rfqOwnerId: user.claims.sub,
                bidId: bid.id,
                rfqId: rfq.id,
                projectName: rfq.projectName,
                action: status === "accepted" ? "accepted" : "rejected",
                notes: notes,
                actionDate: new Date(),
                organizationId: rfq.organizationId,
              });
              console.log(`✅ Bid ${status} notification sent to contractor`);
            }
          }
        } catch (notificationError) {
          console.error(
            "⚠️ Failed to send bid action notification:",
            notificationError
          );
          // Don't fail the bid update if notification fails
        }

        res.json(updatedBid);
      } catch (error) {
        console.error("Error updating bid:", error);
        res.status(500).json({ message: "Failed to update bid" });
      }
    }
  );

  // Get bid documents with access control
  app.get(
    "/api/bids/:bidId/documents",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { bidId } = req.params;
        const user = req.user;

        // Get the bid to check ownership and RFQ relationship
        const bid = await storage.getBid(bidId);
        if (!bid) {
          return res.status(404).json({ message: "Bid not found" });
        }

        // Get the RFQ to check if user is the owner
        const rfq = bid.rfqId ? await storage.getRfq(bid.rfqId) : null;
        if (!rfq) {
          return res.status(404).json({ message: "Associated RFQ not found" });
        }

        const isRfqOwner = rfq.createdBy === user.claims.sub;

        // Check if user is the contractor who submitted this bid
        const contractor = await storage.getContractorByUserId(user.claims.sub);
        const isBidOwner = contractor && bid.contractorId === contractor.id;

        if (!isRfqOwner && !isBidOwner) {
          return res.status(403).json({
            message:
              "Access denied. You can only view your own bids or bids for your RFQs.",
          });
        }

        const limit = req.query.limit ? parseInt(String(req.query.limit)) : undefined;
        const offset = req.query.offset ? parseInt(String(req.query.offset)) : undefined;
        const documents = await storage.getBidDocuments(bidId, limit || offset ? { limit, offset } : undefined);
        res.json(documents);
      } catch (error) {
        console.error("Error fetching bid documents:", error);
        res.status(500).json({ message: "Failed to fetch bid documents" });
      }
    }
  );

  // Get individual bid with access control
  app.get(
    "/api/bids/:bidId",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { bidId } = req.params;
        const user = req.user;

        const bid = await storage.getBid(bidId);
        if (!bid) {
          return res.status(404).json({ message: "Bid not found" });
        }

        // Get the RFQ to check if user is the owner
        const rfq = bid.rfqId ? await storage.getRfq(bid.rfqId) : null;
        if (!rfq) {
          return res.status(404).json({ message: "Associated RFQ not found" });
        }

        const isRfqOwner = rfq.createdBy === user.claims.sub;

        // Check if user is the contractor who submitted this bid
        const contractor = await storage.getContractorByUserId(user.claims.sub);
        const isBidOwner = contractor && bid.contractorId === contractor.id;

        if (!isRfqOwner && !isBidOwner) {
          return res.status(403).json({
            message:
              "Access denied. You can only view your own bids or bids for your RFQs.",
          });
        }

        res.json(bid);
      } catch (error) {
        console.error("Error fetching bid:", error);
        res.status(500).json({ message: "Failed to fetch bid" });
      }
    }
  );

  // Dashboard stats route
  app.get("/api/dashboard/stats", async (req, res) => {
    try {
      const stats = await storage.getDashboardStats();
      res.json(stats);
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      res.status(500).json({ message: "Failed to fetch dashboard stats" });
    }
  });

  // Forecast materials routes
  app.get(
    "/api/forecast/materials",
    protectedRoute,
    addUserToRequest,
    async (req, res) => {
      try {
        const materials = await storage.getForecastMaterials();
        res.json(materials);
      } catch (error) {
        console.error("Error fetching forecast materials:", error);
        res.status(500).json({ message: "Failed to fetch forecast materials" });
      }
    }
  );

  app.post(
    "/api/forecast/materials",
    protectedRoute,
    addUserToRequest,
    async (req, res) => {
      try {
        const { insertForecastMaterialSchema } = await import("@shared/schema");
        const materialData = insertForecastMaterialSchema.parse(req.body);
        const material = await storage.createForecastMaterial(
          asInsert<InsertForecastMaterial>(materialData)
        );
        res.json(material);
      } catch (error) {
        console.error("Error creating forecast material:", error);
        res.status(500).json({ message: "Failed to create forecast material" });
      }
    }
  );

  // Material search route using Perplexity API
  app.post(
    "/api/forecast/search",
    protectedRoute,
    addUserToRequest,
    async (req, res) => {
      try {
        const { searchMaterialPricing } = await import(
          "./services/perplexityService"
        );
        const { materialName, unit } = req.body;

        if (!materialName || !unit) {
          return res
            .status(400)
            .json({ message: "Material name and unit are required" });
        }

        const pricingData = await searchMaterialPricing(materialName, unit);
        res.json(pricingData);
      } catch (error) {
        console.error("Error searching material pricing:", error);
        res.status(500).json({ message: "Failed to search material pricing" });
      }
    }
  );

  // Contractor favorites management
  app.get(
    "/api/contractors/favorites",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const favorites = await storage.getFavoriteContractors(user.claims.sub);
        res.json(favorites);
      } catch (error) {
        console.error("Error fetching favorite contractors:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch favorite contractors" });
      }
    }
  );

  app.post(
    "/api/contractors/:contractorId/favorite",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { contractorId } = req.params;
        const { notes } = req.body;
        const user = req.user;

        if (!user || !user.claims?.sub) {
          return res.status(401).json({ message: "User not authenticated" });
        }

        const favorite = await storage.addContractorToFavorites(
          user.claims.sub,
          contractorId,
          notes
        );
        res.status(201).json(favorite);
      } catch (error) {
        console.error("Error adding contractor to favorites:", error);
        res
          .status(500)
          .json({ message: "Failed to add contractor to favorites" });
      }
    }
  );

  app.delete(
    "/api/contractors/:contractorId/favorite",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { contractorId } = req.params;
        const user = req.user;

        const success = await storage.removeContractorFromFavorites(
          user.claims.sub,
          contractorId
        );
        if (!success) {
          return res.status(404).json({ message: "Favorite not found" });
        }

        res.status(204).send();
      } catch (error) {
        console.error("Error removing contractor from favorites:", error);
        res
          .status(500)
          .json({ message: "Failed to remove contractor from favorites" });
      }
    }
  );

  // RFQ distribution management
  app.post(
    "/api/rfqs/:rfqId/distribute",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const { contractorIds, method } = req.body;
        const user = req.user;

        // Ownership check: only RFQ owner can distribute
        const rfqRecord = await storage.getRfq(rfqId);
        if (!rfqRecord) {
          return res.status(404).json({ message: "RFQ not found" });
        }
        if (rfqRecord.createdBy !== user.claims.sub) {
          return res
            .status(403)
            .json({ message: "Only the RFQ owner can distribute this RFQ" });
        }

        if (
          !contractorIds ||
          !Array.isArray(contractorIds) ||
          contractorIds.length === 0
        ) {
          return res
            .status(400)
            .json({ message: "Contractor IDs are required" });
        }

        if (!["favorites", "broadcast"].includes(method)) {
          return res
            .status(400)
            .json({ message: "Method must be 'favorites' or 'broadcast'" });
        }

        const distributions = await storage.distributeRfq(
          rfqId,
          contractorIds,
          method
        );

        // 📝 AUDIT LOG: RFQ Distribution
        const rfq = await storage.getRfq(rfqId);
        await AuditService.logRfqDistribution({
          userId: req.user.claims.sub,
          rfqId: rfqId,
          projectName: rfq?.projectName || "Unknown Project",
          contractorIds: contractorIds,
          distributionMethod: method,
          contractorCount: contractorIds.length,
          req,
        });

        res.status(201).json(distributions);
      } catch (error) {
        console.error("Error distributing RFQ:", error);
        res.status(500).json({ message: "Failed to distribute RFQ" });
      }
    }
  );

  app.get(
    "/api/rfqs/:rfqId/distributions",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const rfqRecord = await storage.getRfq(rfqId);
        if (!rfqRecord) {
          return res.status(404).json({ message: "RFQ not found" });
        }
        if (rfqRecord.createdBy !== req.user.claims.sub) {
          return res
            .status(403)
            .json({ message: "Only the RFQ owner can view distributions" });
        }

        const distributions = await storage.getRfqDistributions(rfqId);
        res.json(distributions);
      } catch (error) {
        console.error("Error fetching RFQ distributions:", error);
        res.status(500).json({ message: "Failed to fetch RFQ distributions" });
      }
    }
  );

  // Get all active RFQs available for bidding
  app.get(
    "/api/contractors/rfqs/all",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const contractor = await storage.getContractorByUserId(user.claims.sub);

        if (!contractor) {
          return res
            .status(400)
            .json({ message: "Contractor profile not found" });
        }

        // Get all active RFQs that are available for bidding
        const allRfqs = await storage.getRfqs();
        console.log(
          "All RFQs:",
          allRfqs.map((r) => ({
            id: r.id,
            projectName: r.projectName,
            status: r.status,
            dueDate: r.dueDate,
          }))
        );
        const activeRfqs = allRfqs.filter(
          (rfq) => rfq.status === "Active" && new Date(rfq.dueDate) > new Date()
        );
        console.log(
          "Active RFQs:",
          activeRfqs.map((r) => ({
            id: r.id,
            projectName: r.projectName,
            status: r.status,
            dueDate: r.dueDate,
          }))
        );

        // Convert to distribution format for compatibility
        const rfqDistributions = activeRfqs.map((rfq) => ({
          id: `dist-${rfq.id}`, // Generate a distribution ID
          rfqId: rfq.id,
          sentAt: rfq.createdAt,
          viewedAt: null,
          declinedAt: null,
          declineReason: null,
          rfq: {
            id: rfq.id,
            projectName: rfq.projectName,
            projectLocation: rfq.projectLocation,
            description: rfq.description,
            tradeCategory: rfq.tradeCategory,
            dueDate: rfq.dueDate,
            status: rfq.status,
            extractedData: rfq.extractedData,
            createdAt: rfq.createdAt,
          },
        }));

        res.json(rfqDistributions);
      } catch (error) {
        console.error("Error fetching contractor RFQs:", error);
        res.status(500).json({ message: "Failed to fetch contractor RFQs" });
      }
    }
  );

  // Contractor RFQ access with distribution details (original endpoint)
  app.get(
    "/api/contractors/rfqs",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const contractor = await storage.getContractorByUserId(user.claims.sub);

        if (!contractor) {
          return res
            .status(400)
            .json({ message: "Contractor profile not found" });
        }

        const distributions = await storage.getContractorRfqDistributions(
          contractor.id
        );
        res.json(distributions);
      } catch (error) {
        console.error("Error fetching contractor RFQs:", error);
        res.status(500).json({ message: "Failed to fetch contractor RFQs" });
      }
    }
  );

  app.patch(
    "/api/rfqs/:rfqId/view",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const user = req.user;

        const contractor = await storage.getContractorByUserId(user.claims.sub);
        if (!contractor) {
          return res
            .status(400)
            .json({ message: "Contractor profile not found" });
        }

        await storage.markRfqViewed(rfqId, contractor.id);
        res.status(204).send();
      } catch (error) {
        console.error("Error marking RFQ as viewed:", error);
        res.status(500).json({ message: "Failed to mark RFQ as viewed" });
      }
    }
  );

  // Decline RFQ
  app.patch(
    "/api/rfqs/:rfqId/decline",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const { reason } = req.body;
        const user = req.user;

        const contractor = await storage.getContractorByUserId(user.claims.sub);
        if (!contractor) {
          return res
            .status(400)
            .json({ message: "Contractor profile not found" });
        }

        await storage.declineRfq(rfqId, contractor.id, reason);
        res.status(204).send();
      } catch (error) {
        console.error("Error declining RFQ:", error);
        res.status(500).json({ message: "Failed to decline RFQ" });
      }
    }
  );

  app.patch(
    "/api/rfqs/:rfqId/decline",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const { reason } = req.body;
        const user = req.user;

        const contractor = await storage.getContractorByUserId(user.claims.sub);
        if (!contractor) {
          return res
            .status(400)
            .json({ message: "Contractor profile not found" });
        }

        await storage.declineRfq(rfqId, contractor.id, reason);
        res.status(204).send();
      } catch (error) {
        console.error("Error declining RFQ:", error);
        res.status(500).json({ message: "Failed to decline RFQ" });
      }
    }
  );

  // Cost code category analysis for RFQ bids
  app.get(
    "/api/rfqs/:rfqId/category-analysis",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const user = req.user;
        const userId = user.claims.sub;

        // Get user with role from database
        const dbUser = await storage.getUser(userId);

        // Get RFQ to verify access
        const rfq = await storage.getRfq(rfqId);
        if (!rfq) {
          return res.status(404).json({ message: "RFQ not found" });
        }

        // Check if user has access to this RFQ (RFQ owner or admin)
        if (
          rfq.createdBy !== userId &&
          dbUser?.role !== "SuperUser" &&
          dbUser?.role !== "OrganizationAdmin"
        ) {
          return res.status(403).json({ message: "Access denied" });
        }

        // Get all bids for this RFQ
        const bids = await storage.getBidsByRfq(rfqId);
        if (!bids || bids.length === 0) {
          return res.json([]);
        }

        // Get contractors for each bid
        const bidContractors = await Promise.all(
          bids.map(async (bid) => {
            const contractor = await storage.getContractor(bid.contractorId!);
            return { bid, contractor };
          })
        );

        // Get structured data (line items) for each bid
        const bidsWithLineItems = await Promise.all(
          bidContractors.map(async ({ bid, contractor }) => {
            if (!contractor) return null;

            const lineItems = await storage.getBidLineItems(bid!.id);
            return {
              bidId: bid!.id,
              contractorId: contractor.id,
              contractorName: contractor.primaryContactName || "Unknown",
              companyName: contractor.companyName,
              bidAmount: Number(bid!.bidAmount || bid!.extractedAmount || 0),
              timeline: bid!.timeline,
              status: bid!.status || "submitted",
              competitiveScore: Number(bid!.competitiveScore || 0),
              lineItems: lineItems || [],
              proposalText: bid!.proposalText,
              notes: bid!.notes,
            };
          })
        );

        // Filter out null results
        const validBids = bidsWithLineItems.filter(Boolean);

        // Helper function to extract main cost code (XX-000 format)
        const getMainCostCode = (costCode: string): string => {
          if (!costCode) return "Other";

          // Handle different formats: "03.01", "03-01", "0301", etc.
          const match = costCode.match(/^(\d{2})/);
          return match ? `${match[1]}-000` : "Other";
        };

        // Helper function to get category name
        const getCategoryName = (mainCostCode: string): string => {
          const categoryNames: Record<string, string> = {
            "01-000": "General Requirements",
            "02-000": "Site Construction",
            "03-000": "Concrete",
            "04-000": "Masonry",
            "05-000": "Metals",
            "06-000": "Wood & Plastics",
            "07-000": "Thermal & Moisture Protection",
            "08-000": "Doors & Windows",
            "09-000": "Finishes",
            "10-000": "Specialties",
            "11-000": "Equipment",
            "12-000": "Furnishings",
            "13-000": "Special Construction",
            "14-000": "Conveying Systems",
            "15-000": "Mechanical",
            "16-000": "Electrical",
            "21-000": "Fire Suppression",
            "22-000": "Plumbing",
            "23-000": "HVAC",
            "26-000": "Electrical",
            "27-000": "Communications",
            "28-000": "Electronic Safety",
          };

          return categoryNames[mainCostCode] || mainCostCode;
        };

        // Group bids by cost code categories
        const categoryGroups: Record<string, any[]> = {};

        validBids.forEach((bid) => {
          if (!bid.lineItems || bid.lineItems.length === 0) {
            // If no line items, use the whole bid amount under "General"
            const mainCostCode = "01-000"; // General Requirements
            if (!categoryGroups[mainCostCode]) {
              categoryGroups[mainCostCode] = [];
            }
            categoryGroups[mainCostCode].push({
              ...bid,
              categoryAmount: bid.bidAmount,
            });
          } else {
            // Group line items by main cost code and calculate totals per category
            const bidCategoryTotals: Record<string, number> = {};
            const bidCategoryItems: Record<string, any[]> = {};

            bid.lineItems.forEach((lineItem) => {
              const mainCostCode = getMainCostCode(lineItem.costCode);
              const itemTotal = Number(lineItem.totalPrice || 0);

              if (!bidCategoryTotals[mainCostCode]) {
                bidCategoryTotals[mainCostCode] = 0;
                bidCategoryItems[mainCostCode] = [];
              }

              bidCategoryTotals[mainCostCode] += itemTotal;
              bidCategoryItems[mainCostCode].push(lineItem);
            });

            // Add bid to each category it participates in
            Object.keys(bidCategoryTotals).forEach((mainCostCode) => {
              if (!categoryGroups[mainCostCode]) {
                categoryGroups[mainCostCode] = [];
              }

              categoryGroups[mainCostCode].push({
                ...bid,
                categoryAmount: bidCategoryTotals[mainCostCode],
                lineItems: bidCategoryItems[mainCostCode],
              });
            });
          }
        });

        // Create category summaries
        const categorySummaries = Object.keys(categoryGroups).map(
          (mainCostCode) => {
            const contractorBids = categoryGroups[mainCostCode];
            const amounts = contractorBids.map((b) => b.categoryAmount);

            const lowest = Math.min(...amounts);
            const highest = Math.max(...amounts);
            const average =
              amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;

            return {
              mainCostCode,
              categoryName: getCategoryName(mainCostCode),
              contractorBids: contractorBids.map((bid) => ({
                ...bid,
                bidAmount: bid.categoryAmount, // Use category amount instead of total bid amount
              })),
              totalContractors: contractorBids.length,
              priceRange: {
                lowest,
                highest,
                average,
              },
            };
          }
        );

        // Sort categories by cost code
        categorySummaries.sort((a, b) =>
          a.mainCostCode.localeCompare(b.mainCostCode)
        );

        res.json(categorySummaries);
      } catch (error) {
        console.error("Error generating category analysis:", error);
        res
          .status(500)
          .json({ message: "Failed to generate category analysis" });
      }
    }
  );

  // **NEW ENDPOINT**: Comprehensive Bid Analysis for Side-by-Side Comparison
  app.get(
    "/api/rfqs/:rfqId/comprehensive-bid-analysis",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const user = req.user;
        const userId = user.claims.sub;

        // Get user with role from database
        const dbUser = await storage.getUser(userId);

        // Get RFQ to verify access
        const rfq = await storage.getRfq(rfqId);
        if (!rfq) {
          return res.status(404).json({ message: "RFQ not found" });
        }

        // Check if user has access to this RFQ (RFQ owner or admin)
        if (
          rfq.createdBy !== userId &&
          dbUser?.role !== "SuperUser" &&
          dbUser?.role !== "OrganizationAdmin"
        ) {
          return res.status(403).json({ message: "Access denied" });
        }

        // Get all bids for this RFQ
        const bids = await storage.getBidsByRfq(rfqId);
        if (!bids || bids.length === 0) {
          return res.json([]);
        }

        // Get complete bid data with contractors and line items
        const comprehensiveBidData = await Promise.all(
          bids.map(async (bid) => {
            console.log(
              `🔍 Processing bid ${bid.id} for contractor ${bid.contractorId}`
            );

            const contractor = await storage.getContractor(bid.contractorId);
            if (!contractor) {
              console.log(`❌ No contractor found for bid ${bid.id}`);
              return null;
            }

            console.log(`👷 Found contractor: ${contractor.companyName}`);
            console.log(`🎯 Fetching line items for bid: ${bid.id}`);

            const lineItems = await storage.getBidLineItems(bid.id);
            console.log(
              `📊 Retrieved ${lineItems?.length || 0} line items for ${contractor.companyName}`
            );

            if (lineItems && lineItems.length > 0) {
              console.log(
                `📋 Line items preview:`,
                lineItems
                  .slice(0, 2)
                  .map(
                    (li) =>
                      `${li.costCode}: ${li.description} - $${li.totalPrice}`
                  )
              );
            }

            return {
              bidId: bid.id,
              contractorId: contractor.id,
              contractorName: contractor.primaryContactName || "Unknown",
              companyName: contractor.companyName,
              bidAmount: Number(bid.bidAmount || bid.extractedAmount || 0),
              timeline: bid.timeline,
              status: bid.status || "submitted",
              proposalText: bid.proposalText,
              notes: bid.notes,
              lineItems: lineItems || [],
              contractorInfo: {
                yearsInBusiness: contractor.yearsInBusiness || 0,
                licenseNumber: contractor.licenseNumber,
                generalLiability: contractor.generalLiability,
                bondingSingle: contractor.bondingSingle,
                bondingAggregate: contractor.bondingAggregate,
                businessAddress: contractor.businessAddress,
                primaryContactPhone: contractor.primaryContactPhone,
                primaryContactEmail: contractor.primaryContactEmail,
              },
            };
          })
        );

        // Filter out null results and return
        const validBids = comprehensiveBidData.filter(Boolean);
        console.log(
          `🎯 Comprehensive bid analysis returning ${validBids.length} bids with complete line items`
        );

        res.json(validBids);
      } catch (error) {
        console.error("Error generating comprehensive bid analysis:", error);
        res
          .status(500)
          .json({ message: "Failed to generate comprehensive bid analysis" });
      }
    }
  );

  // Contractor bid dashboard - view all their bids
  app.get(
    "/api/contractors/bids",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const contractor = await storage.getContractorByUserId(user.claims.sub);

        if (!contractor) {
          return res
            .status(400)
            .json({ message: "Contractor profile not found" });
        }

        const contractorBidResults = await storage.getBidsByContractor(
          contractor.id
        );

        // Remove any duplicates based on bid ID
        const uniqueBidResults = contractorBidResults.filter(
          (item, index, self) =>
            index === self.findIndex((b) => b.bid.id === item.bid.id)
        );

        console.log(
          `Found ${contractorBidResults.length} bid results, returning ${uniqueBidResults.length} unique bids`
        );

        // Map to expected interface structure
        const formattedBids = uniqueBidResults.map(({ bid, rfq }) => ({
          ...bid,
          rfq: {
            title: rfq.projectName,
            projectLocation: rfq.projectLocation,
            dueDate: rfq.dueDate,
          },
        }));

        res.json(formattedBids);
      } catch (error) {
        console.error("Error fetching contractor bids:", error);
        res.status(500).json({ message: "Failed to fetch contractor bids" });
      }
    }
  );

  // Performance and Security Monitoring Endpoints
  app.get(
    "/api/admin/performance/cache",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        if (user.role !== "SuperUser" && user.role !== "Admin") {
          return res.status(403).json({ message: "Insufficient permissions" });
        }

        const cacheStats = cache.getStats();
        res.json({
          cache: cacheStats,
          timestamp: new Date(),
        });
      } catch (error) {
        console.error("Cache stats error:", error);
        res.status(500).json({ message: "Failed to get cache statistics" });
      }
    }
  );

  app.get(
    "/api/admin/performance/files",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        if (user.role !== "SuperUser" && user.role !== "Admin") {
          return res.status(403).json({ message: "Insufficient permissions" });
        }

        const fileStats = getFileProcessingStats();
        res.json({
          fileProcessing: fileStats,
          timestamp: new Date(),
        });
      } catch (error) {
        console.error("File stats error:", error);
        res
          .status(500)
          .json({ message: "Failed to get file processing statistics" });
      }
    }
  );

  app.get(
    "/api/admin/security/audit",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        if (user.role !== "SuperUser" && user.role !== "Admin") {
          return res.status(403).json({ message: "Insufficient permissions" });
        }

        const { limit = 100, offset = 0 } = req.query;
        const auditLogs = getAuditLogs(Number(limit), Number(offset));
        const securityStats = getSecurityStats();

        res.json({
          auditLogs,
          securityStats,
          timestamp: new Date(),
        });
      } catch (error) {
        console.error("Audit logs error:", error);
        res.status(500).json({ message: "Failed to get audit logs" });
      }
    }
  );

  // Session invalidation endpoint for administrative security actions
  app.post(
    "/api/admin/security/invalidate-sessions",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const { targetUserId, reason } = req.body;
        const currentUser = req.currentUser;

        if (!targetUserId) {
          return res
            .status(400)
            .json({ message: "Target user ID is required" });
        }

        // Security check: ensure the admin can manage this user
        if (currentUser.role !== "SuperUser") {
          const targetUser = await storage.getUser(targetUserId);
          if (
            !targetUser ||
            targetUser.organizationId !== currentUser.organizationId
          ) {
            return res.status(403).json({
              message:
                "Cannot invalidate sessions for users outside your organization",
            });
          }
        }

        // Note: Clerk handles session invalidation automatically
        const sessionsInvalidated = 0; // Clerk manages sessions

        // Log the security action
        console.log(
          `🔒 Security: Admin ${currentUser.email} invalidated sessions for user ${targetUserId}. Reason: ${reason || "Administrative action"}`
        );

        res.json({
          success: true,
          sessionsInvalidated,
          targetUserId,
          reason: reason || "Administrative action",
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error("Error invalidating user sessions:", error);
        res.status(500).json({ message: "Failed to invalidate user sessions" });
      }
    }
  );

  // Enhanced API Key Security Endpoints
  app.post(
    "/api/admin/api-keys/rotate",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const { currentApiKey, securityToken, newOptions } = req.body;
        const currentUser = req.currentUser;

        if (!currentApiKey || !securityToken) {
          return res.status(400).json({
            message: "Current API key and security token are required",
          });
        }

        const result = await rotateApiKey(
          currentApiKey,
          securityToken,
          newOptions
        );

        if (!result) {
          return res.status(400).json({
            message: "Failed to rotate API key. Please check your credentials.",
          });
        }

        console.log(
          `🔄 API Key Security: Admin ${currentUser.email} rotated API key`
        );

        res.json({
          success: true,
          newApiKey: result.newApiKey,
          newSecurityToken: result.newSecurityToken,
          message: "API key rotated successfully",
        });
      } catch (error) {
        console.error("Error rotating API key:", error);
        res.status(500).json({ message: "Failed to rotate API key" });
      }
    }
  );

  app.get(
    "/api/admin/api-keys/security-stats",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const stats = await getAdvancedApiKeyStats();
        res.json(stats);
      } catch (error) {
        console.error("Error fetching API key security stats:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch API key security statistics" });
      }
    }
  );

  app.get(
    "/api/admin/api-keys/rotation-needed",
    protectedRoute,
    addUserToRequest,
    requireOrgAdmin(),
    async (req: any, res) => {
      try {
        const { userId } = req.query;
        const currentUser = req.currentUser;

        // If specific userId requested, check permissions
        let targetUserId = userId as string;
        if (!targetUserId) {
          targetUserId = currentUser.id;
        } else if (currentUser.role !== "SuperUser") {
          const targetUser = await storage.getUser(targetUserId);
          if (
            !targetUser ||
            targetUser.organizationId !== currentUser.organizationId
          ) {
            return res.status(403).json({
              message: "Cannot view keys for users outside your organization",
            });
          }
        }

        const keysNeedingRotation = await getKeysNeedingRotation(targetUserId);
        res.json(keysNeedingRotation);
      } catch (error) {
        console.error("Error fetching keys needing rotation:", error);
        res.status(500).json({ message: "Failed to fetch rotation data" });
      }
    }
  );

  app.get(
    "/api/admin/backup/stats",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        if (user.role !== "SuperUser" && user.role !== "Admin") {
          return res.status(403).json({ message: "Insufficient permissions" });
        }

        const backupStats = await getBackupStats();
        res.json({
          backupStats,
          timestamp: new Date(),
        });
      } catch (error) {
        console.error("Backup stats error:", error);
        res.status(500).json({ message: "Failed to get backup statistics" });
      }
    }
  );

  app.post(
    "/api/admin/backup/create",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        if (user.role !== "SuperUser") {
          return res
            .status(403)
            .json({ message: "SuperUser permissions required" });
        }

        const { type = "database" } = req.body;

        let backup;
        if (type === "database") {
          backup = await createDatabaseBackup();
        } else if (type === "files") {
          backup = await createFileStorageBackup();
        } else {
          return res.status(400).json({ message: "Invalid backup type" });
        }

        res.json({
          backup,
          message: `${type} backup created successfully`,
        });
      } catch (error) {
        console.error("Backup creation error:", error);
        res.status(500).json({ message: "Failed to create backup" });
      }
    }
  );

  // Cache management endpoints
  app.post(
    "/api/admin/cache/clear",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        if (user.role !== "SuperUser" && user.role !== "Admin") {
          return res.status(403).json({ message: "Insufficient permissions" });
        }

        const { pattern } = req.body;

        if (pattern) {
          const count = cache.invalidatePattern(pattern);
          res.json({
            message: `Cleared ${count} cache entries matching pattern: ${pattern}`,
          });
        } else {
          cache.clear();
          res.json({ message: "All cache cleared successfully" });
        }
      } catch (error) {
        console.error("Cache clear error:", error);
        res.status(500).json({ message: "Failed to clear cache" });
      }
    }
  );

  // Waitlist endpoint (public - no authentication required)
  app.post("/api/waitlist", async (req, res) => {
    try {
      const { email, companyName, firstName, lastName, jobTitle } = req.body;

      if (!email || !email.includes("@")) {
        return res.status(400).json({ message: "Valid email is required" });
      }

      // Check if email already exists
      const exists = await storage.checkEmailInWaitlist(email);
      if (exists) {
        return res.status(409).json({ message: "Email already registered" });
      }

      const waitlistEntry = await storage.addToWaitlist({
        email: email.toLowerCase().trim(),
        companyName: companyName?.trim() || null,
        firstName: firstName?.trim() || null,
        lastName: lastName?.trim() || null,
        jobTitle: jobTitle?.trim() || null,
        source: "landing_page",
      });

      const count = await storage.getWaitlistCount();

      res.status(201).json({
        message: "Successfully added to waitlist",
        position: count,
        id: waitlistEntry.id,
      });
    } catch (error) {
      console.error("Error adding to waitlist:", error);
      res.status(500).json({ message: "Failed to add to waitlist" });
    }
  });

  // Get waitlist count (public endpoint)
  app.get("/api/waitlist/count", async (req, res) => {
    try {
      const count = await storage.getWaitlistCount();
      res.json({ count });
    } catch (error) {
      console.error("Error getting waitlist count:", error);
      res.status(500).json({ message: "Failed to get waitlist count" });
    }
  });

  // User Feedback Routes
  app.post(
    "/api/user-feedback",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const feedbackData = req.body;

        // Validate input
        const validatedData = insertUserFeedbackSchema.parse({
          ...feedbackData,
          userId: user.claims.sub,
        });

        // Create database record first
        const feedback = await storage.createUserFeedback(
          asInsert<InsertUserFeedback>(validatedData)
        );

        // Attempt to create GitHub issue
        try {
          // Get user details for GitHub issue
          const dbUser = await storage.getUser(user.claims.sub);
          const contractor = await storage.getContractorByUserId(user.claims.sub);
          
          const githubResult = await githubService.createIssue({
            type: feedbackData.type,
            message: feedbackData.message,
            priority: feedbackData.priority || 'medium',
            userId: user.claims.sub,
            userEmail: dbUser?.email,
            organizationName: contractor?.companyName,
            feedbackId: feedback.id,
          });

          // Update feedback record with GitHub issue info if successful
          if (githubResult) {
            await storage.updateUserFeedback(feedback.id, {
              ...validatedData,
              githubIssueUrl: githubResult.issueUrl,
              githubIssueNumber: githubResult.issueNumber,
            } as any);
            
            // Return updated feedback with GitHub info
            const updatedFeedback = await storage.getUserFeedbackById(feedback.id);
            res.status(201).json(updatedFeedback);
          } else {
            // Return original feedback if GitHub creation failed
            res.status(201).json(feedback);
          }
        } catch (githubError) {
          console.error("GitHub issue creation failed:", githubError);
          // Still return success since database record was created
          res.status(201).json(feedback);
        }
      } catch (error) {
        console.error("Error creating user feedback:", error);
        res.status(500).json({ message: "Failed to submit feedback" });
      }
    }
  );

  app.get(
    "/api/user-feedback",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;

        // Get user from database to check role
        const dbUser = await storage.getUser(user.claims.sub);
        if (!dbUser || dbUser.role !== "SuperUser") {
          return res.status(403).json({ message: "Access denied" });
        }

        const feedback = await storage.getUserFeedback();
        res.json(feedback);
      } catch (error) {
        console.error("Error fetching user feedback:", error);
        res.status(500).json({ message: "Failed to fetch feedback" });
      }
    }
  );

  app.get(
    "/api/user-feedback/stats",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;

        // Get user from database to check role
        const dbUser = await storage.getUser(user.claims.sub);
        if (!dbUser || dbUser.role !== "SuperUser") {
          return res.status(403).json({ message: "Access denied" });
        }

        const stats = await storage.getUserFeedbackStats();
        res.json(stats);
      } catch (error) {
        console.error("Error fetching feedback stats:", error);
        res.status(500).json({ message: "Failed to fetch feedback stats" });
      }
    }
  );

  app.patch(
    "/api/user-feedback/:id",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;

        // Get user from database to check role
        const dbUser = await storage.getUser(user.claims.sub);
        if (!dbUser || dbUser.role !== "SuperUser") {
          return res.status(403).json({ message: "Access denied" });
        }

        const { id } = req.params;
        const updates = req.body;

        // Add resolvedBy if marking as resolved
        if (updates.status === "resolved" && !updates.resolvedBy) {
          updates.resolvedBy = user.claims.sub;
          updates.resolvedAt = new Date();
        }

        const feedback = await storage.updateUserFeedback(id, updates);
        if (!feedback) {
          return res.status(404).json({ message: "Feedback not found" });
        }

        res.json(feedback);
      } catch (error) {
        console.error("Error updating user feedback:", error);
        res.status(500).json({ message: "Failed to update feedback" });
      }
    }
  );

  // **PHASE 1: NOTIFICATION SYSTEM API ENDPOINTS**

  // Import notification service
  const { notificationService } = await import(
    "./services/notificationService"
  );

  // Get user notifications
  app.get(
    "/api/notifications",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const limit = req.query.limit ? parseInt(String(req.query.limit)) : 50;
        const offset = req.query.offset ? parseInt(String(req.query.offset)) : 0;
        
        const currentUser = await storage.getUser(user.claims.sub);
        if (!currentUser) {
          return res.status(404).json({ message: "User not found" });
        }

        const notifications = await storage.getNotifications(
          user.claims.sub,
          currentUser.organizationId || undefined,
          { limit, offset }
        );
        res.json(notifications);
      } catch (error) {
        console.error("Error fetching notifications:", error);
        res.status(500).json({ message: "Failed to fetch notifications" });
      }
    }
  );

  // Get unread notifications count
  app.get(
    "/api/notifications/unread",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const currentUser = await storage.getUser(user.claims.sub);
        if (!currentUser) {
          return res.status(404).json({ message: "User not found" });
        }
        
        const count = await storage.getUnreadNotificationCount(
          user.claims.sub,
          currentUser.organizationId || undefined
        );
        const unreadNotifications =
          await notificationService.getUnreadNotifications(
            user.claims.sub,
            currentUser.organizationId || undefined
          );
          
        res.json({
          count,
          notifications: unreadNotifications,
        });
      } catch (error) {
        console.error("Error fetching unread notifications:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch unread notifications" });
      }
    }
  );

  // Mark notification as read
  app.patch(
    "/api/notifications/:id/read",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const { id } = req.params;

        const success = await notificationService.markAsRead(
          id,
          user.claims.sub
        );
        if (!success) {
          return res.status(404).json({ message: "Notification not found" });
        }

        res.json({ success: true });
      } catch (error) {
        console.error("Error marking notification as read:", error);
        res
          .status(500)
          .json({ message: "Failed to mark notification as read" });
      }
    }
  );

  // Mark all notifications as read
  app.post(
    "/api/notifications/mark-all-read",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const currentUser = await storage.getUser(user.claims.sub);
        if (!currentUser) {
          return res.status(404).json({ message: "User not found" });
        }

        const markedCount = await storage.markAllNotificationsRead(
          user.claims.sub,
          currentUser.organizationId || undefined
        );

        res.json({ 
          success: true, 
          markedCount 
        });
      } catch (error) {
        console.error("Error marking all notifications as read:", error);
        res
          .status(500)
          .json({ message: "Failed to mark all notifications as read" });
      }
    }
  );

  // Get user notification preferences
  app.get(
    "/api/notifications/preferences",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        console.log(
          "🔐 Notification preferences request - User:",
          user ? "exists" : "null"
        );
        console.log("🔐 User claims:", user?.claims);
        console.log("🔐 User authenticated via Clerk:", user ? "yes" : "no");

        if (!user || !user.claims || !user.claims.sub) {
          console.log(
            "🚨 User not properly authenticated for notification preferences"
          );
          return res.status(401).json({ message: "User not authenticated" });
        }

        let preferences = await storage.getUserNotificationPreferences(
          user.claims.sub
        );
        
        // If user has no preferences, seed default ones
        if (preferences.length === 0) {
          console.log("🌱 No preferences found, seeding defaults for user");
          await notificationService.seedDefaultPreferences(user.claims.sub);
          preferences = await storage.getUserNotificationPreferences(
            user.claims.sub
          );
        }
        
        console.log("✅ Retrieved preferences:", preferences);
        // Prevent caching to ensure fresh data
        res.set("Cache-Control", "no-cache, no-store, must-revalidate");
        res.set("Pragma", "no-cache");
        res.set("Expires", "0");
        res.json(preferences);
      } catch (error) {
        console.error("Error fetching notification preferences:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch notification preferences" });
      }
    }
  );

  // Update notification preferences
  app.put(
    "/api/notifications/preferences/:type",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const { type } = req.params;
        const preferences = req.body;

        console.log(
          "🔐 Update notification preferences - User:",
          user ? "exists" : "null"
        );
        console.log("🔐 Type:", type, "Preferences:", preferences);

        if (!user || !user.claims || !user.claims.sub) {
          console.log(
            "🚨 User not properly authenticated for updating notification preferences"
          );
          return res.status(401).json({ message: "User not authenticated" });
        }

        const updatedPreferences = await notificationService.updatePreferences(
          user.claims.sub,
          type,
          preferences
        );

        res.json(updatedPreferences);
      } catch (error) {
        console.error("Error updating notification preferences:", error);
        res
          .status(500)
          .json({ message: "Failed to update notification preferences" });
      }
    }
  );

  // **TEST ENDPOINT**: Create a test notification (Phase 1 & 2 testing)
  app.post(
    "/api/notifications/test",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const user = req.user;
        const {
          type = "system_test",
          title = "Test Notification",
          message = "This is a test notification",
          testEmail = false,
        } = req.body;

        console.log(
          `🧪 Creating test notification for user ${user.claims.sub}, email test: ${testEmail}`
        );

        if (testEmail) {
          // Phase 2: Test email notification
          const result = await notificationService.sendTestEmailNotification(
            user.claims.sub
          );
          res.status(201).json({
            message: "Test email notification sent successfully",
            success: result.success,
            email: result.email,
            notification: result.notification,
          });
        } else {
          // Phase 1: Test in-app notification
          const result = await notificationService.createNotification(
            user.claims.sub,
            type,
            title,
            message,
            { testData: true, timestamp: new Date() },
            "medium"
          );

          res.status(201).json({
            message: "Test notification created successfully",
            notification: result.notification,
            deliveries: result.deliveries.length,
          });
        }
      } catch (error) {
        console.error("Error creating test notification:", error);
        res.status(500).json({
          message: "Failed to create test notification",
          error: (error as Error).message,
        });
      }
    }
  );

  // Custom test email endpoint
  app.post(
    "/api/notifications/send-custom-email",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { message = "Hello from Bidaible!" } = req.body;
        const user = req.user;

        // Get user details
        const userRecord = await storage.getUser(user.claims.sub);
        if (!userRecord || !userRecord.email) {
          return res.status(400).json({ message: "User email not found" });
        }

        const userName = userRecord.firstName || "User";
        console.log(`📧 User record found:`, {
          id: userRecord.id,
          email: userRecord.email,
          firstName: userRecord.firstName,
          lastName: userRecord.lastName,
        });
        console.log(`📧 Sending custom test email to ${userRecord.email}`);

        // Log test email attempt to audit log
        await storage.createBusinessAuditLog({
          userId: user.claims.sub,
          eventType: "email_test_attempted",
          eventData: {
            emailAddress: userRecord.email,
            messageContent: message,
            testType: "custom_test_email",
            subject: "Hello from Bidaible!",
            deliveryMethod: "email",
          },
          resourceId: `test_email_${Date.now()}`,
          ipAddress: req.ip || req.connection?.remoteAddress,
          userAgent: req.get("User-Agent"),
        });

        // Send the custom email using EmailService directly
        const { emailService } = await import("./services/emailService");
        const result = await emailService.sendTestEmail(
          userRecord.email,
          userName,
          message
        );

        if (result.success) {
          // Log successful test email to audit log
          await storage.createBusinessAuditLog({
            userId: user.claims.sub,
            eventType: "email_test_sent",
            eventData: {
              emailAddress: userRecord.email,
              messageContent: message,
              testType: "custom_test_email",
              subject: "Hello from Bidaible!",
              messageId: result.messageId,
              deliveryMethod: "email",
              status: "delivered",
            },
            resourceId: result.messageId || `test_email_${Date.now()}`,
            ipAddress: req.ip || req.connection?.remoteAddress,
            userAgent: req.get("User-Agent"),
          });

          res.status(200).json({
            message: "Custom test email sent successfully!",
            success: true,
            email: userRecord.email,
            messageId: result.messageId,
          });
        } else {
          // Log failed test email to audit log
          await storage.createBusinessAuditLog({
            userId: user.claims.sub,
            eventType: "email_test_failed",
            eventData: {
              emailAddress: userRecord.email,
              messageContent: message,
              testType: "custom_test_email",
              subject: "Hello from Bidaible!",
              deliveryMethod: "email",
              errorMessage: result.error,
              status: "failed",
            },
            resourceId: `test_email_${Date.now()}`,
            ipAddress: req.ip || req.connection?.remoteAddress,
            userAgent: req.get("User-Agent"),
          });

          res.status(500).json({
            message: "Failed to send custom test email",
            success: false,
            error: result.error,
          });
        }
      } catch (error) {
        console.error("Error sending custom test email:", error);
        res.status(500).json({
          message: "Failed to send custom test email",
          error: (error as Error).message,
        });
      }
    }
  );

  // Enhanced Analytics Routes
  app.use("/api/analytics", analyticsRoutes);

  // Enhanced Bid Management Routes for RFQ Owners - With AI Analysis
  app.get(
    "/api/rfqs/:rfqId/bids/analysis",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const { refresh } = req.query; // Optional refresh parameter
        const user = req.user;

        // Verify user owns this RFQ
        const rfq = await storage.getRfq(rfqId);
        if (!rfq || rfq.createdBy !== user.claims.sub) {
          return res.status(403).json({ message: "Access denied" });
        }

        const { getRfqBidSummary } = await import(
          "./services/bidAnalysisService"
        );
        const { generateBidAnalysis } = await import("./services/aiService");

        // Get bid summary with stored AI data included
        const summary = await getRfqBidSummary(rfqId, true); // includeAIData = true

        // Check if we have bids to analyze
        if (summary.bids && summary.bids.length > 0) {
          // Check if we have stored AI analysis and no refresh is requested
          const hasStoredAnalysis = summary.bids.some(
            (bc) => bc.bid.aiAnalysis || bc.bid.aiSummary
          );

          if (!refresh && hasStoredAnalysis) {
            // Return stored analysis immediately
            console.log(
              "Returning stored AI analysis for",
              summary.bids.length,
              "bids"
            );
            return res.json(summary);
          }

          // Only generate fresh analysis if no stored analysis exists or refresh requested
          try {
            console.log(
              "Generating fresh AI analysis for",
              summary.bids.length,
              "bids (refresh requested:",
              !!refresh,
              ")"
            );
            const aiAnalysis = await generateBidAnalysis(
              summary.rfq,
              summary.bids
            );

            // Enhance the response with fresh AI analysis
            res.json({
              ...summary,
              aiAnalysis,
            });
          } catch (aiError) {
            console.warn(
              "AI analysis failed, returning stored/basic summary:",
              aiError
            );
            // Return stored data or basic summary if AI analysis fails
            res.json(summary);
          }
        } else {
          console.log("No bids found for AI analysis");
          // No bids to analyze
          res.json(summary);
        }
      } catch (error) {
        console.error("Error fetching RFQ bid analysis:", error);
        res.status(500).json({ message: "Failed to fetch bid analysis" });
      }
    }
  );

  app.post(
    "/api/bids/:bidId/action",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { bidId } = req.params;
        const { action, notes, message } = req.body;
        const user = req.user;

        // Verify ownership
        const bid = await storage.getBid(bidId);
        if (!bid) {
          return res.status(404).json({ message: "Bid not found" });
        }

        const rfq = await storage.getRfq(bid.rfqId!);
        if (!rfq || rfq.createdBy !== user.claims.sub) {
          return res.status(403).json({ message: "Access denied" });
        }

        let updateData: any = {};

        switch (action) {
          case "accept":
            updateData = { status: "Accepted" };
            break;
          case "reject":
            updateData = { status: "Rejected" };
            break;
          case "request_info":
            updateData = {
              status: "Request Info",
              requestInfoMessage: message,
            };
            break;
          default:
            return res.status(400).json({ message: "Invalid action" });
        }

        const updatedBid = await storage.updateBidStatus(
          bidId,
          updateData.status,
          user.claims.sub,
          notes
        );

        if (updateData.requestInfoMessage) {
          await storage.updateBid(bidId, { requestInfoMessage: message });
        }

        // 📝 AUDIT LOG: Bid Action (Accept/Reject)
        await AuditService.logBidAction({
          userId: req.user.claims.sub,
          bidId: bidId,
          contractorId: bid.contractorId || "unknown",
          contractorName: "Unknown Contractor",
          bidAmount:
            typeof bid.bidAmount === "string"
              ? parseFloat(bid.bidAmount)
              : bid.bidAmount || null,
          action: updateData.status,
          previousStatus: bid.status || "unknown",
          newStatus: updateData.status,
          req,
        });

        // Trigger bid analysis update
        const { updateRfqBidAnalysis } = await import(
          "./services/bidAnalysisService"
        );
        await updateRfqBidAnalysis(bid.rfqId!);

        res.json(updatedBid);
      } catch (error) {
        console.error("Error updating bid status:", error);
        res.status(500).json({ message: "Failed to update bid status" });
      }
    }
  );

  // Buffer Management Route
  app.put(
    "/api/rfqs/:rfqId/buffer",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const { bufferPercentage, bufferNotes } = req.body;
        const user = req.user;

        // Verify ownership
        const rfq = await storage.getRfq(rfqId);
        if (!rfq || rfq.createdBy !== user.claims.sub) {
          return res.status(403).json({ message: "Access denied" });
        }

        const updatedRfq = await storage.updateRfq(rfqId, {
          bufferPercentage: bufferPercentage.toString(),
          bufferNotes,
        } as any);

        res.json(updatedRfq);
      } catch (error) {
        console.error("Error updating buffer settings:", error);
        res.status(500).json({ message: "Failed to update buffer settings" });
      }
    }
  );

  app.post(
    "/api/bids/:bidId/analyze",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { bidId } = req.params;
        const user = req.user;

        // Verify ownership
        const bid = await storage.getBid(bidId);
        if (!bid) {
          return res.status(404).json({ message: "Bid not found" });
        }

        const rfq = await storage.getRfq(bid.rfqId!);
        if (!rfq || rfq.createdBy !== user.claims.sub) {
          return res.status(403).json({ message: "Access denied" });
        }

        const { generateBidAnalysis } = await import(
          "./services/bidAnalysisService"
        );
        const analysis = await generateBidAnalysis(bidId, true);
        res.json(analysis);
      } catch (error) {
        console.error("Error generating bid analysis:", error);
        res.status(500).json({ message: "Failed to generate bid analysis" });
      }
    }
  );

  // Register processing stats routes
  registerProcessingStatsRoutes(app);

  // Get processing stats
  app.get(
    "/api/processing/stats",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const aiStats = getProcessingStats();
        const fileStats = getFileProcessingStats();

        res.json({
          ai: aiStats,
          file: fileStats,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error("Error getting processing stats:", error);
        res.status(500).json({ message: "Failed to get processing stats" });
      }
    }
  );

  // Generate master summary for RFQ
  app.post(
    "/api/rfqs/:rfqId/generate-master-summary",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const user = req.user;

        // Verify ownership
        const rfq = await storage.getRfq(rfqId);
        if (!rfq || rfq.createdBy !== user.claims.sub) {
          return res.status(403).json({ message: "Access denied" });
        }

        // Generate master summary
        const { generateMasterSummary } = await import(
          "./services/masterSummaryService"
        );
        const result = await generateMasterSummary(rfqId);

        res.json(result);
      } catch (error) {
        console.error("Error generating master summary:", error);
        res.status(500).json({ message: "Failed to generate master summary" });
      }
    }
  );

  // Get bid comparison report
  app.get(
    "/api/rfqs/:rfqId/bid-comparison",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const user = req.user;

        // Verify ownership
        const rfq = await storage.getRfq(rfqId);
        if (!rfq || rfq.createdBy !== user.claims.sub) {
          return res.status(403).json({ message: "Access denied" });
        }

        // Generate comparison report
        const { generateBidComparisonReport } = await import(
          "./services/enhancedBidComparison"
        );
        const report = await generateBidComparisonReport(rfqId);

        res.json(report);
      } catch (error) {
        console.error("Error generating bid comparison report:", error);
        res
          .status(500)
          .json({ message: "Failed to generate bid comparison report" });
      }
    }
  );

  // Generate PDF export of bid comparison
  app.get(
    "/api/rfqs/:rfqId/bid-comparison/export",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { rfqId } = req.params;
        const { format = "json" } = req.query;
        const user = req.user;

        // Verify ownership
        const rfq = await storage.getRfq(rfqId);
        if (!rfq || rfq.createdBy !== user.claims.sub) {
          return res.status(403).json({ message: "Access denied" });
        }

        const { generateBidComparisonReport } = await import(
          "./services/enhancedBidComparison"
        );
        const report = await generateBidComparisonReport(rfqId);

        if (format === "csv") {
          // Generate CSV format
          const csvHeaders = [
            "Contractor",
            "Bid Amount",
            "Timeline",
            "Warranty",
            "Risk Factors",
          ];
          const csvRows = report.comparisons.map((comp) => [
            comp.contractorName,
            comp.bidAmount.toString(),
            comp.timeline,
            comp.warranty,
            comp.riskFactors.join("; "),
          ]);

          const csvContent = [csvHeaders, ...csvRows]
            .map((row) => row.map((cell) => `"${cell}"`).join(","))
            .join("\n");

          res.setHeader("Content-Type", "text/csv");
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="bid-comparison-${rfqId}.csv"`
          );
          res.send(csvContent);
        } else {
          res.json(report);
        }
      } catch (error) {
        console.error("Error exporting bid comparison:", error);
        res.status(500).json({ message: "Failed to export bid comparison" });
      }
    }
  );

  // Email reporting endpoint - Phase 3 Item 5: Export and Reporting Capabilities
  app.post(
    "/api/reports/email",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        const { reportType, bidIds, filters } = req.body;

        // For now, simulate email report scheduling
        // In production, this would integrate with an email service like SendGrid or Nodemailer
        const reportData = {
          reportId: `report_${Date.now()}`,
          reportType,
          userId,
          bidIds,
          filters,
          scheduledAt: new Date().toISOString(),
          status: "scheduled",
          deliveryEmail: req.user.email || "<EMAIL>",
        };

        // Log the email report request
        console.log(`📧 Email report scheduled:`, {
          reportType,
          bidCount: bidIds?.length || 0,
          userId: userId.slice(0, 8),
          timestamp: reportData.scheduledAt,
        });

        res.json({
          success: true,
          reportId: reportData.reportId,
          message: "Email report scheduled for delivery",
          estimatedDelivery: "5-10 minutes",
          deliveryEmail: reportData.deliveryEmail,
        });
      } catch (error) {
        console.error("Error scheduling email report:", error);
        res.status(500).json({ message: "Failed to schedule email report" });
      }
    }
  );

  // Contractor Performance Analytics
  app.get(
    "/api/contractors/performance-stats",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;

        // Get contractor profile to get contractor ID
        const contractor = await storage.getContractorByUserId(userId);
        if (!contractor) {
          return res
            .status(404)
            .json({ message: "Contractor profile not found" });
        }

        // Get all bids for this contractor
        const bidResults = await storage.getBidsByContractor(contractor.id);
        const bids = bidResults.map((result) => result.bid);

        // Calculate statistics
        const totalBidsSubmitted = bids.length;
        const wonBids = bids.filter(
          (bid) => bid.status === "accept" || bid.status === "accepted"
        );
        const totalBidsWon = wonBids.length;
        const winRate =
          totalBidsSubmitted > 0
            ? (totalBidsWon / totalBidsSubmitted) * 100
            : 0;

        // Calculate financial metrics
        const totalEarnings = wonBids.reduce((sum, bid) => {
          const amount = bid.extractedAmount || bid.bidAmount || 0;
          return (
            sum +
            (typeof amount === "string" ? parseFloat(amount) || 0 : amount)
          );
        }, 0);

        // Calculate average bid amount from ALL submitted bids (not just won)
        const allBidAmounts = bids
          .map((bid) => {
            const amount = bid.extractedAmount || bid.bidAmount || 0;
            return typeof amount === "string"
              ? parseFloat(amount) || 0
              : amount;
          })
          .filter((amount) => amount > 0);

        const averageBidAmount =
          allBidAmounts.length > 0
            ? allBidAmounts.reduce((sum, amount) => sum + amount, 0) /
              allBidAmounts.length
            : 0;

        // Generate monthly activity data for the last 6 months
        const monthlyActivity = [];
        const now = new Date();

        for (let i = 5; i >= 0; i--) {
          const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
          const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
          const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

          const monthBids = bids.filter((bid) => {
            const bidDate = bid.createdAt
              ? new Date(bid.createdAt)
              : new Date();
            return bidDate >= monthStart && bidDate <= monthEnd;
          });

          const monthWonBids = monthBids.filter(
            (bid) => bid.status === "accept" || bid.status === "accepted"
          );
          const monthEarnings = monthWonBids.reduce((sum, bid) => {
            const amount = bid.extractedAmount || bid.bidAmount || 0;
            return (
              sum +
              (typeof amount === "string" ? parseFloat(amount) || 0 : amount)
            );
          }, 0);

          monthlyActivity.push({
            month: date.toLocaleDateString("en-US", { month: "short" }),
            bidsSubmitted: monthBids.length,
            bidsWon: monthWonBids.length,
            earnings: monthEarnings,
          });
        }

        // Calculate bid status distribution
        const statusCounts = bids.reduce(
          (acc, bid) => {
            const status = bid.status || "pending";
            acc[status] = (acc[status] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        );

        const statusColors = {
          accept: "#10b981",
          accepted: "#10b981",
          reject: "#ef4444",
          rejected: "#ef4444",
          pending: "#f59e0b",
          submitted: "#3b82f6",
        };

        const bidsByStatus = Object.entries(statusCounts).map(
          ([status, count]) => ({
            status: status.charAt(0).toUpperCase() + status.slice(1),
            count,
            color:
              (statusColors as Record<string, string>)[status] || "#6b7280",
          })
        );

        // Calculate recent performance trends
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const previousMonth = new Date(
          now.getFullYear(),
          now.getMonth() - 2,
          1
        );

        const lastMonthBids = bids.filter((bid) => {
          const bidDate = bid.createdAt ? new Date(bid.createdAt) : new Date();
          return bidDate >= lastMonth;
        });

        const previousMonthBids = bids.filter((bid) => {
          const bidDate = bid.createdAt ? new Date(bid.createdAt) : new Date();
          return bidDate >= previousMonth && bidDate < lastMonth;
        });

        const lastMonthWins = lastMonthBids.filter(
          (bid) => bid.status === "accept" || bid.status === "accepted"
        ).length;

        let trend: "up" | "down" | "stable" = "stable";
        let trendPercentage = 0;

        if (previousMonthBids.length > 0) {
          const change = lastMonthBids.length - previousMonthBids.length;
          trendPercentage = (change / previousMonthBids.length) * 100;

          if (Math.abs(trendPercentage) > 5) {
            trend = trendPercentage > 0 ? "up" : "down";
          }
        }

        const stats = {
          totalBidsSubmitted,
          totalBidsWon,
          winRate,
          averageBidAmount,
          totalEarnings,
          monthlyActivity,
          bidsByStatus,
          recentPerformance: {
            lastMonthBids: lastMonthBids.length,
            lastMonthWins,
            trend,
            trendPercentage: Math.abs(trendPercentage),
          },
          // Add latest bid info for Recent Bid Activity
          latestBid:
            bids.length > 0
              ? {
                  amount: bids[0].extractedAmount || bids[0].bidAmount || 0,
                  status: bids[0].status,
                  submittedAt: bids[0].submittedAt,
                }
              : null,
        };

        console.log("📊 Performance stats debug:", {
          totalBids: bids.length,
          wonBids: wonBids.length,
          allBidAmounts,
          averageBidAmount,
          latestBidAmount:
            bids.length > 0
              ? bids[0].extractedAmount || bids[0].bidAmount
              : null,
        });

        res.json(stats);
      } catch (error) {
        console.error("Performance stats error:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch performance statistics" });
      }
    }
  );

  // Add unified PDF extractor test route
  addUnifiedExtractorTestRoute(app);

  // Organization Creation Endpoint - Syncs Clerk org creation with Bidaible database
  app.post(
    "/api/organizations",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const { name, slug } = req.body;
        const userId = req.user.claims.sub;

        if (!name || !slug) {
          return res
            .status(400)
            .json({ message: "Organization name and slug are required" });
        }

        // Use the sync service to create organization in both Clerk and Bidaible
        const { default: OrganizationSyncService } = await import('./services/organizationSyncService');
        const { clerkOrg, bidaibleOrg } = await OrganizationSyncService.createOrganization(
          userId,
          name,
          slug
        );

        res.status(201).json({
          success: true,
          organization: {
            id: bidaibleOrg.id,
            name: bidaibleOrg.name,
            slug: bidaibleOrg.slug,
          },
          clerkOrganization: {
            id: clerkOrg.id,
            name: clerkOrg.name,
            slug: clerkOrg.slug,
          }
        });
      } catch (error) {
        console.error("Error creating organization:", error);
        res.status(500).json({ message: "Failed to create organization" });
      }
    }
  );

  // Clerk Organization Management Routes
  app.get(
    "/api/organizations/:orgId/users",
    protectedRoute,
    async (req: any, res) => {
      try {
        const { userId } = getAuth(req);
        const { orgId } = req.params;

        // Verify user is admin of the organization
        const userMembership =
          await clerkClient.organizations.getOrganizationMembershipList({
            organizationId: orgId,
            userId: [userId],
          });

        const isAdmin = userMembership.data.some(
          (membership) =>
            membership.role === "org:admin" &&
            membership.organization.id === orgId
        );

        if (!isAdmin) {
          return res
            .status(403)
            .json({ message: "Access denied. Admin role required." });
        }

        // Fetch all organization members
        const members =
          await clerkClient.organizations.getOrganizationMembershipList({
            organizationId: orgId,
          });

        const formattedMembers = members.data.map((membership) => ({
          id: membership.publicUserData?.userId,
          email: membership.publicUserData?.identifier,
          firstName: membership.publicUserData?.firstName,
          lastName: membership.publicUserData?.lastName,
          profileImageUrl: membership.publicUserData?.imageUrl,
          role: membership.role,
          createdAt: membership.createdAt,
          updatedAt: membership.updatedAt,
        }));

        res.json(formattedMembers);
      } catch (error) {
        console.error("Error fetching organization users:", error);
        res.status(500).json({ message: "Failed to fetch organization users" });
      }
    }
  );

  // Invite user to organization
  app.post(
    "/api/organizations/:orgId/invitations",
    protectedRoute,
    async (req: any, res) => {
      try {
        const { userId } = getAuth(req);
        const { orgId } = req.params;
        const { email, role = "org:member" } = req.body;

        // Verify user is admin of the organization
        const userMembership =
          await clerkClient.organizations.getOrganizationMembershipList({
            organizationId: orgId,
            userId: [userId],
          });

        const isAdmin = userMembership.data.some(
          (membership) =>
            membership.role === "org:admin" &&
            membership.organization.id === orgId
        );

        if (!isAdmin) {
          return res
            .status(403)
            .json({ message: "Access denied. Admin role required." });
        }

        // Send invitation
        const invitation =
          await clerkClient.organizations.createOrganizationInvitation({
            organizationId: orgId,
            emailAddress: email,
            role,
          } as any);

        res.json(invitation);
      } catch (error) {
        console.error("Error inviting user:", error);
        res.status(500).json({ message: "Failed to invite user" });
      }
    }
  );

  // Update user role in organization
  app.patch(
    "/api/organizations/:orgId/users/:targetUserId/role",
    protectedRoute,
    async (req: any, res) => {
      try {
        const { userId } = getAuth(req);
        const { orgId, targetUserId } = req.params;
        const { role } = req.body;

        // Verify user is admin of the organization
        const userMembership =
          await clerkClient.organizations.getOrganizationMembershipList({
            organizationId: orgId,
            userId: [userId],
          });

        const isAdmin = userMembership.data.some(
          (membership) =>
            membership.role === "org:admin" &&
            membership.organization.id === orgId
        );

        if (!isAdmin) {
          return res
            .status(403)
            .json({ message: "Access denied. Admin role required." });
        }

        // Update user role
        const updatedMembership =
          await clerkClient.organizations.updateOrganizationMembership({
            organizationId: orgId,
            userId: targetUserId,
            role,
          });

        res.json(updatedMembership);
      } catch (error) {
        console.error("Error updating user role:", error);
        res.status(500).json({ message: "Failed to update user role" });
      }
    }
  );

  // Remove user from organization
  app.delete(
    "/api/organizations/:orgId/users/:targetUserId",
    protectedRoute,
    async (req: any, res) => {
      try {
        const { userId } = getAuth(req);
        const { orgId, targetUserId } = req.params;

        // Verify user is admin of the organization
        const userMembership =
          await clerkClient.organizations.getOrganizationMembershipList({
            organizationId: orgId,
            userId: [userId],
          });

        const isAdmin = userMembership.data.some(
          (membership) =>
            membership.role === "org:admin" &&
            membership.organization.id === orgId
        );

        if (!isAdmin) {
          return res
            .status(403)
            .json({ message: "Access denied. Admin role required." });
        }

        // Remove user from organization
        await clerkClient.organizations.deleteOrganizationMembership({
          organizationId: orgId,
          userId: targetUserId,
        });

        res.json({ message: "User removed from organization successfully" });
      } catch (error) {
        console.error("Error removing user:", error);
        res
          .status(500)
          .json({ message: "Failed to remove user from organization" });
      }
    }
  );

  // Get pending invitations
  app.get(
    "/api/organizations/:orgId/invitations",
    protectedRoute,
    async (req: any, res) => {
      try {
        const { userId } = getAuth(req);
        const { orgId } = req.params;

        // Verify user is admin of the organization
        const userMembership =
          await clerkClient.organizations.getOrganizationMembershipList({
            organizationId: orgId,
            userId: [userId],
          });

        const isAdmin = userMembership.data.some(
          (membership) =>
            membership.role === "org:admin" &&
            membership.organization.id === orgId
        );

        if (!isAdmin) {
          return res
            .status(403)
            .json({ message: "Access denied. Admin role required." });
        }

        // Fetch pending invitations
        const invitations =
          await clerkClient.organizations.getOrganizationInvitationList({
            organizationId: orgId,
          });

        res.json(invitations.data);
      } catch (error) {
        console.error("Error fetching invitations:", error);
        res.status(500).json({ message: "Failed to fetch invitations" });
      }
    }
  );

  // Add compatibility endpoint for old authentication route
  app.get("/api/auth/user", protectedRoute, async (req: any, res) => {
    try {
      const { userId } = getAuth(req);
      if (!userId) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      // Get user from Clerk
      const clerkUser = await clerkClient.users.getUser(userId);

      // Get or create user in our database - but preserve existing role
      let dbUser = await storage.getUser(userId);

      if (!dbUser) {
        // User doesn't exist, create new one
        dbUser = await storage.upsertUser(
          asInsert<InsertUser>({
            id: userId,
            email: clerkUser.emailAddresses[0]?.emailAddress,
            firstName: clerkUser.firstName,
            lastName: clerkUser.lastName,
            profileImageUrl: clerkUser.imageUrl,
          })
        );
      } else {
        // User exists, only update Clerk-managed fields (preserve role, organizationId, etc.)
        dbUser = await storage.upsertUser(
          asInsert<InsertUser>({
            id: userId,
            email: clerkUser.emailAddresses[0]?.emailAddress,
            firstName: clerkUser.firstName,
            lastName: clerkUser.lastName,
            profileImageUrl: clerkUser.imageUrl,
            // Preserve existing database fields
            role: dbUser.role,
            organizationId: dbUser.organizationId,
          })
        );
      }

      console.log("🔍 Returning user with role:", dbUser.role);
      res.json(dbUser);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Update user (classification, profile fields)
  app.patch(
    "/api/auth/user",
    protectedRoute,
    addUserToRequest,
    async (req: any, res) => {
      try {
        const userId = req.user?.claims?.sub;
        if (!userId) {
          return res.status(401).json({ message: "Unauthorized" });
        }
        const existing = await storage.getUser(userId);
        if (!existing) {
          return res.status(404).json({ message: "User not found" });
        }
        const { userClassification, firstName, lastName, profileImageUrl } =
          req.body || {};
        const updated = await storage.upsertUser({
          id: userId,
          email: existing.email,
          firstName: firstName ?? existing.firstName,
          lastName: lastName ?? existing.lastName,
          profileImageUrl: profileImageUrl ?? existing.profileImageUrl,
          organizationId: existing.organizationId,
          userClassification: userClassification ?? existing.userClassification,
        } as any);
        res.json(updated);
      } catch (error) {
        console.error("Error updating user:", error);
        res.status(500).json({ message: "Failed to update user" });
      }
    }
  );

  // Add Clerk logout endpoint
  app.get("/api/logout", (req, res) => {
    // For Clerk, logout is handled on the frontend
    // Just redirect to home page
    res.redirect("/");
  });

  const httpServer = createServer(app);
  return httpServer;
}
