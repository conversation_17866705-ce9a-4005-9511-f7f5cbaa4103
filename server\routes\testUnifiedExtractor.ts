/**
 * Test endpoint for unified PDF extractor
 * Add this route to test the new extractor with existing PDF uploads
 */

import type { Express } from "express";
import { unifiedPDFExtractor } from "../services/core/pdfExtractor";
import { promises as fs } from 'fs';

export function addUnifiedExtractorTestRoute(app: Express) {
  // Test endpoint for unified PDF extractor
  app.get('/api/test/unified-pdf-extractor', async (req, res) => {
    try {
      console.log('🧪 Testing Unified PDF Extractor via API...');
      
      // Look for any recent PDF upload in uploads directory
      const uploadsDir = './uploads';
      const files = await fs.readdir(uploadsDir);
      
      // Check files by content (not extension) since uploads use hash names
      let testFile = null;
      for (const file of files) {
        const filePath = `${uploadsDir}/${file}`;
        try {
          const buffer = await fs.readFile(filePath);
          // Check if file starts with PDF magic number
          if (buffer.length > 4 && buffer.slice(0, 4).toString() === '%PDF') {
            testFile = file;
            console.log(`📄 Found PDF file: ${file}`);
            break;
          }
        } catch (err) {
          continue;
        }
      }
      
      if (!testFile) {
        return res.json({
          success: false,
          message: 'No PDF files found for testing. Please upload a PDF first.',
          availableFiles: files.slice(0, 10), // Show first 10 for debugging
          note: 'Files are checked by content, not extension'
        });
      }
      const filePath = `${uploadsDir}/${testFile}`;
      const fileBuffer = await fs.readFile(filePath);
      
      console.log(`📄 Testing with: ${testFile} (${fileBuffer.length} bytes)`);
      
      // Test unified extractor
      const startTime = Date.now();
      const result = await unifiedPDFExtractor.extractText(fileBuffer, {
        onProgress: (progress) => {
          console.log(`📈 ${progress.stage}: ${progress.percentage}% - ${progress.message}`);
        }
      });
      
      const testTime = Date.now() - startTime;
      
      // Analysis
      const hasRealContent = result.text.length > 50 && 
                            !result.text.match(/^(\s*---\s*Page\s*\d+\s*---\s*)+$/);
      
      const response = {
        success: result.success && hasRealContent,
        testFile,
        fileSize: fileBuffer.length,
        extraction: {
          method: result.extractionMethod,
          textLength: result.text.length,
          pageCount: result.pageCount,
          confidence: result.confidence,
          processingTime: testTime,
          hasRealContent
        },
        textPreview: result.text.substring(0, 500),
        analysis: {
          passedTest: hasRealContent,
          issue: hasRealContent ? null : 'Only page separators extracted (29-char issue reproduced)'
        }
      };
      
      console.log(`✅ Test Result: ${response.success ? 'PASSED' : 'FAILED'}`);
      console.log(`📊 Extracted ${result.text.length} characters using ${result.extractionMethod}`);
      
      res.json(response);
      
    } catch (error: any) {
      console.error('❌ Unified PDF Extractor Test Error:', error);
      res.status(500).json({
        success: false,
        error: error.message,
        stack: error.stack
      });
    }
  });
}