# Bidaible Unified File Processing Test Suite

Comprehensive testing framework for the unified file upload and processing system with Wasabi storage integration.

## 🎯 Overview

This test suite validates the complete file processing pipeline including:
- **Wasabi S3-compatible storage integration**
- **Unified PDF text extraction with fallback strategies**
- **AI processing with multiple provider support**
- **Multi-file batch processing with priorities**
- **Real-time progress tracking via SSE**
- **Error handling and resilience**
- **Performance benchmarking**

## 📋 Test Categories

### 1. Wasabi Storage Integration Tests
- Basic file upload to Wasabi
- Large file chunked upload (>10MB)
- Multi-file batch upload
- Progress tracking during upload
- File validation and security
- Storage operations (list, delete, presigned URLs)

### 2. AI Processing & PDF Extraction Tests
- PDF.js primary extraction method
- AI processing with Groq integration
- Multi-file AI processing with priorities
- AI processing fallback mechanisms
- Large file performance testing
- PDF extraction confidence scoring

## 🚀 Quick Start

### Prerequisites

1. **Server Running**: Ensure Bidaible server is running on `localhost:5000`
2. **Environment Variables**: Configure required environment variables
3. **Dependencies**: Install test dependencies

### Environment Setup

Create a `.env` file in the project root with:

```bash
# Wasabi Storage (Required)
WASABI_ACCESS_KEY_ID=your_wasabi_access_key_id
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_access_key
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com
WASABI_REGION=us-east-1

# AI Services (At least one required)
GROQ_API_KEY=gsk_your_groq_api_key
OPENAI_API_KEY=sk-your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# Database
DATABASE_URL=postgresql://username:password@host:port/database

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
```

### Installation

```bash
# Navigate to tests directory
cd tests

# Install dependencies
npm install

# Or use the convenience script
npm run install-deps
```

### Running Tests

```bash
# Run all test suites
npm test

# Run individual test suites
npm run test:wasabi    # Wasabi storage tests only
npm run test:ai        # AI processing tests only

# Watch mode (re-run on changes)
npm run test:watch

# Clean up test artifacts
npm run clean

# Open HTML report (after running tests)
npm run report
```

## 📊 Test Reports

The test suite generates comprehensive reports:

### HTML Report (`results/test-report.html`)
- Visual dashboard with success rates
- Detailed test results by suite
- Performance metrics and charts
- Error details and recommendations

### JSON Summary (`results/test-summary.json`)
- Machine-readable test results
- Performance benchmarks
- Environment information
- Recommendations for improvements

### Raw Data (`results/master-results.json`)
- Complete test execution data
- Individual test timings
- Performance metrics
- Error logs and stack traces

## 🔧 Test Configuration

### Authentication

The tests require authentication tokens. Update the test files to use your authentication method:

```javascript
// In test files, replace:
'Authorization': 'Bearer test-token'

// With your actual auth token or implement token generation
```

### Test Data

Tests automatically generate realistic construction RFQ documents:
- PDF files with construction-specific content
- Text files with specifications
- CSV files with cost codes
- Various file sizes for performance testing

### Timeouts

Default timeouts are configured for different operations:
- Basic uploads: 30 seconds
- Large files: 60 seconds
- AI processing: 90 seconds
- Performance tests: 120 seconds

## 📈 Performance Benchmarks

### Target Performance Metrics

| Test Category | Target | Measurement |
|---------------|--------|-------------|
| Basic Upload | <5s | Files up to 10MB |
| Large Upload | <30s | Files up to 50MB |
| PDF Extraction | <5s | Standard documents |
| AI Processing | <15s | Per document |
| Multi-file Batch | <60s | 5 files total |

### Success Criteria

- **Functional**: 100% file upload success rate
- **Extraction**: >95% PDF text extraction success
- **Performance**: Processing within target timeouts
- **Reliability**: Graceful error handling
- **Security**: File validation prevents malicious uploads

## 🛠️ Troubleshooting

### Common Issues

#### Server Not Running
```
❌ Server not accessible: connect ECONNREFUSED 127.0.0.1:5000
```
**Solution**: Start the Bidaible server with `npm run dev`

#### Missing Environment Variables
```
❌ Wasabi storage configured: false
```
**Solution**: Check `.env` file has all required Wasabi credentials

#### Authentication Errors
```
❌ Upload failed: 401
```
**Solution**: Update test files with valid authentication tokens

#### AI Processing Failures
```
❌ AI processing failed - no summary or extracted data
```
**Solution**: Verify AI service API keys are valid and have sufficient credits

### Debug Mode

Enable verbose logging by setting:
```bash
DEBUG=bidaible:tests npm test
```

### Manual Testing

For manual verification, use the individual test endpoints:

```bash
# Test file upload
curl -X POST http://localhost:5000/api/rfqs \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "documents=@test-file.pdf" \
  -F "projectName=Test Project"

# Test progress tracking
curl http://localhost:5000/api/upload/start-session \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔍 Test Development

### Adding New Tests

1. **Create test function**:
```javascript
async function testNewFeature() {
  const testName = 'New Feature Test';
  const startTime = Date.now();
  
  try {
    // Test implementation
    const result = await performTest();
    
    if (result.success) {
      recordTest(testName, true, Date.now() - startTime, 'Test passed');
    } else {
      recordTest(testName, false, Date.now() - startTime, 'Test failed');
    }
  } catch (error) {
    recordTest(testName, false, Date.now() - startTime, `Error: ${error.message}`);
  }
}
```

2. **Add to test runner**:
```javascript
// In runTests() function
await testNewFeature();
```

3. **Update documentation**:
- Add test description to README
- Update test plan documentation
- Include in performance benchmarks if applicable

### Test Data Generation

Create realistic test data:

```javascript
// Generate construction RFQ PDF
const testFile = createConstructionRFQ('test-file.pdf', 10); // 10 pages

// Generate specifications text
const specsContent = `
TECHNICAL SPECIFICATIONS
========================
1. CONCRETE WORK (03.01)
   - Ready-mix concrete, 4000 PSI minimum
   - Reinforcing steel, Grade 60
`;
```

### Performance Testing

Add performance metrics:

```javascript
const performance = {
  fileSize: fs.statSync(testFile).size,
  processingTime: duration,
  extractedTextLength: extractedText.length,
  meetsTarget: duration < 30000
};

recordTest(testName, passed, duration, notes, performance);
```

## 📚 Related Documentation

- [Unified File Processing Analysis](../docs/unified-file-processing-analysis.md)
- [Wasabi Storage Integration](../docs/REFACTORING_CHECKPOINT_AUGUST_2025.md)
- [API Documentation](../docs/API_CONSOLIDATED.md)
- [Deployment Guide](../docs/DEPLOYMENT.md)

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/new-test`
3. **Add tests**: Follow existing patterns and conventions
4. **Run test suite**: Ensure all tests pass
5. **Update documentation**: Include test descriptions and benchmarks
6. **Submit pull request**: Include test results and performance impact

## 📞 Support

For issues with the test suite:

1. **Check logs**: Review test output and error messages
2. **Verify environment**: Ensure all prerequisites are met
3. **Run individual tests**: Isolate failing components
4. **Check documentation**: Review setup and configuration guides
5. **Report issues**: Include test results and environment details

---

**Test Suite Version**: 1.0.0  
**Last Updated**: August 14, 2025  
**Compatibility**: Node.js 16+, Bidaible v2.0+
