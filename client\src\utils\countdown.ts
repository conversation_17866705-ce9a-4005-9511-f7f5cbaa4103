import { differenceInMilliseconds, format } from "date-fns";

export interface CountdownData {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  isExpired: boolean;
  totalMs: number;
}

export function calculateCountdown(targetDate: string | Date): CountdownData {
  const now = new Date();
  const target = new Date(targetDate);
  const totalMs = differenceInMilliseconds(target, now);
  
  if (totalMs <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      isExpired: true,
      totalMs: 0,
    };
  }
  
  const days = Math.floor(totalMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((totalMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((totalMs % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((totalMs % (1000 * 60)) / 1000);
  
  return {
    days,
    hours,
    minutes,
    seconds,
    isExpired: false,
    totalMs,
  };
}

export function formatCountdown(countdown: CountdownData, format: 'short' | 'long' = 'short'): string {
  if (countdown.isExpired) {
    return 'Expired';
  }
  
  if (format === 'long') {
    const parts = [];
    if (countdown.days > 0) parts.push(`${countdown.days} day${countdown.days !== 1 ? 's' : ''}`);
    if (countdown.hours > 0) parts.push(`${countdown.hours} hour${countdown.hours !== 1 ? 's' : ''}`);
    if (countdown.minutes > 0 && countdown.days === 0) parts.push(`${countdown.minutes} minute${countdown.minutes !== 1 ? 's' : ''}`);
    
    return parts.join(', ') || 'Less than 1 minute';
  }
  
  // Short format
  if (countdown.days > 0) {
    return `${countdown.days}d ${countdown.hours}h`;
  }
  
  if (countdown.hours > 0) {
    return `${countdown.hours}h ${countdown.minutes}m`;
  }
  
  return `${countdown.minutes}m`;
}

export function getCountdownUrgency(countdown: CountdownData): 'safe' | 'warning' | 'urgent' | 'expired' {
  if (countdown.isExpired) return 'expired';
  
  const totalHours = countdown.totalMs / (1000 * 60 * 60);
  
  if (totalHours <= 24) return 'urgent';
  if (totalHours <= 72) return 'warning';
  return 'safe';
}

export function getUrgencyColor(urgency: 'safe' | 'warning' | 'urgent' | 'expired'): string {
  switch (urgency) {
    case 'safe':
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
    case 'warning':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
    case 'urgent':
      return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
    case 'expired':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  }
}

export function validateDeadlineOrder(bidProposalDeadline: Date, rfqDeadline: Date): boolean {
  return bidProposalDeadline < rfqDeadline;
}
