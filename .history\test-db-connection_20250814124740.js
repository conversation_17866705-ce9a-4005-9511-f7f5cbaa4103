// This script is designed to be run in the Railway environment to test the database connection.
// It will immediately exit with a success or failure message.
// To run it, use the command: `node test-db-connection.js`

const { Pool } = require('pg');

const DATABASE_URL = process.env.DATABASE_URL;

console.log('--- Database Connection Test ---');

if (!DATABASE_URL) {
  console.error('❌ ERROR: DATABASE_URL environment variable is not set.');
  console.log('Please ensure the DATABASE_URL is configured in your Railway service variables.');
  process.exit(1);
}

console.log('✅ DATABASE_URL is present.');
console.log('Attempting to connect to the database...');

const pool = new Pool({
  connectionString: DATABASE_URL,
  ssl: {
    rejectUnauthorized: false // Required for Railway's self-signed certificates
  }
});

pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ FAILED to connect to the database.');
    console.error('Error:', err.message);
    console.error('Stack:', err.stack);
    console.log('--- End of Test ---');
    process.exit(1);
  }
  
  console.log('✅ SUCCESS: Connected to the database successfully!');
  
  release();
  pool.end();
  
  console.log('Connection closed.');
  console.log('--- End of Test ---');
  process.exit(0);
});
