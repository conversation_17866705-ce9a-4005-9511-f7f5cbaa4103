# Route Map

| Method | Path | File |
|---|---|---|
| POST | /api/admin/api-keys/rotate | server\routes.ts |
| GET | /api/admin/api-keys/rotation-needed | server\routes.ts |
| GET | /api/admin/api-keys/security-stats | server\routes.ts |
| GET | /api/admin/audit/access | server\routes.ts |
| GET | /api/admin/audit/business | server\routes.ts |
| POST | /api/admin/audit/business/backfill | server\routes.ts |
| GET | /api/admin/audit/roles | server\routes.ts |
| POST | /api/admin/backup/create | server\routes.ts |
| GET | /api/admin/backup/stats | server\routes.ts |
| POST | /api/admin/cache/clear | server\routes.ts |
| GET | /api/admin/performance/cache | server\routes.ts |
| GET | /api/admin/performance/files | server\routes.ts |
| GET | /api/admin/security/audit | server\routes.ts |
| POST | /api/admin/security/invalidate-sessions | server\routes.ts |
| GET | /api/admin/users | server\routes.ts |
| PATCH | /api/admin/users/:userId/role | server\routes.ts |
| GET | /api/auth/api-keys | server\routes.ts |
| POST | /api/auth/api-keys | server\routes.ts |
| DELETE | /api/auth/api-keys/:id | server\routes.ts |
| PATCH | /api/auth/api-keys/:id | server\routes.ts |
| GET | /api/auth/api-keys/:id/stats | server\routes.ts |
| GET | /api/auth/user | server\routes.ts |
| PATCH | /api/auth/user | server\routes.ts |
| GET | /api/bids/:bidId | server\routes.ts |
| PATCH | /api/bids/:bidId | server\routes.ts |
| POST | /api/bids/:bidId/action | server\routes.ts |
| POST | /api/bids/:bidId/analyze | server\routes.ts |
| GET | /api/bids/:bidId/documents | server\routes.ts |
| GET | /api/bids/:bidId/structured-data | server\routes.ts |
| PATCH | /api/clerk/accept-terms | server\routes.ts |
| GET | /api/clerk/user | server\routes.ts |
| GET | /api/contractors | server\routes.ts |
| POST | /api/contractors | server\routes.ts |
| DELETE | /api/contractors/:contractorId/favorite | server\routes.ts |
| POST | /api/contractors/:contractorId/favorite | server\routes.ts |
| PATCH | /api/contractors/:id | server\routes.ts |
| GET | /api/contractors/bids | server\routes.ts |
| GET | /api/contractors/favorites | server\routes.ts |
| GET | /api/contractors/performance-stats | server\routes.ts |
| GET | /api/contractors/profile | server\routes.ts |
| PUT | /api/contractors/profile | server\routes.ts |
| GET | /api/contractors/rfqs | server\routes.ts |
| GET | /api/contractors/rfqs/all | server\routes.ts |
| GET | /api/cost-codes | server\routes.ts |
| GET | /api/cost-codes/stats | server\routes.ts |
| GET | /api/dashboard/stats | server\routes.ts |
| GET | /api/dashboard/stats | server\routes.ts |
| GET | /api/debug/assets | server\index.ts |
| GET | /api/files/:documentId | server\routes.ts |
| GET | /api/forecast/materials | server\routes.ts |
| POST | /api/forecast/materials | server\routes.ts |
| POST | /api/forecast/search | server\routes.ts |
| GET | /api/health | server\routes\health.ts |
| GET | /api/integrations/export/rfqs | server\routes.ts |
| GET | /api/integrations/project-budget/:rfqId/:bidId | server\routes.ts |
| GET | /api/integrations/quickbooks | server\routes.ts |
| POST | /api/integrations/sage | server\routes.ts |
| GET | /api/logout | server\routes.ts |
| GET | /api/notifications | server\routes.ts |
| PATCH | /api/notifications/:id/read | server\routes.ts |
| GET | /api/notifications/preferences | server\routes.ts |
| PUT | /api/notifications/preferences/:type | server\routes.ts |
| POST | /api/notifications/send-custom-email | server\routes.ts |
| POST | /api/notifications/test | server\routes.ts |
| GET | /api/notifications/unread | server\routes.ts |
| POST | /api/organizations | server\routes.ts |
| GET | /api/organizations/:orgId/invitations | server\routes.ts |
| POST | /api/organizations/:orgId/invitations | server\routes.ts |
| GET | /api/organizations/:orgId/users | server\routes.ts |
| DELETE | /api/organizations/:orgId/users/:targetUserId | server\routes.ts |
| PATCH | /api/organizations/:orgId/users/:targetUserId/role | server\routes.ts |
| GET | /api/processing/stats | server\routes\processingStats.ts |
| GET | /api/processing/stats | server\routes.ts |
| POST | /api/reports/email | server\routes.ts |
| GET | /api/rfqs | server\routes.ts |
| POST | /api/rfqs | server\routes.ts |
| GET | /api/rfqs/:id | server\routes.ts |
| PUT | /api/rfqs/:id | server\routes.ts |
| GET | /api/rfqs/:id/documents | server\routes.ts |
| GET | /api/rfqs/:rfqId/bid-comparison | server\routes.ts |
| GET | /api/rfqs/:rfqId/bid-comparison/export | server\routes.ts |
| GET | /api/rfqs/:rfqId/bids | server\routes.ts |
| POST | /api/rfqs/:rfqId/bids | server\routes.ts |
| GET | /api/rfqs/:rfqId/bids/analysis | server\routes.ts |
| PUT | /api/rfqs/:rfqId/buffer | server\routes.ts |
| GET | /api/rfqs/:rfqId/category-analysis | server\routes.ts |
| GET | /api/rfqs/:rfqId/comprehensive-bid-analysis | server\routes.ts |
| PATCH | /api/rfqs/:rfqId/decline | server\routes.ts |
| PATCH | /api/rfqs/:rfqId/decline | server\routes.ts |
| POST | /api/rfqs/:rfqId/distribute | server\routes.ts |
| GET | /api/rfqs/:rfqId/distributions | server\routes.ts |
| POST | /api/rfqs/:rfqId/generate-master-summary | server\routes.ts |
| PATCH | /api/rfqs/:rfqId/view | server\routes.ts |
| POST | /api/rfqs/process-documents | server\routes.ts |
| POST | /api/test/api-key | server\routes.ts |
| POST | /api/test/contractor | server\routes.ts |
| POST | /api/test/distribute | server\routes.ts |
| POST | /api/test/org | server\routes.ts |
| POST | /api/test/rfq | server\routes.ts |
| POST | /api/test/rfq-document | server\routes.ts |
| POST | /api/test/rfqs | server\routes.ts |
| GET | /api/test/unified-pdf-extractor | server\routes\testUnifiedExtractor.ts |
| POST | /api/test/user | server\routes.ts |
| GET | /api/upload/progress/:sessionId | server\routes.ts |
| POST | /api/upload/start-session | server\routes.ts |
| GET | /api/user-feedback | server\routes.ts |
| POST | /api/user-feedback | server\routes.ts |
| PATCH | /api/user-feedback/:id | server\routes.ts |
| GET | /api/user-feedback/stats | server\routes.ts |
| GET | /api/user/activity | server\routes.ts |
| POST | /api/waitlist | server\routes.ts |
| GET | /api/waitlist/count | server\routes.ts |
| GET | /attached_assets/:fileName | server\routes.ts |
| GET | /bid-analysis/:rfqId | server\routes\analytics.ts |
| GET | /competitive-intelligence/:rfqId | server\routes\analytics.ts |
| GET | /default-criteria | server\routes\analytics.ts |
| POST | /evaluation-scores/:rfqId | server\routes\analytics.ts |
| GET | /market-intelligence/:region/:trade | server\routes\analytics.ts |
| GET | /predictive-analytics/:rfqId | server\routes\analytics.ts |
| GET | /risk-assessment/:bidId | server\routes\analytics.ts |
| GET | env | server\index.ts |