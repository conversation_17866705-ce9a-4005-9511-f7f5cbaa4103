import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, AlertCircle, FileText, Calculator, User, Package } from "lucide-react";

interface BidDataQualityIndicatorProps {
  dataCompleteness: {
    hasLineItems: boolean;
    hasScopeDefinition: boolean;
    hasContactInfo: boolean;
    calculatedTotal: number;
    bidAmountMatch: boolean;
  };
  hasStructuredData: boolean;
  lineItemsCount?: number;
  scopeItemsCount?: number;
}

export function BidDataQualityIndicator({ 
  dataCompleteness, 
  hasStructuredData,
  lineItemsCount = 0,
  scopeItemsCount = 0
}: BidDataQualityIndicatorProps) {
  const getDataQualityScore = () => {
    let score = 0;
    if (dataCompleteness.hasLineItems) score += 30;
    if (dataCompleteness.hasScopeDefinition) score += 25;
    if (dataCompleteness.hasContactInfo) score += 20;
    if (dataCompleteness.bidAmountMatch) score += 25;
    return score;
  };

  const qualityScore = getDataQualityScore();
  
  const getQualityColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getQualityLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Limited';
  };

  const indicators = [
    {
      icon: Package,
      label: 'Line Items',
      value: dataCompleteness.hasLineItems,
      detail: lineItemsCount > 0 ? `${lineItemsCount} items` : 'Not available',
      weight: 30
    },
    {
      icon: FileText,
      label: 'Scope Definition',
      value: dataCompleteness.hasScopeDefinition,
      detail: scopeItemsCount > 0 ? `${scopeItemsCount} items` : 'Not available',
      weight: 25
    },
    {
      icon: User,
      label: 'Contact Info',
      value: dataCompleteness.hasContactInfo,
      detail: dataCompleteness.hasContactInfo ? 'Complete' : 'Not available',
      weight: 20
    },
    {
      icon: Calculator,
      label: 'Amount Match',
      value: dataCompleteness.bidAmountMatch,
      detail: dataCompleteness.bidAmountMatch ? 'Totals match' : 'Totals differ',
      weight: 25
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          Data Quality Assessment
        </CardTitle>
        <CardDescription>
          Evaluation of bid data completeness and structure
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Score */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2">
            <span className="text-2xl font-bold">Overall Quality:</span>
            <Badge className={`${getQualityColor(qualityScore)} text-lg px-3 py-1`}>
              {getQualityLabel(qualityScore)} ({qualityScore}%)
            </Badge>
          </div>
          <Progress value={qualityScore} className="w-full h-3" />
        </div>

        {/* Structured Data Indicator */}
        <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            {hasStructuredData ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertCircle className="h-5 w-5 text-yellow-600" />
            )}
            <span className="font-medium">Data Source</span>
          </div>
          <Badge variant={hasStructuredData ? "default" : "secondary"}>
            {hasStructuredData ? "Structured Form Data" : "AI Extracted Only"}
          </Badge>
        </div>

        {/* Detailed Indicators */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm text-muted-foreground">Data Completeness</h4>
          {indicators.map((indicator) => {
            const Icon = indicator.icon;
            return (
              <div key={indicator.label} className="flex items-center justify-between p-2 rounded border">
                <div className="flex items-center gap-2">
                  {indicator.value ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <Icon className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium text-sm">{indicator.label}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm">{indicator.detail}</p>
                  <p className="text-xs text-muted-foreground">
                    {indicator.weight}% weight
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Recommendations */}
        {qualityScore < 80 && (
          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              Recommendations
            </h4>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              {!dataCompleteness.hasLineItems && (
                <li>• Consider requesting detailed cost breakdown from contractor</li>
              )}
              {!dataCompleteness.hasScopeDefinition && (
                <li>• Request clarification on included/excluded scope items</li>
              )}
              {!dataCompleteness.hasContactInfo && (
                <li>• Obtain complete contact information for follow-up</li>
              )}
              {!dataCompleteness.bidAmountMatch && (
                <li>• Verify total amounts and line item calculations</li>
              )}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}