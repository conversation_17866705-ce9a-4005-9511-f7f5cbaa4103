// Test script for GitHub integration
import { githubService } from './server/services/githubService.js';

async function testGitHubIntegration() {
  console.log('Testing GitHub service configuration...');
  
  if (!githubService.isConfigured()) {
    console.error('❌ GitHub service not configured. Check your environment variables:');
    console.log('  - GITHUB_TOKEN');
    console.log('  - GITHUB_REPO_OWNER (should be: roygatling)');
    console.log('  - GITHUB_REPO_NAME (should be: Bidaible)');
    return;
  }

  console.log('✅ GitHub service is configured');
  
  // Test creating a GitHub issue
  console.log('Creating test GitHub issue...');
  
  try {
    const result = await githubService.createIssue({
      type: 'bug',
      message: 'Test GitHub integration - This is a test issue created by the GitHub integration system. You can safely close this issue.',
      priority: 'medium',
      userId: 'test-user-id',
      userEmail: '<EMAIL>',
      organizationName: 'Test Organization',
      feedbackId: 'test-feedback-' + Date.now(),
    });

    if (result) {
      console.log('✅ GitHub issue created successfully!');
      console.log(`   Issue Number: ${result.issueNumber}`);
      console.log(`   Issue URL: ${result.issueUrl}`);
    } else {
      console.log('❌ Failed to create GitHub issue');
    }
  } catch (error) {
    console.error('❌ Error testing GitHub integration:', error);
  }
}

testGitHubIntegration();
