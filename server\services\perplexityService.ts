interface PerplexityResponse {
  id: string;
  model: string;
  object: string;
  created: number;
  citations: string[];
  choices: {
    index: number;
    finish_reason: string;
    message: {
      role: string;
      content: string;
    };
    delta: {
      role: string;
      content: string;
    };
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export async function searchMaterialPricing(materialName: string, unit: string): Promise<{
  currentPrice?: number;
  priceRange?: string;
  marketTrend?: string;
  lastUpdated?: string;
}> {
  try {
    const query = `Current market price for ${materialName} per ${unit} in commercial construction 2025`;
    
    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.PERPLEXITY_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama-3.1-sonar-small-128k-online',
        messages: [
          {
            role: 'system',
            content: 'You are a construction materials pricing expert. Provide current market prices, price ranges, and market trends for construction materials. Be precise and include units.'
          },
          {
            role: 'user',
            content: query
          }
        ],
        max_tokens: 300,
        temperature: 0.2,
        top_p: 0.9,
        return_images: false,
        return_related_questions: false,
        search_recency_filter: 'month',
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`Perplexity API error: ${response.status}`);
    }

    const data = await response.json() as any;
    const content = data.choices[0]?.message?.content || '';
    
    // Parse the response to extract pricing information
    const priceMatch = content.match(/\$?(\d+(?:\.\d{2})?)/);
    const currentPrice = priceMatch ? parseFloat(priceMatch[1]) : undefined;
    
    return {
      currentPrice,
      priceRange: extractPriceRange(content),
      marketTrend: extractMarketTrend(content),
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error searching material pricing for ${materialName}:`, error);
    return {};
  }
}

function extractPriceRange(content: string): string {
  const rangeMatch = content.match(/\$?(\d+(?:\.\d{2})?)\s*[-–to]\s*\$?(\d+(?:\.\d{2})?)/i);
  return rangeMatch ? `$${rangeMatch[1]} - $${rangeMatch[2]}` : '';
}

function extractMarketTrend(content: string): string {
  const trendKeywords = ['increasing', 'decreasing', 'stable', 'rising', 'falling', 'volatile'];
  for (const keyword of trendKeywords) {
    if (content.toLowerCase().includes(keyword)) {
      return keyword;
    }
  }
  return '';
}

export async function batchSearchMaterialPricing(materials: Array<{ name: string; unit: string }>): Promise<Map<string, any>> {
  const results = new Map();
  
  // Process materials in batches to avoid rate limiting
  const batchSize = 5;
  for (let i = 0; i < materials.length; i += batchSize) {
    const batch = materials.slice(i, i + batchSize);
    const promises = batch.map(async (material) => {
      const result = await searchMaterialPricing(material.name, material.unit);
      return { key: `${material.name}_${material.unit}`, result };
    });
    
    const batchResults = await Promise.all(promises);
    batchResults.forEach(({ key, result }) => {
      results.set(key, result);
    });
    
    // Add delay between batches
    if (i + batchSize < materials.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}