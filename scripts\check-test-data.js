/**
 * Test Data Check Script
 * 
 * Quick script to identify test data without performing any cleanup
 * Run with: node scripts/check-test-data.js
 */

import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from 'ws';
import { organizations, users, apiKeys } from '../shared/schema.ts';
import { like, or, inArray } from 'drizzle-orm';
import * as dotenv from 'dotenv';

dotenv.config();
neonConfig.webSocketConstructor = ws;

if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

const pool = new Pool({ connectionString: process.env.DATABASE_URL });
const db = drizzle({ client: pool });

async function checkTestData() {
  console.log('🔍 Checking for test data in production database...\n');

  // Check test organizations
  const testOrgs = await db.select().from(organizations).where(
    or(
      like(organizations.name, '%test%'),
      like(organizations.name, '%demo%'),
      like(organizations.name, '%example%'),
      like(organizations.slug, '%test%'),
      like(organizations.slug, '%demo%')
    )
  );

  // Check test users
  const testUsers = await db.select().from(users).where(
    or(
      like(users.email, '%test%'),
      like(users.email, '%demo%'), 
      like(users.email, '%example%'),
      like(users.email, '%@test.com'),
      like(users.email, '%@example.com'),
      like(users.firstName, 'test%'),
      like(users.firstName, 'demo%')
    )
  );

  // Check API keys for test users
  const testApiKeys = testUsers.length > 0 ? await db.select().from(apiKeys).where(
    inArray(apiKeys.userId, testUsers.map(user => user.id))
  ) : [];

  console.log('📊 Test Data Summary:');
  console.log(`Organizations: ${testOrgs.length}`);
  console.log(`Users: ${testUsers.length}`);  
  console.log(`API Keys: ${testApiKeys.length}\n`);

  if (testOrgs.length > 0) {
    console.log('🏢 Test Organizations:');
    testOrgs.forEach(org => {
      console.log(`  - ${org.name} (${org.slug}) - Created: ${org.createdAt?.toLocaleDateString()}`);
    });
    console.log('');
  }

  if (testUsers.length > 0) {
    console.log('👥 Test Users:');
    testUsers.forEach(user => {
      console.log(`  - ${user.email || 'No email'} (${user.firstName} ${user.lastName}) - Role: ${user.role}`);
    });
    console.log('');
  }

  if (testApiKeys.length > 0) {
    console.log('🔑 Test API Keys:');
    testApiKeys.forEach(key => {
      console.log(`  - ${key.name} - Permissions: ${key.permissions} - Last used: ${key.lastUsedAt?.toLocaleDateString() || 'Never'}`);
    });
  }

  if (testOrgs.length === 0 && testUsers.length === 0 && testApiKeys.length === 0) {
    console.log('✅ No obvious test data found!');
  } else {
    console.log(`\n⚠️  Found ${testOrgs.length + testUsers.length + testApiKeys.length} test records`);
    console.log('Run cleanup script with: node scripts/cleanup-test-data.js --dry-run');
  }
}

checkTestData().catch(console.error).finally(() => pool.end());
