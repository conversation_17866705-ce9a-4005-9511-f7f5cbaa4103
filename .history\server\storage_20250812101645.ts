import {
  users,
  contractors,
  contractorFavorites,
  rfqs,
  rfqDocuments,
  bids,
  bidDocuments,
  bidLineItems,
  bidInclusionsExclusions,
  rfqDistribution,
  forecastMaterials,
  apiKeys,
  apiKeyUsage,
  organizations,
  roleAuditLog,
  accessAuditLog,
  waitlist,
  userFeedback,
  businessAuditLog,
  notifications,
  notificationPreferences,
  notificationDeliveries,
  type User,
  type InsertUser,
  type Contractor,
  type InsertContractor,
  type Rfq,
  type InsertRfq,
  type Bid,
  type InsertBid,
  type Notification,
  type InsertNotification,
  type NotificationPreferences,
  type InsertNotificationPreferences,
  type NotificationDelivery,
  type InsertNotificationDelivery,
  type UpsertUser,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, sql, or, lt } from "drizzle-orm";
import { cache, CACHE_KEYS, CACHE_TTL } from "./services/cacheService";

export interface IStorage {
  // User operations (required for Replit Auth)
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  acceptUserTerms(userId: string): Promise<User | undefined>;

  // Contractor operations
  getContractor(id: string): Promise<Contractor | undefined>;
  getContractorByUserId(userId: string): Promise<Contractor | undefined>;
  createContractor(contractor: InsertContractor): Promise<Contractor>;
  updateContractor(id: string, contractor: Partial<InsertContractor>): Promise<Contractor | undefined>;
  getContractors(): Promise<Contractor[]>;

  // RFQ operations
  getRfq(id: string): Promise<Rfq | undefined>;
  createRfq(rfq: InsertRfq): Promise<Rfq>;
  updateRfq(id: string, rfq: Partial<InsertRfq>): Promise<Rfq | undefined>;
  getRfqs(): Promise<Rfq[]>;
  getRfqsByUser(userId: string): Promise<Rfq[]>;

  // RFQ document operations
  createRfqDocument(document: typeof rfqDocuments.$inferInsert): Promise<RfqDocument>;
  getRfqDocument(documentId: string): Promise<RfqDocument | undefined>;
  getRfqDocuments(rfqId: string): Promise<RfqDocument[]>;

  // Bid operations
  getBid(id: string): Promise<Bid | undefined>;
  createBid(bid: InsertBid): Promise<Bid>;
  updateBid(id: string, bid: Partial<InsertBid>): Promise<Bid | undefined>;
  getBidsByRfq(rfqId: string): Promise<Bid[]>;

  // **ENHANCED BID DATA OPERATIONS** - Phase 3 Implementation
  createBidLineItem(lineItem: typeof bidLineItems.$inferInsert): Promise<typeof bidLineItems.$inferSelect>;
  getBidLineItems(bidId: string): Promise<typeof bidLineItems.$inferSelect[]>;
  createBidInclusionExclusion(scopeItem: typeof bidInclusionsExclusions.$inferInsert): Promise<typeof bidInclusionsExclusions.$inferSelect>;
  getBidInclusionsExclusions(bidId: string): Promise<typeof bidInclusionsExclusions.$inferSelect[]>;
  getBidsByContractor(contractorId: string): Promise<Array<{ bid: Bid; rfq: Rfq }>>;

  // Enhanced bid management operations
  updateBidStatus(bidId: string, status: string, reviewedBy: string, notes?: string): Promise<Bid | undefined>;
  getBidsByRfqWithContractors(rfqId: string): Promise<Array<{ bid: Bid; contractor: Contractor }>>;

  // Bid document operations  
  createBidDocument(document: typeof bidDocuments.$inferInsert): Promise<BidDocument>;
  getBidDocument(documentId: string): Promise<BidDocument | undefined>;
  getBidDocuments(bidId: string): Promise<BidDocument[]>;

  // Contractor favorites operations
  addContractorToFavorites(userId: string, contractorId: string, notes?: string): Promise<ContractorFavorite>;
  removeContractorFromFavorites(userId: string, contractorId: string): Promise<boolean>;
  getFavoriteContractors(userId: string): Promise<ContractorFavorite[]>;

  // RFQ distribution operations
  distributeRfq(rfqId: string, contractorIds: string[], method: "favorites" | "broadcast"): Promise<RfqDistribution[]>;
  markRfqViewed(rfqId: string, contractorId: string): Promise<void>;
  declineRfq(rfqId: string, contractorId: string, reason?: string): Promise<void>;
  getRfqDistributions(rfqId: string): Promise<RfqDistribution[]>;
  getContractorRfqs(contractorId: string): Promise<Rfq[]>;
  getContractorRfqDistributions(contractorId: string): Promise<any[]>;

  // Dashboard statistics
  getDashboardStats(): Promise<{
    activeRfqs: number;
    totalBids: number;
    avgResponseTime: number;
    successRate: number;
  }>;

  // Forecast materials operations
  getForecastMaterials(): Promise<ForecastMaterial[]>;
  createForecastMaterial(material: InsertForecastMaterial): Promise<ForecastMaterial>;
  updateForecastMaterial(id: string, material: Partial<InsertForecastMaterial>): Promise<ForecastMaterial | undefined>;
  deleteForecastMaterial(id: string): Promise<boolean>;

  // API Key operations
  createApiKey(apiKey: InsertApiKey): Promise<ApiKey>;
  getApiKey(id: string): Promise<ApiKey | undefined>;
  getApiKeyByHash(keyHash: string): Promise<ApiKey | undefined>;
  getApiKeysByUserId(userId: string): Promise<ApiKey[]>;
  updateApiKey(id: string, updates: Partial<InsertApiKey>): Promise<ApiKey | undefined>;
  deleteApiKey(id: string): Promise<boolean>;
  updateApiKeyLastUsed(id: string): Promise<void>;

  // API Key usage operations
  recordApiKeyUsage(apiKeyId: string, endpoint: string, method: string): Promise<void>;
  getApiKeyUsageToday(apiKeyId: string, date: string): Promise<ApiKeyUsage[]>;
  getApiKeyUsageStats(apiKeyId: string): Promise<ApiKeyUsage[]>;

  // Organization operations
  createOrganization(organization: InsertOrganization): Promise<Organization>;
  getOrganization(id: string): Promise<Organization | undefined>;
  getOrganizationBySlug(slug: string): Promise<Organization | undefined>;
  updateOrganization(id: string, updates: Partial<InsertOrganization>): Promise<Organization | undefined>;
  getOrganizations(): Promise<Organization[]>;

  // Role management operations
  getUsersByOrganization(organizationId: string): Promise<User[]>;
  updateUserRole(userId: string, newRole: string, changedBy: string, reason?: string, request?: any): Promise<User | undefined>;
  getAllUsers(): Promise<User[]>; // SuperAdmin only

  // Audit operations
  createRoleAuditLog(auditData: InsertRoleAuditLog): Promise<RoleAuditLog>;
  createAccessAuditLog(auditData: InsertAccessAuditLog): Promise<AccessAuditLog>;
  getRoleAuditLogs(organizationId?: string): Promise<RoleAuditLog[]>;
  getAccessAuditLogs(organizationId?: string): Promise<AccessAuditLog[]>;

  // Waitlist operations
  addToWaitlist(data: any): Promise<any>;
  getWaitlistCount(): Promise<number>;
  checkEmailInWaitlist(email: string): Promise<boolean>;

  // User feedback operations
  createUserFeedback(feedback: InsertUserFeedback): Promise<UserFeedback>;
  getUserFeedback(): Promise<UserFeedback[]>; // Super user only
  updateUserFeedback(id: string, updates: Partial<InsertUserFeedback>): Promise<UserFeedback | undefined>;
  getUserFeedbackStats(): Promise<{ total: number; bugs: number; suggestions: number; open: number; resolved: number }>;

  // Business audit log operations
  createBusinessAuditLog(auditData: InsertBusinessAuditLog): Promise<BusinessAuditLog>;
  getBusinessAuditLogs(criteria?: {
    eventType?: string;
    userId?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<BusinessAuditLog[]>;

  // Notification operations
  createNotification(notification: InsertNotification): Promise<Notification>;
  getNotifications(userId: string, limit?: number): Promise<Notification[]>;
  markNotificationRead(notificationId: string, userId: string): Promise<boolean>;
  getUserNotificationPreferences(userId: string): Promise<NotificationPreferences[]>;
  getNotificationPreference(userId: string, type: string): Promise<NotificationPreferences | undefined>;
  upsertNotificationPreference(preference: InsertNotificationPreferences): Promise<NotificationPreferences>;
  updateNotificationPreferences(userId: string, type: string, preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences>;
  createNotificationDelivery(delivery: InsertNotificationDelivery): Promise<NotificationDelivery>;
  updateDeliveryStatus(deliveryId: string, status: string, response?: any): Promise<boolean>;
}

export class DatabaseStorage implements IStorage {
  db: typeof db;
  schema: { waitlist: typeof waitlist };

  constructor() {
    // Use the imported database instance and schema
    this.db = db;
    this.schema = { waitlist };
  }

  // User operations
  async getUser(id: string): Promise<User | undefined> {
    return await cache.cached(
      CACHE_KEYS.userProfile(id),
      async () => {
        const [user] = await this.db.select().from(users).where(eq(users.id, id));
        return user;
      },
      CACHE_TTL.USER_PROFILE
    );
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    try {
      // First check if user already exists by ID
      const existingUserById = await this.getUser(userData.id);
      if (existingUserById) {
        // User exists with this ID, just update the fields (don't change ID)
        const [user] = await this.db
          .update(users)
          .set({
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            profileImageUrl: userData.profileImageUrl,
            updatedAt: new Date(),
          })
          .where(eq(users.id, userData.id))
          .returning();
        return user;
      }

      // Check for existing user by email (migration case)
      if (userData.email) {
        const existingUserByEmail = await this.db
          .select()
          .from(users)
          .where(eq(users.email, userData.email))
          .limit(1);
          
        if (existingUserByEmail.length > 0) {
          // Don't change the ID (to avoid FK constraint issues), just update other fields
          const [user] = await this.db
            .update(users)
            .set({
              firstName: userData.firstName,
              lastName: userData.lastName,
              profileImageUrl: userData.profileImageUrl,
              updatedAt: new Date(),
            })
            .where(eq(users.email, userData.email))
            .returning();
          return user;
        }
      }
      
      // No existing user, create new
      const [user] = await this.db
        .insert(users)
        .values(userData)
        .returning();
      return user;
    } catch (error) {
      console.error('Error in upsertUser:', error);
      throw error;
    }
  }

  async acceptUserTerms(userId: string): Promise<User | undefined> {
    const [user] = await this.db
      .update(users)
      .set({
        termsAccepted: true,
        termsAcceptedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning();
    
    // Clear cache for this user
    cache.delete(CACHE_KEYS.userProfile(userId));
    
    return user;
  }

  // Contractor operations
  async getContractor(id: string): Promise<Contractor | undefined> {
    try {
      const [contractor] = await this.db.select().from(contractors).where(eq(contractors.id, id));
      return contractor;
    } catch (error) {
      console.error('Error fetching contractor:', error);
      // Return a basic contractor object if there are schema mismatches
      const [basicContractor] = await this.db.select({
        id: contractors.id,
        userId: contractors.userId,
        companyName: contractors.companyName,
        primaryContactName: contractors.primaryContactName,
        primaryContactEmail: contractors.primaryContactEmail,
        primaryContactPhone: contractors.primaryContactPhone,
        tradeTypes: contractors.tradeTypes,
        isApproved: contractors.isApproved,
        createdAt: contractors.createdAt,
        updatedAt: contractors.updatedAt,
      }).from(contractors).where(eq(contractors.id, id));
      return basicContractor as any;
    }
  }

  async getContractorByUserId(userId: string): Promise<Contractor | undefined> {
    try {
      console.log("🔍 Database query for contractor, userId:", userId);
      
      // First try direct user ID lookup
      let [contractor] = await this.db
        .select()
        .from(contractors)
        .where(eq(contractors.userId, userId))
        .orderBy(desc(contractors.createdAt))
        .limit(1);
      
      if (contractor) {
        console.log("✅ Found contractor in DB by userId:", contractor.id, contractor.companyName);
        return contractor;
      }
      
      // If Clerk user ID not found, try to find contractor by email mapping
      // This handles the migration from old user IDs to Clerk user IDs
      const user = await this.getUser(userId);
      if (user && user.email) {
        console.log("🔄 Trying to find contractor by email:", user.email);
        
        // Find contractors for users with the same email
        const contractorsWithEmail = await this.db
          .select()
          .from(contractors)
          .innerJoin(users, eq(contractors.userId, users.id))
          .where(eq(users.email, user.email))
          .orderBy(desc(contractors.createdAt))
          .limit(1);
          
        if (contractorsWithEmail.length > 0) {
          const foundContractor = contractorsWithEmail[0].contractors;
          console.log("✅ Found contractor by email mapping:", foundContractor.id, foundContractor.companyName);
          return foundContractor;
        }
      }
      
      console.log("❌ No contractor found for userId:", userId);
      return undefined;
    } catch (error) {
      console.error("❌ Database error in getContractorByUserId:", error);
      return undefined;
    }
  }

  async createContractor(contractor: InsertContractor): Promise<Contractor> {
    const [newContractor] = await this.db
      .insert(contractors)
      .values({
        ...contractor,
        organizationId: contractor.organizationId, // Ensure organizationId is set
      })
      .returning();
    return newContractor;
  }

  async updateContractor(id: string, contractor: Partial<InsertContractor>): Promise<Contractor | undefined> {
    const [updated] = await this.db
      .update(contractors)
      .set({ ...contractor, updatedAt: new Date() })
      .where(eq(contractors.id, id))
      .returning();
    return updated;
  }

  async getContractors(): Promise<Contractor[]> {
    return await this.db.select({
      id: contractors.id,
      userId: contractors.userId,
      organizationId: contractors.organizationId,
      companyName: contractors.companyName,
      primaryContactName: contractors.primaryContactName,
      primaryContactEmail: contractors.primaryContactEmail,
      primaryContactPhone: contractors.primaryContactPhone,
      businessAddress: contractors.primaryAddress,
      tradeTypes: contractors.tradeTypes,
      verified: contractors.isApproved,
      createdAt: contractors.createdAt,
      updatedAt: contractors.updatedAt,
    }).from(contractors).orderBy(desc(contractors.createdAt));
  }

  // New method: Get contractors filtered by organization
  async getContractorsByOrganization(organizationId: string): Promise<Contractor[]> {
    return await this.db.select({
      id: contractors.id,
      userId: contractors.userId,
      organizationId: contractors.organizationId,
      companyName: contractors.companyName,
      primaryContactName: contractors.primaryContactName,
      primaryContactEmail: contractors.primaryContactEmail,
      primaryContactPhone: contractors.primaryContactPhone,
      businessAddress: contractors.primaryAddress,
      tradeTypes: contractors.tradeTypes,
      verified: contractors.isApproved,
      createdAt: contractors.createdAt,
      updatedAt: contractors.updatedAt,
    }).from(contractors)
    .where(eq(contractors.organizationId, organizationId))
    .orderBy(desc(contractors.createdAt));
  }

  // RFQ operations
  async getRfq(id: string): Promise<Rfq | undefined> {
    const [rfq] = await this.db.select().from(rfqs).where(eq(rfqs.id, id));
    return rfq;
  }

  async createRfq(rfq: InsertRfq): Promise<Rfq> {
    const [newRfq] = await this.db
      .insert(rfqs)
      .values({
        id: rfq.id,
        createdBy: rfq.createdBy,
        organizationId: rfq.organizationId, // Multi-tenant isolation
        projectName: rfq.projectName,
        projectLocation: rfq.projectLocation,
        tradeCategory: rfq.tradeCategory,
        description: rfq.description,
        dueDate: rfq.dueDate,
        status: rfq.status,
        extractedData: rfq.extractedData,
        aiSummary: rfq.aiSummary,
        bufferPercentage: rfq.bufferPercentage,
        bufferNotes: rfq.bufferNotes,
        createdAt: rfq.createdAt,
        updatedAt: rfq.updatedAt,
      })
      .returning();
    return newRfq;
  }

  async updateRfq(id: string, data: Partial<InsertRfq>): Promise<Rfq | null> {
    const [rfq] = await this.db
      .update(rfqs)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(rfqs.id, id))
      .returning();
    return rfq || null;
  }

  async getRfqs(): Promise<Rfq[]> {
    return await this.db.select({
      id: rfqs.id,
      createdBy: rfqs.createdBy,
      organizationId: rfqs.organizationId,
      projectName: rfqs.projectName,
      projectLocation: rfqs.projectLocation,
      tradeCategory: rfqs.tradeCategory,
      description: rfqs.description,
      dueDate: rfqs.dueDate,
      status: rfqs.status,
      extractedData: rfqs.extractedData,
      aiSummary: rfqs.aiSummary,
      bufferPercentage: rfqs.bufferPercentage,
      bufferNotes: rfqs.bufferNotes,
      createdAt: rfqs.createdAt,
      updatedAt: rfqs.updatedAt,
    }).from(rfqs).orderBy(desc(rfqs.createdAt));
  }

  // New method: Get RFQs filtered by organization
  async getRfqsByOrganization(organizationId: string): Promise<Rfq[]> {
    return await this.db.select({
      id: rfqs.id,
      createdBy: rfqs.createdBy,
      organizationId: rfqs.organizationId,
      projectName: rfqs.projectName,
      projectLocation: rfqs.projectLocation,
      tradeCategory: rfqs.tradeCategory,
      description: rfqs.description,
      dueDate: rfqs.dueDate,
      status: rfqs.status,
      extractedData: rfqs.extractedData,
      aiSummary: rfqs.aiSummary,
      bufferPercentage: rfqs.bufferPercentage,
      bufferNotes: rfqs.bufferNotes,
      createdAt: rfqs.createdAt,
      updatedAt: rfqs.updatedAt,
    }).from(rfqs)
    .where(eq(rfqs.organizationId, organizationId))
    .orderBy(desc(rfqs.createdAt));
  }

  async getRfqsByUser(userId: string): Promise<Rfq[]> {
    return await this.db.select().from(rfqs).where(eq(rfqs.createdBy, userId)).orderBy(desc(rfqs.createdAt));
  }

  // RFQ document operations
  async createRfqDocument(document: typeof rfqDocuments.$inferInsert): Promise<RfqDocument> {
    const [newDocument] = await this.db
      .insert(rfqDocuments)
      .values(document)
      .returning();
    return newDocument;
  }

  async getRfqDocument(documentId: string): Promise<RfqDocument | undefined> {
    const [document] = await this.db.select().from(rfqDocuments).where(eq(rfqDocuments.id, documentId));
    return document;
  }

  async getRfqDocuments(rfqId: string): Promise<RfqDocument[]> {
    return await this.db.select().from(rfqDocuments).where(eq(rfqDocuments.rfqId, rfqId));
  }

  // Bid operations
  async getBid(id: string): Promise<Bid | undefined> {
    const [bid] = await this.db.select().from(bids).where(eq(bids.id, id));
    return bid;
  }

  async createBid(bid: InsertBid): Promise<Bid> {
    const [newBid] = await this.db
      .insert(bids)
      .values(bid)
      .returning();
    return newBid;
  }

  async updateBid(id: string, bidData: Partial<InsertBid>): Promise<Bid | undefined> {
    const [bid] = await this.db
      .update(bids)
      .set(bidData)
      .where(eq(bids.id, id))
      .returning();
    return bid;
  }

  async getBidsByRfq(rfqId: string): Promise<Bid[]> {
    return await this.db.select().from(bids).where(eq(bids.rfqId, rfqId)).orderBy(desc(bids.submittedAt));
  }

  async getBidsByContractor(contractorId: string): Promise<Array<{ bid: Bid; rfq: Rfq }>> {
    // Clear any existing cache for this contractor
    cache.invalidatePattern(`bids:contractor:${contractorId}`);

    const bidResults = await this.db
      .select({
        bid: bids,
        rfq: rfqs,
      })
      .from(bids)
      .innerJoin(rfqs, eq(bids.rfqId, rfqs.id))
      .where(eq(bids.contractorId, contractorId))
      .orderBy(desc(bids.submittedAt));

    return bidResults;
  }

  // Enhanced bid management operations
  async updateBidStatus(bidId: string, status: string, reviewedBy: string, notes?: string): Promise<Bid | undefined> {
    const [updated] = await this.db
      .update(bids)
      .set({
        status,
        reviewedBy,
        reviewNotes: notes,
        reviewedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(bids.id, bidId))
      .returning();

    // Invalidate related cache entries
    if (updated) {
      cache.invalidatePattern(`bids:rfq:${updated.rfqId}`);
      cache.invalidatePattern(`bids:contractor:${updated.contractorId}`);
    }

    return updated;
  }

  async getBidsByRfqWithContractors(rfqId: string): Promise<Array<{ bid: Bid; contractor: Contractor }>> {
    return await cache.cached(
      `bids:rfq:${rfqId}:with_contractors`,
      async () => {
        const bidResults = await this.db
          .select({
            bid: bids,
            contractor: contractors,
          })
          .from(bids)
          .innerJoin(contractors, eq(bids.contractorId, contractors.id))
          .where(eq(bids.rfqId, rfqId))
          .orderBy(desc(bids.submittedAt), desc(bids.createdAt));

        return bidResults;
      },
      CACHE_TTL.BID_SUMMARY
    );
  }

  // Bid document operations
  async createBidDocument(document: typeof bidDocuments.$inferInsert): Promise<BidDocument> {
    const [created] = await this.db.insert(bidDocuments).values(document).returning();
    return created;
  }

  async getBidDocument(documentId: string): Promise<BidDocument | undefined> {
    const [document] = await this.db.select().from(bidDocuments).where(eq(bidDocuments.id, documentId));
    return document;
  }

  async getBidDocuments(bidId: string): Promise<BidDocument[]> {
    return await this.db.select().from(bidDocuments).where(eq(bidDocuments.bidId, bidId));
  }

  // Contractor favorites operations
  async addContractorToFavorites(userId: string, contractorId: string, notes?: string): Promise<ContractorFavorite> {


    const [favorite] = await this.db
      .insert(contractorFavorites)
      .values({
        userId: userId,
        contractorId: contractorId,
        notes: notes || null,
      })
      .returning();
    return favorite;
  }

  async removeContractorFromFavorites(userId: string, contractorId: string): Promise<boolean> {
    const result = await this.db
      .delete(contractorFavorites)
      .where(
        and(
          eq(contractorFavorites.userId, userId),
          eq(contractorFavorites.contractorId, contractorId)
        )
      );
    return result.rowCount > 0;
  }

  async getFavoriteContractors(userId: string): Promise<ContractorFavorite[]> {
    const results = await this.db
      .select({
        favoriteId: contractorFavorites.id,
        contractorId: contractorFavorites.contractorId,
        notes: contractorFavorites.notes,
        favoriteCreatedAt: contractorFavorites.createdAt,
        // Contractor fields
        id: contractors.id,
        userId: contractors.userId,
        companyName: contractors.companyName,
        primaryContactName: contractors.primaryContactName,
        primaryContactEmail: contractors.primaryContactEmail,
        primaryContactPhone: contractors.primaryContactPhone,
        businessAddress: contractors.primaryAddress,
        tradeTypes: contractors.tradeTypes,
        verified: contractors.isApproved,
        createdAt: contractors.createdAt,
        updatedAt: contractors.updatedAt,
      })
      .from(contractorFavorites)
      .innerJoin(contractors, eq(contractorFavorites.contractorId, contractors.id))
      .where(eq(contractorFavorites.userId, userId));

    return results.map(r => ({
      id: r.favoriteId,
      contractorId: r.contractorId,
      notes: r.notes,
      createdAt: r.favoriteCreatedAt,
      contractor: {
        id: r.id,
        userId: r.userId,
        companyName: r.companyName,
        primaryContactName: r.primaryContactName,
        primaryContactEmail: r.primaryContactEmail,
        primaryContactPhone: r.primaryContactPhone,
        businessAddress: r.businessAddress,
        tradeTypes: r.tradeTypes,
        verified: r.verified,
        createdAt: r.createdAt,
        updatedAt: r.updatedAt,
      }
    }));
  }

  // RFQ distribution operations
  async distributeRfq(rfqId: string, contractorIds: string[], method: "favorites" | "broadcast"): Promise<RfqDistribution[]> {
    const distributions = contractorIds.map(contractorId => ({
      rfqId,
      contractorId,
      distributionMethod: method,
      distributedAt: new Date(),
    }));

    return await this.db.insert(rfqDistribution).values(distributions).returning();
  }

  async markRfqViewed(rfqId: string, contractorId: string): Promise<void> {
    await this.db
      .update(rfqDistribution)
      .set({ viewedAt: new Date() })
      .where(
        and(
          eq(rfqDistribution.rfqId, rfqId),
          eq(rfqDistribution.contractorId, contractorId)
        )
      );
  }

  async declineRfq(rfqId: string, contractorId: string, reason?: string): Promise<void> {
    await this.db
      .update(rfqDistribution)
      .set({ 
        declinedAt: new Date(),
        declineReason: reason 
      })
      .where(
        and(
          eq(rfqDistribution.rfqId, rfqId),
          eq(rfqDistribution.contractorId, contractorId)
        )
      );
  }

  async getRfqDistributions(rfqId: string): Promise<RfqDistribution[]> {
    return await this.db.select().from(rfqDistribution).where(eq(rfqDistribution.rfqId, rfqId));
  }

  async getContractorRfqs(contractorId: string): Promise<Rfq[]> {
    const results = await this.db
      .select({
        rfq: rfqs,
      })
      .from(rfqDistribution)
      .leftJoin(rfqs, eq(rfqDistribution.rfqId, rfqs.id))
      .where(eq(rfqDistribution.contractorId, contractorId));

    return results.map(r => r.rfq).filter(Boolean);
  }

  async getContractorRfqDistributions(contractorId: string): Promise<any[]> {
    const results = await this.db
      .select({
        id: rfqDistribution.id,
        rfqId: rfqDistribution.rfqId,
        sentAt: rfqDistribution.sentAt,
        viewedAt: rfqDistribution.viewedAt,
        declinedAt: rfqDistribution.declinedAt,
        declineReason: rfqDistribution.declineReason,
        rfq: {
          id: rfqs.id,
          projectName: rfqs.projectName,
          projectLocation: rfqs.projectLocation,
          description: rfqs.description,
          tradeCategory: rfqs.tradeCategory,
          dueDate: rfqs.dueDate,
          status: rfqs.status,
          extractedData: rfqs.extractedData,
          createdAt: rfqs.createdAt,
        }
      })
      .from(rfqDistribution)
      .innerJoin(rfqs, eq(rfqDistribution.rfqId, rfqs.id))
      .where(eq(rfqDistribution.contractorId, contractorId))
      .orderBy(rfqs.dueDate);

    return results.map(r => ({
      id: r.id,
      rfqId: r.rfqId,
      sentAt: r.sentAt,
      viewedAt: r.viewedAt,
      declinedAt: r.declinedAt,
      declineReason: r.declineReason,
      rfq: r.rfq
    }));
  }

  // Dashboard statistics
  async getDashboardStats(): Promise<{
    activeRfqs: number;
    totalBids: number;
    avgResponseTime: number;
    successRate: number;
  }> {
    const [activeRfqsResult] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(rfqs)
      .where(eq(rfqs.status, "Active"));

    const [totalBidsResult] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(bids);

    return {
      activeRfqs: activeRfqsResult.count,
      totalBids: totalBidsResult.count,
      avgResponseTime: 4.2, // This would need more complex calculation
      successRate: 87, // This would need more complex calculation
    };
  }

  // Forecast materials operations
  async getForecastMaterials(): Promise<ForecastMaterial[]> {
    return await this.db.select().from(forecastMaterials).orderBy(forecastMaterials.category, forecastMaterials.name);
  }

  async createForecastMaterial(material: InsertForecastMaterial): Promise<ForecastMaterial> {
    const [newMaterial] = await this.db
      .insert(forecastMaterials)
      .values(material)
      .returning();
    return newMaterial;
  }

  async updateForecastMaterial(id: string, material: Partial<InsertForecastMaterial>): Promise<ForecastMaterial | undefined> {
    const [updatedMaterial] = await this.db
      .update(forecastMaterials)
      .set({ ...material, lastUpdated: new Date() })
      .where(eq(forecastMaterials.id, id))
      .returning();
    return updatedMaterial;
  }

  async deleteForecastMaterial(id: string): Promise<boolean> {
    const result = await this.db
      .delete(forecastMaterials)
      .where(eq(forecastMaterials.id, id));
    return (result.rowCount || 0) > 0;
  }

  // API Key operations
  async createApiKey(apiKey: InsertApiKey): Promise<ApiKey> {
    const [newApiKey] = await this.db
      .insert(apiKeys)
      .values(apiKey)
      .returning();
    return newApiKey;
  }

  async getApiKey(id: string): Promise<ApiKey | undefined> {
    const [apiKey] = await this.db
      .select()
      .from(apiKeys)
      .where(eq(apiKeys.id, id));
    return apiKey;
  }

  async getApiKeyByHash(keyHash: string): Promise<ApiKey | undefined> {
    const [apiKey] = await this.db
      .select()
      .from(apiKeys)
      .where(and(
        eq(apiKeys.keyHash, keyHash),
        eq(apiKeys.isActive, true),
        or(
          eq(apiKeys.expiresAt, null),
          sql`${apiKeys.expiresAt} > NOW()`
        )
      ));
    return apiKey;
  }

  async getApiKeysByUserId(userId: string): Promise<ApiKey[]> {
    return await this.db
      .select()
      .from(apiKeys)
      .where(eq(apiKeys.userId, userId))
      .orderBy(desc(apiKeys.createdAt));
  }

  async updateApiKey(id: string, updates: Partial<InsertApiKey>): Promise<ApiKey | undefined> {
    const [updatedApiKey] = await this.db
      .update(apiKeys)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(apiKeys.id, id))
      .returning();
    return updatedApiKey;
  }

  async deleteApiKey(id: string): Promise<boolean> {
    const result = await this.db
      .delete(apiKeys)
      .where(eq(apiKeys.id, id));
    return (result.rowCount || 0) > 0;
  }

  async updateApiKeyLastUsed(id: string): Promise<void> {
    await this.db
      .update(apiKeys)
      .set({ lastUsedAt: new Date() })
      .where(eq(apiKeys.id, id));
  }

  // API Key usage operations
  async recordApiKeyUsage(apiKeyId: string, endpoint: string, method: string): Promise<void> {
    const today = new Date().toISOString().split('T')[0];

    // Try to update existing usage record for today
    const [existingUsage] = await this.db
      .select()
      .from(apiKeyUsage)
      .where(and(
        eq(apiKeyUsage.apiKeyId, apiKeyId),
        eq(apiKeyUsage.endpoint, endpoint),
        eq(apiKeyUsage.method, method),
        eq(apiKeyUsage.requestDate, today)
      ));

    if (existingUsage) {
      // Update existing record
      await this.db
        .update(apiKeyUsage)
        .set({
          requestCount: sql`${apiKeyUsage.requestCount} + 1`,
          lastRequestAt: new Date()
        })
        .where(eq(apiKeyUsage.id, existingUsage.id));
    } else {
      // Create new record
      await this.db
        .insert(apiKeyUsage)
        .values({
          apiKeyId,
          endpoint,
          method,
          requestCount: 1,
          requestDate: today
        });
    }
  }

  async getApiKeyUsageToday(apiKeyId: string, date: string): Promise<ApiKeyUsage[]> {
    return await this.db
      .select()
      .from(apiKeyUsage)
      .where(and(
        eq(apiKeyUsage.apiKeyId, apiKeyId),
        eq(apiKeyUsage.requestDate, date)
      ));
  }

  async getApiKeyUsageStats(apiKeyId: string): Promise<ApiKeyUsage[]> {
    return await this.db
      .select()
      .from(apiKeyUsage)
      .where(eq(apiKeyUsage.apiKeyId, apiKeyId))
      .orderBy(desc(apiKeyUsage.lastRequestAt));
  }

  // Organization operations
  async createOrganization(organizationData: InsertOrganization): Promise<Organization> {
    const [organization] = await this.db
      .insert(organizations)
      .values(organizationData)
      .returning();
    return organization;
  }

  async getOrganization(id: string): Promise<Organization | undefined> {
    const [organization] = await this.db
      .select()
      .from(organizations)
      .where(eq(organizations.id, id));
    return organization;
  }

  async getOrganizationBySlug(slug: string): Promise<Organization | undefined> {
    const [organization] = await this.db
      .select()
      .from(organizations)
      .where(eq(organizations.slug, slug));
    return organization;
  }

  async updateOrganization(id: string, updates: Partial<InsertOrganization>): Promise<Organization | undefined> {
    const [updatedOrganization] = await this.db
      .update(organizations)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(organizations.id, id))
      .returning();
    return updatedOrganization;
  }

  async getOrganizations(): Promise<Organization[]> {
    return await this.db
      .select()
      .from(organizations)
      .where(eq(organizations.isActive, true))
      .orderBy(desc(organizations.createdAt));
  }

  // Role management operations
  async getUsersByOrganization(organizationId: string): Promise<User[]> {
    return await this.db
      .select()
      .from(users)
      .where(and(
        eq(users.organizationId, organizationId),
        eq(users.isActive, true)
      ))
      .orderBy(desc(users.createdAt));
  }

  async updateUserRole(userId: string, newRole: string, changedBy: string, reason?: string, request?: any): Promise<User | undefined> {
    // Get current user data to track previous role
    const currentUser = await this.getUser(userId);
    if (!currentUser) {
      return undefined;
    }

    // Update user role
    const [updatedUser] = await this.db
      .update(users)
      .set({ 
        role: newRole as any,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId))
      .returning();

    // Create audit log entry
    if (updatedUser) {
      await this.createRoleAuditLog({
        targetUserId: userId,
        changedByUserId: changedBy,
        organizationId: currentUser.organizationId,
        previousRole: currentUser.role as any,
        newRole: newRole as any,
        reason,
        ipAddress: request?.ip,
        userAgent: request?.get?.('User-Agent'),
      });

      // Invalidate cache
      cache.invalidatePattern(`user:${userId}*`);
    }

    return updatedUser;
  }

  async getAllUsers(): Promise<User[]> {
    return await this.db
      .select()
      .from(users)
      .where(eq(users.isActive, true))
      .orderBy(desc(users.createdAt));
  }

  // Audit operations
  async createRoleAuditLog(auditData: InsertRoleAuditLog): Promise<RoleAuditLog> {
    const [auditLog] = await this.db
      .insert(roleAuditLog)
      .values(auditData)
      .returning();
    return auditLog;
  }

  async createAccessAuditLog(auditData: InsertAccessAuditLog): Promise<AccessAuditLog> {
    const [auditLog] = await this.db
      .insert(accessAuditLog)
      .values(auditData)
      .returning();
    return auditLog;
  }

  async getRoleAuditLogs(organizationId?: string): Promise<RoleAuditLog[]> {
    let query = this.db.select().from(roleAuditLog);

    if (organizationId) {
      query = query.where(eq(roleAuditLog.organizationId, organizationId)) as any;
    }

    return await query.orderBy(desc(roleAuditLog.createdAt));
  }

  async getAccessAuditLogs(organizationId?: string): Promise<AccessAuditLog[]> {
    let query = this.db.select().from(accessAuditLog);

    if (organizationId) {
      query = query.where(eq(accessAuditLog.organizationId, organizationId)) as any;
    }

    return await query.orderBy(desc(accessAuditLog.createdAt));
  }

  // Waitlist management
  async addToWaitlist(data: any): Promise<any> {
    const [waitlistEntry] = await this.db
      .insert(waitlist)
      .values({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        companyName: data.companyName,
        jobTitle: data.jobTitle || null,
        source: data.source || "landing_page"
      })
      .returning();
    return waitlistEntry;
  }

  async getWaitlistCount(): Promise<number> {
    try {
      const result = await this.db.select({ count: sql<number>`count(*)` }).from(waitlist);
      return result[0]?.count || 0;
    } catch (error) {
      console.error("Error getting waitlist count:", error);
      throw error;
    }
  }

  async checkEmailInWaitlist(email: string): Promise<boolean> {
    const result = await this.db
      .select()
      .from(waitlist)
      .where(eq(waitlist.email, email))
      .limit(1);
    return result.length > 0;
  }
  // **ENHANCED BID DATA OPERATIONS** - Phase 3 Implementation
  async createBidLineItem(lineItem: typeof bidLineItems.$inferInsert): Promise<typeof bidLineItems.$inferSelect> {
    console.log("🔧 Creating bid line item:", lineItem.costCode, lineItem.description);
    const result = await db.insert(bidLineItems).values(lineItem).returning();
    return result[0];
  }

  async getBidLineItems(bidId: string): Promise<typeof bidLineItems.$inferSelect[]> {
    const cacheKey = `${CACHE_KEYS.BID_LINE_ITEMS}:${bidId}`;
    return cache.cached(cacheKey, async () => {
      return await db
        .select()
        .from(bidLineItems)
        .where(eq(bidLineItems.bidId, bidId))
        .orderBy(bidLineItems.costCode);
    }, CACHE_TTL.BIDS);
  }

  async createBidInclusionExclusion(scopeItem: typeof bidInclusionsExclusions.$inferInsert): Promise<typeof bidInclusionsExclusions.$inferSelect> {
    console.log("📋 Creating scope definition:", scopeItem.itemType, scopeItem.description);
    const result = await db.insert(bidInclusionsExclusions).values(scopeItem).returning();
    return result[0];
  }

  async getBidInclusionsExclusions(bidId: string): Promise<typeof bidInclusionsExclusions.$inferSelect[]> {
    const cacheKey = `${CACHE_KEYS.BID_SCOPE}:${bidId}`;
    return cache.cached(cacheKey, async () => {
      return await db
        .select()
        .from(bidInclusionsExclusions)
        .where(eq(bidInclusionsExclusions.bidId, bidId))
        .orderBy(bidInclusionsExclusions.itemType, bidInclusionsExclusions.category);
    }, CACHE_TTL.BIDS);
  }

  // User feedback operations
  async createUserFeedback(feedback: InsertUserFeedback): Promise<UserFeedback> {
    console.log("📝 Creating user feedback:", feedback.type, feedback.message?.substring(0, 50) + "...");
    const [result] = await this.db.insert(userFeedback).values(feedback).returning();
    return result;
  }

  async getUserFeedback(): Promise<UserFeedback[]> {
    console.log("📝 Fetching all user feedback");
    return await this.db
      .select({
        id: userFeedback.id,
        userId: userFeedback.userId,
        type: userFeedback.type,
        message: userFeedback.message,
        status: userFeedback.status,
        priority: userFeedback.priority,
        adminNotes: userFeedback.adminNotes,
        resolvedAt: userFeedback.resolvedAt,
        resolvedBy: userFeedback.resolvedBy,
        createdAt: userFeedback.createdAt,
        updatedAt: userFeedback.updatedAt,
        user: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
        }
      })
      .from(userFeedback)
      .leftJoin(users, eq(userFeedback.userId, users.id))
      .orderBy(desc(userFeedback.createdAt));
  }

  async updateUserFeedback(id: string, updates: Partial<InsertUserFeedback>): Promise<UserFeedback | undefined> {
    console.log("📝 Updating user feedback:", id, updates);
    const [result] = await this.db
      .update(userFeedback)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(userFeedback.id, id))
      .returning();
    return result;
  }

  async getUserFeedbackStats(): Promise<{ total: number; bugs: number; suggestions: number; open: number; resolved: number }> {
    console.log("📊 Fetching user feedback statistics");
    const feedbacks = await this.db.select({
      type: userFeedback.type,
      status: userFeedback.status,
    }).from(userFeedback);

    const stats = feedbacks.reduce((acc, feedback) => {
      acc.total++;
      if (feedback.type === 'bug') acc.bugs++;
      if (feedback.type === 'suggestion') acc.suggestions++;
      if (feedback.status === 'open') acc.open++;
      if (feedback.status === 'resolved') acc.resolved++;
      return acc;
    }, { total: 0, bugs: 0, suggestions: 0, open: 0, resolved: 0 });

    return stats;
  }

  // Business audit log operations
  async createBusinessAuditLog(auditData: InsertBusinessAuditLog): Promise<BusinessAuditLog> {
    const [auditLog] = await this.db
      .insert(businessAuditLog)
      .values(auditData)
      .returning();
    return auditLog;
  }

  async getBusinessAuditLogs(criteria: {
    eventType?: string;
    userId?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  } = {}): Promise<BusinessAuditLog[]> {
    let query = this.db
      .select({
        id: businessAuditLog.id,
        userId: businessAuditLog.userId,
        eventType: businessAuditLog.eventType,
        eventData: businessAuditLog.eventData,
        resourceId: businessAuditLog.resourceId,
        ipAddress: businessAuditLog.ipAddress,
        userAgent: businessAuditLog.userAgent,
        createdAt: businessAuditLog.createdAt,
        user: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
        }
      })
      .from(businessAuditLog)
      .leftJoin(users, eq(businessAuditLog.userId, users.id));

    // Apply filters
    const conditions = [];
    if (criteria.eventType) {
      conditions.push(eq(businessAuditLog.eventType, criteria.eventType));
    }
    if (criteria.userId) {
      conditions.push(eq(businessAuditLog.userId, criteria.userId));
    }
    if (criteria.resourceId) {
      conditions.push(eq(businessAuditLog.resourceId, criteria.resourceId));
    }
    if (criteria.startDate) {
      conditions.push(sql`${businessAuditLog.createdAt} >= ${criteria.startDate}`);
    }
    if (criteria.endDate) {
      conditions.push(sql`${businessAuditLog.createdAt} <= ${criteria.endDate}`);
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions)) as any;
    }

    return await query
      .orderBy(desc(businessAuditLog.createdAt))
      .limit(criteria.limit || 100);
  }

  // Helper methods for backfill operation
  async getAllRfqs(): Promise<RFQ[]> {
    return await this.db.select().from(rfqs).orderBy(desc(rfqs.createdAt));
  }

  async getAllRfqDocuments(): Promise<RFQDocument[]> {
    return await this.db.select().from(rfqDocuments).orderBy(desc(rfqDocuments.createdAt));
  }

  async getAllBids(): Promise<Bid[]> {
    return await this.db.select().from(bids).orderBy(desc(bids.submittedAt));
  }

  // Notification operations
  async createNotification(notification: InsertNotification): Promise<Notification> {
    console.log("🔔 Creating notification:", notification.type, "for user:", notification.userId);
    const [result] = await this.db.insert(notifications).values(notification).returning();
    
    // Invalidate user notifications cache
    const cacheKey = `user_notifications:${notification.userId}`;
    cache.delete(cacheKey);
    
    return result;
  }

  async getNotifications(userId: string, limit: number = 50): Promise<Notification[]> {
    const cacheKey = `user_notifications:${userId}`;
    return cache.get(cacheKey, async () => {
      console.log("📬 Fetching notifications for user:", userId);
      return await this.db
        .select()
        .from(notifications)
        .where(eq(notifications.userId, userId))
        .orderBy(desc(notifications.createdAt))
        .limit(limit);
    }, 300); // 5 minute cache
  }

  async markNotificationRead(notificationId: string, userId: string): Promise<boolean> {
    console.log("👁️ Marking notification read:", notificationId);
    
    const result = await this.db
      .update(notifications)
      .set({ 
        readAt: new Date(),
        deliveredAt: new Date() 
      })
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.userId, userId)
      ))
      .returning();

    // Invalidate cache
    const cacheKey = `user_notifications:${userId}`;
    cache.delete(cacheKey);
    
    return result.length > 0;
  }

  async getUserNotificationPreferences(userId: string): Promise<NotificationPreferences[]> {
    console.log("⚙️ Fetching notification preferences for user:", userId);
    try {
      const result = await this.db
        .select()
        .from(notificationPreferences)
        .where(eq(notificationPreferences.userId, userId));
      console.log("📋 Database query result:", result);
      return result;
    } catch (error) {
      console.error("💥 Error fetching notification preferences:", error);
      return [];
    }
  }

  async getNotificationPreference(userId: string, type: string): Promise<NotificationPreferences | undefined> {
    try {
      const [preference] = await this.db
        .select()
        .from(notificationPreferences)
        .where(and(
          eq(notificationPreferences.userId, userId),
          eq(notificationPreferences.type, type)
        ));
      return preference;
    } catch (error) {
      console.error("Error getting notification preference:", error);
      return undefined;
    }
  }

  async upsertNotificationPreference(preference: InsertNotificationPreferences): Promise<NotificationPreferences> {
    try {
      const [result] = await this.db
        .insert(notificationPreferences)
        .values(preference)
        .onConflictDoUpdate({
          target: [notificationPreferences.userId, notificationPreferences.type],
          set: {
            inAppEnabled: preference.inAppEnabled,
            emailEnabled: preference.emailEnabled,
            smsEnabled: preference.smsEnabled,
            frequency: preference.frequency,
            updatedAt: new Date(),
          }
        })
        .returning();
      
      // Invalidate cache
      const cacheKey = `user_preferences:${preference.userId}`;
      cache.delete(cacheKey);
      
      return result;
    } catch (error) {
      console.error("Error upserting notification preference:", error);
      throw error;
    }
  }

  async updateNotificationPreferences(
    userId: string, 
    type: string, 
    preferences: Partial<NotificationPreferences>
  ): Promise<NotificationPreferences> {
    console.log("🔧 Updating notification preferences:", userId, type);
    
    const [result] = await this.db
      .insert(notificationPreferences)
      .values({
        userId,
        type,
        ...preferences,
        updatedAt: new Date(),
      } as any)
      .onConflictDoUpdate({
        target: [notificationPreferences.userId, notificationPreferences.type],
        set: {
          ...preferences,
          updatedAt: new Date(),
        },
      })
      .returning();

    // Invalidate cache
    const cacheKey = `user_preferences:${userId}`;
    cache.delete(cacheKey);
    
    return result;
  }

  async createNotificationDelivery(delivery: InsertNotificationDelivery): Promise<NotificationDelivery> {
    console.log("📤 Creating notification delivery:", delivery.deliveryMethod, "to", delivery.recipient);
    const [result] = await this.db.insert(notificationDeliveries).values(delivery).returning();
    return result;
  }

  async updateDeliveryStatus(deliveryId: string, status: string, response?: any): Promise<boolean> {
    console.log("📊 Updating delivery status:", deliveryId, status);
    
    const result = await this.db
      .update(notificationDeliveries)
      .set({ 
        status: status as any,
        deliveredAt: status === 'delivered' ? new Date() : undefined,
        providerResponse: response ? JSON.stringify(response) : undefined,
      })
      .where(eq(notificationDeliveries.id, deliveryId))
      .returning();

    return result.length > 0;
  }
}

export const storage = new DatabaseStorage();