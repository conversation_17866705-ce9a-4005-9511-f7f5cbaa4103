/**
 * Manual test runner for PDF extractor
 * Run with: tsx server/test-pdf-extractor.js
 */

import { testUnifiedPDFExtractor } from './services/core/testPdfExtractor.js';

async function runTest() {
  console.log('🚀 Starting PDF Extractor Test...\n');
  
  const result = await testUnifiedPDFExtractor();
  
  console.log('\n📊 Test Summary:');
  console.log('================');
  console.log('Success:', result.success);
  if (result.success) {
    console.log('Extracted Length:', result.extractedLength);
    console.log('Method Used:', result.method);
    console.log('Processing Time:', result.processingTime + 'ms');
  } else {
    console.log('Error:', result.message);
    if (result.extractedText) {
      console.log('Extracted Text:', result.extractedText);
    }
  }
  
  process.exit(result.success ? 0 : 1);
}

runTest().catch(console.error);