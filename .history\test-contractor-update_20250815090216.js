const { storage } = require('./server/storage.ts');

async function testContractorUpdate() {
  try {
    console.log('🧪 Testing contractor profile update...');
    
    // Test data
    const testUserId = 'user_test_123';
    const testContractorData = {
      companyName: 'Test Company Updated',
      primaryContactName: '<PERSON> Updated',
      primaryContactEmail: '<EMAIL>',
      primaryContactPhone: '555-0123',
      businessAddress: '123 Test St, Updated City, ST 12345',
      tradeTypes: ['electrical', 'plumbing'],
      yearsInBusiness: 10,
      licenseNumber: 'LIC123456',
      generalLiability: 1000000,
      bondingSingle: 500000,
      bondingAggregate: 1000000,
      organizationId: '4ac05788-dc19-4be1-a580-de8441e822ab'
    };
    
    // First, create a test contractor
    console.log('📝 Creating test contractor...');
    const createdContractor = await storage.createContractor({
      ...testContractorData,
      userId: testUserId,
      companyName: 'Test Company Original'
    });
    console.log('✅ Created contractor:', createdContractor.id);
    
    // Now try to update it
    console.log('🔄 Updating contractor...');
    const updatedContractor = await storage.updateContractor(createdContractor.id, {
      companyName: 'Test Company Updated',
      primaryContactName: 'John Doe Updated'
    });
    
    if (updatedContractor) {
      console.log('✅ Update successful:', updatedContractor.companyName);
      console.log('📊 Updated fields:', {
        id: updatedContractor.id,
        companyName: updatedContractor.companyName,
        primaryContactName: updatedContractor.primaryContactName
      });
    } else {
      console.log('❌ Update failed - no contractor returned');
    }
    
    // Clean up
    console.log('🧹 Cleaning up test data...');
    // Note: We don't have a delete method, so we'll leave the test data
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

testContractorUpdate();
