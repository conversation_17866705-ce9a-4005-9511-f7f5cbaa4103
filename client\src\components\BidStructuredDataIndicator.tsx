import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Package, CheckCircle, User, FileText, AlertCircle } from "lucide-react";

interface BidStructuredDataIndicatorProps {
  hasStructuredData?: boolean;
  lineItemsCount?: number;
  scopeItemsCount?: number;
  hasContactInfo?: boolean;
  dataQualityScore?: number;
  compact?: boolean;
}

export function BidStructuredDataIndicator({ 
  hasStructuredData = false,
  lineItemsCount = 0,
  scopeItemsCount = 0,
  hasContactInfo = false,
  dataQualityScore = 0,
  compact = false
}: BidStructuredDataIndicatorProps) {
  
  const getQualityColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
  };

  const getQualityLabel = (score: number) => {
    if (score >= 80) return 'High Quality';
    if (score >= 60) return 'Good Quality';
    if (score >= 40) return 'Fair Quality';
    return 'Basic Data';
  };

  if (compact) {
    return (
      <TooltipProvider>
        <div className="flex items-center gap-1">
          {hasStructuredData ? (
            <Tooltip>
              <TooltipTrigger>
                <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
                  <Package className="h-3 w-3 mr-1" />
                  Structured
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm">
                  <p className="font-medium">Structured Data Available</p>
                  <ul className="mt-1 space-y-1">
                    {lineItemsCount > 0 && <li>• {lineItemsCount} line items</li>}
                    {scopeItemsCount > 0 && <li>• {scopeItemsCount} scope items</li>}
                    {hasContactInfo && <li>• Contact information</li>}
                  </ul>
                </div>
              </TooltipContent>
            </Tooltip>
          ) : (
            <Tooltip>
              <TooltipTrigger>
                <Badge variant="secondary" className="text-xs">
                  <FileText className="h-3 w-3 mr-1" />
                  Document Only
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">AI-extracted data only. No structured breakdown available.</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          {dataQualityScore > 0 && (
            <Tooltip>
              <TooltipTrigger>
                <Badge className={getQualityColor(dataQualityScore)} variant="outline">
                  {dataQualityScore}%
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">{getQualityLabel(dataQualityScore)}</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </TooltipProvider>
    );
  }

  return (
    <div className="space-y-2">
      {/* Main Indicator */}
      <div className="flex items-center gap-2">
        {hasStructuredData ? (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <Package className="h-4 w-4 mr-1" />
            Structured Data Available
          </Badge>
        ) : (
          <Badge variant="secondary">
            <FileText className="h-4 w-4 mr-1" />
            Document-Based Submission
          </Badge>
        )}
        
        {dataQualityScore > 0 && (
          <Badge className={getQualityColor(dataQualityScore)}>
            Quality: {dataQualityScore}%
          </Badge>
        )}
      </div>

      {/* Detailed Breakdown */}
      {hasStructuredData && (
        <div className="flex flex-wrap gap-1">
          {lineItemsCount > 0 && (
            <Badge variant="outline" className="text-xs">
              <Package className="h-3 w-3 mr-1" />
              {lineItemsCount} Line Items
            </Badge>
          )}
          
          {scopeItemsCount > 0 && (
            <Badge variant="outline" className="text-xs">
              <CheckCircle className="h-3 w-3 mr-1" />
              {scopeItemsCount} Scope Items
            </Badge>
          )}
          
          {hasContactInfo && (
            <Badge variant="outline" className="text-xs">
              <User className="h-3 w-3 mr-1" />
              Contact Info
            </Badge>
          )}
          
          {!lineItemsCount && !scopeItemsCount && !hasContactInfo && (
            <Badge variant="outline" className="text-xs">
              <AlertCircle className="h-3 w-3 mr-1" />
              Minimal Structure
            </Badge>
          )}
        </div>
      )}

      {/* Recommendations */}
      {!hasStructuredData && (
        <p className="text-xs text-muted-foreground">
          Consider requesting a detailed breakdown from the contractor for better analysis.
        </p>
      )}
    </div>
  );
}