/* React Toastify Theme Integration */
:root {
  --toastify-color-light: hsl(var(--background));
  --toastify-color-dark: hsl(var(--foreground));
  --toastify-color-info: hsl(var(--primary));
  --toastify-color-success: hsl(142 76% 36%);
  --toastify-color-warning: hsl(32 95% 44%);
  --toastify-color-error: hsl(var(--destructive));
  --toastify-text-color-light: hsl(var(--foreground));
  --toastify-text-color-dark: hsl(var(--background));
}

.Toastify__toast {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) - 2px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.Toastify__toast--success {
  background: hsl(var(--background));
  border-color: hsl(142 76% 36%);
}

.Toastify__toast--error {
  background: hsl(var(--background));
  border-color: hsl(var(--destructive));
}

.Toastify__toast--warning {
  background: hsl(var(--background));
  border-color: hsl(32 95% 44%);
}

.Toastify__toast--info {
  background: hsl(var(--background));
  border-color: hsl(var(--primary));
}

.Toastify__progress-bar {
  background: hsl(var(--primary));
}

.Toastify__progress-bar--success {
  background: hsl(142 76% 36%);
}

.Toastify__progress-bar--error {
  background: hsl(var(--destructive));
}

.Toastify__progress-bar--warning {
  background: hsl(32 95% 44%);
}

.Toastify__close-button {
  color: hsl(var(--muted-foreground));
}

.Toastify__close-button:hover {
  color: hsl(var(--foreground));
}
