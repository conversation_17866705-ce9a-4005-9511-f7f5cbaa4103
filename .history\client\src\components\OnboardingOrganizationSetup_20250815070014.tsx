import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useOrganization } from "@clerk/clerk-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  Building, 
  Hammer, 
  ClipboardList, 
  BarChart3, 
  FileText,
  CheckCircle2,
  ArrowRight,
  Info
} from "lucide-react";

const organizationFormSchema = z.object({
  name: z.string().min(1, "Organization name is required"),
  slug: z.string()
    .min(3, "Slug must be at least 3 characters")
    .max(50, "Slug must be less than 50 characters")
    .regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens"),
  userClassification: z.enum(["general_contractor", "contractor"], {
    required_error: "Please select your classification",
  }),
});

interface OnboardingOrganizationSetupProps {
  onComplete: () => void;
}

export default function OnboardingOrganizationSetup({ onComplete }: OnboardingOrganizationSetupProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { organization } = useOrganization();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof organizationFormSchema>>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues: {
      name: "",
      slug: "",
      userClassification: undefined,
    },
  });

  const watchedName = form.watch("name");
  const watchedClassification = form.watch("userClassification");

  // Auto-generate slug from organization name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-+|-+$/g, "");
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    form.setValue("name", name);
    if (name) {
      form.setValue("slug", generateSlug(name));
    }
  };

  const onSubmit = async (data: z.infer<typeof organizationFormSchema>) => {
    setIsSubmitting(true);
    
    try {
      // First create the organization via Clerk
      const clerkResponse = await fetch("/api/organizations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: data.name,
          slug: data.slug,
        }),
      });

      if (!clerkResponse.ok) {
        throw new Error("Failed to create organization");
      }

      // Update user classification in our database
      const userResponse = await apiRequest('PATCH', '/api/auth/user', {
        userClassification: data.userClassification,
      });

      if (!userResponse.ok) {
        throw new Error("Failed to update user classification");
      }

      toast({
        title: "Organization Created",
        description: "Your organization has been set up successfully!",
      });

      onComplete();
    } catch (error) {
      console.error("Error creating organization:", error);
      toast({
        title: "Error",
        description: "Failed to create organization. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const classificationFeatures = {
    general_contractor: {
      title: "General Contractor",
      description: "Oversee projects and manage subcontractors",
      icon: <Building className="h-5 w-5" />,
      features: [
        "Create and manage RFQs",
        "Distribute RFQs to contractors", 
        "Review and compare bids",
        "Manage contractor network",
        "Project analytics and reporting"
      ],
      sidebarItems: [
        { icon: <ClipboardList className="h-4 w-4" />, label: "My RFQs" },
        { icon: <BarChart3 className="h-4 w-4" />, label: "Bid Management" },
        { icon: <Users className="h-4 w-4" />, label: "Contractors" },
        { icon: <FileText className="h-4 w-4" />, label: "Analytics" }
      ]
    },
    contractor: {
      title: "Contractor",
      description: "Submit bids and manage your contracting business",
      icon: <Hammer className="h-5 w-5" />,
      features: [
        "Browse available RFQs",
        "Submit competitive bids",
        "Track bid status",
        "Manage your profile",
        "View limited analytics"
      ],
      sidebarItems: [
        { icon: <ClipboardList className="h-4 w-4" />, label: "Available RFQs" },
        { icon: <FileText className="h-4 w-4" />, label: "My Bids" },
        { icon: <BarChart3 className="h-4 w-4" />, label: "Analytics" }
      ]
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Complete Your Setup</h1>
          <Badge variant="outline">Step 2 of 3</Badge>
        </div>
        <Progress value={66} className="h-2" />
        <div className="flex justify-between text-sm text-muted-foreground mt-2">
          <span className="flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3 text-green-600" />
            Terms Accepted
          </span>
          <span className="flex items-center gap-1">
            <div className="h-3 w-3 bg-primary rounded-full animate-pulse" />
            Organization Setup
          </span>
          <span className="text-muted-foreground">Profile Setup</span>
        </div>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Organization Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Organization Details
            </CardTitle>
            <CardDescription>
              Set up your organization to get started with Bidaible
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Organization Name *</Label>
              <Input
                id="name"
                {...form.register("name")}
                onChange={handleNameChange}
                placeholder="Enter your company name"
                className="mt-1"
              />
              {form.formState.errors.name && (
                <p className="text-red-500 text-sm mt-1">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="slug">Organization Slug *</Label>
              <Input
                id="slug"
                {...form.register("slug")}
                placeholder="your-organization-slug"
                className="mt-1 font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground mt-1">
                This will be used in URLs: bidaible.com/org/{form.watch("slug") || "your-slug"}
              </p>
              {form.formState.errors.slug && (
                <p className="text-red-500 text-sm mt-1">
                  {form.formState.errors.slug.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* User Classification */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Your Classification
            </CardTitle>
            <CardDescription>
              This determines what features and interface you'll see in Bidaible
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup
              onValueChange={(value) => form.setValue("userClassification", value as any)}
              value={watchedClassification}
              className="space-y-4"
            >
              {(Object.keys(classificationFeatures) as Array<keyof typeof classificationFeatures>).map((type) => {
                const config = classificationFeatures[type];
                const isSelected = watchedClassification === type;

                return (
                  <div
                    key={type}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      isSelected
                        ? "border-primary bg-primary/5 ring-2 ring-primary ring-offset-2"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => form.setValue("userClassification", type)}
                  >
                    <div className="flex items-start space-x-3">
                      <RadioGroupItem value={type} id={type} className="mt-1" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {config.icon}
                          <Label htmlFor={type} className="text-base font-semibold cursor-pointer">
                            {config.title}
                          </Label>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {config.description}
                        </p>

                        {isSelected && (
                          <div className="space-y-3 animate-in slide-in-from-top-2">
                            {/* Features */}
                            <div>
                              <h4 className="text-sm font-medium mb-2">Key Features:</h4>
                              <ul className="space-y-1">
                                {config.features.map((feature, index) => (
                                  <li key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                                    <CheckCircle2 className="h-3 w-3 text-green-600 flex-shrink-0" />
                                    {feature}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            {/* Sidebar Preview */}
                            <div>
                              <h4 className="text-sm font-medium mb-2">Your Sidebar will include:</h4>
                              <div className="flex flex-wrap gap-2">
                                {config.sidebarItems.map((item, index) => (
                                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                    {item.icon}
                                    {item.label}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </RadioGroup>

            {form.formState.errors.userClassification && (
              <p className="text-red-500 text-sm mt-2">
                {form.formState.errors.userClassification.message}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Info Card */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="text-blue-900 font-medium mb-1">
                  Don't worry - you can change these settings later
                </p>
                <p className="text-blue-700">
                  Your classification and organization details can be updated in your settings at any time.
                  This initial setup just helps us customize your experience.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end pt-6">
          <Button
            type="submit"
            disabled={isSubmitting || !form.formState.isValid}
            className="flex items-center gap-2 min-w-[160px]"
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Creating...
              </>
            ) : (
              <>
                Continue Setup
                <ArrowRight className="h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
