/**
 * Optimized File Processing Service
 * Handles file uploads, compression, validation, and streaming
 */

import multer from "multer";
import path from "path";
import { pipeline } from "stream/promises";
import { createReadStream, createWriteStream } from "fs";
import { promisify } from "util";
import { exec } from "child_process";

const execAsync = promisify(exec);

// File processing configuration
const FILE_CONFIG = {
  MAX_FILE_SIZE: 250 * 1024 * 1024, // 250MB - Updated for large RFQ files
  SUPPORTED_TYPES: ['.pdf', '.txt', '.csv'] as const,
  COMPRESSION_THRESHOLD: 5 * 1024 * 1024, // 5MB - compress files larger than this
  CHUNK_SIZE: 64 * 1024, // 64KB chunks for streaming
  TEMP_DIRECTORY: './temp',
  LARGE_FILE_THRESHOLD: 5 * 1024 * 1024, // 5MB - use disk storage for files larger than this to prevent RAM exhaustion
  MAX_CONCURRENT_UPLOADS: 8, // **UPDATED**: Support 5-8 files per RFQ for multi-file batch processing
} as const;

// MIME type mapping
const MIME_TYPES: Record<string, string> = {
  '.pdf': 'application/pdf',
  '.csv': 'text/csv',
  '.txt': 'text/plain',
};

// File validation result
interface FileValidationResult {
  isValid: boolean;
  error?: string;
  mimeType?: string;
  size?: number;
}

// Processing result
interface ProcessingResult {
  success: boolean;
  originalSize: number;
  compressedSize?: number;
  compressionRatio?: number;
  processingTime: number;
  tempPath?: string;
}

/**
 * Enhanced file filter with security checks
 */
function secureFileFilter(req: any, file: Express.Multer.File, cb: multer.FileFilterCallback): void {
  const ext = path.extname(file.originalname).toLowerCase() as '.pdf' | '.txt' | '.csv';
  
  // Check file extension
  if (!FILE_CONFIG.SUPPORTED_TYPES.includes(ext)) {
    return cb(new Error(`Unsupported file type: ${ext}. Supported types: ${FILE_CONFIG.SUPPORTED_TYPES.join(', ')}`));
  }
  
  // Check MIME type matches extension
  const expectedMimeType = MIME_TYPES[ext];
  if (expectedMimeType && file.mimetype !== expectedMimeType) {
    return cb(new Error(`MIME type mismatch. Expected ${expectedMimeType}, got ${file.mimetype}`));
  }
  
  // Additional security: check for suspicious patterns in filename
  const suspiciousPatterns = [
    /\.\./,           // Directory traversal
    /[<>:"|?*]/,      // Windows reserved characters
    /\.exe$/i,        // Executable files
    /\.bat$/i,        // Batch files
    /\.cmd$/i,        // Command files
    /\.scr$/i,        // Screensaver files (often malware)
    /\.php$/i,        // PHP files
    /\.js$/i,         // JavaScript files
  ];
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(file.originalname)) {
      return cb(new Error(`Filename contains suspicious patterns: ${file.originalname}`));
    }
  }
  
  cb(null, true);
}

/**
 * Optimized multer configuration
 */
export const optimizedUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: FILE_CONFIG.MAX_FILE_SIZE,
    files: FILE_CONFIG.MAX_CONCURRENT_UPLOADS,
    fieldSize: 60 * 1024 * 1024, // 60MB for form fields (buffer for metadata)
  },
  fileFilter: secureFileFilter,
});

// Object Storage direct upload for large files (>5MB)
export const largeFileUpload = multer({
  storage: multer.diskStorage({
    destination: '/tmp/',
    filename: (req, file, cb) => {
      const uniqueName = `${Date.now()}-${file.originalname}`;
      cb(null, uniqueName);
    }
  }),
  limits: {
    fileSize: FILE_CONFIG.MAX_FILE_SIZE,
    files: FILE_CONFIG.MAX_CONCURRENT_UPLOADS,
    fieldSize: 60 * 1024 * 1024, // 60MB for form fields
  },
  fileFilter: secureFileFilter,
});

/**
 * Smart upload selector - automatically chooses storage strategy based on file size
 */
export function getUploadMiddleware(expectedFileSize?: number): multer.Multer {
  // Use disk storage for files >5MB to prevent RAM exhaustion
  if (expectedFileSize && expectedFileSize > FILE_CONFIG.LARGE_FILE_THRESHOLD) {
    console.log(`Using disk storage for file size: ${(expectedFileSize / 1024 / 1024).toFixed(1)}MB`);
    return largeFileUpload;
  }
  
  // Default to memory storage for smaller files
  return optimizedUpload;
}

/**
 * Validate file content beyond just extension
 */
export async function validateFileContent(buffer: Buffer, filename: string): Promise<FileValidationResult> {
  const ext = path.extname(filename).toLowerCase();
  
  try {
    // PDF validation
    if (ext === '.pdf') {
      const header = buffer.slice(0, 4).toString();
      if (!header.startsWith('%PDF')) {
        return { isValid: false, error: 'Invalid PDF file format' };
      }
    }
    

    
    // Text file validation
    if (ext === '.txt' || ext === '.csv') {
      // Check for binary content in text files
      const sample = buffer.slice(0, 1024).toString('utf8', 0, Math.min(1024, buffer.length));
      if (/[\x00-\x08\x0E-\x1F\x7F]/.test(sample)) {
        return { isValid: false, error: 'File appears to contain binary data' };
      }
    }
    
    return {
      isValid: true,
      mimeType: MIME_TYPES[ext],
      size: buffer.length,
    };
    
  } catch (error) {
    return {
      isValid: false,
      error: `File validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Compress file if it exceeds threshold
 */
export async function compressFileIfNeeded(buffer: Buffer, filename: string): Promise<ProcessingResult> {
  const startTime = Date.now();
  const originalSize = buffer.length;
  
  try {
    // Only compress large files
    if (originalSize <= FILE_CONFIG.COMPRESSION_THRESHOLD) {
      return {
        success: true,
        originalSize,
        processingTime: Date.now() - startTime,
      };
    }
    
    // For now, we'll skip actual compression to avoid external dependencies
    // In production, you might use libraries like zlib or third-party services
    
    return {
      success: true,
      originalSize,
      processingTime: Date.now() - startTime,
    };
    
  } catch (error) {
    console.error('File compression failed:', error);
    return {
      success: false,
      originalSize,
      processingTime: Date.now() - startTime,
    };
  }
}

/**
 * Stream file processing for large files
 */
export async function processLargeFile(
  inputPath: string,
  outputPath: string,
  processor: (chunk: Buffer) => Buffer
): Promise<void> {
  try {
    const readStream = createReadStream(inputPath, { highWaterMark: FILE_CONFIG.CHUNK_SIZE });
    const writeStream = createWriteStream(outputPath);
    
    await pipeline(
      readStream,
      async function* (source) {
        for await (const chunk of source) {
          yield processor(chunk as Buffer);
        }
      },
      writeStream
    );
    
  } catch (error) {
    console.error('Large file processing failed:', error);
    throw error;
  }
}

/**
 * Extract file metadata
 */
export function extractFileMetadata(file: Express.Multer.File): {
  originalName: string;
  size: number;
  mimeType: string;
  extension: string;
  uploadedAt: Date;
  checksum?: string;
} {
  return {
    originalName: file.originalname,
    size: file.size,
    mimeType: file.mimetype,
    extension: path.extname(file.originalname).toLowerCase(),
    uploadedAt: new Date(),
  };
}

/**
 * Calculate file checksum for integrity verification
 */
export function calculateBufferChecksum(buffer: Buffer): string {
  const crypto = require('crypto');
  return crypto.createHash('md5').update(buffer).digest('hex');
}

/**
 * Optimize file for storage (placeholder for future enhancements)
 */
export async function optimizeForStorage(buffer: Buffer, filename: string): Promise<{
  optimizedBuffer: Buffer;
  optimization: {
    originalSize: number;
    optimizedSize: number;
    savings: number;
    method: string;
  };
}> {
  const originalSize = buffer.length;
  
  // For now, return the original buffer
  // In the future, this could include:
  // - PDF optimization
  // - Image compression within documents
  // - Metadata removal
  
  return {
    optimizedBuffer: buffer,
    optimization: {
      originalSize,
      optimizedSize: buffer.length,
      savings: 0,
      method: 'none',
    },
  };
}

/**
 * File processing middleware for Express
 */
export function fileProcessingMiddleware() {
  return async (req: any, res: any, next: any) => {
    if (req.files || req.file) {
      const files = req.files || [req.file];
      
      for (const file of files) {
        // Validate file content
        const validation = await validateFileContent(file.buffer, file.originalname);
        if (!validation.isValid) {
          return res.status(400).json({ message: validation.error });
        }
        
        // Add checksum to file metadata
        file.checksum = calculateBufferChecksum(file.buffer);
        
        // Add processing metadata
        file.processed = {
          validatedAt: new Date(),
          checksum: file.checksum,
          validation,
        };
      }
    }
    
    next();
  };
}

/**
 * Get file processing statistics
 */
export function getFileProcessingStats(): {
  supportedTypes: string[];
  maxFileSize: number;
  compressionThreshold: number;
  maxConcurrentUploads: number;
} {
  return {
    supportedTypes: [...FILE_CONFIG.SUPPORTED_TYPES],
    maxFileSize: FILE_CONFIG.MAX_FILE_SIZE,
    compressionThreshold: FILE_CONFIG.COMPRESSION_THRESHOLD,
    maxConcurrentUploads: FILE_CONFIG.MAX_CONCURRENT_UPLOADS,
  };
}