const OpenAI = require('openai');
const fs = require('fs').promises;
const path = require('path');
const pdf = require('pdf-parse');
const sharp = require('sharp'); // For image processing if needed

class LLMDataExtractor {
  constructor(config = {}) {
    this.openai = new OpenAI({
      apiKey: config.openaiApiKey || process.env.OPENAI_API_KEY
    });
    
    this.model = config.model || 'gpt-4-turbo-preview';
    this.maxTokens = config.maxTokens || 2000;
    this.temperature = config.temperature || 0.1; // Low temperature for consistent extraction
  }

  /**
   * Extract structured data from text using LLM
   * @param {string} text - Raw text to extract data from
   * @param {Object} schema - Database schema definition
   * @param {string} context - Additional context for extraction
   * @returns {Promise<Object>} Extracted data ready for database insertion
   */
  async extractData(text, schema, context = '') {
    try {
      const prompt = this.buildExtractionPrompt(text, schema, context);
      
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: "system",
            content: "You are a data extraction specialist. Extract structured data from the provided text according to the given schema. Return only valid JSON that matches the schema exactly. If a field cannot be determined, use null."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: this.maxTokens,
        temperature: this.temperature,
        response_format: { type: "json_object" }
      });

      const extractedData = JSON.parse(response.choices[0].message.content);
      return this.validateAndCleanData(extractedData, schema);
      
    } catch (error) {
      console.error('Error extracting data:', error);
      throw new Error(`Data extraction failed: ${error.message}`);
    }
  }

  /**
   * Process a file and extract data
   * @param {string} filePath - Path to the file to process
   * @param {Object} schema - Database schema definition
   * @param {string} context - Additional context
   * @param {Object} pdfOptions - PDF-specific processing options
   * @returns {Promise<Object>} Extracted data
   */
  async extractFromFile(filePath, schema, context = '', pdfOptions = {}) {
    try {
      const fileExtension = path.extname(filePath).toLowerCase();
      let text;
      let metadata = {};

      switch (fileExtension) {
        case '.txt':
        case '.md':
          text = await fs.readFile(filePath, 'utf-8');
          break;
        case '.pdf':
          const result = await this.extractFromPDF(filePath, pdfOptions);
          text = result.text;
          metadata = result.metadata;
          break;
        case '.json':
          const jsonData = JSON.parse(await fs.readFile(filePath, 'utf-8'));
          text = JSON.stringify(jsonData, null, 2);
          break;
        default:
          throw new Error(`Unsupported file type: ${fileExtension}`);
      }

      // Include PDF metadata in context if available
      const enhancedContext = metadata.pageCount 
        ? `${context}\n\nDocument info: ${metadata.pageCount} pages, created: ${metadata.creationDate || 'unknown'}`
        : context;

      return await this.extractData(text, schema, enhancedContext);
    } catch (error) {
      console.error('Error processing file:', error);
      throw error;
    }
  }

  /**
   * Extract text and metadata from PDF
   * @param {string} filePath - Path to PDF file
   * @param {Object} options - PDF processing options
   * @returns {Promise<Object>} Extracted text and metadata
   */
  async extractFromPDF(filePath, options = {}) {
    try {
      const dataBuffer = await fs.readFile(filePath);
      
      const pdfOptions = {
        // Normalize whitespace and handle tables better
        normalizeWhitespace: options.normalizeWhitespace !== false,
        // Max pages to process (useful for large PDFs)
        max: options.maxPages || undefined,
        // Page range to extract
        pagerender: options.pageRange ? this.createPageRenderer(options.pageRange) : undefined
      };

      const pdfData = await pdf(dataBuffer, pdfOptions);
      
      let text = pdfData.text;
      
      // Post-process the extracted text
      if (options.cleanText !== false) {
        text = this.cleanPDFText(text, options);
      }
      
      // Handle structured data extraction for forms/tables
      if (options.extractTables) {
        const structuredData = this.extractTabularData(text);
        text = this.formatTabularData(structuredData, text);
      }

      const metadata = {
        pageCount: pdfData.numpages,
        info: pdfData.info,
        creationDate: pdfData.info?.CreationDate,
        title: pdfData.info?.Title,
        author: pdfData.info?.Author,
        subject: pdfData.info?.Subject
      };

      return { text, metadata };
      
    } catch (error) {
      console.error('PDF extraction error:', error);
      throw new Error(`Failed to extract PDF: ${error.message}`);
    }
  }

  /**
   * Clean and normalize PDF text
   * @param {string} text - Raw PDF text
   * @param {Object} options - Cleaning options
   * @returns {string} Cleaned text
   */
  cleanPDFText(text, options = {}) {
    let cleaned = text;
    
    // Remove excessive whitespace and line breaks
    cleaned = cleaned.replace(/\s+/g, ' ');
    cleaned = cleaned.replace(/\n\s*\n/g, '\n\n');
    
    // Fix common PDF extraction issues
    cleaned = cleaned.replace(/([a-z])([A-Z])/g, '$1 $2'); // Add space between camelCase
    cleaned = cleaned.replace(/(\d)([A-Za-z])/g, '$1 $2'); // Space between numbers and letters
    cleaned = cleaned.replace(/([A-Za-z])(\d)/g, '$1 $2'); // Space between letters and numbers
    
    // Remove page numbers and headers/footers if specified
    if (options.removePageNumbers) {
      cleaned = cleaned.replace(/\b(?:Page\s+)?\d+\s*(?:of\s+\d+)?\b/gi, '');
    }
    
    if (options.removeHeaders) {
      // Remove repeated header text (appears on multiple pages)
      const lines = cleaned.split('\n');
      const frequency = {};
      lines.forEach(line => {
        const trimmed = line.trim();
        if (trimmed.length > 10) {
          frequency[trimmed] = (frequency[trimmed] || 0) + 1;
        }
      });
      
      // Remove lines that appear too frequently (likely headers/footers)
      const threshold = Math.max(2, Math.floor(lines.length / 10));
      const commonLines = Object.keys(frequency).filter(line => frequency[line] >= threshold);
      
      commonLines.forEach(commonLine => {
        cleaned = cleaned.replace(new RegExp(commonLine, 'g'), '');
      });
    }
    
    // Normalize spacing
    cleaned = cleaned.replace(/\s+/g, ' ').trim();
    
    return cleaned;
  }

  /**
   * Extract tabular data from PDF text
   * @param {string} text - PDF text
   * @returns {Object} Structured data
   */
  extractTabularData(text) {
    const tables = [];
    const lines = text.split('\n');
    
    let currentTable = null;
    let tableStartPattern = /^[\s]*([A-Za-z\s]+)\s+([A-Za-z\s]+)\s+([A-Za-z\s]+)/; // Basic 3+ column detection
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Detect potential table headers (3+ columns with consistent spacing)
      if (tableStartPattern.test(line) && line.split(/\s{2,}/).length >= 3) {
        if (currentTable) {
          tables.push(currentTable);
        }
        
        currentTable = {
          headers: line.split(/\s{2,}/),
          rows: [],
          startLine: i
        };
      } else if (currentTable && line.length > 0) {
        // Check if line looks like a data row
        const columns = line.split(/\s{2,}/);
        if (columns.length >= currentTable.headers.length - 1) {
          currentTable.rows.push(columns);
        } else if (currentTable.rows.length === 0) {
          // Might be a continuation of headers or subheader
          continue;
        } else {
          // End of table
          if (currentTable.rows.length > 0) {
            tables.push(currentTable);
          }
          currentTable = null;
        }
      }
    }
    
    if (currentTable && currentTable.rows.length > 0) {
      tables.push(currentTable);
    }
    
    return { tables };
  }

  /**
   * Format tabular data for better LLM processing
   * @param {Object} structuredData - Extracted tables
   * @param {string} originalText - Original text
   * @returns {string} Formatted text
   */
  formatTabularData(structuredData, originalText) {
    if (!structuredData.tables || structuredData.tables.length === 0) {
      return originalText;
    }
    
    let formatted = originalText;
    
    structuredData.tables.forEach((table, index) => {
      let tableText = `\n\n--- TABLE ${index + 1} ---\n`;
      tableText += `Headers: ${table.headers.join(' | ')}\n`;
      
      table.rows.forEach((row, rowIndex) => {
        tableText += `Row ${rowIndex + 1}: ${row.join(' | ')}\n`;
      });
      
      tableText += '--- END TABLE ---\n\n';
      formatted += tableText;
    });
    
    return formatted;
  }

  /**
   * Create page renderer for specific page ranges
   * @param {Array|Object} pageRange - Page range specification
   * @returns {Function} Page renderer function
   */
  createPageRenderer(pageRange) {
    return function(pageData) {
      const pageNum = pageData.pageIndex + 1;
      
      if (Array.isArray(pageRange)) {
        return pageRange.includes(pageNum);
      } else if (typeof pageRange === 'object') {
        const start = pageRange.start || 1;
        const end = pageRange.end || Infinity;
        return pageNum >= start && pageNum <= end;
      }
      
      return true;
    };
  }

  /**
   * Build the extraction prompt
   * @param {string} text - Text to extract from
   * @param {Object} schema - Database schema
   * @param {string} context - Additional context
   * @returns {string} Formatted prompt
   */
  buildExtractionPrompt(text, schema, context) {
    const schemaDescription = this.describeSchema(schema);
    
    return `
Extract data from the following text according to this database schema:

${schemaDescription}

${context ? `Additional Context: ${context}` : ''}

Text to extract from:
---
${text}
---

Return the extracted data as a JSON object that matches the schema exactly. Use null for any fields that cannot be determined from the text.
    `.trim();
  }

  /**
   * Describe the database schema for the LLM
   * @param {Object} schema - Schema definition
   * @returns {string} Human-readable schema description
   */
  describeSchema(schema) {
    let description = "Database Schema:\n";
    
    for (const [tableName, tableSchema] of Object.entries(schema)) {
      description += `\nTable: ${tableName}\n`;
      description += "Fields:\n";
      
      for (const [fieldName, fieldConfig] of Object.entries(tableSchema.fields)) {
        const required = fieldConfig.required ? " (REQUIRED)" : " (optional)";
        const type = fieldConfig.type || 'string';
        const desc = fieldConfig.description ? ` - ${fieldConfig.description}` : '';
        
        description += `  - ${fieldName}: ${type}${required}${desc}\n`;
      }
      
      if (tableSchema.relationships) {
        description += "Relationships:\n";
        for (const [rel, config] of Object.entries(tableSchema.relationships)) {
          description += `  - ${rel}: ${config.type} relationship to ${config.table}\n`;
        }
      }
    }
    
    return description;
  }

  /**
   * Validate and clean extracted data
   * @param {Object} data - Extracted data
   * @param {Object} schema - Schema to validate against
   * @returns {Object} Validated and cleaned data
   */
  validateAndCleanData(data, schema) {
    const cleaned = {};
    
    for (const [tableName, tableData] of Object.entries(data)) {
      if (!schema[tableName]) {
        console.warn(`Unknown table: ${tableName}`);
        continue;
      }
      
      cleaned[tableName] = {};
      const tableSchema = schema[tableName];
      
      // Validate and clean each field
      for (const [fieldName, fieldConfig] of Object.entries(tableSchema.fields)) {
        const value = tableData[fieldName];
        
        // Check required fields
        if (fieldConfig.required && (value === null || value === undefined)) {
          throw new Error(`Required field missing: ${tableName}.${fieldName}`);
        }
        
        // Type conversion and validation
        cleaned[tableName][fieldName] = this.convertFieldType(value, fieldConfig.type);
      }
    }
    
    return cleaned;
  }

  /**
   * Convert field to appropriate type
   * @param {*} value - Value to convert
   * @param {string} type - Target type
   * @returns {*} Converted value
   */
  convertFieldType(value, type) {
    if (value === null || value === undefined) return null;
    
    switch (type) {
      case 'integer':
        return parseInt(value);
      case 'float':
        return parseFloat(value);
      case 'boolean':
        return Boolean(value);
      case 'date':
        return new Date(value);
      case 'json':
        return typeof value === 'string' ? JSON.parse(value) : value;
      default:
        return String(value);
    }
  }

  /**
   * Batch process multiple texts
   * @param {Array} texts - Array of texts to process
   * @param {Object} schema - Database schema
   * @param {string} context - Additional context
   * @returns {Promise<Array>} Array of extracted data objects
   */
  async batchExtract(texts, schema, context = '') {
    const results = [];
    
    for (let i = 0; i < texts.length; i++) {
      try {
        console.log(`Processing item ${i + 1}/${texts.length}`);
        const extracted = await this.extractData(texts[i], schema, context);
        results.push({
          index: i,
          success: true,
          data: extracted
        });
        
        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.error(`Failed to process item ${i + 1}:`, error.message);
        results.push({
          index: i,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }
}

// Example usage with PDF-specific features
async function pdfExample() {
  const schema = {
    invoices: {
      fields: {
        invoice_number: { type: 'string', required: true, description: 'Invoice number or ID' },
        date: { type: 'date', required: true, description: 'Invoice date' },
        vendor_name: { type: 'string', required: true, description: 'Vendor or supplier name' },
        total_amount: { type: 'float', required: true, description: 'Total invoice amount' },
        line_items: { type: 'json', required: false, description: 'Array of line items with descriptions and amounts' }
      }
    }
  };

  const extractor = new LLMDataExtractor({
    model: 'gpt-4-turbo-preview',
    temperature: 0.1
  });

  // PDF-specific options
  const pdfOptions = {
    cleanText: true,
    removePageNumbers: true,
    removeHeaders: true,
    extractTables: true,
    maxPages: 10, // Only process first 10 pages
    pageRange: { start: 1, end: 5 } // Or specific page range
  };

  try {
    const extractedData = await extractor.extractFromFile(
      './sample-invoice.pdf', 
      schema, 
      'Extract invoice details including line items from PDF invoice documents',
      pdfOptions
    );
    
    console.log('Extracted invoice data:', JSON.stringify(extractedData, null, 2));
    
  } catch (error) {
    console.error('PDF extraction failed:', error);
  }
}

// Advanced PDF processing example
async function advancedPdfExample() {
  const extractor = new LLMDataExtractor();
  
  // Process multiple PDFs with different strategies
  const documents = [
    {
      path: './financial-report.pdf',
      options: { 
        extractTables: true, 
        removeHeaders: true,
        pageRange: { start: 3, end: 15 } // Skip cover pages, focus on data
      }
    },
    {
      path: './contract.pdf', 
      options: { 
        cleanText: true, 
        removePageNumbers: false // Keep page refs for contracts
      }
    }
  ];

  const schema = {
    financial_data: {
      fields: {
        company_name: { type: 'string', required: true },
        revenue: { type: 'float', required: false },
        expenses: { type: 'float', required: false },
        net_income: { type: 'float', required: false },
        reporting_period: { type: 'string', required: false }
      }
    }
  };

  for (const doc of documents) {
    try {
      const result = await extractor.extractFromFile(
        doc.path,
        schema,
        'Extract financial information from business documents',
        doc.options
      );
      
      console.log(`Results from ${doc.path}:`, result);
    } catch (error) {
      console.error(`Failed to process ${doc.path}:`, error.message);
    }
  }
}

// Database insertion helper (example for PostgreSQL)
async function insertIntoDatabase(extractedData, dbClient) {
  const transaction = await dbClient.query('BEGIN');
  
  try {
    for (const [tableName, data] of Object.entries(extractedData)) {
      const fields = Object.keys(data);
      const values = Object.values(data);
      const placeholders = fields.map((_, i) => `$${i + 1}`).join(', ');
      
      const query = `
        INSERT INTO ${tableName} (${fields.join(', ')})
        VALUES (${placeholders})
        RETURNING id
      `;
      
      const result = await dbClient.query(query, values);
      console.log(`Inserted into ${tableName}, ID: ${result.rows[0].id}`);
    }
    
    await dbClient.query('COMMIT');
    console.log('All data inserted successfully');
    
  } catch (error) {
    await dbClient.query('ROLLBACK');
    console.error('Database insertion failed:', error);
    throw error;
  }
}

module.exports = { LLMDataExtractor, insertIntoDatabase };

// Uncomment to run the examples
// pdfExample();
// advancedPdfExample();