# Database (keep existing)
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Clerk Authentication (keep existing)
CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key

# AI Services (keep existing)
GROQ_API_KEY=your_groq_api_key
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# NEW: Wasabi Object Storage
WASABI_ACCESS_KEY_ID=your_wasabi_access_key
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_key
WASABI_BUCKET_NAME=your_bucket_name
WASABI_ENDPOINT=s3.wasabisys.com
WASABI_REGION=us-east-1

# Application
NODE_ENV=development
PRIMARY_MODEL=groq
SESSION_SECRET=your_session_secret
JWT_SECRET=your_jwt_secret
