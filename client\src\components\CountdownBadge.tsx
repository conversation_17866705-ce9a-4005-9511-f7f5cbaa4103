import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Clock, AlertTriangle, CheckCircle } from "lucide-react";
import { 
  calculateCountdown, 
  formatCountdown, 
  getCountdownUrgency, 
  getUrgencyColor,
  CountdownData 
} from "@/utils/countdown";

interface CountdownBadgeProps {
  targetDate: string | Date;
  label: string;
  className?: string;
  showIcon?: boolean;
  format?: 'short' | 'long';
}

export function CountdownBadge({ 
  targetDate, 
  label, 
  className = "",
  showIcon = true,
  format = 'short'
}: CountdownBadgeProps) {
  const [countdown, setCountdown] = useState<CountdownData>(() => 
    calculateCountdown(targetDate)
  );

  useEffect(() => {
    const updateCountdown = () => {
      setCountdown(calculateCountdown(targetDate));
    };

    updateCountdown(); // Initial update
    const interval = setInterval(updateCountdown, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [targetDate]);

  const urgency = getCountdownUrgency(countdown);
  const urgencyColor = getUrgencyColor(urgency);
  
  const getIcon = () => {
    if (!showIcon) return null;
    
    switch (urgency) {
      case 'safe':
        return <CheckCircle className="h-3 w-3" />;
      case 'warning':
        return <Clock className="h-3 w-3" />;
      case 'urgent':
      case 'expired':
        return <AlertTriangle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  return (
    <Badge className={`${urgencyColor} ${className}`} variant="secondary">
      {getIcon()}
      <span className={showIcon ? "ml-1" : ""}>
        {label} {formatCountdown(countdown, format)}
      </span>
    </Badge>
  );
}

interface DualCountdownProps {
  bidProposalDeadline: string | Date;
  rfqDeadline: string | Date;
  className?: string;
}

export function DualCountdownBadges({ 
  bidProposalDeadline, 
  rfqDeadline, 
  className = "" 
}: DualCountdownProps) {
  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      <CountdownBadge
        targetDate={bidProposalDeadline}
        label="Proposals due in"
        format="short"
      />
      <CountdownBadge
        targetDate={rfqDeadline}
        label="RFQ closes in"
        format="short"
      />
    </div>
  );
}
