/**
 * File Processing Debug Script
 * Tests the complete file processing pipeline to identify where AI processing stops
 */

import fs from 'fs';
import path from 'path';

// Test configuration
const TEST_CONFIG = {
  testFile: './attached_assets/RFQ #2021-301 Response - ACCENT ELECTRICAL_1753707304170.pdf',
  tempDir: './temp-debug',
  logFile: './debug-processing.log'
};

// Logging utility
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  console.log(logMessage);
  
  // Also write to log file
  fs.appendFileSync(TEST_CONFIG.logFile, logMessage + '\n');
}

async function testFileProcessing() {
  log('🔍 Starting File Processing Debug Session');
  
  try {
    // Clear previous log
    if (fs.existsSync(TEST_CONFIG.logFile)) {
      fs.unlinkSync(TEST_CONFIG.logFile);
    }
    
    // Create temp directory
    if (!fs.existsSync(TEST_CONFIG.tempDir)) {
      fs.mkdirSync(TEST_CONFIG.tempDir, { recursive: true });
    }
    
    // Check if test file exists
    if (!fs.existsSync(TEST_CONFIG.testFile)) {
      log(`❌ Test file not found: ${TEST_CONFIG.testFile}`, 'ERROR');
      return;
    }
    
    log(`✅ Test file found: ${TEST_CONFIG.testFile}`);
    const fileStats = fs.statSync(TEST_CONFIG.testFile);
    log(`📊 File size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);
    
    // Test 1: Environment Variables Check
    log('🔧 Checking Environment Variables...');
    const requiredEnvVars = [
      'WASABI_ACCESS_KEY_ID',
      'WASABI_SECRET_ACCESS_KEY', 
      'WASABI_BUCKET_NAME',
      'GROQ_API_KEY',
      'OPENAI_API_KEY',
      'GEMINI_API_KEY'
    ];
    
    const missingVars = [];
    requiredEnvVars.forEach(varName => {
      if (!process.env[varName]) {
        missingVars.push(varName);
      } else {
        log(`✅ ${varName}: ${process.env[varName].substring(0, 10)}...`);
      }
    });
    
    if (missingVars.length > 0) {
      log(`❌ Missing environment variables: ${missingVars.join(', ')}`, 'ERROR');
    }
    
    // Test 2: AI Service Connectivity
    log('🤖 Testing AI Service Connectivity...');
    await testAIServices();
    
    // Test 3: PDF Text Extraction
    log('📄 Testing PDF Text Extraction...');
    await testPDFExtraction();
    
    // Test 4: Database Connection
    log('🗄️ Testing Database Connection...');
    await testDatabaseConnection();
    
    // Test 5: Wasabi Storage Connection
    log('☁️ Testing Wasabi Storage Connection...');
    await testWasabiConnection();
    
    log('🎉 Debug session completed. Check debug-processing.log for details.');
    
  } catch (error) {
    log(`❌ Debug session failed: ${error.message}`, 'ERROR');
    log(`Stack trace: ${error.stack}`, 'ERROR');
  }
}

async function testAIServices() {
  // Test Groq API
  try {
    const { OpenAI } = await import('openai');
    const groq = new OpenAI({
      apiKey: process.env.GROQ_API_KEY,
      baseURL: "https://api.groq.com/openai/v1",
      timeout: 10000
    });
    
    const testCompletion = await groq.chat.completions.create({
      model: "llama3-8b-8192",
      messages: [{ role: "user", content: "Test connection. Reply with 'OK'." }],
      max_tokens: 10
    });
    
    log(`✅ Groq API: ${testCompletion.choices[0]?.message?.content || 'Connected'}`);
  } catch (error) {
    log(`❌ Groq API failed: ${error.message}`, 'ERROR');
  }
  
  // Test OpenAI API
  try {
    const { OpenAI } = await import('openai');
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      timeout: 10000
    });
    
    const testCompletion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: "Test connection. Reply with 'OK'." }],
      max_tokens: 10
    });
    
    log(`✅ OpenAI API: ${testCompletion.choices[0]?.message?.content || 'Connected'}`);
  } catch (error) {
    log(`❌ OpenAI API failed: ${error.message}`, 'ERROR');
  }
  
  // Test Gemini API
  try {
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });
    
    const result = await model.generateContent("Test connection. Reply with 'OK'.");
    const response = await result.response;
    
    log(`✅ Gemini API: ${response.text() || 'Connected'}`);
  } catch (error) {
    log(`❌ Gemini API failed: ${error.message}`, 'ERROR');
  }
}

async function testPDFExtraction() {
  try {
    // Copy test file to temp location
    const tempFile = path.join(TEST_CONFIG.tempDir, 'test.pdf');
    fs.copyFileSync(TEST_CONFIG.testFile, tempFile);
    
    // Test PDF.js extraction
    try {
      const pdfParse = (await import('pdf-parse')).default;
      const pdfBuffer = fs.readFileSync(tempFile);
      const pdfData = await pdfParse(pdfBuffer);
      
      log(`✅ PDF.js extraction: ${pdfData.text.length} characters extracted`);
      log(`📄 Sample text: ${pdfData.text.substring(0, 200)}...`);
      
      // Save extracted text for inspection
      fs.writeFileSync(path.join(TEST_CONFIG.tempDir, 'extracted-text.txt'), pdfData.text);
      
    } catch (pdfError) {
      log(`❌ PDF.js extraction failed: ${pdfError.message}`, 'ERROR');
    }
    
    // Clean up
    fs.unlinkSync(tempFile);
    
  } catch (error) {
    log(`❌ PDF extraction test failed: ${error.message}`, 'ERROR');
  }
}

async function testDatabaseConnection() {
  try {
    // Import database connection
    const { storage } = await import('./server/storage.js');
    
    // Test basic query
    const stats = await storage.getDashboardStats();
    log(`✅ Database connection: Retrieved ${JSON.stringify(stats)}`);
    
    // Test RFQ creation (dry run)
    log('🧪 Testing RFQ schema compatibility...');
    // We won't actually create an RFQ, just test the schema
    
  } catch (error) {
    log(`❌ Database connection failed: ${error.message}`, 'ERROR');
  }
}

async function testWasabiConnection() {
  try {
    const { S3Client, ListObjectsV2Command } = await import('@aws-sdk/client-s3');
    
    const s3Client = new S3Client({
      endpoint: process.env.WASABI_ENDPOINT || 'https://s3.wasabisys.com',
      region: process.env.WASABI_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.WASABI_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.WASABI_SECRET_ACCESS_KEY || '',
      },
      forcePathStyle: true,
    });
    
    const command = new ListObjectsV2Command({
      Bucket: process.env.WASABI_BUCKET_NAME || 'bidaible-storage',
      MaxKeys: 5,
      Prefix: 'rfq-documents/'
    });
    
    const response = await s3Client.send(command);
    log(`✅ Wasabi connection: Found ${response.Contents?.length || 0} files in rfq-documents/`);
    
    if (response.Contents && response.Contents.length > 0) {
      log(`📁 Sample files: ${response.Contents.slice(0, 3).map(obj => obj.Key).join(', ')}`);
    }
    
  } catch (error) {
    log(`❌ Wasabi connection failed: ${error.message}`, 'ERROR');
  }
}

// Run the debug session
if (require.main === module) {
  testFileProcessing().catch(console.error);
}

module.exports = { testFileProcessing };
