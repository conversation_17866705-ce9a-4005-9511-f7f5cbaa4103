import { useQuery } from "@tanstack/react-query";
import { useUser } from "@clerk/clerk-react";
import { getQueryFn } from "@/lib/queryClient";

export function useAuth() {
  const { isSignedIn, isLoaded, user: clerkUser } = useUser();
  
  // Fetch additional user data from our database when user is authenticated
  const { data: dbUser, isLoading: isUserLoading } = useQuery({
    queryKey: ["/api/clerk/user"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: isSignedIn && isLoaded,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  return {
    user: dbUser || null, // Database user data
    clerkUser, // Clerk user data
    isLoading: !isLoaded || isUserLoading,
    isAuthenticated: isSignedIn && isLoaded,
    isSignedIn,
    isLoaded
  };
}
