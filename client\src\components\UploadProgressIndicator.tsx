import React, { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, AlertCircle, Upload, Loader2 } from 'lucide-react';

interface UploadProgress {
  fileName: string;
  uploadedBytes: number;
  totalBytes: number;
  percentage: number;
  status: 'uploading' | 'processing' | 'complete' | 'error';
  stage: 'upload_start' | 'upload_chunked' | 'storage_processing' | 'text_extraction' | 'ai_processing' | 'finalization' | 'complete';
  fileSizeMB?: number;
  isLargeFile?: boolean;
  estimatedTimeRemaining?: string;
}

interface UploadProgressIndicatorProps {
  sessionId: string | null;
  files: File[];
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export function UploadProgressIndicator({ 
  sessionId, 
  files, 
  onComplete, 
  onError 
}: UploadProgressIndicatorProps) {
  const [progress, setProgress] = useState<Map<string, UploadProgress>>(new Map());
  const [eventSource, setEventSource] = useState<EventSource | null>(null);

  useEffect(() => {
    if (!sessionId) return;

    // Initialize progress for all files
    const initialProgress = new Map<string, UploadProgress>();
    files.forEach(file => {
      initialProgress.set(file.name, {
        fileName: file.name,
        uploadedBytes: 0,
        totalBytes: file.size,
        percentage: 0,
        status: 'uploading',
        stage: 'upload_start'
      });
    });
    setProgress(initialProgress);

    // Connect to Server-Sent Events
    const es = new EventSource(`/api/upload/progress/${sessionId}`);
    setEventSource(es);

    es.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('Progress update:', data);
      } catch (error) {
        console.error('Error parsing SSE data:', error);
      }
    };

    es.addEventListener('progress', (event) => {
      try {
        const progressData: UploadProgress = JSON.parse(event.data);
        setProgress(prev => {
          const newProgress = new Map(prev);
          newProgress.set(progressData.fileName, progressData);
          return newProgress;
        });
      } catch (error) {
        console.error('Error parsing progress data:', error);
      }
    });

    es.addEventListener('complete', (event: MessageEvent) => {
      try {
        const progressData: UploadProgress = JSON.parse(event.data);
        setProgress(prev => {
          const newProgress = new Map(prev);
          newProgress.set(progressData.fileName, {
            ...progressData,
            status: 'complete',
            stage: 'complete',
            percentage: 100
          });

          // Check if all files are complete
          const allFiles = Array.from(newProgress.values());
          const completedFiles = allFiles.filter((p: UploadProgress) => p.status === 'complete').length;
          if (completedFiles === files.length) {
            setTimeout(() => onComplete?.(), 500);
          }
          
          return newProgress;
        });
      } catch (error) {
        console.error('Error parsing complete data:', error);
      }
    });

    es.addEventListener('error', (event: MessageEvent) => {
      try {
        const errorData = JSON.parse(event.data);
        setProgress(prev => {
          const newProgress = new Map(prev);
          newProgress.set(errorData.fileName, {
            ...errorData,
            status: 'error'
          });
          return newProgress;
        });
        onError?.(errorData.error || 'Upload failed');
      } catch (error) {
        console.error('Error parsing error data:', error);
      }
    });

    es.onerror = (error) => {
      console.error('SSE connection error:', error);
      onError?.('Connection error');
    };

    return () => {
      es.close();
      setEventSource(null);
    };
  }, [sessionId, files.length, onComplete, onError]);

  const getStageLabel = (stage: string, status: string, isLargeFile?: boolean, eta?: string) => {
    if (status === 'error') return 'Failed';
    if (status === 'complete') return 'Complete';
    
    const sizeContext = isLargeFile ? ' (large file)' : '';
    const etaText = eta ? ` - ${eta} remaining` : '';
    
    switch (stage) {
      case 'upload_start':
        return `Initializing upload${sizeContext}${etaText}`;
      case 'upload_chunked':
      case 'upload':
        return `Uploading file${sizeContext}${etaText}`;
      case 'storage_processing':
      case 'storage':
        return `Saving to secure storage${etaText}`;
      case 'text_extraction':
        return `Extracting text content${sizeContext}${etaText}`;
      case 'ai_processing':
        return `AI analysis in progress${etaText}`;
      case 'finalization':
        return `Finalizing and saving${etaText}`;
      case 'complete':
        return 'Processing complete';
      default:
        return `Processing${sizeContext}${etaText}`;
    }
  };

  const getStageIcon = (status: string, stage: string) => {
    if (status === 'error') return <AlertCircle className="h-4 w-4 text-red-500" />;
    if (status === 'complete') return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (stage === 'upload') return <Upload className="h-4 w-4 text-blue-500" />;
    return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (!sessionId || progress.size === 0) {
    return null;
  }

  return (
    <Card className="mt-4">
      <CardContent className="pt-6">
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Upload Progress</h4>
          {Array.from(progress.values()).map((fileProgress) => (
            <div key={fileProgress.fileName} className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  {getStageIcon(fileProgress.status, fileProgress.stage)}
                  <span className="font-medium truncate">
                    {fileProgress.fileName}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <span className="text-xs">
                    {formatFileSize(fileProgress.uploadedBytes)} / {formatFileSize(fileProgress.totalBytes)}
                  </span>
                  <span className="text-xs font-medium">
                    {fileProgress.percentage}%
                  </span>
                </div>
              </div>
              
              <div className="space-y-1">
                <Progress 
                  value={fileProgress.percentage} 
                  className="h-2"
                />
                <p className="text-xs text-muted-foreground">
                  {getStageLabel(fileProgress.stage, fileProgress.status, fileProgress.isLargeFile, fileProgress.estimatedTimeRemaining)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export default UploadProgressIndicator;