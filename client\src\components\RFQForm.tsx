import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { X, Loader2, <PERSON>rk<PERSON>, Upload, FileText } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import UploadProgressIndicator from "./UploadProgressIndicator";
import { MultiFileUploadInterface, FileWithMetadata } from "./MultiFileUploadInterface";
import { nanoid } from "nanoid";

interface RFQFormProps {
  onClose: () => void;
}

export function RFQForm({ onClose }: RFQFormProps) {
  const [files, setFiles] = useState<FileWithMetadata[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadSessionId, setUploadSessionId] = useState<string | null>(null);
  const [showProgress, setShowProgress] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Create upload session for progress tracking
  const createSessionMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/upload/start-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to create upload session');
      }
      
      return response.json();
    },
  });

  // Process documents with AI and auto-save to database (multi-file batch)
  const processDocumentsMutation = useMutation({
    mutationFn: async ({ files, sessionId }: { files: FileWithMetadata[]; sessionId?: string }) => {
      const formData = new FormData();
      
      // Generate batch ID for grouping related files
      const batchId = nanoid();
      formData.append('uploadBatchId', batchId);
      
      // Add files with metadata
      files.forEach((fileItem, index) => {
        formData.append('documents', fileItem.file);
        formData.append(`fileMetadata[${index}]`, JSON.stringify({
          id: fileItem.id,
          fileType: fileItem.fileType,
          sequence: fileItem.sequence,
          fileName: fileItem.file.name
        }));
      });
      
      // Add session ID for progress tracking if available
      if (sessionId) {
        formData.append('sessionId', sessionId);
      }
      
      // Send to enhanced RFQ creation endpoint with batch support
      const response = await fetch('/api/rfqs', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to process RFQ documents');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Multi-File RFQ Upload Complete",
        description: `Successfully processed ${files.length} files with AI extraction`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/rfqs"] });
      setTimeout(() => {
        setIsProcessing(false);
        setShowProgress(false);
        setUploadSessionId(null);
        setFiles([]);
        onClose();
      }, 1000);
    },
    onError: (error) => {
      toast({
        title: "Upload Failed",
        description: "Could not process RFQ documents",
        variant: "destructive",
      });
      setIsProcessing(false);
      setShowProgress(false);
      setUploadSessionId(null);
    },
  });

  // Handle multi-file upload from the enhanced interface
  const handleMultiFileUpload = async (fileItems: FileWithMetadata[]) => {
    if (fileItems.length === 0) return;
    
    setIsProcessing(true);

    // Always show progress tracking for multi-file processing
    try {
      // Create progress tracking session
      const sessionData = await createSessionMutation.mutateAsync();
      setUploadSessionId(sessionData.sessionId);
      setShowProgress(true);
      
      // Start upload with progress tracking
      processDocumentsMutation.mutate({
        files: fileItems,
        sessionId: sessionData.sessionId
      });
    } catch (error) {
      console.error('Failed to create session:', error);
      // Fallback to regular upload without progress
      processDocumentsMutation.mutate({ files: fileItems });
    }
  };

  const handleProgressComplete = () => {
    // Progress tracking completed, but keep processing state until mutation completes
    setShowProgress(false);
  };

  const handleProgressError = (error: string) => {
    toast({
      title: "Upload Error",
      description: error,
      variant: "destructive",
    });
    setIsProcessing(false);
    setShowProgress(false);
    setUploadSessionId(null);
  };

  // File management is now handled by MultiFileUploadInterface
  const handleFilesChange = (newFiles: FileWithMetadata[]) => {
    setFiles(newFiles);
  };

  return (
    <Card className="max-w-2xl w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Create New RFQ</CardTitle>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-center gap-2 mb-4">
              <h3 className="text-lg font-medium">Multi-File RFQ Upload</h3>
              <Sparkles className="h-5 w-5 text-orange-500" />
              <span className="text-sm text-muted-foreground">AI-powered batch processing</span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Upload multiple RFQ documents with intelligent file classification and priority-based processing. 
              Our AI will extract project details from each file and consolidate the information.
            </p>
          </div>
          
          {/* Processing State */}
          {isProcessing && (
            <div className="text-center py-8">
              <Loader2 className="h-12 w-12 mx-auto animate-spin text-primary mb-4" />
              <div>
                <p className="text-lg font-medium">Processing {files.length} Files...</p>
                <p className="text-sm text-muted-foreground">
                  AI is processing files in priority order and extracting project details
                </p>
              </div>
            </div>
          )}
          
          {/* Multi-File Upload Interface */}
          {!isProcessing && (
            <MultiFileUploadInterface
              files={files}
              onFilesChange={handleFilesChange}
              onUpload={handleMultiFileUpload}
              isProcessing={isProcessing}
              maxFiles={8}
            />
          )}

          {/* Progress Indicator for Batch Processing */}
          {showProgress && files.length > 0 && (
            <UploadProgressIndicator
              sessionId={uploadSessionId}
              files={files.map(f => f.file)}
              onComplete={handleProgressComplete}
              onError={handleProgressError}
            />
          )}
          
          {/* Cancel Button */}
          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={onClose} disabled={isProcessing}>
              {isProcessing ? "Processing..." : "Cancel"}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}