/**
 * <PERSON><PERSON><PERSON> to create a test API key for testing purposes
 * This bypasses Clerk authentication by directly using the API key service
 */

const { generateApiKey } = require('../server/services/apiKeyService');
const { storage } = require('../server/storage');

async function createTestApiKey() {
  try {
    console.log('🔑 Creating test API key...');
    
    // Create a test user ID (this would normally come from <PERSON>)
    const testUserId = 'test-user-' + Date.now();
    
    // Generate API key with full access for testing
    const result = await generateApiKey(
      testUserId,
      'Test API Key for Integration Tests',
      'full-access', // full permissions for testing
      1000, // high rate limit for testing
      {
        expirationDays: 365,
        environment: 'development'
      }
    );
    
    console.log('✅ Test API key created successfully!');
    console.log('🔑 API Key:', result.apiKey);
    console.log('📋 Key ID:', result.keyRecord.id);
    console.log('👤 User ID:', testUserId);
    console.log('🔒 Permissions:', result.keyRecord.permissions);
    console.log('⏰ Rate Limit:', result.keyRecord.rateLimit);
    
    // Save to a file for the tests to use
    const fs = require('fs');
    const testConfig = {
      apiKey: result.apiKey,
      keyId: result.keyRecord.id,
      userId: testUserId,
      permissions: result.keyRecord.permissions,
      rateLimit: result.keyRecord.rateLimit,
      createdAt: new Date().toISOString()
    };
    
    fs.writeFileSync('./tests/test-api-key.json', JSON.stringify(testConfig, null, 2));
    console.log('💾 Test configuration saved to tests/test-api-key.json');
    
    return result;
  } catch (error) {
    console.error('❌ Error creating test API key:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  createTestApiKey()
    .then(() => {
      console.log('🎉 Test API key setup complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Failed to create test API key:', error);
      process.exit(1);
    });
}

module.exports = { createTestApiKey };
