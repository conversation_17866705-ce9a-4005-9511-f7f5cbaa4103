import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";
import * as fs from "fs";
import path from "path";
import * as pdfjsLib from "pdfjs-dist/legacy/build/pdf.mjs";
import { unifiedPDFExtractor } from "./core/pdfExtractor";
// pdf-parse will be dynamically imported to avoid startup issues

// Initialize AI clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
let groq = new OpenAI({ 
  apiKey: process.env.GROQ_API_KEY,
  baseURL: "https://api.groq.com/openai/v1"
});

// Function to ensure fresh Groq client with current environment
function getGroqClient() {
  return new OpenAI({
    apiKey: process.env.GROQ_API_KEY,
    baseURL: "https://api.groq.com/openai/v1"
  });
}

// Generic Groq API caller for other services
export async function callGroqAPI(prompt: string, context: string = 'general'): Promise<string> {
  try {
    console.log(`[callGroqAPI] Making ${context} request to Groq API`);
    
    const freshGroq = getGroqClient();
    
    const response = await freshGroq.chat.completions.create({
      model: GROQ_MODEL,
      messages: [
        {
          role: "system",
          content: "You are an expert AI assistant specializing in construction industry analysis. Provide detailed, accurate responses."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 4000,
      temperature: 0.1
    });

    const content = response.choices[0]?.message?.content;
    
    if (!content) {
      throw new Error('No content received from Groq API');
    }

    console.log(`[callGroqAPI] ${context} request completed successfully`);
    return content;
    
  } catch (error) {
    console.error(`[callGroqAPI] Error in ${context} request:`, error);
    throw new Error(`Groq API call failed for ${context}: ${error.message}`);
  }
}

// Model configuration
const PRIMARY_MODEL = process.env.PRIMARY_MODEL || "groq";
const OPENAI_MODEL = "gpt-4.1-mini"; // Use the specified model as per requirements
const GEMINI_MODEL = "gemini-2.5-pro";
const GROQ_MODEL = "openai/gpt-oss-120b"; // Use OpenAI GPT-OSS-120B model for Groq

// Initialize Gemini client
let gemini: any = null;
try {
  if (process.env.GEMINI_API_KEY) {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    gemini = genAI.getGenerativeModel({ model: GEMINI_MODEL });
  }
} catch (error) {
  console.error("Failed to initialize Gemini client:", error);
}

export interface ExtractedRfqData {
  text?: string;
  structuredData?: {
    fileName?: string;
    projectDescription?: string;
    contactName?: string;
    contactEmail?: string;
    projectSummary?: string;
    requirements?: string | string[];
    finalAwardDate?: string;
    aiSummary?: string;
  };
}

export interface ExtractedBidData {
  text?: string;
  structuredData?: {
    fileName?: string;
    bidAmount?: number;
    timeline?: string;
    scope?: string;
    conditions?: string;
    laborBreakdown?: string;
    materialBreakdown?: string;
    equipmentCosts?: string;
    startDate?: string;
    completionDate?: string;
    warranty?: string;
    bondingRequired?: boolean;
    insuranceDetails?: string;
  };
  confidence?: number;
}

export interface BidAnalysisResult {
  aiExecutiveSummary: {
    overview: string;
    keyInsights: string[];
    recommendations: string[];
    riskFactors: string[];
  };
  bidRanking: {
    rankedBids: Array<{
      bidId: string;
      aiScore: number;
      reasoning: string;
      riskLevel: 'low' | 'medium' | 'high';
      competitivePosition: string;
    }>;
  };
  marketAnalysis: {
    priceSpread: { min: number; max: number; avg: number; median: number };
    competitivePositioning: string;
    riskAssessment: string;
  };
}

export async function processRfqDocument(filePath: string, originalName?: string): Promise<ExtractedRfqData> {
  console.log("Processing file:", originalName || path.basename(filePath));

  try {
    const fileBuffer = await fs.promises.readFile(filePath);
    const fileName = originalName || filePath;
    const fileExtension = path.extname(fileName).toLowerCase();

    let extractedText = "";
    let structuredData = {};

    // Handle different file types
    if (fileExtension === '.pdf') {
      // Use primary model for PDF processing
      if (PRIMARY_MODEL === "openai") {
        const result = await extractPdfWithOpenAI(fileBuffer, fileName);
        extractedText = result.text;
        structuredData = result.structuredData;
      } else if (PRIMARY_MODEL === "groq") {
        const result = await extractPdfWithGroq(fileBuffer, fileName);
        extractedText = result.text;
        structuredData = result.structuredData;
      } else {
        // Fallback to Gemini
        extractedText = await extractPdfWithGemini(fileBuffer);
        structuredData = await extractStructuredDataWithGemini(extractedText);
      }
    } else if (['.txt', '.text'].includes(fileExtension)) {
      extractedText = fileBuffer.toString('utf8');
      if (PRIMARY_MODEL === "groq") {
        structuredData = await extractStructuredDataWithGroq(extractedText);
      } else {
        structuredData = await extractStructuredDataWithGemini(extractedText);
      }
    } else if (['.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.csv'].includes(fileExtension)) {
      // For Office documents, try primary model first then fallback
      if (PRIMARY_MODEL === "openai") {
        try {
          const result = await extractDocWithOpenAI(fileBuffer, fileExtension, fileName);
          extractedText = result.text;
          structuredData = result.structuredData;
        } catch (error) {
          console.log("OpenAI processing failed, trying Gemini fallback:", error.message);
          extractedText = await extractDocWithGemini(fileBuffer, fileExtension);
          structuredData = await extractStructuredDataWithGemini(extractedText);
        }
      } else if (PRIMARY_MODEL === "groq") {
        try {
          const result = await extractDocWithGroq(fileBuffer, fileExtension, fileName);
          extractedText = result.text;
          structuredData = result.structuredData;
        } catch (error) {
          console.log("Groq processing failed, trying Gemini fallback:", error.message);
          extractedText = await extractDocWithGemini(fileBuffer, fileExtension);
          structuredData = await extractStructuredDataWithGemini(extractedText);
        }
      } else {
        extractedText = await extractDocWithGemini(fileBuffer, fileExtension);
        structuredData = await extractStructuredDataWithGemini(extractedText);
      }
    } else {
      throw new Error(`Unsupported file type: ${fileExtension}. Supported types: .pdf, .doc, .docx, .ppt, .pptx, .xls, .xlsx, .csv, .txt`);
    }

    // Clean the extracted text
    extractedText = cleanExtractedText(extractedText);

    console.log("Text extraction completed. Length:", extractedText.length);
    console.log("Text preview:", extractedText.substring(0, 500));

    // If no meaningful text was extracted, throw error
    if (!extractedText || extractedText.length < 50) {
      throw new Error("No meaningful text could be extracted from the document");
    }

    // Generate AI Summary separately (non-blocking)
    let aiSummary = "Summary generation in progress...";
    try {
      aiSummary = await generateRfqSummary(extractedText, originalName);
    } catch (summaryError) {
      console.warn("Summary generation failed, continuing with extraction:", summaryError);
      aiSummary = "Summary generation failed";
    }

    return {
      text: extractedText,
      structuredData: {
        ...structuredData,
        aiSummary
      }
    };
  } catch (error) {
    console.error("Error processing RFQ document:", error);
    throw error;
  }
}

/**
 * Generate comprehensive AI bid analysis using Groq Kimi K2
 */
export async function generateBidAnalysis(rfqData: any, bidsData: any[]): Promise<BidAnalysisResult> {
  const bidAnalysisPrompt = `
Analyze the following construction project bids and provide a comprehensive executive summary and ranking.

PROJECT CONTEXT:
- Project: ${rfqData.projectName}
- Location: ${rfqData.projectLocation}
- Description: ${rfqData.description}
- Due Date: ${rfqData.dueDate}

BIDS TO ANALYZE:
${bidsData.map((bid, index) => `
Bid #${index + 1}:
- Contractor: ${bid.contractor?.companyName || 'Unknown'}
- Amount: $${(bid.bid.extractedAmount || bid.bid.bidAmount || 0).toLocaleString()}
- Timeline: ${bid.bid.timeline || 'Not specified'}
- Status: ${bid.bid.status}
- Submitted: ${bid.bid.submittedAt}
- Existing AI Summary: ${bid.bid.aiSummary || 'None'}
`).join('\n')}

Please provide a comprehensive analysis in the following JSON format:
{
  "aiExecutiveSummary": {
    "overview": "Executive summary of the bidding landscape",
    "keyInsights": ["insight1", "insight2", "insight3"],
    "recommendations": ["recommendation1", "recommendation2"],
    "riskFactors": ["risk1", "risk2"]
  },
  "bidRanking": {
    "rankedBids": [
      {
        "bidId": "bid_id",
        "aiScore": 85,
        "reasoning": "Why this bid scored this way",
        "riskLevel": "low|medium|high",
        "competitivePosition": "Excellent value proposition"
      }
    ]
  },
  "marketAnalysis": {
    "priceSpread": {"min": 0, "max": 0, "avg": 0, "median": 0},
    "competitivePositioning": "Market analysis summary",
    "riskAssessment": "Overall risk assessment"
  }
}

Focus on:
1. Value proposition analysis
2. Risk assessment based on pricing, timeline, and contractor reputation
3. Competitive positioning
4. Actionable recommendations for decision making
`;

  try {
    // Try primary model first (following same pattern as other functions)
    if (PRIMARY_MODEL === "groq") {
      console.log("Generating AI bid analysis with Groq...");
      return await generateBidAnalysisWithGroq(bidAnalysisPrompt, bidsData);
    } else if (PRIMARY_MODEL === "openai") {
      console.log("Generating AI bid analysis with OpenAI...");
      return await generateBidAnalysisWithOpenAI(bidAnalysisPrompt, bidsData);
    } else {
      console.log("Generating AI bid analysis with Gemini...");
      return await generateBidAnalysisWithGemini(bidAnalysisPrompt, bidsData);
    }
  } catch (error) {
    console.warn(`Primary model (${PRIMARY_MODEL}) failed for bid analysis, trying fallback:`, error);

    // Fallback chain (same pattern as existing code)
    try {
      if (PRIMARY_MODEL === "groq") {
        // Try Gemini fallback (since OpenAI may not be available)
        try {
          console.log("Groq failed, trying Gemini fallback...");
          return await generateBidAnalysisWithGemini(bidAnalysisPrompt, bidsData);
        } catch (geminiError) {
          console.warn("Gemini fallback failed:", geminiError);
        }
      } else {
        // Try Groq fallback for other primary models
        try {
          console.log("Trying Groq fallback...");
          return await generateBidAnalysisWithGroq(bidAnalysisPrompt, bidsData);
        } catch (groqError) {
          console.warn("Groq fallback failed, trying Gemini:", groqError);
          return await generateBidAnalysisWithGemini(bidAnalysisPrompt, bidsData);
        }
      }
    } catch (finalError) {
      console.error("All AI models failed for bid analysis:", finalError);
      // Return fallback analysis
      return generateFallbackBidAnalysis(bidsData);
    }
  }
}

/**
 * Generate bid analysis using Groq
 */
async function generateBidAnalysisWithGroq(prompt: string, bidsData: any[]): Promise<BidAnalysisResult> {
  // Debug logging to trace the issue
  console.log("🔍 Groq client debug:");
  console.log("- API Key exists:", !!process.env.GROQ_API_KEY);
  console.log("- API Key length:", process.env.GROQ_API_KEY?.length);
  console.log("- Model:", GROQ_MODEL);
  
  // Use fresh client to ensure current environment variables
  const groqClient = getGroqClient();
  const response = await groqClient.chat.completions.create({
    model: GROQ_MODEL,
    messages: [
      {
        role: "system",
        content: "You are an expert construction project analyst specializing in bid evaluation and risk assessment. Provide detailed, actionable analysis for construction professionals."
      },
      {
        role: "user",
        content: prompt
      }
    ],
    response_format: { type: "json_object" },
    max_tokens: 4000,
    temperature: 0.3
  });

  const responseText = response.choices[0].message.content;
  if (!responseText) {
    throw new Error("No response from Groq AI analysis");
  }

  console.log("✅ AI analysis generated successfully with Groq");
  return JSON.parse(responseText);
}

/**
 * Generate bid analysis using OpenAI
 */
async function generateBidAnalysisWithOpenAI(prompt: string, bidsData: any[]): Promise<BidAnalysisResult> {
  const response = await openai.chat.completions.create({
    model: OPENAI_MODEL,
    messages: [
      {
        role: "system",
        content: "You are an expert construction project analyst specializing in bid evaluation and risk assessment. Provide detailed, actionable analysis for construction professionals."
      },
      {
        role: "user",
        content: prompt
      }
    ],
    response_format: { type: "json_object" },
    max_tokens: 4000,
    temperature: 0.3
  });

  const responseText = response.choices[0].message.content;
  if (!responseText) {
    throw new Error("No response from OpenAI analysis");
  }

  console.log("✅ AI analysis generated successfully with OpenAI");
  return JSON.parse(responseText);
}

/**
 * Generate bid analysis using Gemini
 */
async function generateBidAnalysisWithGemini(prompt: string, bidsData: any[]): Promise<BidAnalysisResult> {
  if (!gemini) {
    throw new Error("Gemini client not initialized");
  }

  const result = await gemini.generateContent([
    {
      text: `${prompt}

Return valid JSON only, no additional text or formatting.`
    }
  ]);

  const responseText = result.response.text();
  if (!responseText) {
    throw new Error("No response from Gemini analysis");
  }

  // Clean the response to extract JSON
  const cleanedResponse = responseText.replace(/```json\n?|\n?```/g, '').trim();
  const analysisResult = JSON.parse(cleanedResponse);
  
  // Add metadata about which model was used
  console.log("✅ AI analysis generated successfully with Gemini");
  return analysisResult;
}

/**
 * Generate fallback analysis when all AI models fail
 */
function generateFallbackBidAnalysis(bidsData: any[]): BidAnalysisResult {
  return {
    aiExecutiveSummary: {
      overview: "AI analysis temporarily unavailable. Manual review recommended for comprehensive bid evaluation.",
      keyInsights: [
        "Multiple bids received for evaluation", 
        "Price analysis required to identify competitive positioning", 
        "Timeline and contractor experience should be evaluated manually"
      ],
      recommendations: [
        "Review each bid proposal carefully for scope completeness", 
        "Verify contractor credentials and past project experience", 
        "Evaluate total project value including timeline considerations"
      ],
      riskFactors: ["AI analysis service temporarily unavailable - manual review recommended"]
    },
    bidRanking: {
      rankedBids: bidsData.map((bid, index) => ({
        bidId: bid.bid.id,
        aiScore: 75 - (index * 3), // Simple scoring based on order
        reasoning: "Manual analysis required - comprehensive evaluation needed for accurate scoring",
        riskLevel: 'medium' as const,
        competitivePosition: "Requires detailed manual evaluation of proposal completeness and contractor capabilities"
      }))
    },
    marketAnalysis: {
      priceSpread: calculatePriceSpread(bidsData),
      competitivePositioning: "Manual market analysis recommended - review bid amounts against project scope and timeline requirements",
      riskAssessment: "Comprehensive risk assessment requires manual review of contractor qualifications, project timeline feasibility, and scope completeness"
    }
  };
}

/**
 * Calculate basic price spread statistics
 */
function calculatePriceSpread(bidsData: any[]) {
  const amounts = bidsData
    .map(bid => bid.bid.extractedAmount || bid.bid.bidAmount || 0)
    .filter(amount => amount > 0);

  if (amounts.length === 0) {
    return { min: 0, max: 0, avg: 0, median: 0 };
  }

  const sorted = amounts.sort((a, b) => a - b);
  const min = sorted[0];
  const max = sorted[sorted.length - 1];
  const avg = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
  const median = sorted.length % 2 === 0 
    ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
    : sorted[Math.floor(sorted.length / 2)];

  return { min, max, avg, median };
}

/**
 * Extract PDF content using PDF.js for text extraction, then Groq for processing
 */
async function extractPdfWithGroq(fileBuffer: Buffer, fileName: string): Promise<{ text: string; structuredData: any }> {
  try {
    console.log("Using PDF.js + Groq for PDF processing:", fileName);
    console.log('Groq API Key present:', !!process.env.GROQ_API_KEY);
    console.log('Groq API Key length:', process.env.GROQ_API_KEY?.length || 0);

    // First extract text using PDF.js
    const extractedText = await extractTextFromPdf(fileBuffer);

    if (!extractedText || extractedText.length < 50) {
      throw new Error("No meaningful text could be extracted from PDF");
    }

    console.log("PDF text extracted successfully, length:", extractedText.length);

    // Now process the extracted text with Groq
    const response = await groq.chat.completions.create({
      model: GROQ_MODEL,
      messages: [
        {
          role: "user",
          content: `Read the uploaded RFQ file and extract the key data needed for a bid response. Return the extracted data in JSON format.

PDF Content:
${extractedText}`
        }
      ],
      response_format: { type: "json_object" },
      max_tokens: 4000,
      temperature: 0.2
    });

    const responseText = response.choices[0].message.content;

    if (!responseText) {
      throw new Error("No response from Groq processing");
    }

    // Parse the JSON response
    const parsedData = JSON.parse(responseText);

    // Ensure extractedText is included in the response
    parsedData.extractedText = extractedText;

    return {
      text: extractedText,
      structuredData: parsedData
    };

  } catch (error) {
    console.error("Groq PDF processing failed:", error);
    // Fallback to Gemini if Groq fails
    console.log("Falling back to Gemini for PDF processing");
    const extractedText = await extractPdfWithGemini(fileBuffer);
    const structuredData = await extractStructuredDataWithGemini(extractedText);

    return {
      text: extractedText,
      structuredData
    };
  }
}

/**
 * Extract PDF content using PDF.js for text extraction, then OpenAI for processing
 * This approach first extracts clean text, then processes with AI
 */
async function extractPdfWithOpenAI(fileBuffer: Buffer, fileName: string): Promise<{ text: string; structuredData: any }> {
  try {
    console.log("Using PDF.js + OpenAI for PDF processing:", fileName);

    // First extract text using PDF.js
    const extractedText = await extractTextFromPdf(fileBuffer);

    if (!extractedText || extractedText.length < 50) {
      throw new Error("No meaningful text could be extracted from PDF");
    }

    console.log("PDF text extracted successfully, length:", extractedText.length);

    // Now process the extracted text with OpenAI
    const response = await openai.chat.completions.create({
      model: OPENAI_MODEL,
      messages: [
        {
          role: "system",
          content: buildSystemPrompt()
        },
        {
          role: "user",
          content: `Please analyze this extracted PDF content and extract comprehensive RFQ information. Return the data in the exact JSON format specified in the system prompt.

PDF Content:
${extractedText}`
        }
      ],
      response_format: { type: "json_object" },
      max_tokens: 4000
    });

    const responseText = response.choices[0].message.content;

    if (!responseText) {
      throw new Error("No response from OpenAI processing");
    }

    // Parse the JSON response
    const parsedData = JSON.parse(responseText);

    // Ensure extractedText is included in the response
    parsedData.extractedText = extractedText;

    return {
      text: extractedText,
      structuredData: parsedData
    };

  } catch (error) {
    console.error("OpenAI PDF processing failed:", error);
    throw new Error(`OpenAI PDF processing failed: ${error.message}`);
  }
}

/**
 * Robust text extraction from PDF.js text content items
 * Handles various PDF.js text item structures
 */
function extractTextFromItems(items: any[]): string {
  if (!items || !Array.isArray(items)) {
    return '';
  }

  const textParts: string[] = [];
  
  for (const item of items) {
    try {
      let text = '';
      
      // Handle different PDF.js text item structures
      if (typeof item === 'string') {
        text = item;
      } else if (item && typeof item === 'object') {
        // Most common: item.str property
        if (typeof item.str === 'string') {
          text = item.str;
        }
        // Alternative: item.text property
        else if (typeof item.text === 'string') {
          text = item.text;
        }
        // Nested: item.textContent
        else if (item.textContent && typeof item.textContent === 'string') {
          text = item.textContent;
        }
        // Array of nested items
        else if (item.items && Array.isArray(item.items)) {
          text = extractTextFromItems(item.items);
        }
        // Try common properties that might contain text
        else if (typeof item.content === 'string') {
          text = item.content;
        }
        else if (typeof item.value === 'string') {
          text = item.value;
        }
      }
      
      // Include text even if it contains only control characters (custom font encoding)
      if (text) {
        // For PDFs with custom font encoding, control characters may represent actual text
        if (text.trim()) {
          textParts.push(text.trim());
        } else if (text.length > 0) {
          // Include control characters as they may be meaningful in custom fonts
          textParts.push(text);
        }
      }
    } catch (itemError) {
      // Skip malformed items but continue processing
      console.warn('Skipping malformed PDF text item:', itemError);
      continue;
    }
  }
  
  return textParts.join(' ');
}

/**
 * Extract text from PDF using PDF.js
 */
async function extractTextFromPdf(fileBuffer: Buffer): Promise<string> {
  console.log("🚨🚨🚨 CRITICAL: extractTextFromPdf ENTRY POINT - THIS IS THE ACTUAL FUNCTION BEING CALLED");
  console.log("🚨🚨🚨 ENTRY: Buffer length:", fileBuffer.length);
  console.log("🚨🚨🚨 ENTRY: If you see this message, this is the correct execution path");
  
  // COMPREHENSIVE buffer integrity check as recommended
  console.log('🔍 CRITICAL BUFFER ANALYSIS:', {
    bufferLength: fileBuffer.length,
    bufferType: Buffer.isBuffer(fileBuffer) ? 'Buffer' : typeof fileBuffer,
    isUint8Array: fileBuffer instanceof Uint8Array,
    first100Bytes: Array.from(fileBuffer.slice(0, 100)),
    pdfHeader: fileBuffer.slice(0, 10).toString('latin1'),
    isPdfFile: fileBuffer.slice(0, 4).toString() === '%PDF',
    fileBufferConstructor: fileBuffer.constructor.name
  });
  
  try {
    // Load PDF document with comprehensive error handling
    console.log('🔍 Creating PDF.js loading task...');
    const loadingTask = pdfjsLib.getDocument({
      data: new Uint8Array(fileBuffer),
      // Disable font loading to prevent issues in server environment
      disableFontFace: true,
      // Don't fetch additional data
      disableRange: true,
      disableStream: true
    });
    
    console.log('🔍 Loading task created, awaiting PDF promise...');

    const pdf = await loadingTask.promise;
    console.log(`PDF loaded, ${pdf.numPages} pages`);

    let fullText = '';

    // Extract text from each page
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();

      console.log(`🔍 Page ${pageNum} - textContent.items:`, {
        length: textContent.items?.length || 0,
        firstItem: textContent.items?.[0],
        sampleItems: textContent.items?.slice(0, 5),
        allItems: textContent.items
      });

      // Extract text using robust method that handles different PDF.js structures
      const pageText = extractTextFromItems(textContent.items);
      
      console.log(`🔍 Page ${pageNum} - Extracted text length: ${pageText.length}`);
      console.log(`🔍 Page ${pageNum} - Text preview: "${pageText.substring(0, 200)}..."`);
      console.log(`🔍 DETAILED: First 10 chars as codes:`, Array.from(pageText.substring(0, 10)).map(c => c.charCodeAt(0)));
      
      // Try alternative extraction if no text found
      if (pageText.length === 0) {
        console.log(`🔧 Page ${pageNum} - Trying alternative extraction methods`);
        
        // Try getting text from textContent directly including control characters
        const directText = textContent.items?.map((item: any) => {
          // Try all possible text properties
          return item?.str || item?.text || item?.textContent || item?.content || item?.value || '';
        }).filter(text => text).join(' ') || ''; // Include control characters by removing .trim() filter
        
        console.log(`🔧 Page ${pageNum} - Direct extraction length: ${directText.length}`);
        console.log(`🔧 Page ${pageNum} - Direct text preview: "${directText.substring(0, 200)}..."`);
        
        if (directText.length > 0) {
          fullText += `\n--- Page ${pageNum} ---\n${directText}\n`;
        } else {
          fullText += `\n--- Page ${pageNum} ---\n`;
        }
      } else {
        fullText += `\n--- Page ${pageNum} ---\n${pageText}\n`;
      }
    }

    // Clean up the extracted text
    fullText = fullText
      .replace(/\s+/g, ' ')  // Normalize whitespace
      .replace(/\n\s*\n/g, '\n')  // Remove extra line breaks
      .trim();

    console.log("PDF text extraction completed, length:", fullText.length);
    console.log("🔍 DEBUGGING: Full extracted text:", JSON.stringify(fullText.substring(0, 500)));
    console.log("🔍 DEBUGGING: Text preview:", fullText.substring(0, 200));
    return fullText;

  } catch (error) {
    console.error("PDF.js extraction failed:", error);
    throw new Error(`PDF text extraction failed: ${error.message}`);
  }
}

/**
 * Extract Office document content using Groq
 */
async function extractDocWithGroq(fileBuffer: Buffer, fileExtension: string, fileName: string): Promise<{ text: string; structuredData: any }> {
  try {
    console.log("Using Groq for document processing:", fileName);

    let extractedText = "";

    // For simpler file types, extract text directly
    if (fileExtension === '.txt') {
      extractedText = fileBuffer.toString('utf8');
    } else if (fileExtension === '.csv') {
      extractedText = fileBuffer.toString('utf8');
    } else {
      // For complex Office documents, use basic text extraction
      extractedText = fileBuffer.toString('utf8', 0, Math.min(fileBuffer.length, 100000))
        .replace(/[^\x20-\x7E\n\r\t]/g, ' ')  // Replace non-printable chars with spaces
        .replace(/\s+/g, ' ')  // Normalize whitespace
        .trim();
    }

    if (!extractedText || extractedText.length < 20) {
      throw new Error(`No meaningful text could be extracted from ${fileExtension} file`);
    }

    console.log("Document text extracted, length:", extractedText.length);

    // Process the extracted text with Groq
    const response = await groq.chat.completions.create({
      model: GROQ_MODEL,
      messages: [
        {
          role: "user",
          content: `Read the uploaded RFQ file and extract the key data needed for a bid response. Return the extracted data in JSON format.

Document Content:
${extractedText}`
        }
      ],
      response_format: { type: "json_object" },
      max_tokens: 8000,
      temperature: 0.1
    });

    const responseText = response.choices[0].message.content;

    if (!responseText) {
      throw new Error("No response from Groq document processing");
    }

    // Parse the JSON response
    const parsedData = JSON.parse(responseText);

    // Ensure extractedText is included in the response
    parsedData.extractedText = extractedText;

    return {
      text: extractedText,
      structuredData: parsedData
    };

  } catch (error) {
    console.error("Groq document processing failed:", error);
    throw new Error(`Groq document processing failed: ${error.message}`);
  }
}

/**
 * Extract Office document content using OpenAI
 * For Office documents, we try to extract text content first, then process with OpenAI
 */
async function extractDocWithOpenAI(fileBuffer: Buffer, fileExtension: string, fileName: string): Promise<{ text: string; structuredData: any }> {
  try {
    console.log("Using OpenAI for document processing:", fileName);

    let extractedText = "";

    // For simpler file types, extract text directly
    if (fileExtension === '.txt') {
      extractedText = fileBuffer.toString('utf8');
    } else if (fileExtension === '.csv') {
      extractedText = fileBuffer.toString('utf8');
    } else {
      // For complex Office documents, use basic text extraction
      // This is a fallback approach - extract readable text from the buffer
      extractedText = fileBuffer.toString('utf8', 0, Math.min(fileBuffer.length, 100000))
        .replace(/[^\x20-\x7E\n\r\t]/g, ' ')  // Replace non-printable chars with spaces
        .replace(/\s+/g, ' ')  // Normalize whitespace
        .trim();
    }

    if (!extractedText || extractedText.length < 20) {
      throw new Error(`No meaningful text could be extracted from ${fileExtension} file`);
    }

    console.log("Document text extracted, length:", extractedText.length);

    // Process the extracted text with OpenAI
    const response = await openai.chat.completions.create({
      model: OPENAI_MODEL,
      messages: [
        {
          role: "system",
          content: buildSystemPrompt()
        },
        {
          role: "user",
          content: `Please analyze this extracted document content from a ${fileExtension.toUpperCase()} file and extract comprehensive RFQ information. Return the data in the exact JSON format specified in the system prompt.

Document Content:
${extractedText}`
        }
      ],
      response_format: { type: "json_object" },
      max_tokens: 8000
    });

    const responseText = response.choices[0].message.content;

    if (!responseText) {
      throw new Error("No response from OpenAI document processing");
    }

    // Parse the JSON response
    const parsedData = JSON.parse(responseText);

    // Ensure extractedText is included in the response
    parsedData.extractedText = extractedText;

    return {
      text: extractedText,
      structuredData: parsedData
    };

  } catch (error) {
    console.error("OpenAI document processing failed:", error);
    throw new Error(`OpenAI document processing failed: ${error.message}`);
  }
}

async function extractPdfWithGemini(fileBuffer: Buffer): Promise<string> {
  try {
    if (!gemini) {
      throw new Error("Gemini client not initialized");
    }

    const result = await gemini.generateContent([
      "Extract ALL text content from this PDF document. Return the complete text content exactly as it appears in the document.",
      {
        inlineData: {
          data: fileBuffer.toString("base64"),
          mimeType: "application/pdf"
        }
      }
    ]);

    const response = await result.response;
    return response.text() || "";
  } catch (error) {
    console.error("Gemini PDF extraction error:", error);
    // Final fallback to PDF.js only
    return await extractTextFromPdf(fileBuffer);
  }
}

async function extractDocWithGemini(fileBuffer: Buffer, extension: string): Promise<string> {
  try {
    const mimeType = extension === '.docx' 
      ? "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      : "application/msword";

    const response = await gemini.models.generateContent({
      model: GEMINI_MODEL,
      contents: [
        {
          inlineData: {
            data: fileBuffer.toString("base64"),
            mimeType
          }
        },
        "Extract all text content from this document. Return the complete text."
      ]
    });

    return response.text || "";
  } catch (error) {
    console.error("Gemini DOC extraction error:", error);
    // Fallback: treat as binary text
    return fileBuffer.toString('utf8').replace(/[^\x20-\x7E\n\r\t]/g, ' ');
  }
}

function cleanExtractedText(text: string): string {
  // Check if this looks like PDF binary data
  if (text.includes('%PDF-') || text.includes('<</')) {
    console.error("WARNING: Extracted text appears to be PDF binary data, not readable text");
    throw new Error("PDF text extraction failed - binary data returned instead of text");
  }

  // Remove null bytes and control characters
  text = text.replace(/\0/g, '');
  text = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  // Normalize whitespace
  text = text.replace(/\s+/g, ' ');
  text = text.replace(/\n\s*\n\s*\n/g, '\n\n');

  return text.trim();
}

/**
 * Build optimized system prompt for AI extraction (from OpenAI)
 */
function buildSystemPrompt(): string {
  return `You are an expert AI assistant specializing in document analysis and data extraction. Your task is to carefully analyze the contents of the provided PDF file and extract specific pieces of information. Format your final output as a single, clean JSON object.

Instructions:

1. Analyze the PDF: Thoroughly review the entire PDF document to identify the required data points listed below.
2. Extract the Data: Capture the information that matches each key in the JSON schema.
3. Format as JSON: Present the extracted data in a single JSON object. Do not include any explanatory text or introductory phrases before or after the JSON block.
4. Handle Missing Information: If a specific piece of information is not found in the document, use null as the value for that key.

JSON Schema and Data Extraction Guidelines:

Please populate the following JSON structure based on the content of the PDF file:

{
  "fileName": "string",
  "projectDescription": "string",
  "projectLocation": "string",
  "contactName": "string",
  "contactEmail": "string",
  "projectSummary": "string",
  "requirements": "string or array of strings",
  "finalAwardDate": "YYYY-MM-DD"
}

Field-Specific Instructions:

- fileName: Use the original name of the provided PDF file.
- projectDescription: Extract a general description or introduction to the project. May be under headings like "Project Description," "Introduction," or "Overview."
- projectLocation: Extract the complete project location including street address, city, state, and any additional location details. Look for terms like "Project Location," "Address," "Site," "Located at," or similar. Include the full address format: "Street Address, City, State" (e.g., "123 Main St, Portland, OR" or "456 Oak Ave, Seattle, WA"). If only partial information is available, include what is found.
- contactName: Extract the single, primary point of contact for the project. If multiple names are found, select the one most prominently featured or listed first in the most authoritative section (e.g., under "Contact Information," "Point of Contact," or "Procurement Officer").
- contactEmail: Extract the email address associated with the selected primary contact. If multiple are found, follow the same rule as for contactName.
- projectSummary: Extract the executive summary or abstract, usually found at the beginning of the document under "Summary" or "Abstract."
- requirements: Identify the requirements, scope of work, or technical specifications. If listed as bullet points or a numbered list, present as a JSON array of strings. If as a single block of text, present as a string. If ambiguous or semi-structured, use an array if there are clear separations; otherwise, use a string.
- finalAwardDate: Extract the date for the final award decision. Accept the value if it's clearly labeled, and convert to the format YYYY-MM-DD if possible. If the format cannot be converted or is unrecognizable, return null.

Location Extraction Priority:
1. Look for complete addresses with street, city, and state
2. Search for project address sections or location headers
3. Check for city and state mentions throughout the document
4. Include neighborhood or area descriptions if provided
5. Format as: "Street Address, City, State" when possible

If the PDF file cannot be parsed or read at all (e.g., it is corrupted or not a valid PDF), return only:
{
  "error": "Unable to parse PDF file."
}

## Output Format

Your output must be a single JSON object populated according to the following schema:

{
  "fileName": "string",
  "projectDescription": "string",
  "projectLocation": "string",
  "contactName": "string",
  "contactEmail": "string",
  "projectSummary": "string",
  "requirements": "string or array of strings",
  "finalAwardDate": "YYYY-MM-DD"
}

If any field cannot be found or extracted, set its value to null.

If the PDF file cannot be parsed, return only:
{
  "error": "Unable to parse PDF file."
}`;
}

async function extractStructuredDataWithGroq(text: string): Promise<ExtractedRfqData["structuredData"]> {
  try {
    console.log("Text being sent to Groq for extraction:", text.length, "characters");
    console.log("Text preview:", text.substring(0, 500));

    const prompt = `Read the uploaded RFQ file and extract the key data needed for a bid response. Return the extracted data in JSON format.

Document text:
${text}`;

    const response = await groq.chat.completions.create({
      model: GROQ_MODEL,
      messages: [
        {
          role: "system",
          content: "You are an expert construction RFQ document analyst. Extract data and return valid JSON only."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" },
      max_tokens: 4000,
      temperature: 0.1
    });

    const responseText = response.choices[0].message.content;
    const data = JSON.parse(responseText || "{}");
    console.log("Groq extracted data:", JSON.stringify(data, null, 2));
    return data;
  } catch (error) {
    console.error("Error extracting structured data with Groq:", error);
    return {};
  }
}

async function extractStructuredDataWithGemini(text: string): Promise<ExtractedRfqData["structuredData"]> {
  try {
    console.log("Text being sent to Gemini for extraction:", text.length, "characters");
    console.log("Text preview:", text.substring(0, 500));

    if (!gemini) {
      return {};
    }

    const prompt = `You are an expert AI assistant specializing in construction RFQ document analysis. 
Extract specific information from this RFQ document and return it in JSON format.

Extract these fields from the document:
- fileName: Original file name if mentioned
- projectDescription: Brief description of the project scope
- contactName: Primary contact person's name
- contactEmail: Contact person's email address
- projectSummary: A concise summary of the project
- requirements: Array of key requirements or qualifications
- finalAwardDate: Date when the contract will be awarded

Document text:
${text}

Return ONLY valid JSON with the extracted data. Use null for fields that cannot be determined.`;

    const result = await gemini.generateContent(prompt);
    const response = await result.response;
    const responseText = response.text();

    // Try to parse JSON from the response
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const data = JSON.parse(jsonMatch[0]);
      console.log("Gemini extracted data:", JSON.stringify(data, null, 2));
      return data;
    }

    return {};
  } catch (error) {
    console.error("Error extracting structured data with Gemini:", error);
    return {};
  }
}

/**
 * Process bid documents uploaded by contractors
 * Extract bid amounts, timeline, scope, and conditions
 */
export async function processBidDocument(filePath: string, originalName?: string): Promise<ExtractedBidData> {
  try {
    console.log('🔍 Processing bid document:', originalName || filePath);

    const fileExtension = path.extname(originalName || filePath).toLowerCase();
    const fileBuffer = await fs.promises.readFile(filePath);

    let extractedText: string;
    let structuredData: any;

    // Use appropriate extraction method based on file type and primary model
    console.log("🚨🚨🚨 processBidDocument: About to process PDF");
    console.log("🚨🚨🚨 PRIMARY_MODEL:", PRIMARY_MODEL, "fileExtension:", fileExtension);
    if (fileExtension === '.pdf') {
      if (PRIMARY_MODEL === "groq") {
        console.log("🚨🚨🚨 processBidDocument: Calling extractBidPdfWithGroq");
        const result = await extractBidPdfWithGroq(fileBuffer, originalName || 'bid.pdf');
        extractedText = result.text;
        structuredData = result.structuredData;
      } else {
        const result = await extractBidPdfWithOpenAI(fileBuffer, originalName || 'bid.pdf');
        extractedText = result.text;
        structuredData = result.structuredData;
      }
    } else if (['.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'].includes(fileExtension)) {
      if (PRIMARY_MODEL === "groq") {
        const result = await extractBidDocWithGroq(fileBuffer, fileExtension, originalName || 'document');
        extractedText = result.text;
        structuredData = result.structuredData;
      } else {
        const result = await extractBidDocWithOpenAI(fileBuffer, fileExtension, originalName || 'document');
        extractedText = result.text;
        structuredData = result.structuredData;
      }
    } else {
      // Plain text files
      extractedText = fileBuffer.toString('utf-8');
      if (PRIMARY_MODEL === "groq") {
        structuredData = await extractBidStructuredDataWithGroq(extractedText, originalName || 'text');
      } else {
        structuredData = await extractBidStructuredDataWithGemini(extractedText);
      }
    }

    console.log('✅ Bid document processed successfully');
    console.log('📊 Extracted bid amount:', structuredData?.bidAmount);

    return {
      text: extractedText,
      structuredData,
      confidence: calculateExtractionConfidence(structuredData)
    };

  } catch (error) {
    console.error('❌ Error processing bid document:', error);
    throw new Error(`Failed to process bid document: ${error.message}`);
  }
}

/**
 * Extract bid content using Groq for PDFs - NOW USING UNIFIED EXTRACTOR
 */
async function extractBidPdfWithGroq(fileBuffer: Buffer, fileName: string): Promise<{ text: string; structuredData: any }> {
  console.log("🔥 FIXED: extractBidPdfWithGroq now using UNIFIED PDF EXTRACTOR");
  console.log("📄 Buffer size:", fileBuffer.length, "fileName:", fileName);
  
  try {
    // Use the unified PDF extractor that works perfectly
    console.log("🚀 Using UNIFIED PDF EXTRACTOR - the one that actually works!");
    const result = await unifiedPDFExtractor.extractText(fileBuffer);
    
    if (!result.success) {
      throw new Error(`Unified extractor failed: ${result.error}`);
    }
    
    const extractedText = result.text;
    console.log(`✅ UNIFIED EXTRACTOR SUCCESS: ${extractedText.length} characters extracted`);
    console.log(`🔍 Confidence: ${result.confidence}%`);
    console.log(`🔍 First 200 chars: ${extractedText.substring(0, 200)}`);

    // Process with AI
    let structuredData: any = {};
    if (extractedText.length >= 20) {
      structuredData = await extractBidStructuredDataWithGroq(extractedText, fileName);
    } else {
      console.warn("⚠️ Very little text extracted, creating minimal structured data");
      structuredData = {
        fileName: fileName,
        bidAmount: null,
        timeline: null,
        scope: "Text extraction failed - manual review required",
        conditions: null
      };
    }

    return {
      text: extractedText,
      structuredData
    };

  } catch (error) {
    console.error("❌ Unified extractor failed:", error);
    
    // Fallback structured data
    const structuredData = {
      fileName: fileName,
      bidAmount: null,
      timeline: null,
      scope: "PDF extraction failed - manual review required",
      conditions: null
    };

    return {
      text: "--- PDF extraction failed ---",
      structuredData
    };
  }

  return {
    text: extractedText,
    structuredData
  };
}

/**
 * Extract bid content using OpenAI for PDFs
 */
async function extractBidPdfWithOpenAI(fileBuffer: Buffer, fileName: string): Promise<{ text: string; structuredData: any }> {
  try {
    // First extract text using PDF.js
    console.log("🔍 OPENAI: About to call extractTextFromPdf from extractBidPdfWithOpenAI");
    const extractedText = await extractTextFromPdf(fileBuffer);
    console.log(`📄 OPENAI PDF text extracted: ${extractedText.length} characters`);
    console.log("🔍 OPENAI: First 200 chars of extracted text:", extractedText.substring(0, 200));

    // Then process with OpenAI using bid-specific prompt
    const structuredData = await extractBidStructuredDataWithOpenAI(extractedText, fileName);

    return {
      text: extractedText,
      structuredData
    };
  } catch (error) {
    console.error('❌ OpenAI PDF bid extraction failed:', error);
    // Fallback to Gemini
    const extractedText = await extractBidPdfWithGemini(fileBuffer);
    const structuredData = await extractBidStructuredDataWithGemini(extractedText);
    return { text: extractedText, structuredData };
  }
}

/**
 * Extract bid content from Office documents using Groq
 */
async function extractBidDocWithGroq(fileBuffer: Buffer, fileExtension: string, fileName: string): Promise<{ text: string; structuredData: any }> {
  try {
    let extractedText = "";

    // Convert to text based on file type
    if (fileExtension === '.txt') {
      extractedText = fileBuffer.toString('utf8');
    } else if (fileExtension === '.csv') {
      extractedText = fileBuffer.toString('utf8');
    } else {
      // For complex Office documents, use basic text extraction
      extractedText = fileBuffer.toString('utf8', 0, Math.min(fileBuffer.length, 100000))
        .replace(/[^\x20-\x7E\n\r\t]/g, ' ')  // Replace non-printable chars with spaces
        .replace(/\s+/g, ' ')  // Normalize whitespace
        .trim();
    }

    if (!extractedText || extractedText.length < 20) {
      throw new Error(`No meaningful text could be extracted from ${fileExtension} file`);
    }

    console.log(`📊 Processing ${fileExtension} bid document with Groq`);

    const response = await groq.chat.completions.create({
      model: GROQ_MODEL,
      messages: [
        {
          role: 'system',
          content: buildBidSystemPrompt()
        },
        {
          role: 'user',
          content: `Extract bid data from this ${fileExtension} document: ${fileName}\n\n${extractedText}`
        }
      ],
      response_format: { type: "json_object" },
      max_tokens: 4000,
      temperature: 0.1
    });

    const content = response.choices[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from Groq');
    }

    // Extract JSON from response
    const structuredData = parseAIResponse(content);

    return {
      text: extractedText,
      structuredData
    };

  } catch (error) {
    console.error('❌ Groq bid extraction failed:', error);
    // Fallback to Gemini
    const extractedText = await extractBidDocWithGemini(fileBuffer, fileExtension);
    const structuredData = await extractBidStructuredDataWithGemini(extractedText);
    return { text: extractedText, structuredData };
  }
}

/**
 * Extract bid content from Office documents using OpenAI
 */
async function extractBidDocWithOpenAI(fileBuffer: Buffer, fileExtension: string, fileName: string): Promise<{ text: string; structuredData: any }> {
  try {
    // Convert to base64 for OpenAI
    const base64 = fileBuffer.toString('base64');
    const mimeType = getMimeType(fileExtension);

    console.log(`📊 Processing ${fileExtension} bid document with OpenAI`);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-mini-2025-04-14',
        messages: [
          {
            role: 'system',
            content: buildBidSystemPrompt()
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Extract bid data from this ${fileExtension} document: ${fileName}`
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:${mimeType};base64,${base64}`
                }
              }
            ]
          }
        ],
        max_tokens: 4000,
        temperature: 0.1
      }),
    });

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from OpenAI');
    }

    // Extract JSON from response
    const structuredData = parseAIResponse(content);

    return {
      text: content,
      structuredData
    };

  } catch (error) {
    console.error('❌ OpenAI bid extraction failed:', error);
    // Fallback to Gemini
    const extractedText = await extractBidDocWithGemini(fileBuffer, fileExtension);
    const structuredData = await extractBidStructuredDataWithGemini(extractedText);
    return { text: extractedText, structuredData };
  }
}

/**
 * Build optimized system prompt for bid document extraction
 */
function buildBidSystemPrompt(): string {
  return `You are an expert AI assistant specializing in construction bid document analysis and data extraction. Your task is to extract key bid information from contractor submission documents.

EXTRACTION TARGETS:
Extract the following bid information and return as valid JSON:

{
  "fileName": "original document name",
  "bidAmount": 150000.00, // Total bid amount as number
  "timeline": "8 weeks", // Project duration/timeline
  "scope": "Complete electrical installation including...", // Scope of work
  "conditions": "Price valid for 30 days, excludes permits", // Special conditions
  "laborBreakdown": "Electricians: $45,000, Helpers: $12,000", // Labor costs if provided
  "materialBreakdown": "Wire: $8,000, Fixtures: $15,000", // Material costs if provided  
  "equipmentCosts": "Lift rental: $2,000", // Equipment costs if provided
  "startDate": "2025-08-01", // Project start date if specified
  "completionDate": "2025-10-01", // Completion date if specified
  "warranty": "1 year parts and labor", // Warranty terms
  "bondingRequired": true, // If bonding is mentioned
  "insuranceDetails": "General liability $1M, Workers comp included" // Insurance info
}

INSTRUCTIONS:
- Extract actual bid amounts as numbers (remove currency symbols)
- Parse timeline strings (weeks, months, days)
- Capture complete scope descriptions
- Include all special conditions or exclusions
- Note any cost breakdowns provided
- Extract specific dates in YYYY-MM-DD format
- Set bondingRequired to true/false based on mentions of bonds/bonding
- Return valid JSON only, no additional text
- Use null for missing information
- Be precise with financial figures`;
}

/**
 * Extract structured bid data using Groq
 */
async function extractBidStructuredDataWithGroq(text: string, fileName: string): Promise<ExtractedBidData["structuredData"]> {
  try {
    const response = await groq.chat.completions.create({
      model: GROQ_MODEL,
      messages: [
        {
          role: 'system',
          content: buildBidSystemPrompt()
        },
        {
          role: 'user',
          content: `Extract bid data from this text content from file "${fileName}":\n\n${text.substring(0, 24000)}`
        }
      ],
      response_format: { type: "json_object" },
      max_tokens: 8000,
      temperature: 0.1
    });

    const content = response.choices[0]?.message?.content;

    return parseAIResponse(content);
  } catch (error) {
    console.error('❌ Groq bid structured extraction failed:', error);
    // Fallback to Gemini
    return await extractBidStructuredDataWithGemini(text);
  }
}

/**
 * Extract structured bid data using OpenAI
 */
async function extractBidStructuredDataWithOpenAI(text: string, fileName: string): Promise<ExtractedBidData["structuredData"]> {
  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-mini-2025-04-14',
        messages: [
          {
            role: 'system',
            content: buildBidSystemPrompt()
          },
          {
            role: 'user',
            content: `Extract bid data from this text content from file "${fileName}":\n\n${text.substring(0, 24000)}`
          }
        ],
        max_tokens: 8000,
        temperature: 0.1
      }),
    });

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    return parseAIResponse(content);
  } catch (error) {
    console.error('❌ OpenAI bid structured extraction failed:', error);
    // Fallback to Gemini
    return await extractBidStructuredDataWithGemini(text);
  }
}

/**
 * Extract structured bid data using Gemini (fallback)
 */
async function extractBidStructuredDataWithGemini(text: string): Promise<ExtractedBidData["structuredData"]> {
  try {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

    const prompt = `${buildBidSystemPrompt()}\n\nExtract bid data from this text:\n\n${text.substring(0, 20000)}`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const content = response.text();

    return parseAIResponse(content);
  } catch (error) {
    console.error('❌ Gemini bid extraction failed:', error);
    return {};
  }
}

/**
 * Extract bid PDF content using Gemini (fallback)
 */
async function extractBidPdfWithGemini(fileBuffer: Buffer): Promise<string> {
  try {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

    const prompt = "Extract all text content from this bid document. Focus on bid amounts, timelines, scope of work, and conditions.";

    const result = await model.generateContent([
      prompt,
      {
        inlineData: {
          data: fileBuffer.toString('base64'),
          mimeType: 'application/pdf'
        }
      }
    ]);

    const response = await result.response;
    return cleanExtractedText(response.text());
  } catch (error) {
    console.error('❌ Gemini bid PDF extraction failed:', error);
    return '';
  }
}

/**
 * Extract bid Office document content using Gemini (fallback)
 */
async function extractBidDocWithGemini(fileBuffer: Buffer, extension: string): Promise<string> {
  try {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

    const mimeType = getMimeType(extension);
    const prompt = "Extract all text content from this bid document. Focus on bid amounts, timelines, scope of work, and conditions.";

    const result = await model.generateContent([
      prompt,
      {
        inlineData: {
          data: fileBuffer.toString('base64'),
          mimeType
        }
      }
    ]);

    const response = await result.response;
    return cleanExtractedText(response.text());
  } catch (error) {
    console.error('❌ Gemini bid document extraction failed:', error);
    return '';
  }
}

/**
 * Parse AI response to extract JSON data
 */
function parseAIResponse(content: string): any {
  try {
    // Try to parse as JSON directly
    return JSON.parse(content);
  } catch (error) {
    // If that fails, try to extract JSON from the response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[0]);
      } catch (innerError) {
        console.error('Failed to parse extracted JSON:', innerError);
        return {};
      }
    }
    console.error('No valid JSON found in AI response');
    return {};
  }
}

/**
 * Get MIME type for file extension
 */
function getMimeType(extension: string): string {
  const mimeTypes: Record<string, string> = {
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.csv': 'text/csv',
    '.txt': 'text/plain'
  };
  return mimeTypes[extension] || 'application/octet-stream';
}

/**
 * Calculate confidence score based on extracted data completeness
 */
function calculateExtractionConfidence(structuredData: any): number {
  if (!structuredData) return 0;

  const keyFields = ['bidAmount', 'timeline', 'scope'];
  const presentFields = keyFields.filter(field => structuredData[field] != null).length;

  return Math.round((presentFields / keyFields.length) * 100) / 100;
}

/**
 * Generate RFQ summary using fallback pattern (PRIMARY_MODEL -> OpenAI -> Gemini)
 */
async function generateRfqSummary(rfqText: string, fileName?: string): Promise<string> {
  const prompt = buildSummaryPrompt(rfqText, fileName, 'RFQ');

  try {
    if (PRIMARY_MODEL === "groq") {
      return await generateSummaryWithGroq(prompt);
    } else if (PRIMARY_MODEL === "openai") {
      return await generateSummaryWithOpenAI(prompt);
    } else {
      return await generateSummaryWithGemini(prompt);
    }
  } catch (error) {
    console.warn(`Primary model (${PRIMARY_MODEL}) failed for summary generation, trying fallback:`, error);

    // Fallback chain
    try {
      if (PRIMARY_MODEL === "groq") {
        // Try OpenAI first, then Gemini
        try {
          return await generateSummaryWithOpenAI(prompt);
        } catch (openaiError) {
          console.warn("OpenAI fallback failed, trying Gemini:", openaiError);
          return await generateSummaryWithGemini(prompt);
        }
      } else if (PRIMARY_MODEL === "openai") {
        // Try Groq first, then Gemini
        try {
          return await generateSummaryWithGroq(prompt);
        } catch (groqError) {
          console.warn("Groq fallback failed, trying Gemini:", groqError);
          return await generateSummaryWithGemini(prompt);
        }
      } else {
        // Try Groq first, then OpenAI
        try {
          return await generateSummaryWithGroq(prompt);
        } catch (groqError) {
          console.warn("Groq fallback failed, trying OpenAI:", groqError);
          return await generateSummaryWithOpenAI(prompt);
        }
      }
    } catch (fallbackError) {
      console.error("All AI models failed for summary generation:", fallbackError);
      return "Unable to generate summary - all AI services unavailable";
    }
  }
}

/**
 * Generate bid summary using fallback pattern (PRIMARY_MODEL -> OpenAI -> Gemini)
 */
export async function generateBidSummary(bidText: string, fileName?: string): Promise<string> {
  const prompt = buildSummaryPrompt(bidText, fileName, 'bid');

  try {
    if (PRIMARY_MODEL === "groq") {
      return await generateSummaryWithGroq(prompt);
    } else if (PRIMARY_MODEL === "openai") {
      return await generateSummaryWithOpenAI(prompt);
    } else {
      return await generateSummaryWithGemini(prompt);
    }
  } catch (error) {
    console.warn(`Primary model (${PRIMARY_MODEL}) failed for bid summary generation, trying fallback:`, error);

    // Fallback chain
    try {
      if (PRIMARY_MODEL === "groq") {
        // Try OpenAI first, then Gemini
        try {
          return await generateSummaryWithOpenAI(prompt);
        } catch (openaiError) {
          console.warn("OpenAI fallback failed, trying Gemini:", openaiError);
          return await generateSummaryWithGemini(prompt);
        }
      } else if (PRIMARY_MODEL === "openai") {
        // Try Groq first, then Gemini
        try {
          return await generateSummaryWithGroq(prompt);
        } catch (groqError) {
          console.warn("Groq fallback failed, trying Gemini:", groqError);
          return await generateSummaryWithGemini(prompt);
        }
      } else {
        // Try Groq first, then OpenAI
        try {
          return await generateSummaryWithGroq(prompt);
        } catch (groqError) {
          console.warn("Groq fallback failed, trying OpenAI:", groqError);
          return await generateSummaryWithOpenAI(prompt);
        }
      }
    } catch (fallbackError) {
      console.error("All AI models failed for bid summary generation:", fallbackError);
      return "Unable to generate summary - all AI services unavailable";
    }
  }
}

/**
 * Build consistent summary prompt for both RFQ and bid documents
 */
function buildSummaryPrompt(documentText: string, fileName?: string, documentType: string = 'document'): string {
  const typeSpecificGuidance = documentType === 'bid' 
    ? 'Focus on bid amount, timeline, scope, conditions, and other important aspects for evaluating the bid.'
    : 'Focus on providing a concise overview that helps someone quickly understand the document\'s purpose and main points.';

  return `Summarize the key details of the following ${documentType} document, and present the summary in Markdown format. Use headings, lists, and bold text where appropriate to improve readability.

Document Name: ${fileName || 'Unknown Document'}
Document Text: ${documentText.substring(0, 20000)} ${documentText.length > 20000 ? '...[truncated]' : ''}

${typeSpecificGuidance}

Return only the markdown summary, no additional explanations.`;
}

/**
 * Generate summary using Groq
 */
async function generateSummaryWithGroq(prompt: string): Promise<string> {
  const response = await groq.chat.completions.create({
    model: GROQ_MODEL,
    messages: [
      {
        role: "system",
        content: "You are an expert document analyst. Generate concise, well-formatted markdown summaries of construction industry documents. Focus on key information that stakeholders need to know."
      },
      {
        role: "user",
        content: prompt
      }
    ],
    max_tokens: 4000,
    temperature: 0.2
  });

  const content = response.choices[0]?.message?.content;
  if (!content) {
    throw new Error("No response from Groq");
  }

  return content;
}

/**
 * Generate summary using OpenAI
 */
async function generateSummaryWithOpenAI(prompt: string): Promise<string> {
  const response = await openai.chat.completions.create({
    model: OPENAI_MODEL,
    messages: [
      {
        role: "system",
        content: "You are an expert document analyst. Generate concise, well-formatted markdown summaries of construction industry documents. Focus on key information that stakeholders need to know."
      },
      {
        role: "user",
        content: prompt
      }
    ],
    max_tokens: 4000,
    temperature: 0.2
  });

  const content = response.choices[0]?.message?.content;
  if (!content) {
    throw new Error("No response from OpenAI");
  }

  return content;
}

/**
 * Generate summary using Gemini (final fallback)
 */
async function generateSummaryWithGemini(prompt: string): Promise<string> {
  if (!gemini) {
    throw new Error("Gemini client not initialized");
  }

  const result = await gemini.generateContent(prompt);
  const response = await result.response;
  const content = response.text();

  if (!content) {
    throw new Error("No response from Gemini");
  }

  return content;
}

// Utility function to clean and validate strings
const cleanString = (str: string | null | undefined): string => {
  if (!str) return '';
  return str.toString().trim().replace(/\s+/g, ' ');
};

// Extract location from the extracted text
      const extractLocation = (data: any): string => {
        // First priority: AI-extracted project_location if it exists and is valid
        if (data.project_location) {
          const location = cleanString(data.project_location);
          if (location && location !== "TBD" && !location.includes("contact") && !location.includes("email")) {
            return location;
          }
        }

        // Second priority: AI-extracted projectLocation
        if (data.projectLocation) {
          const location = cleanString(data.projectLocation);
          if (location && location !== "TBD" && !location.includes("contact") && !location.includes("email")) {
            return location;
          }
        }

        // Third priority: Look for specific project location patterns in extracted text
        const textToSearch = [data.extractedText, data.projectDescription, data.projectSummary].join(" ");

        // Look for explicit project location mentions
        const projectLocationPatterns = [
          /(?:project\s+location[:\s]*|project\s+site[:\s]*|construction\s+location[:\s]*)([\d\w\s,-]+(?:street|avenue|ave|road|rd|boulevard|blvd|drive|dr|lane|ln|place|pl)[^.]*?)(?:\.|,\s*[A-Z]{2}|\s+[A-Z]{2}\s|\s*$)/i,
          /(?:located\s+at[:\s]*|address[:\s]*|site\s+address[:\s]*)([\d\w\s,-]+(?:street|avenue|ave|road|rd|boulevard|blvd|drive|dr|lane|ln|place|pl)[^.]*?)(?:\.|,\s*[A-Z]{2}|\s+[A-Z]{2}\s|\s*$)/i,
          /([\d]+\s+[^,]+(?:street|avenue|ave|road|rd|boulevard|blvd|drive|dr|lane|ln|place|pl)[^,]*),\s*([a-zA-Z\s]+),\s*([A-Z]{2})/i,
          /([\d]+\s+[^,]+(?:street|avenue|ave|road|rd|boulevard|blvd|drive|dr|lane|ln|place|pl))\s+([a-zA-Z\s]+)\s+([A-Z]{2})/i
        ];

        for (const pattern of projectLocationPatterns) {
          const match = textToSearch.match(pattern);
          if (match) {
            if (match.length >= 4) {
              // Full address with street, city, state
              const [, street, city, state] = match;
              const fullLocation = `${street.trim()}, ${city.trim()}, ${state.trim()}`;
              if (!fullLocation.toLowerCase().includes("contact") && !fullLocation.toLowerCase().includes("email")) {
                return cleanString(fullLocation) || "TBD";
              }
            } else if (match[1]) {
              // Just the address part
              const location = match[1].trim();
              if (!location.toLowerCase().includes("contact") && !location.toLowerCase().includes("email")) {
                return cleanString(location) || "TBD";
              }
            }
          }
        }

        // Fourth priority: Look for numbered addresses (common pattern)
        const numberedAddressPattern = /(\d+\s+[^,]+(?:street|avenue|ave|road|rd|boulevard|blvd|drive|dr|lane|ln|place|pl))[^a-z]*([a-zA-Z\s]+(?:angeles|francisco|york|chicago|houston|phoenix|philadelphia|antonio|diego|dallas|jose|austin|worth|columbus|charlotte|indianapolis|seattle|denver|washington|boston|nashville|baltimore|oklahoma|louisville|portland|vegas|memphis|milwaukee|albuquerque|tucson|fresno|sacramento|mesa|kansas|atlanta|colorado|raleigh|omaha|miami|oakland|tulsa|cleveland|wichita|arlington))/i;
        const numberedMatch = textToSearch.match(numberedAddressPattern);
        if (numberedMatch) {
          const [, street, cityPart] = numberedMatch;
          const location = `${street.trim()}, ${cityPart.trim()}`;
          if (!location.toLowerCase().includes("contact") && !location.toLowerCase().includes("email")) {
            return cleanString(location) || "TBD";
          }
        }

        // Fifth priority: Look for city, state patterns (excluding contact info)
        const cityStatePattern = /(?<!contact|email|phone|attn:?|attention:?)\s+([a-zA-Z\s]+),\s*([A-Z]{2})\b(?!\s*\d{5}(?:-\d{4})?)/i;
        const cityStateMatch = textToSearch.match(cityStatePattern);
        if (cityStateMatch) {
          const [, city, state] = cityStateMatch;
          // Validate that this looks like a real city name and isn't contact info
          if (city.length > 2 && !city.match(/^[A-Z]{2,}$/) && 
              !city.toLowerCase().includes("contact") && 
              !city.toLowerCase().includes("manager") &&
              !city.toLowerCase().includes("officer")) {
            return cleanString(`${city.trim()}, ${state.trim()}`) || "TBD";
          }
        }

        return "TBD";
      };