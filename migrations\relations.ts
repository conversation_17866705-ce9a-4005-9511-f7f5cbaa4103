import { relations } from "drizzle-orm/relations";
import { rfqs, bids, contractors, rfqDocuments, users, bidDocuments, rfqDistribution, contractorFavorites } from "./schema";

export const bidsRelations = relations(bids, ({one, many}) => ({
	rfq: one(rfqs, {
		fields: [bids.rfqId],
		references: [rfqs.id]
	}),
	contractor: one(contractors, {
		fields: [bids.contractorId],
		references: [contractors.id]
	}),
	bidDocuments: many(bidDocuments),
}));

export const rfqsRelations = relations(rfqs, ({one, many}) => ({
	bids: many(bids),
	rfqDocuments: many(rfqDocuments),
	rfqDistributions: many(rfqDistribution),
	user: one(users, {
		fields: [rfqs.createdBy],
		references: [users.id]
	}),
}));

export const contractorsRelations = relations(contractors, ({one, many}) => ({
	bids: many(bids),
	user: one(users, {
		fields: [contractors.userId],
		references: [users.id]
	}),
	rfqDistributions: many(rfqDistribution),
	contractorFavorites: many(contractorFavorites),
}));

export const rfqDocumentsRelations = relations(rfqDocuments, ({one}) => ({
	rfq: one(rfqs, {
		fields: [rfqDocuments.rfqId],
		references: [rfqs.id]
	}),
}));

export const usersRelations = relations(users, ({many}) => ({
	contractors: many(contractors),
	contractorFavorites: many(contractorFavorites),
	rfqs: many(rfqs),
}));

export const bidDocumentsRelations = relations(bidDocuments, ({one}) => ({
	bid: one(bids, {
		fields: [bidDocuments.bidId],
		references: [bids.id]
	}),
}));

export const rfqDistributionRelations = relations(rfqDistribution, ({one}) => ({
	rfq: one(rfqs, {
		fields: [rfqDistribution.rfqId],
		references: [rfqs.id]
	}),
	contractor: one(contractors, {
		fields: [rfqDistribution.contractorId],
		references: [contractors.id]
	}),
}));

export const contractorFavoritesRelations = relations(contractorFavorites, ({one}) => ({
	user: one(users, {
		fields: [contractorFavorites.userId],
		references: [users.id]
	}),
	contractor: one(contractors, {
		fields: [contractorFavorites.contractorId],
		references: [contractors.id]
	}),
}));