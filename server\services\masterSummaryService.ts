
import { db } from "../db"; import { callGroqAPI } from "./aiService";
import { rfqs, rfqDocuments } from "@shared/schema";
import { eq } from "drizzle-orm";

interface ConflictFlag {
  type: 'date' | 'amount' | 'requirement' | 'specification';
  field: string;
  documents: string[];
  values: any[];
  severity: 'low' | 'medium' | 'high';
}

interface DocumentSummary {
  fileName: string;
  extractedText: string;
  summary: string;
}

export async function generateMasterSummary(rfqId: string): Promise<{
  masterSummary: string;
  conflicts: ConflictFlag[];
}> {
  try {
    console.log(`[MasterSummary] Starting generation for RFQ: ${rfqId}`);

    // Get RFQ and all documents
    const [rfq] = await db.select().from(rfqs).where(eq(rfqs.id, rfqId));
    if (!rfq) throw new Error("RFQ not found");

    const documents = await db.select().from(rfqDocuments).where(eq(rfqDocuments.rfqId, rfqId));

    // Extract all summaries and text
    const documentSummaries: DocumentSummary[] = documents.map(doc => ({
      fileName: doc.fileName,
      extractedText: doc.extractedText || '',
      summary: doc.extractedText ? `Summary of ${doc.fileName}` : ''
    }));

    // Detect conflicts
    const conflicts = detectConflicts(documents, rfq.extractedData);

    // Generate consolidated summary using AI
    const masterSummary = await generateConsolidatedSummary({
      projectName: rfq.projectName,
      projectDescription: rfq.description,
      documents: documentSummaries,
      extractedData: rfq.extractedData,
      conflicts
    });

    // Truncate summary if too long to prevent database index size errors
    const maxSummaryLength = 8000; // Conservative limit for database storage
    const truncatedSummary = masterSummary.length > maxSummaryLength 
      ? masterSummary.substring(0, maxSummaryLength) + '...\n\n[Summary truncated due to length]'
      : masterSummary;

    // Update database
    await db.update(rfqs)
      .set({
        masterSummary: truncatedSummary as any,
        conflictFlags: conflicts as any,
        summaryGeneratedAt: new Date()
      } as any)
      .where(eq(rfqs.id, rfqId));

    console.log(`[MasterSummary] Generation complete for RFQ: ${rfqId}`);
    return { masterSummary, conflicts };

  } catch (error) {
    console.error(`[MasterSummary] Error generating summary:`, error);
    throw error;
  }
}

function detectConflicts(documents: any[], extractedData: any): ConflictFlag[] {
  const conflicts: ConflictFlag[] = [];

  if (!extractedData || !documents.length) return conflicts;

  // Date conflict detection
  const dateFields = ['dueDate', 'finalAwardDate', 'completionDate'];
  for (const field of dateFields) {
    const values = extractUniqueDates(documents, extractedData, field);
    if (values.length > 1) {
      conflicts.push({
        type: 'date',
        field,
        documents: values.map(v => v.source),
        values: values.map(v => v.value),
        severity: field === 'dueDate' ? 'high' : 'medium'
      });
    }
  }

  // Amount conflict detection
  const amountFields = ['budgetAmount', 'estimatedValue'];
  for (const field of amountFields) {
    const values = extractUniqueAmounts(documents, extractedData, field);
    if (values.length > 1) {
      const variance = calculateVariance(values.map(v => parseFloat(v.value) || 0));
      conflicts.push({
        type: 'amount',
        field,
        documents: values.map(v => v.source),
        values: values.map(v => v.value),
        severity: variance > 0.2 ? 'high' : 'medium'
      });
    }
  }

  return conflicts;
}

function extractUniqueDates(documents: any[], extractedData: any, field: string): Array<{source: string, value: any}> {
  const dateValues: Array<{source: string, value: any}> = [];

  // Check main extracted data
  if (extractedData && extractedData[field]) {
    dateValues.push({
      source: 'Main Document',
      value: extractedData[field]
    });
  }

  // Check individual documents
  documents.forEach(doc => {
    if (doc.extractedText && doc.extractedText.includes(field)) {
      // Simple extraction - in real implementation, use more sophisticated parsing
      const match = doc.extractedText.match(new RegExp(`${field}[:\\s]+([0-9\\/\\-]+)`, 'i'));
      if (match && match[1] && !dateValues.some(v => v.value === match[1])) {
        dateValues.push({
          source: doc.fileName,
          value: match[1]
        });
      }
    }
  });

  return dateValues;
}

function extractUniqueAmounts(documents: any[], extractedData: any, field: string): Array<{source: string, value: any}> {
  const amountValues: Array<{source: string, value: any}> = [];

  // Check main extracted data
  if (extractedData && extractedData[field]) {
    amountValues.push({
      source: 'Main Document',
      value: extractedData[field]
    });
  }

  // Check individual documents
  documents.forEach(doc => {
    if (doc.extractedText && doc.extractedText.includes('$')) {
      // Simple amount extraction - in real implementation, use more sophisticated parsing
      const matches = doc.extractedText.match(/\$[\d,]+(\.\d{2})?/g);
      if (matches && matches.length > 0) {
        matches.forEach(amount => {
          if (!amountValues.some(v => v.value === amount)) {
            amountValues.push({
              source: doc.fileName,
              value: amount
            });
          }
        });
      }
    }
  });

  return amountValues;
}

function calculateVariance(values: number[]): number {
  if (values.length < 2) return 0;

  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;

  return variance / (mean * mean); // Coefficient of variation
}

async function generateConsolidatedSummary(data: {
  projectName: string;
  projectDescription: string | null;
  documents: DocumentSummary[];
  extractedData: any;
  conflicts: ConflictFlag[];
}): Promise<string> {
  try {
    // Use existing AI service to generate summary
    const prompt = `
You are an expert construction project analyst. Create a comprehensive master summary by consolidating information from multiple documents.

PROJECT INFORMATION:
- Name: ${data.projectName}
- Description: ${data.projectDescription || 'Not provided'}

DOCUMENTS ANALYZED:
${data.documents.map(doc => `- ${doc.fileName}: ${doc.extractedText.substring(0, 200)}...`).join('\n')}

EXTRACTED DATA:
${JSON.stringify(data.extractedData, null, 2)}

CONFLICTS DETECTED:
${data.conflicts.map(c => `- ${c.type.toUpperCase()} conflict in ${c.field}: ${c.values.join(' vs ')}`).join('\n')}

INSTRUCTIONS:
1. Create a comprehensive project summary that consolidates all key information
2. Highlight any conflicting information and provide recommendations
3. Include all critical dates, amounts, requirements, and specifications
4. Structure the summary in clear sections: Overview, Scope, Timeline, Budget, Requirements, Risks
5. Flag any missing or unclear information that needs clarification
6. Provide actionable recommendations for resolving conflicts

Format as HTML with proper headings and structure for display.
`;

    const response = await callGroqAPI(prompt, 'consolidation');
    return response || 'Unable to generate master summary at this time.';

  } catch (error) {
    console.error('[MasterSummary] Error in AI generation:', error);
    return generateFallbackSummary(data);
  }
}

function generateFallbackSummary(data: {
  projectName: string;
  projectDescription: string | null;
  documents: DocumentSummary[];
  extractedData: any;
  conflicts: ConflictFlag[];
}): string {
  return `
    <div>
      <h2>Master Project Summary</h2>
      <h3>Project: ${data.projectName}</h3>

      <h4>Overview</h4>
      <p>${data.projectDescription || 'No description provided'}</p>

      <h4>Documents Analyzed</h4>
      <ul>
        ${data.documents.map(doc => `<li>${doc.fileName}</li>`).join('')}
      </ul>

      <h4>Key Information</h4>
      <pre>${JSON.stringify(data.extractedData, null, 2)}</pre>

      ${data.conflicts.length > 0 ? `
        <h4>Conflicts Detected</h4>
        <ul>
          ${data.conflicts.map(c => `
            <li><strong>${c.type}</strong> in ${c.field}: ${c.values.join(' vs ')}</li>
          `).join('')}
        </ul>
      ` : ''}

      <p><em>This is a basic summary. For enhanced analysis, please ensure AI services are properly configured.</em></p>
    </div>
  `;
}