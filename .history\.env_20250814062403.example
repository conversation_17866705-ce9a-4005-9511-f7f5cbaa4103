# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database

# Clerk Authentication
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key

# AI Services
GROQ_API_KEY=gsk_your_groq_api_key
OPENAI_API_KEY=sk-your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# Wasabi Object Storage (NEW)
WASABI_ACCESS_KEY_ID=your_wasabi_access_key_id
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_access_key
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com
WASABI_REGION=us-east-1

# Application Configuration
NODE_ENV=development
PRIMARY_MODEL=groq
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here

# Optional: For development
PORT=5000
