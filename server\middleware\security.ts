/**
 * Security Middleware Configuration
 * Implements comprehensive security headers and CORS policies
 */

import helmet, { type HelmetOptions } from 'helmet';
import cors from 'cors';
import { Request, Response, NextFunction } from 'express';

// Environment-based configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// CORS Configuration
export const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (mobile apps, curl, server-side fetch)
    if (!origin) return callback(null, true);

    // Development is permissive
    if (isDevelopment) return callback(null, true);

    // Static list + env-provided
    const allowedOrigins = [
      'https://bidaible.com',
      ...(process.env.REPLIT_DOMAINS?.split(',') || []),
      process.env.APP_ORIGIN,
      process.env.WEB_APP_ORIGIN,
    ].filter(Boolean) as string[];

    // Dynamic patterns for common PaaS preview domains (Railway, Vercel, Netlify, Render)
    const dynamicHostPatterns = [
      /\.up\.railway\.app$/i,
      /\.vercel\.app$/i,
      /\.netlify\.app$/i,
      /\.onrender\.com$/i,
    ];

    try {
      const url = new URL(origin);
      const hostname = url.hostname;

      const isExplicitlyAllowed = allowedOrigins.includes(origin);
      const matchesPattern = dynamicHostPatterns.some((re) => re.test(hostname));

      if (isExplicitlyAllowed || matchesPattern) {
        return callback(null, true);
      }
    } catch (_err) {
      // If origin is not a valid URL, reject safely
    }

    // Log rejected origins for monitoring
    console.warn(`CORS: Rejected origin: ${origin}`);
    return callback(new Error('Not allowed by CORS'), false);
  },
  credentials: true, // Allow cookies and authentication headers
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key'
  ],
  exposedHeaders: [
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining', 
    'X-RateLimit-Reset'
  ],
  maxAge: 86400 // 24 hours preflight cache
};

// Helmet Security Configuration
export const helmetOptions: HelmetOptions = {
  // Content Security Policy - DISABLED IN PRODUCTION FOR NOW
  contentSecurityPolicy: isProduction ? false : {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: [
        "'self'",
        "'unsafe-inline'", // Required for Vite builds and inline scripts
        "'unsafe-eval'", // Required for Vite builds in production too
        "https://api.groq.com",
        "https://api.openai.com", 
        "https://api.perplexity.ai",
        "https://replit.com", // Allow Replit banner script
        "https://*.clerk.accounts.dev", // Allow Clerk authentication scripts
        "https://*.clerk.com" // Allow Clerk authentication scripts
      ],
      styleSrc: [
        "'self'",
        "'unsafe-inline'", // Required for dynamic styles
        "https://fonts.googleapis.com"
      ],
      fontSrc: [
        "'self'",
        "https://fonts.gstatic.com"
      ],
      imgSrc: [
        "'self'",
        "data:",
        "blob:",
        "https:"
      ],
      connectSrc: [
        "'self'",
        "https://api.groq.com",
        "https://api.openai.com", 
        "https://api.perplexity.ai",
        "https://replit.com",
        "https://*.clerk.accounts.dev", // Allow Clerk API connections
        "https://*.clerk.com", // Allow Clerk API connections
        "ws://localhost:*", 
        "http://localhost:*",
        "wss://*", // Allow WebSocket connections for deployed apps
        "https://*" // Allow HTTPS connections for deployed apps
      ],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: [
        "https://*.clerk.accounts.dev", // Allow Clerk authentication frames
        "https://*.clerk.com" // Allow Clerk authentication frames
      ],
      workerSrc: [
        "'self'",
        "blob:", // Allow Clerk web workers
        "https://*.clerk.accounts.dev",
        "https://*.clerk.com"
      ],
      baseUri: ["'self'"],
      formAction: ["'self'"]
    },
    reportOnly: true // Always use report-only to prevent blocking during debugging
  },

  // HTTP Strict Transport Security (HSTS)
  hsts: {
    maxAge: ********, // 1 year
    includeSubDomains: true,
    preload: true
  },

  // Prevent clickjacking
  frameguard: {
    action: 'deny'
  },

  // Prevent MIME type sniffing
  noSniff: true,

  // Referrer Policy
  referrerPolicy: {
     policy: ['strict-origin-when-cross-origin'] as const
  },
  
  // Hide X-Powered-By header
  hidePoweredBy: true,
  
  // DNS Prefetch Control
  dnsPrefetchControl: {
     allow: false
  },
  
  // Permission Policy (formerly Feature Policy)
  permittedCrossDomainPolicies: false
};

// Custom security headers middleware
export function additionalSecurityHeaders(req: Request, res: Response, next: NextFunction) {
  // Additional security headers not covered by helmet
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Download-Options', 'noopen');
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  
  // Cache control for security-sensitive endpoints
  if (req.path.includes('/api/auth') || req.path.includes('/api/admin')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }

  next();
}

// Security logging middleware
export function securityLogger(req: Request, res: Response, next: NextFunction) {
  // Log potentially suspicious requests
  const suspiciousPatterns = [
    /\.\./,           // Directory traversal
    /<script/i,       // XSS attempts
    /union.*select/i, // SQL injection attempts
    /javascript:/i,   // JavaScript protocol
    /data:.*base64/i  // Base64 data URLs
  ];

  const userAgent = req.get('User-Agent');
  const referer = req.get('Referer');
  const requestPath = req.path;
  const requestBody = JSON.stringify(req.body);

  // Check for suspicious patterns
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(requestPath) || 
    pattern.test(requestBody) ||
    (userAgent && pattern.test(userAgent)) ||
    (referer && pattern.test(referer))
  );

  if (isSuspicious) {
    console.warn('⚠️ Suspicious request detected:', {
      ip: req.ip,
      method: req.method,
      path: requestPath,
      userAgent,
      referer,
      timestamp: new Date().toISOString()
    });
  }

  next();
}

// Rate limiting for security endpoints
const rateLimitMap = new Map<string, { count: number, resetTime: number }>();

export function securityRateLimit(req: Request, res: Response, next: NextFunction) {
  const securityEndpoints = ['/api/auth', '/api/admin'];
  
  if (!securityEndpoints.some(endpoint => req.path.startsWith(endpoint))) {
    return next();
  }

  const clientId = req.ip || 'unknown';
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxRequests = 100; // Max 100 requests per 15 minutes for security endpoints

  let rateLimitData = rateLimitMap.get(clientId);
  
  if (!rateLimitData || now > rateLimitData.resetTime) {
    rateLimitData = { count: 0, resetTime: now + windowMs };
    rateLimitMap.set(clientId, rateLimitData);
  }

  if (rateLimitData.count >= maxRequests) {
    return res.status(429).json({
      message: 'Too many security requests. Please try again later.',
      retryAfter: Math.ceil((rateLimitData.resetTime - now) / 1000)
    });
  }

  rateLimitData.count++;
  rateLimitMap.set(clientId, rateLimitData);

  // Set rate limit headers
  res.setHeader('X-RateLimit-Limit', maxRequests.toString());
  res.setHeader('X-RateLimit-Remaining', (maxRequests - rateLimitData.count).toString());
  res.setHeader('X-RateLimit-Reset', new Date(rateLimitData.resetTime).toISOString());

  next();
}

// Legacy middleware exports for backward compatibility
export const auditLogger = securityLogger;
export const rateLimiter = securityRateLimit;
export const securityHeaders = additionalSecurityHeaders;

// Simple IP-based rate limiter factory for attaching to specific routes
export function createSimpleRateLimiter(windowMs: number, maxRequests: number) {
  const store = new Map<string, { count: number; resetTime: number }>();
  return function simpleRateLimiter(req: Request, res: Response, next: NextFunction) {
    const clientId = req.ip || 'unknown';
    const now = Date.now();
    let data = store.get(clientId);
    if (!data || now > data.resetTime) {
      data = { count: 0, resetTime: now + windowMs };
      store.set(clientId, data);
    }
    if (data.count >= maxRequests) {
      res.setHeader('Retry-After', Math.ceil((data.resetTime - now) / 1000));
      return res.status(429).json({ message: 'Too many requests. Please try again later.' });
    }
    data.count++;
    store.set(clientId, data);
    res.setHeader('X-RateLimit-Limit', String(maxRequests));
    res.setHeader('X-RateLimit-Remaining', String(Math.max(0, maxRequests - data.count)));
    res.setHeader('X-RateLimit-Reset', new Date(data.resetTime).toISOString());
    next();
  };
}

// Default sensitive endpoints rate limiter (60 req/min per IP)
export const sensitiveRateLimit = createSimpleRateLimiter(60 * 1000, 60);

// Input sanitization middleware
export function sanitizeInput(req: Request, res: Response, next: NextFunction) {
  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    sanitizeObject(req.body);
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    sanitizeObject(req.query);
  }

  next();
}

// Helper function to recursively sanitize objects
function sanitizeObject(obj: any): void {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === 'string') {
        // Remove null characters and control characters
        obj[key] = obj[key].replace(/\0/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizeObject(obj[key]);
      }
    }
  }
}

// Audit logging functions
export async function getAuditLogs(limit: number = 100, offset: number = 0) {
  // Return mock audit logs for now - this would typically come from database
  return {
    logs: [],
    total: 0,
    limit,
    offset
  };
}

export async function getSecurityStats() {
  // Return comprehensive security statistics
  return {
    totalRequests: rateLimitMap.size,
    blockedRequests: 0,
    suspiciousRequests: 0,
    timestamp: new Date().toISOString(),
    securityFeatures: {
      headersEnabled: true,
      corsConfigured: true,
      rateLimitingActive: true,
      inputSanitization: true,
      sessionSecurity: {
        ttl: '72 hours',
        slidingExpiration: true,
        secureCookies: true,
        sameSite: 'strict',
        sessionInvalidationOnRoleChange: true
      }
    }
  };
}