import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getQueryFn } from "@/lib/queryClient";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, MapPin, DollarSign, FileText, Users, Settings, Code, Edit2, Download, File, Eye, Sparkles } from "lucide-react";
import { format } from "date-fns";
import { RFQEditForm } from "./RFQEditForm";
import { SimplePDFViewer } from "./SimplePDFViewer";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Simple markdown renderer for AI summaries
function MarkdownRenderer({ content }: { content: string }) {
  const formatMarkdown = (text: string) => {
    return text
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-4 mb-2 text-foreground">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mt-4 mb-2 text-foreground">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-4 mb-2 text-foreground">$1</h1>')
      .replace(/^\* (.*$)/gm, '<li class="ml-4 mb-1">$1</li>')
      .replace(/^- (.*$)/gm, '<li class="ml-4 mb-1">$1</li>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p class="mb-2">')
      .replace(/(<li.*?>.*?<\/li>)/gs, '<ul class="list-disc ml-4 mb-2">$1</ul>')
      .replace(/<\/ul>\s*<ul class="list-disc ml-4 mb-2">/g, '');
  };

  return (
    <div 
      className="prose prose-sm max-w-none"
      dangerouslySetInnerHTML={{ 
        __html: `<p class="mb-2">${formatMarkdown(content)}</p>` 
      }}
    />
  );
}

interface RFQDetailsModalProps {
  rfq: any;
  open: boolean;
  onClose: () => void;
  onEdit?: () => void;
}

export function RFQDetailsModal({ rfq, open, onClose, onEdit }: RFQDetailsModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [viewingDocument, setViewingDocument] = useState<any>(null);

  // Fetch RFQ documents  
  const { data: documents = [], error, isLoading, isError } = useQuery({
    queryKey: [`/api/rfqs/${rfq?.id}/documents`],
    queryFn: getQueryFn({ on401: "throw" }),
    enabled: !!rfq?.id && open,
    retry: false,
  });

  // Debug logging for documents query
  React.useEffect(() => {
    if (rfq?.id && open) {
      console.log('🔍 [RFQDetailsModal] Documents query state:', {
        rfqId: rfq.id,
        documentsCount: documents?.length || 0,
        isLoading,
        isError,
        error: error?.message
      });
    }
  }, [rfq?.id, open, documents, isLoading, isError, error]);

  if (!rfq) return null;

  const handleEditClick = () => {
    setIsEditing(true);
    onEdit?.();
  };

  const handleSaveEdit = () => {
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Draft": return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
      case "Active": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "Review": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "Closed": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "Awarded": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const getTradeColor = (trade: string) => {
    const colors = {
      electrical: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      plumbing: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      hvac: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      concrete: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
      general: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
      site_work: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    };
    return colors[trade as keyof typeof colors] || colors.general;
  };

  const handleViewFile = async (documentId: string, fileName: string) => {
    // Open the file directly in a new tab
    window.open(`/api/files/${documentId}?view=true`, '_blank');
  };

  const handleDownloadFile = async (documentId: string, fileName: string) => {
    try {
      const response = await fetch(`/api/files/${documentId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to download file');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto" aria-describedby="rfq-details-description">
        <div id="rfq-details-description" className="sr-only">
          View detailed information about the RFQ including AI extracted data and database records
        </div>
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{rfq.projectName}</span>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(rfq.status)}>
                {rfq.status}
              </Badge>
              {!isEditing && (
                <Button variant="outline" size="sm" onClick={handleEditClick}>
                  <Edit2 className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        {isEditing ? (
          <RFQEditForm
            rfq={rfq}
            onSave={handleSaveEdit}
            onCancel={handleCancelEdit}
          />
        ) : (
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details">RFQ Details</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="debug">Debug Data</TabsTrigger>
            </TabsList>

          <TabsContent value="details" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Project Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Project Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Project Name</label>
                    <p className="mt-1">{rfq.projectName}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Location</label>
                    <div className="flex items-center gap-1 mt-1">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <p>{rfq.projectLocation || "TBD"}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="mt-1">{rfq.description || "No description available"}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Trade Category</label>
                    <div className="mt-1">
                      <Badge variant="outline" className={getTradeColor(rfq.tradeCategory)}>
                        {rfq.tradeCategory?.replace('_', ' ') || 'General'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Timeline & Value */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Timeline & Value
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Due Date</label>
                    <div className="flex items-center gap-1 mt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <p>{rfq.dueDate ? format(new Date(rfq.dueDate), "PPP") : "Not specified"}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Project Value</label>
                    <div className="flex items-center gap-1 mt-1">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <p>{rfq.extractedData?.projectValue || "Not specified"}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Created</label>
                    <p className="mt-1">{format(new Date(rfq.createdAt), "PPp")}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Bid Responses</label>
                    <div className="flex items-center gap-1 mt-1">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <p>0 bids received</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* AI Summary */}
            {(rfq.aiSummary || rfq.extractedData) && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI Summary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {rfq.aiSummary ? (
                    
                    <div className="prose prose-sm max-w-none">
                      <ReactMarkdown 
                            remarkPlugins={[remarkGfm]}
                            components={{
                              table: ({ children }) => (
                                <div className="overflow-x-auto">
                                  <table className="min-w-full border border-border rounded-lg">{children}</table>
                                </div>
                              ),
                              th: ({ children }) => (
                                <th className="border border-border bg-muted px-2 py-1 text-left font-medium">{children}</th>
                              ),
                              td: ({ children }) => (
                                <td className="border border-border px-2 py-1">{children}</td>
                              ),
                              h1: ({ children }) => (
                                <h1 className="text-xl font-bold mb-3 text-foreground">{children}</h1>
                              ),
                              h2: ({ children }) => (
                                <h2 className="text-lg font-semibold mb-2 text-foreground">{children}</h2>
                              ),
                              h3: ({ children }) => (
                                <h3 className="text-base font-medium mb-2 text-foreground">{children}</h3>
                              ),
                              ul: ({ children }) => (
                                <ul className="list-disc list-inside space-y-1 mb-3">{children}</ul>
                              ),
                              ol: ({ children }) => (
                                <ol className="list-decimal list-inside space-y-1 mb-3">{children}</ol>
                              ),
                              blockquote: ({ children }) => (
                                <blockquote className="border-l-4 border-primary pl-4 italic bg-muted p-3 rounded-r-lg mb-3">{children}</blockquote>
                              ),
                              strong: ({ children }) => (
                                <strong className="font-semibold text-foreground">{children}</strong>
                              )
                            }}
                          >
                            {rfq.aiSummary}
                          </ReactMarkdown>
                    </div>
                  
                  ) : (
                    <div className="space-y-4">
                      {rfq.extractedData?.contactName && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Contact Name</label>
                          <p className="mt-1 text-sm">{rfq.extractedData.contactName}</p>
                        </div>
                      )}

                      {rfq.extractedData?.contactEmail && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Contact Email</label>
                          <p className="mt-1 text-sm">{rfq.extractedData.contactEmail}</p>
                        </div>
                      )}

                      {rfq.extractedData?.projectDescription && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Project Description</label>
                          <p className="mt-1 text-sm">{rfq.extractedData.projectDescription}</p>
                        </div>
                      )}

                      {rfq.extractedData?.requirements && rfq.extractedData.requirements.length > 0 && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Requirements</label>
                          <ul className="mt-2 space-y-1">
                            {rfq.extractedData.requirements.map((req: string, index: number) => (
                              <li key={index} className="text-sm flex items-start gap-2">
                                <span className="text-muted-foreground">•</span>
                                {req}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {rfq.extractedData?.finalAwardDate && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Final Award Date</label>
                          <p className="mt-1 text-sm">{rfq.extractedData.finalAwardDate}</p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="documents" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <File className="h-5 w-5" />
                  Uploaded Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                {(!documents || documents.length === 0) ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <File className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No documents uploaded for this RFQ</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {documents?.map((doc: any) => (
                      <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">{doc.fileName}</p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{formatFileSize(doc.fileSize || 0)}</span>
                            <span>{doc.mimeType}</span>
                            <span>Uploaded {format(new Date(doc.createdAt), "PPp")}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleViewFile(doc.id, doc.fileName)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDownloadFile(doc.id, doc.fileName)}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Debug Tab */}
        <TabsContent value="debug" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Extracted Data
              </CardTitle>
            </CardHeader>
            <CardContent>
              {rfq.extractedData ? (
                <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto text-sm">
                  {JSON.stringify(rfq.extractedData, null, 2)}
                </pre>
              ) : (
                <p className="text-gray-500">No extracted data available</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Database Record
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto text-sm">
                {JSON.stringify({
                  id: rfq.id,
                  projectName: rfq.projectName,
                  projectLocation: rfq.projectLocation,
                  description: rfq.description,
                  tradeCategory: rfq.tradeCategory,
                  status: rfq.status,
                  dueDate: rfq.dueDate,
                  createdAt: rfq.createdAt,
                  createdBy: rfq.createdBy
                }, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
        )}

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button onClick={() => setIsEditing(true)}>
            <Edit2 className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </DialogContent>
  </Dialog>

    {/* PDF Viewer Modal */}
    {viewingDocument && (
      <Dialog open={!!viewingDocument} onOpenChange={() => setViewingDocument(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden" aria-describedby="pdf-viewer-description">
          <div id="pdf-viewer-description" className="sr-only">
            PDF document viewer for {viewingDocument?.fileName}
          </div>
          <DialogHeader>
            <DialogTitle>
              {viewingDocument?.fileName}
            </DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-hidden h-[75vh]">
            {viewingDocument.isPDF ? (
              <SimplePDFViewer 
                documentId={viewingDocument.id}
                fileName={viewingDocument.fileName}
                onDownload={() => {
                  const link = document.createElement('a');
                  link.href = `/api/files/${viewingDocument.id}`;
                  link.download = viewingDocument.fileName;
                  link.click();
                }}
              />
            ) : (
              <div className="flex flex-col items-center justify-center h-full space-y-4">
                <File className="h-16 w-16 text-gray-400" />
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Preview Not Available
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mt-2">
                    This file type cannot be previewed in the browser.
                  </p>
                  <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                    {viewingDocument.fileName}
                  </p>
                </div>
                <Button 
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = `/api/files/${viewingDocument.id}`;
                    link.download = viewingDocument.fileName;
                    link.click();
                  }}
                  className="mt-4"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download File
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    )}
    </>
  );
}