import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Shield, Users, Activity, FileText, Upload, Trash2, Edit, Eye, User } from "lucide-react";
import { format } from "date-fns";

interface AuditLog {
  id: string;
  userId: string;
  organizationId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  createdAt: string;
}

const actionIcons = {
  'file_upload': Upload,
  'file_delete': Trash2,
  'rfq_create': FileText,
  'rfq_update': Edit,
  'rfq_view': Eye,
  'user_login': User,
  'security_check': Shield,
};

const actionColors = {
  'file_upload': 'bg-blue-100 text-blue-800',
  'file_delete': 'bg-red-100 text-red-800',
  'rfq_create': 'bg-green-100 text-green-800',
  'rfq_update': 'bg-yellow-100 text-yellow-800',
  'rfq_view': 'bg-gray-100 text-gray-800',
  'user_login': 'bg-purple-100 text-purple-800',
  'security_check': 'bg-orange-100 text-orange-800',
};

export default function AuditLogsComponent() {
  const [activeTab, setActiveTab] = useState("business");

  // Fetch different types of audit logs
  const { data: businessLogs, isLoading: businessLoading } = useQuery({
    queryKey: ["/api/user/activity"],
  });

  const { data: accessLogs, isLoading: accessLoading } = useQuery({
    queryKey: ["/api/admin/audit/access"],
    retry: false, // Don't retry if access denied
  });

  const { data: roleLogs, isLoading: roleLoading } = useQuery({
    queryKey: ["/api/admin/audit/roles"],
    retry: false, // Don't retry if access denied
  });

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-2">Audit Logs</h2>
        <p className="text-muted-foreground">Monitor system access and role changes for security compliance.</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Access Audit Trail
          </CardTitle>
        </CardHeader>
        <CardContent>
              {businessLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-pulse text-muted-foreground">Loading audit logs...</div>
                </div>
              ) : !businessLogs?.length ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No business events found</p>
                  <p className="text-sm">Your file uploads and system activity will appear here</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Resource</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Reason</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {businessLogs.slice(0, 20).map((log: any) => (
                        <TableRow key={log.id}>
                          <TableCell>
                            {format(new Date(log.createdAt), 'M/d/yyyy, h:mm:ss a')}
                          </TableCell>
                          <TableCell className="font-medium">
                            {log.userId?.slice(0, 8) || 'Unknown'}
                          </TableCell>
                          <TableCell>
                            <span className="font-mono text-sm">
                              {log.eventType || log.action || 'Unknown Action'}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="font-mono text-sm">
                              {log.resource || log.resourceId || '-'}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge variant={log.success !== false ? "default" : "destructive"}>
                              {log.success !== false ? "Success" : "Failed"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-muted-foreground">
                            {log.details || log.reason || '-'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
        </CardContent>
      </Card>
    </div>
  );
}
