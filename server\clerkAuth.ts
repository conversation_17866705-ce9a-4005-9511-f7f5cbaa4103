import 'dotenv/config';
import { clerkMiddleware, requireAuth, getAuth, clerkClient } from '@clerk/express';
import type { RequestHandler } from 'express';

// Simple Clerk middleware - replaces complex OpenID Connect setup
export const authMiddleware = clerkMiddleware();

// Protected route middleware for API routes - returns JSON errors instead of redirects
export const protectedRoute: RequestHandler = (req, res, next) => {
  const { userId } = getAuth(req);
  
  if (!userId) {
    return res.status(401).json({ message: "Unauthorized" });
  }
  
  next();
};

// Export Clerk utilities
export { getAuth, clerkClient };

// Helper function to get user from Clerk
export const getCurrentUser = async (req: any) => {
  const { userId } = getAuth(req);
  if (!userId) return null;
  
  try {
    return await clerkClient.users.getUser(userId);
  } catch (error) {
    console.error('Error fetching user from Clerk:', error);
    return null;
  }
};

// Compatibility middleware for existing code that expects req.user.claims.sub
export const addUserToRequest: RequestHandler = async (req: any, res, next) => {
  const { userId } = getAuth(req);
  
  if (userId) {
    // Add user info in the format expected by existing code
    req.user = {
      claims: {
        sub: userId
      }
    };
  }
  
  next();
};