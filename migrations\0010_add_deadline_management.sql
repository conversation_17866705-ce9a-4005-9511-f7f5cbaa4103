-- Add bid proposal deadline and notification system for RFQ deadline management

-- Add bid_proposal_deadline_at column to RFQs table with default value based on existing due_date
ALTER TABLE rfqs ADD COLUMN bid_proposal_deadline_at TIMESTAMPTZ;

-- Update existing records to set bid_proposal_deadline_at to 7 days before due_date, or 1 day before if less than 7 days remain
UPDATE rfqs 
SET bid_proposal_deadline_at = CASE 
  WHEN due_date - INTERVAL '7 days' > NOW() THEN due_date - INTERVAL '7 days'
  WHEN due_date - INTERVAL '1 day' > NOW() THEN due_date - INTERVAL '1 day'
  ELSE NOW() + INTERVAL '1 hour'
END;

-- Now make the column NOT NULL
ALTER TABLE rfqs ALTER COLUMN bid_proposal_deadline_at SET NOT NULL;

-- Add check constraint ensuring bid_proposal_deadline_at < due_date (which is rfq_deadline_at)
ALTER TABLE rfqs ADD CONSTRAINT chk_bid_proposal_before_rfq_deadline 
CHECK (bid_proposal_deadline_at < due_date);

-- Create scheduled notification status enum
CREATE TYPE scheduled_notification_status AS ENUM ('pending', 'sent', 'failed', 'cancelled');

-- Update notification type to include RFQ deadline reminders
-- Note: This assumes notification types are stored as varchar, not enum
-- If they're enum, you would need to add the type to the existing enum

-- Create scheduled_notifications table for deadline management
CREATE TABLE scheduled_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_type TEXT NOT NULL,
  user_id VARCHAR REFERENCES users(id) NOT NULL,
  rfq_id UUID REFERENCES rfqs(id), -- Nullable for non-RFQ notifications
  scheduled_for TIMESTAMPTZ NOT NULL,
  payload JSONB NOT NULL DEFAULT '{}',
  status scheduled_notification_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance on scheduled notifications
CREATE INDEX IDX_scheduled_notifications_status_scheduled ON scheduled_notifications(status, scheduled_for);
CREATE INDEX IDX_scheduled_notifications_user_id ON scheduled_notifications(user_id);
CREATE INDEX IDX_scheduled_notifications_rfq_id ON scheduled_notifications(rfq_id);
CREATE INDEX IDX_scheduled_notifications_type ON scheduled_notifications(notification_type);
CREATE INDEX IDX_scheduled_notifications_created_at ON scheduled_notifications(created_at);

-- Add index for RFQ bid proposal deadline queries
CREATE INDEX IDX_rfqs_bid_proposal_deadline ON rfqs(bid_proposal_deadline_at);
CREATE INDEX IDX_rfqs_org_bid_deadline ON rfqs(organization_id, bid_proposal_deadline_at);
