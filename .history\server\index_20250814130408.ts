import { config } from 'dotenv';
import path from 'path';
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { auditLogger, rateLimiter, securityHeaders, sanitizeInput, corsOptions, helmetOptions } from "./middleware/security";
import { scheduleAutomaticBackups } from "./services/backupService";
import helmet from 'helmet';
import cors from 'cors';

// Load .env from root directory only in development
// Railway provides environment variables directly, no .env file needed
if (process.env.NODE_ENV !== 'production') {
  config({ path: path.resolve(process.cwd(), '.env') });
} else {
  // Debug logging for production environment variables
  console.log('=== PRODUCTION ENVIRONMENT DEBUG ===');
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);
  console.log('DATABASE_URL length:', process.env.DATABASE_URL?.length || 0);
  
  // Log all environment variables that contain 'DATABASE' (case insensitive)
  const dbVars = Object.keys(process.env).filter(key => 
    key.toLowerCase().includes('database') || key.toLowerCase().includes('db')
  );
  console.log('Database-related env vars:', dbVars);
  
  // Log first 50 chars of DATABASE_URL if it exists (for debugging without exposing full credentials)
  if (process.env.DATABASE_URL) {
    console.log('DATABASE_URL preview:', process.env.DATABASE_URL.substring(0, 50) + '...');
  }
  console.log('=== END ENVIRONMENT DEBUG ===');
}

const app = express();

// Security middleware (apply early)
app.use(helmet(helmetOptions));
app.use(cors(corsOptions));
app.use(securityHeaders);
app.use(rateLimiter);
app.use(sanitizeInput);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));

// Audit logging
app.use(auditLogger);

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  
  // Check if we're in Replit deployment
  const isReplitDeployment = process.env.REPL_DEPLOYMENT === "1";
  const isDevelopment = !isReplitDeployment && app.get("env") === "development";
  
  if (isDevelopment) {
    await setupVite(app, server);
  } else {
    console.log("Serving static files in production mode");
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = Number(process.env.PORT) || 5000;
  const host = process.env.NODE_ENV === 'production' ? "0.0.0.0" : "localhost";
  
  server.listen(port, host, () => {
    log(`serving on ${host}:${port}`);

    // Initialize automatic backups in production
    if (process.env.NODE_ENV === 'production') {
      scheduleAutomaticBackups();
    }
  });

  // Debug route to list available assets
  app.get('/api/debug/assets', (req, res) => {
    const fs = require('fs');
    const assetsPath = path.join(import.meta.dirname, '..', 'attached_assets');
    try {
      const files = fs.readdirSync(assetsPath);
      const imageFiles = files.filter((file: string) => 
        file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg')
      );
      res.json({ 
        assetsPath, 
        totalFiles: files.length,
        imageFiles: imageFiles.slice(0, 10) // First 10 images
      });
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

})();
