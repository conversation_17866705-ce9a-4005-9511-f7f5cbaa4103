import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import BidAnalyticsDashboard from "@/components/BidAnalyticsDashboard";
import { 
  Search, 
  Eye, 
  Download, 
  MessageSquare, 
  Calendar,
  DollarSign,
  Building2,
  Clock,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart3,
  TrendingUp
} from "lucide-react";

// Mock data for bid responses
const mockBids = [
  {
    id: "bid-001",
    rfqId: "rfq-001",
    projectName: "Downtown Office Complex - Electrical Work",
    contractorName: "Pacific Electric Solutions",
    contractorId: "contractor-001",
    bidAmount: 750000,
    submittedAt: "2024-01-15T10:30:00Z",
    status: "pending",
    tradeType: "electrical",
    proposalText: "Comprehensive electrical installation including power distribution, lighting systems, and emergency power systems. Our team has extensive experience with commercial high-rise projects.",
    projectLocation: "Seattle, WA",
    dueDate: "2024-01-20T17:00:00Z",
    responseTime: "2 days",
    contractorRating: 4.8,
    bondingCapacity: 5000000,
    insurance: "General Liability: $5M, Workers Comp: Current",
    attachments: ["electrical_proposal.pdf", "timeline.pdf", "certifications.pdf"]
  },
  {
    id: "bid-002",
    rfqId: "rfq-002",
    projectName: "Residential Complex - HVAC Installation",
    contractorName: "Climate Control Systems",
    contractorId: "contractor-002",
    bidAmount: 485000,
    submittedAt: "2024-01-14T14:45:00Z",
    status: "under_review",
    tradeType: "hvac",
    proposalText: "Complete HVAC system design and installation for 48-unit residential complex. Includes energy-efficient heat pumps, ductwork, and smart controls.",
    projectLocation: "Portland, OR",
    dueDate: "2024-01-19T17:00:00Z",
    responseTime: "1 day",
    contractorRating: 4.6,
    bondingCapacity: 2000000,
    insurance: "General Liability: $2M, Workers Comp: Current",
    attachments: ["hvac_proposal.pdf", "equipment_specs.pdf"]
  },
  {
    id: "bid-003",
    rfqId: "rfq-003",
    projectName: "Mixed-Use Development - Plumbing",
    contractorName: "Northwest Plumbing Contractors",
    contractorId: "contractor-003",
    bidAmount: 325000,
    submittedAt: "2024-01-13T09:15:00Z",
    status: "accepted",
    tradeType: "plumbing",
    proposalText: "Full plumbing installation for 48-unit residential complex including water, sewer, and gas lines. All work meets current building codes with 10-year warranty.",
    projectLocation: "Bellevue, WA",
    dueDate: "2024-01-18T17:00:00Z",
    responseTime: "3 hours",
    contractorRating: 4.7,
    bondingCapacity: 1500000,
    insurance: "General Liability: $1.5M, Workers Comp: Current",
    attachments: ["plumbing_proposal.pdf", "material_list.pdf"]
  },
  {
    id: "bid-004",
    rfqId: "rfq-004",
    projectName: "Industrial Warehouse - Concrete Work",
    contractorName: "Solid Foundation Contractors",
    contractorId: "contractor-004",
    bidAmount: 890000,
    submittedAt: "2024-01-12T14:20:00Z",
    status: "rejected",
    tradeType: "concrete",
    proposalText: "Concrete foundation and flooring for 100,000 sq ft industrial warehouse. Includes site preparation, reinforcement, and polished concrete finish.",
    projectLocation: "Tacoma, WA",
    dueDate: "2024-01-17T17:00:00Z",
    responseTime: "4 days",
    contractorRating: 4.5,
    bondingCapacity: 3000000,
    insurance: "General Liability: $3M, Workers Comp: Current",
    attachments: ["concrete_proposal.pdf", "foundation_plans.pdf"]
  },
  {
    id: "bid-005",
    rfqId: "rfq-005",
    projectName: "Shopping Center Renovation - General Contract",
    contractorName: "Metro Construction Group",
    contractorId: "contractor-005",
    bidAmount: 1250000,
    submittedAt: "2024-01-11T11:30:00Z",
    status: "pending",
    tradeType: "general",
    proposalText: "Complete renovation of 50,000 sq ft shopping center including structural improvements, new storefronts, and common area upgrades. Timeline: 6 months.",
    projectLocation: "Redmond, WA",
    dueDate: "2024-01-25T17:00:00Z",
    responseTime: "6 days",
    contractorRating: 4.6,
    bondingCapacity: 10000000,
    insurance: "General Liability: $10M, Workers Comp: Current",
    attachments: ["renovation_proposal.pdf", "project_schedule.pdf", "reference_projects.pdf"]
  },
  {
    id: "bid-006",
    rfqId: "rfq-006",
    projectName: "Hospital Addition - Fire Protection Systems",
    contractorName: "SafeGuard Fire Protection",
    contractorId: "contractor-006",
    bidAmount: 425000,
    submittedAt: "2024-01-10T16:45:00Z",
    status: "under_review",
    tradeType: "fire_protection",
    proposalText: "Installation of complete fire protection system including sprinklers, alarms, and emergency lighting for new hospital wing. All systems meet healthcare facility requirements.",
    projectLocation: "Seattle, WA",
    dueDate: "2024-01-22T17:00:00Z",
    responseTime: "12 hours",
    contractorRating: 4.9,
    bondingCapacity: 2500000,
    insurance: "General Liability: $3M, Workers Comp: Current",
    attachments: ["fire_protection_proposal.pdf", "system_diagrams.pdf", "compliance_docs.pdf"]
  }
];

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  under_review: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  accepted: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  rejected: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
};

const statusIcons = {
  pending: <Clock className="h-3 w-3" />,
  under_review: <AlertCircle className="h-3 w-3" />,
  accepted: <CheckCircle className="h-3 w-3" />,
  rejected: <XCircle className="h-3 w-3" />
};

export default function BidResponses() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [tradeFilter, setTradeFilter] = useState("all");
  const [selectedBid, setSelectedBid] = useState<typeof mockBids[0] | null>(null);
  const [activeTab, setActiveTab] = useState("responses");
  const [selectedRfqForAnalytics, setSelectedRfqForAnalytics] = useState<string | null>(null);

  const filteredBids = mockBids.filter(bid => {
    const matchesSearch = bid.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bid.contractorName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || bid.status === statusFilter;
    const matchesTrade = tradeFilter === "all" || bid.tradeType === tradeFilter;
    
    return matchesSearch && matchesStatus && matchesTrade;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getBidStatusSummary = () => {
    const summary = {
      total: mockBids.length,
      pending: mockBids.filter(b => b.status === 'pending').length,
      under_review: mockBids.filter(b => b.status === 'under_review').length,
      accepted: mockBids.filter(b => b.status === 'accepted').length,
      rejected: mockBids.filter(b => b.status === 'rejected').length
    };
    return summary;
  };

  const statusSummary = getBidStatusSummary();

  // Get unique RFQ IDs for analytics dropdown
  const uniqueRfqIds = Array.from(new Set(mockBids.map(bid => bid.rfqId)));
  const rfqProjects = uniqueRfqIds.map(rfqId => {
    const bid = mockBids.find(b => b.rfqId === rfqId);
    return {
      id: rfqId,
      name: bid?.projectName || `RFQ ${rfqId}`,
      location: bid?.projectLocation || 'Unknown'
    };
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Bid Responses</h1>
          <p className="text-muted-foreground">
            Review and manage contractor bid submissions
          </p>
        </div>
      </div>

      {/* Tabs for switching between responses and analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="responses" className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            Bid Responses
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Enhanced Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="responses" className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Bids</p>
                    <p className="text-2xl font-bold">{statusSummary.total}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Pending</p>
                    <p className="text-2xl font-bold text-yellow-600">{statusSummary.pending}</p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Under Review</p>
                    <p className="text-2xl font-bold text-blue-600">{statusSummary.under_review}</p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Accepted</p>
                    <p className="text-2xl font-bold text-green-600">{statusSummary.accepted}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search projects or contractors..."
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="under_review">Under Review</SelectItem>
                    <SelectItem value="accepted">Accepted</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={tradeFilter} onValueChange={setTradeFilter}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Filter by trade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Trades</SelectItem>
                    <SelectItem value="electrical">Electrical</SelectItem>
                    <SelectItem value="hvac">HVAC</SelectItem>
                    <SelectItem value="plumbing">Plumbing</SelectItem>
                    <SelectItem value="concrete">Concrete</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="fire_protection">Fire Protection</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Bids Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Bid Submissions ({filteredBids.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Project</TableHead>
                      <TableHead>Contractor</TableHead>
                      <TableHead>Bid Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Response Time</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredBids.map((bid) => (
                      <TableRow key={bid.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{bid.projectName}</p>
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {bid.projectLocation}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{bid.contractorName}</p>
                            <p className="text-sm text-muted-foreground">
                              Rating: {bid.contractorRating}/5.0
                            </p>
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(bid.bidAmount)}
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary" className={statusColors[bid.status]}>
                            <div className="flex items-center gap-1">
                              {statusIcons[bid.status]}
                              {bid.status.replace('_', ' ')}
                            </div>
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <Calendar className="h-3 w-3" />
                            {formatDate(bid.submittedAt)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{bid.responseTime}</span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setSelectedBid(bid)}
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  View
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                                <DialogHeader>
                                  <DialogTitle>Bid Details - {bid.contractorName}</DialogTitle>
                                </DialogHeader>
                                {selectedBid && (
                                  <div className="space-y-6">
                                    {/* Bid Overview */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      <div>
                                        <Label className="text-sm font-medium">Project</Label>
                                        <p className="text-sm">{selectedBid.projectName}</p>
                                      </div>
                                      <div>
                                        <Label className="text-sm font-medium">Bid Amount</Label>
                                        <p className="text-lg font-semibold">{formatCurrency(selectedBid.bidAmount)}</p>
                                      </div>
                                      <div>
                                        <Label className="text-sm font-medium">Status</Label>
                                        <Badge variant="secondary" className={statusColors[selectedBid.status]}>
                                          {selectedBid.status.replace('_', ' ')}
                                        </Badge>
                                      </div>
                                      <div>
                                        <Label className="text-sm font-medium">Submitted</Label>
                                        <p className="text-sm">{formatDate(selectedBid.submittedAt)}</p>
                                      </div>
                                    </div>

                                    {/* Contractor Info */}
                                    <div>
                                      <Label className="text-sm font-medium">Contractor Information</Label>
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                                        <div>
                                          <p className="text-sm"><strong>Company:</strong> {selectedBid.contractorName}</p>
                                          <p className="text-sm"><strong>Rating:</strong> {selectedBid.contractorRating}/5.0</p>
                                          <p className="text-sm"><strong>Bonding Capacity:</strong> {formatCurrency(selectedBid.bondingCapacity)}</p>
                                        </div>
                                        <div>
                                          <p className="text-sm"><strong>Insurance:</strong> {selectedBid.insurance}</p>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Proposal */}
                                    <div>
                                      <Label className="text-sm font-medium">Proposal</Label>
                                      <Textarea 
                                        value={selectedBid.proposalText} 
                                        readOnly 
                                        className="mt-2 min-h-[100px]"
                                      />
                                    </div>

                                    {/* Attachments */}
                                    <div>
                                      <Label className="text-sm font-medium">Attachments</Label>
                                      <div className="flex flex-wrap gap-2 mt-2">
                                        {selectedBid.attachments.map((attachment, index) => (
                                          <Button key={index} variant="outline" size="sm">
                                            <Download className="h-3 w-3 mr-1" />
                                            {attachment}
                                          </Button>
                                        ))}
                                      </div>
                                    </div>

                                    {/* Actions */}
                                    {selectedBid.status === 'pending' && (
                                      <div className="flex gap-2 pt-4 border-t">
                                        <Button className="flex-1">Accept Bid</Button>
                                        <Button variant="outline" className="flex-1">Request Clarification</Button>
                                        <Button variant="destructive" className="flex-1">Reject Bid</Button>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </DialogContent>
                            </Dialog>
                            
                            <Button variant="ghost" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Select RFQ for Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Choose an RFQ to view enhanced analytics including competitive intelligence, risk assessment, and predictive insights.
                </p>
                <Select value={selectedRfqForAnalytics || ""} onValueChange={setSelectedRfqForAnalytics}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an RFQ to analyze..." />
                  </SelectTrigger>
                  <SelectContent>
                    {rfqProjects.map(project => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name} - {project.location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {selectedRfqForAnalytics && (
            <BidAnalyticsDashboard rfqId={selectedRfqForAnalytics} />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}