import {
  users,
  contractors,
  contractorFavorites,
  rfqs,
  rfqDocuments,
  bids,
  bidDocuments,
  bidLineItems,
  bidInclusionsExclusions,
  rfqDistribution,
  forecastMaterials,
  apiKeys,
  apiKeyUsage,
  organizations,
  roleAuditLog,
  accessAuditLog,
  waitlist,
  userFeedback,
  businessAuditLog,
  notifications,
  notificationPreferences,
  notificationDeliveries,
  scheduledNotifications,
  type User,
  type InsertUser,
  type Contractor,
  type InsertContractor,
  type Rfq,
  type InsertRfq,
  type Bid,
  type InsertBid,
  type Notification,
  type InsertNotification,
  type NotificationPreferences,
  type InsertNotificationPreferences,
  type NotificationDelivery,
  type InsertNotificationDelivery,
  type UpsertUser,
  type RfqDocument,
  type BidDocument,
  type ContractorFavorite,
  type RfqDistribution,
  type ForecastMaterial,
  type InsertForecastMaterial,
  type ApiKey,
  type InsertApiKey,
  type ApiKeyUsage,
  type Organization,
  type InsertOrganization,
  type RoleAuditLog,
  type InsertRoleAuditLog,
  type AccessAuditLog,
  type InsertAccessAuditLog,
  type UserFeedback,
  type InsertUserFeedback,
  type BusinessAuditLog,
  type InsertBusinessAuditLog,
  type RFQ,
  type ScheduledNotification,
  type InsertScheduledNotification,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, sql, or, lt, inArray } from "drizzle-orm";
import { cache, CACHE_KEYS, CACHE_TTL } from "./services/cacheService";

export interface IStorage {
  // User operations (required for Replit Auth)
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  acceptUserTerms(userId: string): Promise<User | undefined>;

  // Contractor operations
  getContractor(id: string): Promise<Contractor | undefined>;
  getContractorByUserId(userId: string): Promise<Contractor | undefined>;
  createContractor(contractor: InsertContractor): Promise<Contractor>;
  updateContractor(id: string, contractor: Partial<InsertContractor>): Promise<Contractor | undefined>;
  getContractors(): Promise<Contractor[]>;

  // RFQ operations
  getRfq(id: string): Promise<Rfq | undefined>;
  createRfq(rfq: InsertRfq): Promise<Rfq>;
  updateRfq(id: string, rfq: Partial<InsertRfq>): Promise<Rfq | undefined>;
  getRfqs(): Promise<Rfq[]>;
  getRfqsByUser(userId: string): Promise<Rfq[]>;
  getRfqsByOrganization(organizationId: string, params?: { limit?: number; offset?: number; includeArchived?: boolean }): Promise<Rfq[]>;
  getArchivedRfqsByOrganization(organizationId: string, params?: { limit?: number; offset?: number }): Promise<Rfq[]>;
  updateRfqsArchiveStatus(rfqIds: string[], organizationId: string, isArchived: boolean): Promise<{ affectedRows: number }>;

  // RFQ document operations
  createRfqDocument(document: typeof rfqDocuments.$inferInsert): Promise<RfqDocument>;
  getRfqDocument(documentId: string): Promise<RfqDocument | undefined>;
  getRfqDocuments(rfqId: string): Promise<RfqDocument[]>;

  // Bid operations
  getBid(id: string): Promise<Bid | undefined>;
  createBid(bid: InsertBid): Promise<Bid>;
  updateBid(id: string, bid: Partial<InsertBid>): Promise<Bid | undefined>;
  getBidsByRfq(rfqId: string): Promise<Bid[]>;

  // **ENHANCED BID DATA OPERATIONS** - Phase 3 Implementation
  createBidLineItem(lineItem: typeof bidLineItems.$inferInsert): Promise<typeof bidLineItems.$inferSelect>;
  getBidLineItems(bidId: string): Promise<typeof bidLineItems.$inferSelect[]>;
  createBidInclusionExclusion(scopeItem: typeof bidInclusionsExclusions.$inferInsert): Promise<typeof bidInclusionsExclusions.$inferSelect>;
  getBidInclusionsExclusions(bidId: string): Promise<typeof bidInclusionsExclusions.$inferSelect[]>;
  getBidsByContractor(contractorId: string): Promise<Array<{ bid: Bid; rfq: Rfq }>>;

  // Enhanced bid management operations
  updateBidStatus(bidId: string, status: string, reviewedBy: string, notes?: string): Promise<Bid | undefined>;
  getBidsByRfqWithContractors(rfqId: string): Promise<Array<{ bid: Bid; contractor: Contractor }>>;

  // Bid document operations  
  createBidDocument(document: typeof bidDocuments.$inferInsert): Promise<BidDocument>;
  getBidDocument(documentId: string): Promise<BidDocument | undefined>;
  getBidDocuments(bidId: string): Promise<BidDocument[]>;

  // Contractor favorites operations
  addContractorToFavorites(userId: string, contractorId: string, notes?: string): Promise<ContractorFavorite>;
  removeContractorFromFavorites(userId: string, contractorId: string): Promise<boolean>;
  getFavoriteContractors(userId: string): Promise<ContractorFavorite[]>;

  // RFQ distribution operations
  distributeRfq(rfqId: string, contractorIds: string[], method: "favorites" | "broadcast"): Promise<RfqDistribution[]>;
  markRfqViewed(rfqId: string, contractorId: string): Promise<void>;
  declineRfq(rfqId: string, contractorId: string, reason?: string): Promise<void>;
  getRfqDistributions(rfqId: string): Promise<RfqDistribution[]>;
  getContractorRfqs(contractorId: string): Promise<Rfq[]>;
  getContractorRfqDistributions(contractorId: string): Promise<any[]>;

  // Dashboard statistics
  getDashboardStats(): Promise<{
    activeRfqs: number;
    totalBids: number;
    avgResponseTime: number;
    successRate: number;
  }>;

  // Forecast materials operations
  getForecastMaterials(): Promise<ForecastMaterial[]>;
  createForecastMaterial(material: InsertForecastMaterial): Promise<ForecastMaterial>;
  updateForecastMaterial(id: string, material: Partial<InsertForecastMaterial>): Promise<ForecastMaterial | undefined>;
  deleteForecastMaterial(id: string): Promise<boolean>;

  // API Key operations
  createApiKey(apiKey: InsertApiKey): Promise<ApiKey>;
  getApiKey(id: string): Promise<ApiKey | undefined>;
  getApiKeyByHash(keyHash: string): Promise<ApiKey | undefined>;
  getApiKeysByUserId(userId: string): Promise<ApiKey[]>;
  updateApiKey(id: string, updates: Partial<InsertApiKey>): Promise<ApiKey | undefined>;
  deleteApiKey(id: string): Promise<boolean>;
  updateApiKeyLastUsed(id: string): Promise<void>;

  // API Key usage operations
  recordApiKeyUsage(apiKeyId: string, endpoint: string, method: string): Promise<void>;
  getApiKeyUsageToday(apiKeyId: string, date: string): Promise<ApiKeyUsage[]>;
  getApiKeyUsageStats(apiKeyId: string): Promise<ApiKeyUsage[]>;

  // Organization operations
  createOrganization(organization: InsertOrganization): Promise<Organization>;
  getOrganization(id: string): Promise<Organization | undefined>;
  getOrganizationBySlug(slug: string): Promise<Organization | undefined>;
  updateOrganization(id: string, updates: Partial<InsertOrganization>): Promise<Organization | undefined>;
  getOrganizations(): Promise<Organization[]>;

  // Role management operations
  getUsersByOrganization(organizationId: string): Promise<User[]>;
  updateUserRole(userId: string, newRole: string, changedBy: string, reason?: string, request?: any): Promise<User | undefined>;
  getAllUsers(): Promise<User[]>; // SuperAdmin only

  // Audit operations
  createRoleAuditLog(auditData: InsertRoleAuditLog): Promise<RoleAuditLog>;
  createAccessAuditLog(auditData: InsertAccessAuditLog): Promise<AccessAuditLog>;
  getRoleAuditLogs(organizationId?: string): Promise<RoleAuditLog[]>;
  getAccessAuditLogs(organizationId?: string): Promise<AccessAuditLog[]>;

  // Waitlist operations
  addToWaitlist(data: any): Promise<any>;
  getWaitlistCount(): Promise<number>;
  checkEmailInWaitlist(email: string): Promise<boolean>;

  // User feedback operations
  createUserFeedback(feedback: InsertUserFeedback): Promise<UserFeedback>;
  getUserFeedback(): Promise<UserFeedback[]>; // Super user only
  getUserFeedbackById(id: string): Promise<UserFeedback | undefined>;
  updateUserFeedback(id: string, updates: Partial<InsertUserFeedback>): Promise<UserFeedback | undefined>;
  getUserFeedbackStats(): Promise<{ total: number; bugs: number; suggestions: number; open: number; resolved: number }>;

  // Business audit log operations
  createBusinessAuditLog(auditData: InsertBusinessAuditLog & Record<string, unknown>): Promise<BusinessAuditLog>;
  getBusinessAuditLogs(criteria?: {
    eventType?: string;
    userId?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<BusinessAuditLog[]>;

  // Notification operations
  createNotification(notification: InsertNotification & Record<string, unknown>): Promise<Notification>;
  getNotifications(userId: string, organizationId?: string, options?: { limit?: number; offset?: number }): Promise<Notification[]>;
  markNotificationRead(notificationId: string, userId: string): Promise<boolean>;
  markAllNotificationsRead(userId: string, organizationId?: string): Promise<number>;
  getUnreadNotificationCount(userId: string, organizationId?: string): Promise<number>;
  getUserNotificationPreferences(userId: string): Promise<NotificationPreferences[]>;
  getNotificationPreference(userId: string, type: string): Promise<NotificationPreferences | undefined>;
  upsertNotificationPreference(preference: InsertNotificationPreferences): Promise<NotificationPreferences>;
  updateNotificationPreferences(userId: string, type: string, preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences>;
  createNotificationDelivery(delivery: InsertNotificationDelivery & Record<string, unknown>): Promise<NotificationDelivery>;
  updateDeliveryStatus(deliveryId: string, status: string, response?: any): Promise<boolean>;
  
  // Scheduled notification operations
  createScheduledNotification(notification: InsertScheduledNotification): Promise<ScheduledNotification>;
  cancelScheduledNotifications(rfqId: string, notificationType: string): Promise<void>;
  getPendingScheduledNotifications(): Promise<ScheduledNotification[]>;
  updateScheduledNotificationStatus(notificationId: string, status: 'sent' | 'failed' | 'cancelled'): Promise<void>;
}

export class DatabaseStorage implements IStorage {
  db: typeof db;
  schema: { waitlist: typeof waitlist };

  constructor() {
    // Use the imported database instance and schema
    this.db = db;
    this.schema = { waitlist };
  }

  // User operations
  async getUser(id: string): Promise<User | undefined> {
    return await cache.cached(
      CACHE_KEYS.userProfile(id),
      async () => {
        const [user] = await this.db.select().from(users).where(eq(users.id, id));
        return user;
      },
      CACHE_TTL.USER_PROFILE
    );
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const { email, firstName, lastName, profileImageUrl, organizationId, id } = userData as any;
    try {
      // First check if user already exists by ID
      const existingUserById = await this.getUser(id);
      if (existingUserById) {
        // User exists with this ID, just update the fields (don't change ID)
        const [user] = await this.db
          .update(users)
          .set({
            email,
            firstName,
            lastName,
            profileImageUrl,
            organizationId,
            updatedAt: new Date(),
          } as any)
          .where(eq(users.id, userData.id))
          .returning();
        return user;
      }

      // Check for existing user by email (migration case)
      if (email) {
        const existingUserByEmail = await this.db
          .select()
          .from(users)
          .where(eq(users.email, email))
          .limit(1);
          
        if (existingUserByEmail.length > 0) {
          // Don't change the ID (to avoid FK constraint issues), just update other fields
          const [user] = await this.db
            .update(users)
            .set({
              firstName,
              lastName,
              profileImageUrl,
              organizationId,
              updatedAt: new Date(),
            } as any)
            .where(eq(users.email, email))
            .returning();
          return user;
        }
      }
      
      // No existing user, create new
      const [user] = await this.db
        .insert(users)
        .values(userData)
        .returning();
      return user;
    } catch (error) {
      console.error('Error in upsertUser:', error);
      throw error;
    }
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | undefined> {
    const [user] = await this.db
      .update(users)
      .set({ ...updates, updatedAt: new Date() } as any)
      .where(eq(users.id, id))
      .returning();
    
    // Clear cache for this user
    cache.delete(CACHE_KEYS.userProfile(id));
    
    return user;
  }

  async acceptUserTerms(userId: string): Promise<User | undefined> {
    const [user] = await this.db
      .update(users)
      .set({
        termsAccepted: true,
        termsAcceptedAt: new Date(),
        updatedAt: new Date(),
      } as any)
      .where(eq(users.id, userId))
      .returning();
    
    // Clear cache for this user
    cache.delete(CACHE_KEYS.userProfile(userId));
    
    return user;
  }

  // Contractor operations
  async getContractor(id: string): Promise<Contractor | undefined> {
    try {
      const [contractor] = await this.db.select().from(contractors).where(eq(contractors.id, id));
      return contractor;
    } catch (error) {
      console.error('Error fetching contractor:', error);
      // Return a basic contractor object if there are schema mismatches
      const [basicContractor] = await this.db.select({
        id: contractors.id,
        userId: contractors.userId,
        companyName: contractors.companyName,
        primaryContactName: contractors.primaryContactName,
        primaryContactEmail: contractors.primaryContactEmail,
        primaryContactPhone: contractors.primaryContactPhone,
        tradeTypes: contractors.tradeTypes,
        isApproved: contractors.isApproved,
        createdAt: contractors.createdAt,
        updatedAt: contractors.updatedAt,
      }).from(contractors).where(eq(contractors.id, id));
      return basicContractor as any;
    }
  }

  async getContractorByUserId(userId: string): Promise<Contractor | undefined> {
    try {
      console.log("🔍 Database query for contractor, userId:", userId);
      
      // First try direct user ID lookup
      let [contractor] = await this.db
        .select()
        .from(contractors)
        .where(eq(contractors.userId, userId))
        .orderBy(desc(contractors.createdAt))
        .limit(1);
      
      if (contractor) {
        console.log("✅ Found contractor in DB by userId:", contractor.id, contractor.companyName);
        return contractor;
      }
      
      // If Clerk user ID not found, try to find contractor by email mapping
      // This handles the migration from old user IDs to Clerk user IDs
      const user = await this.getUser(userId);
      if (user && user.email) {
        console.log("🔄 Trying to find contractor by email:", user.email);
        
        // Find contractors for users with the same email
        const contractorsWithEmail = await this.db
          .select()
          .from(contractors)
          .innerJoin(users, eq(contractors.userId, users.id))
          .where(eq(users.email, user.email))
          .orderBy(desc(contractors.createdAt))
          .limit(1);
          
        if (contractorsWithEmail.length > 0) {
          const foundContractor = contractorsWithEmail[0].contractors;
          console.log("✅ Found contractor by email mapping:", foundContractor.id, foundContractor.companyName);
          return foundContractor;
        }
      }
      
      console.log("❌ No contractor found for userId:", userId);
      return undefined;
    } catch (error) {
      console.error("❌ Database error in getContractorByUserId:", error);
      return undefined;
    }
  }

  async createContractor(contractor: InsertContractor): Promise<Contractor> {
    const [newContractor] = await this.db
      .insert(contractors)
      .values({
        ...contractor,
        organizationId: contractor.organizationId, // Ensure organizationId is set
      })
      .returning();
    return newContractor;
  }

  async updateContractor(id: string, contractor: Partial<InsertContractor>): Promise<Contractor | undefined> {
    const [updated] = await this.db
      .update(contractors)
      .set({ ...contractor, updatedAt: new Date() } as any)
      .where(eq(contractors.id, id))
      .returning();
    return updated;
  }

  async getContractors(): Promise<Contractor[]> {
    return await this.db.select().from(contractors).orderBy(desc(contractors.createdAt));
  }

  // New method: Get contractors filtered by organization
  async getContractorsByOrganization(organizationId: string): Promise<Contractor[]> {
    return await this.db.select().from(contractors)
    .where(eq(contractors.organizationId, organizationId))
    .orderBy(desc(contractors.createdAt));
  }

  // RFQ operations
  async getRfq(id: string): Promise<Rfq | undefined> {
    const [rfq] = await this.db.select().from(rfqs).where(eq(rfqs.id, id));
    return rfq;
  }

  async createRfq(rfq: InsertRfq): Promise<Rfq> {
    const [newRfq] = await this.db
      .insert(rfqs)
      .values(rfq as any)
      .returning();
    return newRfq;
  }

  async updateRfq(id: string, data: Partial<InsertRfq>): Promise<Rfq | null> {
    const [rfq] = await this.db
      .update(rfqs)
      .set({ ...data, updatedAt: new Date() } as any)
      .where(eq(rfqs.id, id))
      .returning();
    return rfq || null;
  }

  async getRfqs(params?: { limit?: number; offset?: number }): Promise<Rfq[]> {
    let q = this.db.select().from(rfqs)
      .where(eq(rfqs.isArchived, false))
      .orderBy(desc(rfqs.createdAt));
    if (params?.limit != null) q = (q as any).limit(params.limit);
    if (params?.offset != null) q = (q as any).offset(params.offset);
    return await q;
  }

  // New method: Get RFQs filtered by organization (excluding archived by default)
  async getRfqsByOrganization(organizationId: string, params?: { limit?: number; offset?: number; includeArchived?: boolean }): Promise<Rfq[]> {
    const whereConditions = params?.includeArchived 
      ? eq(rfqs.organizationId, organizationId)
      : and(eq(rfqs.organizationId, organizationId), eq(rfqs.isArchived, false));
    
    let q = this.db.select().from(rfqs)
      .where(whereConditions)
      .orderBy(desc(rfqs.createdAt));
    if (params?.limit != null) q = (q as any).limit(params.limit);
    if (params?.offset != null) q = (q as any).offset(params.offset);
    return await q;
  }

  async getRfqsByUser(userId: string): Promise<Rfq[]> {
    return await this.db.select().from(rfqs).where(eq(rfqs.createdBy, userId)).orderBy(desc(rfqs.createdAt));
  }

  // Archive management methods
  async updateRfqsArchiveStatus(rfqIds: string[], organizationId: string, isArchived: boolean): Promise<{ affectedRows: number }> {
    const result = await this.db
      .update(rfqs)
      .set({ isArchived, updatedAt: new Date() } as any)
      .where(and(
        inArray(rfqs.id, rfqIds),
        eq(rfqs.organizationId, organizationId)
      ))
      .returning({ id: rfqs.id });
    
    return { affectedRows: result.length };
  }

  async getArchivedRfqsByOrganization(organizationId: string, params?: { limit?: number; offset?: number }): Promise<Rfq[]> {
    let q = this.db.select().from(rfqs)
      .where(and(
        eq(rfqs.organizationId, organizationId),
        eq(rfqs.isArchived, true)
      ))
      .orderBy(desc(rfqs.createdAt));
    if (params?.limit != null) q = (q as any).limit(params.limit);
    if (params?.offset != null) q = (q as any).offset(params.offset);
    return await q;
  }

  // RFQ document operations
  async createRfqDocument(document: typeof rfqDocuments.$inferInsert): Promise<RfqDocument> {
    const [newDocument] = await this.db
      .insert(rfqDocuments)
      .values(document)
      .returning();
    return newDocument;
  }

  async getRfqDocument(documentId: string): Promise<RfqDocument | undefined> {
    const [document] = await this.db.select().from(rfqDocuments).where(eq(rfqDocuments.id, documentId));
    return document;
  }

  async getRfqDocuments(rfqId: string, params?: { limit?: number; offset?: number }): Promise<RfqDocument[]> {
    let q = this.db.select().from(rfqDocuments).where(eq(rfqDocuments.rfqId, rfqId));
    if (params?.limit != null) q = (q as any).limit(params.limit);
    if (params?.offset != null) q = (q as any).offset(params.offset);
    return await q;
  }

  // Bid operations
  async getBid(id: string): Promise<Bid | undefined> {
    const [bid] = await this.db.select().from(bids).where(eq(bids.id, id));
    return bid;
  }

  async createBid(bid: InsertBid): Promise<Bid> {
    const [newBid] = await this.db
      .insert(bids)
      .values(bid)
      .returning();
    return newBid;
  }

  async updateBid(id: string, bidData: Partial<InsertBid>): Promise<Bid | undefined> {
    const [bid] = await this.db
      .update(bids)
      .set(bidData)
      .where(eq(bids.id, id))
      .returning();
    return bid;
  }

  async getBidsByRfq(rfqId: string, params?: { limit?: number; offset?: number }): Promise<Bid[]> {
    let q = this.db.select().from(bids).where(eq(bids.rfqId, rfqId)).orderBy(desc(bids.submittedAt));
    if (params?.limit != null) q = (q as any).limit(params.limit);
    if (params?.offset != null) q = (q as any).offset(params.offset);
    return await q;
  }

  async getBidsByContractor(contractorId: string): Promise<Array<{ bid: Bid; rfq: Rfq }>> {
    // Clear any existing cache for this contractor
    cache.invalidatePattern(`bids:contractor:${contractorId}`);

    const bidResults = await this.db
      .select({
        bid: bids,
        rfq: rfqs,
      })
      .from(bids)
      .innerJoin(rfqs, eq(bids.rfqId, rfqs.id))
      .where(eq(bids.contractorId, contractorId))
      .orderBy(desc(bids.submittedAt));

    return bidResults;
  }

  // Enhanced bid management operations
  async updateBidStatus(bidId: string, status: string, reviewedBy: string, notes?: string): Promise<Bid | undefined> {
    const [updated] = await this.db
      .update(bids)
      .set({
        status,
        reviewedBy,
        reviewNotes: notes,
        reviewedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(bids.id, bidId))
      .returning();

    // Invalidate related cache entries
    if (updated) {
      cache.invalidatePattern(`bids:rfq:${updated.rfqId}`);
      cache.invalidatePattern(`bids:contractor:${updated.contractorId}`);
    }

    return updated;
  }

  async getBidsByRfqWithContractors(rfqId: string): Promise<Array<{ bid: Bid; contractor: Contractor }>> {
    return await cache.cached(
      `bids:rfq:${rfqId}:with_contractors`,
      async () => {
        const bidResults = await this.db
          .select({
            bid: bids,
            contractor: contractors,
          })
          .from(bids)
          .innerJoin(contractors, eq(bids.contractorId, contractors.id))
          .where(eq(bids.rfqId, rfqId))
          .orderBy(desc(bids.submittedAt), desc(bids.createdAt));

        return bidResults;
      },
      CACHE_TTL.BID_SUMMARY
    );
  }

  // Bid document operations
  async createBidDocument(document: typeof bidDocuments.$inferInsert): Promise<BidDocument> {
    const [created] = await this.db.insert(bidDocuments).values(document).returning();
    return created;
  }

  async getBidDocument(documentId: string): Promise<BidDocument | undefined> {
    const [document] = await this.db.select().from(bidDocuments).where(eq(bidDocuments.id, documentId));
    return document;
  }

  async getBidDocuments(bidId: string, params?: { limit?: number; offset?: number }): Promise<BidDocument[]> {
    let q = this.db.select().from(bidDocuments).where(eq(bidDocuments.bidId, bidId));
    if (params?.limit != null) q = (q as any).limit(params.limit);
    if (params?.offset != null) q = (q as any).offset(params.offset);
    return await q;
  }

  // Contractor favorites operations
  async addContractorToFavorites(userId: string, contractorId: string, notes?: string): Promise<ContractorFavorite> {


    const [favorite] = await this.db
      .insert(contractorFavorites)
      .values({
        userId: userId,
        contractorId: contractorId,
        notes: notes || null,
      } as any)
      .returning();
    return favorite;
  }

  async removeContractorFromFavorites(userId: string, contractorId: string): Promise<boolean> {
    const result = await this.db
      .delete(contractorFavorites)
      .where(
        and(
          eq(contractorFavorites.userId, userId),
          eq(contractorFavorites.contractorId, contractorId)
        )
      );
    return result.rowCount > 0;
  }

  async getFavoriteContractors(userId: string): Promise<ContractorFavorite[]> {
    const results = await this.db
      .select()
      .from(contractorFavorites)
      .where(eq(contractorFavorites.userId, userId));

    return results;
  }

  // RFQ distribution operations
  async distributeRfq(rfqId: string, contractorIds: string[], method: "favorites" | "broadcast"): Promise<RfqDistribution[]> {
    const distributions = contractorIds.map(contractorId => ({
      rfqId,
      contractorId,
      distributionMethod: method,
      distributedAt: new Date(),
    }));

    return await this.db.insert(rfqDistribution).values(distributions).returning();
  }

  async markRfqViewed(rfqId: string, contractorId: string): Promise<void> {
    await this.db
      .update(rfqDistribution)
      .set({ viewedAt: new Date() } as any)
      .where(
        and(
          eq(rfqDistribution.rfqId, rfqId),
          eq(rfqDistribution.contractorId, contractorId)
        )
      );
  }

  async declineRfq(rfqId: string, contractorId: string, reason?: string): Promise<void> {
    await this.db
      .update(rfqDistribution)
      .set({ 
        declinedAt: new Date(),
        declineReason: reason 
      } as any)
      .where(
        and(
          eq(rfqDistribution.rfqId, rfqId),
          eq(rfqDistribution.contractorId, contractorId)
        )
      );
  }

  async getRfqDistributions(rfqId: string): Promise<RfqDistribution[]> {
    return await this.db.select().from(rfqDistribution).where(eq(rfqDistribution.rfqId, rfqId));
  }

  async getContractorRfqs(contractorId: string): Promise<Rfq[]> {
    const results = await this.db
      .select({
        rfq: rfqs,
      })
      .from(rfqDistribution)
      .leftJoin(rfqs, eq(rfqDistribution.rfqId, rfqs.id))
      .where(eq(rfqDistribution.contractorId, contractorId));

    return results.map(r => r.rfq).filter(Boolean);
  }

  async getContractorRfqDistributions(contractorId: string): Promise<any[]> {
    const results = await this.db
      .select({
        id: rfqDistribution.id,
        rfqId: rfqDistribution.rfqId,
        sentAt: rfqDistribution.sentAt,
        viewedAt: rfqDistribution.viewedAt,
        declinedAt: rfqDistribution.declinedAt,
        declineReason: rfqDistribution.declineReason,
        rfq: {
          id: rfqs.id,
          projectName: rfqs.projectName,
          projectLocation: rfqs.projectLocation,
          description: rfqs.description,
          tradeCategory: rfqs.tradeCategory,
          dueDate: rfqs.dueDate,
          status: rfqs.status,
          extractedData: rfqs.extractedData,
          createdAt: rfqs.createdAt,
        }
      })
      .from(rfqDistribution)
      .innerJoin(rfqs, eq(rfqDistribution.rfqId, rfqs.id))
      .where(eq(rfqDistribution.contractorId, contractorId))
      .orderBy(rfqs.dueDate);

    return results.map(r => ({
      id: r.id,
      rfqId: r.rfqId,
      sentAt: r.sentAt,
      viewedAt: r.viewedAt,
      declinedAt: r.declinedAt,
      declineReason: r.declineReason,
      rfq: r.rfq
    }));
  }

  // Dashboard statistics
  async getDashboardStats(): Promise<{
    activeRfqs: number;
    totalBids: number;
    avgResponseTime: number;
    successRate: number;
  }> {
    const [activeRfqsResult] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(rfqs)
      .where(eq(rfqs.status, "Active"));

    const [totalBidsResult] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(bids);

    return {
      activeRfqs: activeRfqsResult.count,
      totalBids: totalBidsResult.count,
      avgResponseTime: 4.2, // This would need more complex calculation
      successRate: 87, // This would need more complex calculation
    };
  }

  // Forecast materials operations
  async getForecastMaterials(): Promise<ForecastMaterial[]> {
    return await this.db.select().from(forecastMaterials).orderBy(forecastMaterials.category, forecastMaterials.name);
  }

  async createForecastMaterial(material: InsertForecastMaterial): Promise<ForecastMaterial> {
    const [newMaterial] = await this.db
      .insert(forecastMaterials)
      .values(material)
      .returning();
    return newMaterial;
  }

  async updateForecastMaterial(id: string, material: Partial<InsertForecastMaterial>): Promise<ForecastMaterial | undefined> {
    const [updatedMaterial] = await this.db
      .update(forecastMaterials)
      .set({ ...material, lastUpdated: new Date() } as any)
      .where(eq(forecastMaterials.id, id))
      .returning();
    return updatedMaterial;
  }

  async deleteForecastMaterial(id: string): Promise<boolean> {
    const result = await this.db
      .delete(forecastMaterials)
      .where(eq(forecastMaterials.id, id));
    return (result.rowCount || 0) > 0;
  }

  // API Key operations
  async createApiKey(apiKey: InsertApiKey): Promise<ApiKey> {
    const [newApiKey] = await this.db
      .insert(apiKeys)
      .values(apiKey)
      .returning();
    return newApiKey;
  }

  async getApiKey(id: string): Promise<ApiKey | undefined> {
    const [apiKey] = await this.db
      .select()
      .from(apiKeys)
      .where(eq(apiKeys.id, id));
    return apiKey;
  }

  async getApiKeyByHash(keyHash: string): Promise<ApiKey | undefined> {
    const [apiKey] = await this.db
      .select()
      .from(apiKeys)
      .where(and(
        eq(apiKeys.keyHash, keyHash),
        eq(apiKeys.isActive, true),
        or(
          eq(apiKeys.expiresAt, null),
          sql`${apiKeys.expiresAt} > NOW()`
        )
      ));
    return apiKey;
  }

  async getApiKeysByUserId(userId: string): Promise<ApiKey[]> {
    return await this.db
      .select()
      .from(apiKeys)
      .where(eq(apiKeys.userId, userId))
      .orderBy(desc(apiKeys.createdAt));
  }

  async updateApiKey(id: string, updates: Partial<InsertApiKey>): Promise<ApiKey | undefined> {
    const [updatedApiKey] = await this.db
      .update(apiKeys)
      .set({ ...updates, updatedAt: new Date() } as any)
      .where(eq(apiKeys.id, id))
      .returning();
    return updatedApiKey;
  }

  async deleteApiKey(id: string): Promise<boolean> {
    const result = await this.db
      .delete(apiKeys)
      .where(eq(apiKeys.id, id));
    return (result.rowCount || 0) > 0;
  }

  async updateApiKeyLastUsed(id: string): Promise<void> {
    await this.db
      .update(apiKeys)
      .set({ lastUsedAt: new Date() } as any)
      .where(eq(apiKeys.id, id));
  }

  // API Key usage operations - Optimized UPSERT to eliminate N+1 queries
  async recordApiKeyUsage(apiKeyId: string, endpoint: string, method: string): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    const now = new Date();

    // Single UPSERT operation - no SELECT required
    // Uses the unique index: idx_api_key_usage_unique_daily (api_key_id, request_date, endpoint, method)
    await this.db
      .insert(apiKeyUsage)
      .values({
        apiKeyId,
        endpoint,
        method,
        requestCount: 1,
        requestDate: today,
        lastRequestAt: now
      } as any)
      .onConflictDoUpdate({
        target: [
          apiKeyUsage.apiKeyId,
          apiKeyUsage.requestDate,
          apiKeyUsage.endpoint,
          apiKeyUsage.method
        ],
        set: {
          requestCount: sql`${apiKeyUsage.requestCount} + 1`,
          lastRequestAt: now
        }
      });
  }

  async getApiKeyUsageToday(apiKeyId: string, date: string): Promise<ApiKeyUsage[]> {
    return await this.db
      .select()
      .from(apiKeyUsage)
      .where(and(
        eq(apiKeyUsage.apiKeyId, apiKeyId),
        eq(apiKeyUsage.requestDate, date)
      ));
  }

  async getApiKeyUsageStats(apiKeyId: string): Promise<ApiKeyUsage[]> {
    return await this.db
      .select()
      .from(apiKeyUsage)
      .where(eq(apiKeyUsage.apiKeyId, apiKeyId))
      .orderBy(desc(apiKeyUsage.lastRequestAt));
  }

  // Organization operations
  async createOrganization(organizationData: InsertOrganization): Promise<Organization> {
    const [organization] = await this.db
      .insert(organizations)
      .values(organizationData)
      .returning();
    return organization;
  }

  async getOrganization(id: string): Promise<Organization | undefined> {
    const [organization] = await this.db
      .select()
      .from(organizations)
      .where(eq(organizations.id, id));
    return organization;
  }

  async getOrganizationBySlug(slug: string): Promise<Organization | undefined> {
    const [organization] = await this.db
      .select()
      .from(organizations)
      .where(eq(organizations.slug, slug));
    return organization;
  }

  async updateOrganization(id: string, updates: Partial<InsertOrganization>): Promise<Organization | undefined> {
    const [updatedOrganization] = await this.db
      .update(organizations)
      .set({ ...updates, updatedAt: new Date() } as any)
      .where(eq(organizations.id, id))
      .returning();
    return updatedOrganization;
  }

  async getOrganizations(): Promise<Organization[]> {
    return await this.db
      .select()
      .from(organizations)
      .where(eq(organizations.isActive, true))
      .orderBy(desc(organizations.createdAt));
  }

  // Role management operations
  async getUsersByOrganization(organizationId: string, params?: { limit?: number; offset?: number }): Promise<User[]> {
    let q = this.db
      .select()
      .from(users)
      .where(and(
        eq(users.organizationId, organizationId),
        eq(users.isActive, true)
      ))
      .orderBy(desc(users.createdAt)) as any;
    if (params?.limit != null) q = q.limit(params.limit);
    if (params?.offset != null) q = q.offset(params.offset);
    return await q;
  }

  async updateUserRole(userId: string, newRole: string, changedBy: string, reason?: string, request?: any): Promise<User | undefined> {
    // Get current user data to track previous role
    const currentUser = await this.getUser(userId);
    if (!currentUser) {
      return undefined;
    }

    // Update user role
    const [updatedUser] = await this.db
      .update(users)
      .set({ 
        role: newRole as any,
        updatedAt: new Date()
      } as any)
      .where(eq(users.id, userId))
      .returning();

    // Create audit log entry
    if (updatedUser) {
      await this.createRoleAuditLog({
        targetUserId: userId,
        changedByUserId: changedBy,
        organizationId: currentUser.organizationId,
        previousRole: currentUser.role as any,
        newRole: newRole as any,
        reason,
        ipAddress: request?.ip,
        userAgent: request?.get?.('User-Agent'),
      } as any);

      // Invalidate cache
      cache.invalidatePattern(`user:${userId}*`);
    }

    return updatedUser;
  }

  async getAllUsers(): Promise<User[]> {
    return await this.db
      .select()
      .from(users)
      .where(eq(users.isActive, true))
      .orderBy(desc(users.createdAt));
  }

  // Audit operations
  async createRoleAuditLog(auditData: InsertRoleAuditLog): Promise<RoleAuditLog> {
    const [auditLog] = await this.db
      .insert(roleAuditLog)
      .values(auditData as any)
      .returning();
    return auditLog;
  }

  async createAccessAuditLog(auditData: InsertAccessAuditLog): Promise<AccessAuditLog> {
    const [auditLog] = await this.db
      .insert(accessAuditLog)
      .values(auditData as any)
      .returning();
    return auditLog;
  }

  async getRoleAuditLogs(organizationId?: string, params?: { limit?: number; offset?: number }): Promise<RoleAuditLog[]> {
  let query: any = this.db.select().from(roleAuditLog);
     if (organizationId) {
    query = query.where(eq(roleAuditLog.organizationId, organizationId));
  }
  query = query.orderBy(desc(roleAuditLog.createdAt));
     if (params?.limit != null) query = query.limit(params.limit);
  if (params?.offset != null) query = query.offset(params.offset);
    return await query;
   }
  
  async getAccessAuditLogs(organizationId?: string, params?: { limit?: number; offset?: number }): Promise<AccessAuditLog[]> {
     let query: any = this.db.select().from(accessAuditLog);
  if (organizationId) {
  query = query.where(eq(accessAuditLog.organizationId, organizationId));
  }
     query = query.orderBy(desc(accessAuditLog.createdAt));
  if (params?.limit != null) query = query.limit(params.limit);
    if (params?.offset != null) query = query.offset(params.offset);
    return await query;
  }

  // Waitlist management
  async addToWaitlist(data: any): Promise<any> {
    const [waitlistEntry] = await this.db
      .insert(waitlist)
      .values({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        companyName: data.companyName,
        jobTitle: data.jobTitle || null,
        source: data.source || "landing_page"
      } as any)
      .returning();
    return waitlistEntry;
  }

  async getWaitlistCount(): Promise<number> {
    try {
      const result = await this.db.select({ count: sql<number>`count(*)` }).from(waitlist);
      return result[0]?.count || 0;
    } catch (error) {
      console.error("Error getting waitlist count:", error);
      throw error;
    }
  }

  async checkEmailInWaitlist(email: string): Promise<boolean> {
    const result = await this.db
      .select()
      .from(waitlist)
      .where(eq(waitlist.email, email))
      .limit(1);
    return result.length > 0;
  }
  // **ENHANCED BID DATA OPERATIONS** - Phase 3 Implementation
  async createBidLineItem(lineItem: typeof bidLineItems.$inferInsert): Promise<typeof bidLineItems.$inferSelect> {
    console.log("🔧 Creating bid line item:", lineItem.costCode, lineItem.description);
    const result = await db.insert(bidLineItems).values(lineItem).returning();
    return result[0];
  }

  async getBidLineItems(bidId: string): Promise<typeof bidLineItems.$inferSelect[]> {
    const cacheKey = `${CACHE_KEYS.BID_LINE_ITEMS}:${bidId}`;
    return cache.cached(cacheKey, async () => {
      return await db
        .select()
        .from(bidLineItems)
        .where(eq(bidLineItems.bidId, bidId))
        .orderBy(bidLineItems.costCode);
    }, CACHE_TTL.BIDS);
  }

  async createBidInclusionExclusion(scopeItem: typeof bidInclusionsExclusions.$inferInsert): Promise<typeof bidInclusionsExclusions.$inferSelect> {
    console.log("📋 Creating scope definition:", scopeItem.itemType, scopeItem.description);
    const result = await db.insert(bidInclusionsExclusions).values(scopeItem).returning();
    return result[0];
  }

  async getBidInclusionsExclusions(bidId: string): Promise<typeof bidInclusionsExclusions.$inferSelect[]> {
    const cacheKey = `${CACHE_KEYS.BID_SCOPE}:${bidId}`;
    return cache.cached(cacheKey, async () => {
      return await db
        .select()
        .from(bidInclusionsExclusions)
        .where(eq(bidInclusionsExclusions.bidId, bidId))
        .orderBy(bidInclusionsExclusions.itemType, bidInclusionsExclusions.category);
    }, CACHE_TTL.BIDS);
  }

  // User feedback operations
  async createUserFeedback(feedback: InsertUserFeedback): Promise<UserFeedback> {
    console.log("📝 Creating user feedback:", feedback.type, feedback.message?.substring(0, 50) + "...");
    const [result] = await this.db.insert(userFeedback).values(feedback).returning();
    return result;
  }

  async getUserFeedback(): Promise<UserFeedback[]> {
    console.log("📝 Fetching all user feedback");
    return await this.db
      .select({
        id: userFeedback.id,
        userId: userFeedback.userId,
        type: userFeedback.type,
        message: userFeedback.message,
        status: userFeedback.status,
        priority: userFeedback.priority,
        adminNotes: userFeedback.adminNotes,
        resolvedAt: userFeedback.resolvedAt,
        resolvedBy: userFeedback.resolvedBy,
        createdAt: userFeedback.createdAt,
        updatedAt: userFeedback.updatedAt,
        user: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
        }
      })
      .from(userFeedback)
      .leftJoin(users, eq(userFeedback.userId, users.id))
      .orderBy(desc(userFeedback.createdAt));
  }

  async getUserFeedbackById(id: string): Promise<UserFeedback | undefined> {
    console.log("🔍 Getting user feedback by ID:", id);
    const [result] = await this.db
      .select()
      .from(userFeedback)
      .where(eq(userFeedback.id, id));
    return result;
  }

  async updateUserFeedback(id: string, updates: Partial<InsertUserFeedback>): Promise<UserFeedback | undefined> {
    console.log("📝 Updating user feedback:", id, updates);
    const [result] = await this.db
    .update(userFeedback)
    .set({ ...updates, updatedAt: new Date() } as any)
    .where(eq(userFeedback.id, id))
    .returning();
    return result;
  }

  async getUserFeedbackStats(): Promise<{ total: number; bugs: number; suggestions: number; open: number; resolved: number }> {
    console.log("📊 Fetching user feedback statistics");
    const feedbacks = await this.db.select({
      type: userFeedback.type,
      status: userFeedback.status,
    }).from(userFeedback);

    const stats = feedbacks.reduce((acc, feedback) => {
      acc.total++;
      if (feedback.type === 'bug') acc.bugs++;
      if (feedback.type === 'suggestion') acc.suggestions++;
      if (feedback.status === 'open') acc.open++;
      if (feedback.status === 'resolved') acc.resolved++;
      return acc;
    }, { total: 0, bugs: 0, suggestions: 0, open: 0, resolved: 0 });

    return stats;
  }

  // Business audit log operations
  async createBusinessAuditLog(auditData: InsertBusinessAuditLog & Record<string, unknown>): Promise<BusinessAuditLog> {
    const [auditLog] = await this.db
      .insert(businessAuditLog)
      .values(auditData)
      .returning();
    return auditLog;
  }

  async getBusinessAuditLogs(criteria: {
    eventType?: string;
    userId?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  } = {}): Promise<BusinessAuditLog[]> {
    let query = this.db
      .select({
        id: businessAuditLog.id,
        userId: businessAuditLog.userId,
        eventType: businessAuditLog.eventType,
        eventData: businessAuditLog.eventData,
        resourceId: businessAuditLog.resourceId,
        ipAddress: businessAuditLog.ipAddress,
        userAgent: businessAuditLog.userAgent,
        createdAt: businessAuditLog.createdAt,
        user: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
        }
      })
      .from(businessAuditLog)
      .leftJoin(users, eq(businessAuditLog.userId, users.id));

    // Apply filters
    const conditions = [];
    if (criteria.eventType) {
      conditions.push(eq(businessAuditLog.eventType, criteria.eventType));
    }
    if (criteria.userId) {
      conditions.push(eq(businessAuditLog.userId, criteria.userId));
    }
    if (criteria.resourceId) {
      conditions.push(eq(businessAuditLog.resourceId, criteria.resourceId));
    }
    if (criteria.startDate) {
      conditions.push(sql`${businessAuditLog.createdAt} >= ${criteria.startDate}`);
    }
    if (criteria.endDate) {
      conditions.push(sql`${businessAuditLog.createdAt} <= ${criteria.endDate}`);
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions)) as any;
    }

    return await query
      .orderBy(desc(businessAuditLog.createdAt))
      .limit(criteria.limit || 100);
  }

  // Helper methods for backfill operation
  async getAllRfqs(): Promise<Rfq[]> {
    return await this.db.select().from(rfqs).orderBy(desc(rfqs.createdAt));
  }

  async getAllRfqDocuments(): Promise<RfqDocument[]> {
    return await this.db.select().from(rfqDocuments).orderBy(desc(rfqDocuments.createdAt));
  }

  async getAllBids(): Promise<Bid[]> {
    return await this.db.select().from(bids).orderBy(desc(bids.submittedAt));
  }

  // Notification operations
  async createNotification(notification: InsertNotification & Record<string, unknown>): Promise<Notification> {
    console.log("🔔 Creating notification:", notification.type, "for user:", notification.userId);
    const [result] = await this.db.insert(notifications).values(notification).returning();
    
    // Invalidate user notifications cache
    const cacheKey = `user_notifications:${notification.userId}`;
    cache.delete(cacheKey);
    if (notification.organizationId) {
      const orgCacheKey = `org_notifications:${notification.organizationId}`;
      cache.delete(orgCacheKey);
    }
    
    return result;
  }

  async getNotifications(userId: string, organizationId?: string, options: { limit?: number; offset?: number } = {}): Promise<Notification[]> {
    const { limit = 50, offset } = options;
    const cacheKey = organizationId ? `org_notifications:${organizationId}:${userId}` : `user_notifications:${userId}`;
    
    return cache.cached(cacheKey, async () => {
      console.log("📬 Fetching notifications for user:", userId, "org:", organizationId);
      
      const conditions = [eq(notifications.userId, userId)];
      if (organizationId) {
        conditions.push(eq(notifications.organizationId, organizationId));
      }
      
      let q = this.db
        .select()
        .from(notifications)
        .where(and(...conditions))
        .orderBy(desc(notifications.createdAt))
        .limit(limit) as any;
      if (offset != null) q = q.offset(offset);
      return await q;
    }); // default TTL
  }

  async markNotificationRead(notificationId: string, userId: string): Promise<boolean> {
    console.log("👁️ Marking notification read:", notificationId);
    
    const result = await this.db
      .update(notifications)
      .set({ 
        readAt: new Date(),
        deliveredAt: new Date() 
      } as any)
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.userId, userId)
      ))
      .returning();

    // Invalidate cache
    const cacheKey = `user_notifications:${userId}`;
    cache.delete(cacheKey);
    
    // Also invalidate org cache if notification has organizationId
    if (result.length > 0 && result[0].organizationId) {
      const orgCacheKey = `org_notifications:${result[0].organizationId}:${userId}`;
      cache.delete(orgCacheKey);
    }
    
    return result.length > 0;
  }

  async markAllNotificationsRead(userId: string, organizationId?: string): Promise<number> {
    console.log("👁️ Marking all notifications read for user:", userId, "org:", organizationId);
    
    const conditions = [eq(notifications.userId, userId)];
    if (organizationId) {
      conditions.push(eq(notifications.organizationId, organizationId));
    }
    
    const result = await this.db
      .update(notifications)
      .set({ 
        readAt: new Date(),
        deliveredAt: new Date() 
      } as any)
      .where(and(...conditions))
      .returning();

    // Invalidate caches
    const cacheKey = `user_notifications:${userId}`;
    cache.delete(cacheKey);
    if (organizationId) {
      const orgCacheKey = `org_notifications:${organizationId}:${userId}`;
      cache.delete(orgCacheKey);
    }
    
    return result.length;
  }

  async getUnreadNotificationCount(userId: string, organizationId?: string): Promise<number> {
    console.log("📊 Getting unread notification count for user:", userId, "org:", organizationId);
    
    const conditions = [
      eq(notifications.userId, userId),
      eq(notifications.readAt, null)
    ];
    if (organizationId) {
      conditions.push(eq(notifications.organizationId, organizationId));
    }
    
    const [result] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(notifications)
      .where(and(...conditions));
    
    return Number(result.count);
  }

  async getUserNotificationPreferences(userId: string): Promise<NotificationPreferences[]> {
    console.log("⚙️ Fetching notification preferences for user:", userId);
    try {
      const result = await this.db
        .select()
        .from(notificationPreferences)
        .where(eq(notificationPreferences.userId, userId));
      console.log("📋 Database query result:", result);
      return result;
    } catch (error) {
      console.error("💥 Error fetching notification preferences:", error);
      return [];
    }
  }

  async getNotificationPreference(userId: string, type: string): Promise<NotificationPreferences | undefined> {
    try {
      const [preference] = await this.db
        .select()
        .from(notificationPreferences)
        .where(and(
          eq(notificationPreferences.userId, userId),
          eq(notificationPreferences.type, type)
        ));
      return preference;
    } catch (error) {
      console.error("Error getting notification preference:", error);
      return undefined;
    }
  }

  async upsertNotificationPreference(preference: InsertNotificationPreferences): Promise<NotificationPreferences> {
    const { inAppEnabled, emailEnabled, smsEnabled, frequency } = preference as any;
    try {
      const [result] = await this.db
        .insert(notificationPreferences)
        .values(preference as any)
        .onConflictDoUpdate({
          target: [notificationPreferences.userId, notificationPreferences.type],
          set: {
            inAppEnabled,
            emailEnabled,
            smsEnabled,
            frequency,
            updatedAt: new Date(),
          } as any
        })
        .returning();
      
      // Invalidate cache
      const cacheKey = `user_preferences:${preference.userId}`;
      cache.delete(cacheKey);
      
      return result;
    } catch (error) {
      console.error("Error upserting notification preference:", error);
      throw error;
    }
  }

  async updateNotificationPreferences(
    userId: string, 
    type: string, 
    preferences: Partial<NotificationPreferences>
  ): Promise<NotificationPreferences> {
    console.log("🔧 Updating notification preferences:", userId, type);
    
    const [result] = await this.db
      .insert(notificationPreferences)
      .values({
        userId,
        type,
        ...preferences,
        updatedAt: new Date(),
      } as any)
      .onConflictDoUpdate({
        target: [notificationPreferences.userId, notificationPreferences.type],
        set: {
          ...preferences,
          updatedAt: new Date(),
        } as any,
      })
      .returning();

    // Invalidate cache
    const cacheKey = `user_preferences:${userId}`;
    cache.delete(cacheKey);
    
    return result;
  }

  async createNotificationDelivery(delivery: InsertNotificationDelivery & Record<string, unknown>): Promise<NotificationDelivery> {
    console.log("📤 Creating notification delivery:", delivery.deliveryMethod, "to", delivery.recipient);
    const [result] = await this.db.insert(notificationDeliveries).values(delivery as any).returning();
    return result;
  }

  async updateDeliveryStatus(deliveryId: string, status: string, response?: any): Promise<boolean> {
    console.log("📊 Updating delivery status:", deliveryId, status);
    
    const result = await this.db
      .update(notificationDeliveries)
      .set({ 
        status: status as any,
        deliveredAt: status === 'delivered' ? new Date() : undefined,
        providerResponse: response ? JSON.stringify(response) : undefined,
      } as any)
      .where(eq(notificationDeliveries.id, deliveryId))
      .returning();

    return result.length > 0;
  }

  // Scheduled notification operations
  async createScheduledNotification(notification: InsertScheduledNotification): Promise<ScheduledNotification> {
    console.log("📅 Creating scheduled notification:", notification.notificationType, "for RFQ:", (notification as any).rfqId || 'none');
    const [result] = await this.db.insert(scheduledNotifications).values(notification as any).returning();
    return result;
  }

  async cancelScheduledNotifications(rfqId: string, notificationType: string): Promise<void> {
    console.log("🚫 Cancelling scheduled notifications for RFQ:", rfqId, "type:", notificationType);
    await this.db
      .update(scheduledNotifications)
      .set({ 
        status: "cancelled",
        updatedAt: new Date()
      } as any)
      .where(
        and(
          eq(scheduledNotifications.rfqId, rfqId),
          eq(scheduledNotifications.notificationType, notificationType),
          eq(scheduledNotifications.status, "pending")
        )
      );
  }

  async getPendingScheduledNotifications(): Promise<ScheduledNotification[]> {
    const now = new Date();
    return await this.db
      .select()
      .from(scheduledNotifications)
      .where(
        and(
          eq(scheduledNotifications.status, "pending"),
          lt(scheduledNotifications.scheduledFor, now)
        )
      )
      .orderBy(scheduledNotifications.scheduledFor);
  }

  async updateScheduledNotificationStatus(notificationId: string, status: 'sent' | 'failed' | 'cancelled'): Promise<void> {
    await this.db
      .update(scheduledNotifications)
      .set({ 
        status: status,
        updatedAt: new Date()
      } as any)
      .where(eq(scheduledNotifications.id, notificationId));
  }
}

export const storage = new DatabaseStorage();
