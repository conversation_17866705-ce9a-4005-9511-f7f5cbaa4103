<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Construction Materials Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0a0a0a;
            color: #ffffff;
            font-size: 14px;
            line-height: 1.4;
        }

        .dashboard {
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 10px 0;
            border-bottom: 1px solid #333;
        }

        .filter-button {
            padding: 6px 12px;
            background-color: #222;
            border: 1px solid #444;
            color: #fff;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .filter-button.active {
            background-color: #f39c12;
            border-color: #f39c12;
            color: #000;
        }

        .filter-button:hover {
            background-color: #333;
        }

        .filter-button.active:hover {
            background-color: #e67e22;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead th {
            text-align: left;
            padding: 12px 15px;
            background-color: #1a1a1a;
            font-weight: normal;
            color: #999;
            font-size: 13px;
            border-bottom: 2px solid #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tbody tr {
            border-bottom: 1px solid #222;
        }

        tbody tr:hover {
            background-color: #1a1a1a;
        }

        td {
            padding: 10px 15px;
            font-size: 13px;
        }

        .material-name {
            color: #f39c12;
            font-weight: 500;
        }

        .unit {
            color: #666;
            font-size: 11px;
        }

        .chart-cell {
            width: 100px;
            height: 30px;
            position: relative;
        }

        .mini-chart {
            width: 100%;
            height: 100%;
        }

        .positive {
            color: #27ae60;
        }

        .negative {
            color: #e74c3c;
        }

        .neutral {
            color: #95a5a6;
        }

        .section-header {
            background-color: #1a1a1a;
            font-weight: bold;
            color: #f39c12;
            padding: 8px 15px;
            border-top: 2px solid #333;
            border-bottom: 1px solid #333;
        }

        .dropdown {
            appearance: none;
            background-color: #f39c12;
            color: #000;
            padding: 6px 25px 6px 10px;
            border: none;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2712%27%20height%3D%276%27%20viewBox%3D%270%200%2012%206%27%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%3E%3Cpath%20d%3D%27M0%200l6%206%206-6z%27%20fill%3D%27%23000%27%2F%3E%3C%2Fsvg%3E');
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 12px;
        }

        .time-column {
            color: #666;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .dashboard {
                padding: 10px;
            }
            
            th, td {
                padding: 8px 10px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <button class="filter-button active">Standard</button>
            <span style="color: #666;">▼</span>
            <button class="filter-button">▢ Trending</button>
            <button class="filter-button">▢ Volatility</button>
            <button class="filter-button">▢ Regional</button>

            <select class="dropdown">
                <option>1W</option>
                <option>1M</option>
                <option>3M</option>
                <option>YTD</option>
            </select>
            <span style="color: #666;">▼</span>
            <button class="filter-button">%Chg YTD</button>
            <span style="color: #666;">▼</span>
            <button class="filter-button">USD</button>
            <span style="color: #666;">▼</span>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th style="width: 50px;">Market</th>
                        <th style="width: 200px;">RMI</th>
                        <th style="width: 100px;">30Day</th>
                        <th style="width: 120px;">Value</th>
                        <th style="width: 100px;">Net Chg</th>
                        <th style="width: 80px;">%Chg</th>
                        <th style="width: 80px;">Time</th>
                        <th style="width: 80px;">%Ytd</th>
                        <th style="width: 80px;">%YtdC</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="8" class="section-header">1) Structural Materials</td>
                    </tr>
                    <tr>
                        <td>11)</td>
                        <td><span class="material-name">STEEL REBAR</span> <span class="unit">[ton]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,20 10,18 20,22 30,19 40,17 50,15 60,18 70,16 80,14 90,12 100,10" 
                                         fill="none" stroke="#27ae60" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$785.42</td>
                        <td class="positive">+$12.35</td>
                        <td class="positive">*****%</td>
                        <td class="time-column">10:46</td>
                        <td class="positive">8.45%</td>
                        <td class="positive">12.30%</td>
                    </tr>
                    <tr>
                        <td>12)</td>
                        <td><span class="material-name">CONCRETE MIX</span> <span class="unit">[yd³]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,15 10,14 20,16 30,15 40,14 50,13 60,14 70,13 80,12 90,11 100,10" 
                                         fill="none" stroke="#27ae60" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$142.75</td>
                        <td class="positive">+$2.10</td>
                        <td class="positive">*****%</td>
                        <td class="time-column">10:32</td>
                        <td class="positive">5.20%</td>
                        <td class="positive">7.65%</td>
                    </tr>
                    <tr>
                        <td>13)</td>
                        <td><span class="material-name">LUMBER 2x4</span> <span class="unit">[MBF]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,10 10,12 20,15 30,18 40,20 50,18 60,16 70,17 80,19 90,20 100,22" 
                                         fill="none" stroke="#e74c3c" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$485.00</td>
                        <td class="negative">-$8.25</td>
                        <td class="negative">-1.67%</td>
                        <td class="time-column">10:31</td>
                        <td class="negative">-12.30%</td>
                        <td class="negative">-18.50%</td>
                    </tr>
                    <tr>
                        <td>14)</td>
                        <td><span class="material-name">PLYWOOD OSB</span> <span class="unit">[MSF]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,15 10,16 20,17 30,16 40,15 50,14 60,13 70,14 80,15 90,16 100,17" 
                                         fill="none" stroke="#95a5a6" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$325.80</td>
                        <td class="neutral">+$0.45</td>
                        <td class="neutral">+0.14%</td>
                        <td class="time-column">10:26</td>
                        <td class="neutral">-2.10%</td>
                        <td class="negative">-5.25%</td>
                    </tr>
                    <tr>
                        <td>15)</td>
                        <td><span class="material-name">CEMENT PORT</span> <span class="unit">[ton]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10" 
                                         fill="none" stroke="#27ae60" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$124.50</td>
                        <td class="positive">+$1.85</td>
                        <td class="positive">*****%</td>
                        <td class="time-column">10:26</td>
                        <td class="positive">6.75%</td>
                        <td class="positive">9.20%</td>
                    </tr>
                    <tr>
                        <td colspan="8" class="section-header">2) Finishing Materials</td>
                    </tr>
                    <tr>
                        <td>21)</td>
                        <td><span class="material-name">DRYWALL 1/2"</span> <span class="unit">[sheet]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,15 10,14 20,13 30,12 40,11 50,10 60,11 70,12 80,11 90,10 100,9" 
                                         fill="none" stroke="#27ae60" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$12.85</td>
                        <td class="positive">+$0.22</td>
                        <td class="positive">*****%</td>
                        <td class="time-column">09:50</td>
                        <td class="positive">8.95%</td>
                        <td class="positive">11.20%</td>
                    </tr>
                    <tr>
                        <td>22)</td>
                        <td><span class="material-name">PAINT INT</span> <span class="unit">[gal]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,18 10,17 20,16 30,15 40,16 50,17 60,18 70,17 80,16 90,15 100,14" 
                                         fill="none" stroke="#27ae60" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$42.75</td>
                        <td class="positive">+$0.85</td>
                        <td class="positive">*****%</td>
                        <td class="time-column">09:35</td>
                        <td class="positive">15.40%</td>
                        <td class="positive">18.25%</td>
                    </tr>
                    <tr>
                        <td>23)</td>
                        <td><span class="material-name">CERAMIC TILE</span> <span class="unit">[sq ft]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,20 10,19 20,18 30,19 40,20 50,21 60,20 70,19 80,18 90,17 100,16" 
                                         fill="none" stroke="#27ae60" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$3.85</td>
                        <td class="positive">+$0.05</td>
                        <td class="positive">*****%</td>
                        <td class="time-column">10:05</td>
                        <td class="positive">3.45%</td>
                        <td class="positive">6.80%</td>
                    </tr>
                    <tr>
                        <td colspan="8" class="section-header">3) Mechanical/Electrical</td>
                    </tr>
                    <tr>
                        <td>31)</td>
                        <td><span class="material-name">COPPER PIPE</span> <span class="unit">[lb]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,10 10,12 20,15 30,18 40,20 50,22 60,21 70,20 80,19 90,18 100,17" 
                                         fill="none" stroke="#e74c3c" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$4.62</td>
                        <td class="negative">-$0.08</td>
                        <td class="negative">-1.70%</td>
                        <td class="time-column">00:15</td>
                        <td class="negative">-8.90%</td>
                        <td class="negative">-12.45%</td>
                    </tr>
                    <tr>
                        <td>32)</td>
                        <td><span class="material-name">PVC PIPE 4"</span> <span class="unit">[ft]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10" 
                                         fill="none" stroke="#27ae60" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$8.45</td>
                        <td class="positive">+$0.12</td>
                        <td class="positive">*****%</td>
                        <td class="time-column">02:08</td>
                        <td class="positive">12.30%</td>
                        <td class="positive">15.85%</td>
                    </tr>
                    <tr>
                        <td>33)</td>
                        <td><span class="material-name">WIRE 12AWG</span> <span class="unit">[ft]</span></td>
                        <td class="chart-cell">
                            <svg class="mini-chart" viewBox="0 0 100 30">
                                <polyline points="0,15 10,16 20,17 30,18 40,17 50,16 60,15 70,14 80,13 90,12 100,11" 
                                         fill="none" stroke="#27ae60" stroke-width="1.5"/>
                            </svg>
                        </td>
                        <td>$0.85</td>
                        <td class="positive">+$0.02</td>
                        <td class="positive">*****%</td>
                        <td class="time-column">01:00</td>
                        <td class="positive">18.25%</td>
                        <td class="positive">22.40%</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>