/**
 * Multi-tenant Privacy Automated Test
 * - Sets up two GC users (Org A/B) and one Contractor user
 * - Creates RFQ under Org A
 * - Verifies GC isolation and Contractor visibility/bid scoping
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const { storage } = require('../server/storage');
const { generateApiKey } = require('../server/services/apiKeyService');

const BASE_URL = process.env.BASE_URL || 'http://localhost:5000';

async function createUserWithKey({ userId, classification, organizationId }) {
  // Upsert user in DB
  await storage.upsertUser({
    id: userId,
    email: `${userId}@example.com`,
    firstName: 'Test',
    lastName: 'User',
    organizationId,
    userClassification: classification,
  });
  // Create API key for the user
  const { apiKey } = await generateApiKey(userId, `Key for ${userId}`, 'full-access', 1000, {
    expirationDays: 365,
    environment: 'development',
  });
  const client = axios.create({
    baseURL: BASE_URL,
    headers: { Authorization: `Bearer ${apiKey}` },
    timeout: 30000,
  });
  return { userId, client, apiKey };
}

async function run() {
  console.log('🧪 Running Multi-tenant Privacy Test');

  // 1) Create two orgs and three users
  const orgA = await storage.createOrganization({ id: uuidv4(), name: 'Org A', slug: `org-a-${Date.now()}`, isActive: true });
  const orgB = await storage.createOrganization({ id: uuidv4(), name: 'Org B', slug: `org-b-${Date.now()}`, isActive: true });
  const orgC = await storage.createOrganization({ id: uuidv4(), name: 'Org C', slug: `org-c-${Date.now()}`, isActive: true });

  const gcA = await createUserWithKey({ userId: `gc_a_${Date.now()}`, classification: 'general_contractor', organizationId: orgA.id });
  const gcB = await createUserWithKey({ userId: `gc_b_${Date.now()}`, classification: 'general_contractor', organizationId: orgB.id });
  const contractorX = await createUserWithKey({ userId: `contractor_x_${Date.now()}`, classification: 'contractor', organizationId: orgC.id });

  // Contractor profile (required by server)
  await storage.createContractor({
    id: uuidv4(),
    userId: contractorX.userId,
    organizationId: orgC.id,
    companyName: 'Contractor X Co',
    primaryContactName: 'X Person',
    primaryContactEmail: `${contractorX.userId}@example.com`,
    primaryContactPhone: null,
    primaryAddress: null,
    tradeTypes: ['general'],
    isApproved: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  // 2) Create an RFQ in Org A owned by GC_A
  const rfq = await storage.createRfq({
    id: uuidv4(),
    createdBy: gcA.userId,
    organizationId: orgA.id,
    projectName: 'Test Project A',
    projectLocation: 'Seattle, WA',
    tradeCategory: 'general',
    description: 'Org A RFQ',
    dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
    status: 'Active',
    extractedData: {},
    aiSummary: null,
    bufferPercentage: null,
    bufferNotes: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    extractedText: null,
    masterSummary: null,
    summaryGeneratedAt: null,
    conflictFlags: null,
  });

  // Create a dummy RFQ document for access checks (object storage fetch is gated by our new org check)
  const doc = await storage.createRfqDocument({
    id: uuidv4(),
    rfqId: rfq.id,
    fileName: 'dummy.pdf',
    mimeType: 'application/pdf',
    objectKey: `org/${orgA.id}/rfq/${rfq.id}/dummy.pdf`,
    createdAt: new Date(),
  });

  // 3) GC list scoping
  const gcAList = await gcA.client.get('/api/rfqs').then(r=>r.data);
  const gcBList = await gcB.client.get('/api/rfqs').then(r=>r.data);
  if (!gcAList.every(x => x.organizationId === orgA.id)) throw new Error('GC_A list contains RFQs outside Org A');
  if (!gcBList.every(x => x.organizationId === orgB.id)) throw new Error('GC_B list contains RFQs outside Org B');
  console.log('✅ RFQ list scoping enforced for GCs');

  // 4) GC_B cannot access Org A RFQ and related resources
  const expect403 = async (fn, label) => {
    try { await fn(); console.error(`❌ Expected 403 for ${label}`); throw new Error(label); }
    catch (e) { if (e.response && e.response.status === 403) console.log(`✅ 403 enforced: ${label}`); else throw e; }
  };
  await expect403(() => gcB.client.get(`/api/rfqs/${rfq.id}`), 'GC_B get RFQ of Org A');
  await expect403(() => gcB.client.get(`/api/rfqs/${rfq.id}/documents`), 'GC_B rfq documents of Org A');
  await expect403(() => gcB.client.get(`/api/files/${doc.id}?view=true`), 'GC_B file download of Org A');

  // 5) Distributions owner-only
  await expect403(() => gcB.client.post(`/api/rfqs/${rfq.id}/distribute`, { contractorIds: [], method: 'broadcast' }), 'GC_B distribute Org A rfq');

  // 6) Analytics cross-org denial (we only require 403, not 200)
  await expect403(() => gcB.client.get(`/api/analytics/competitive-intelligence/${rfq.id}`), 'GC_B analytics rfq');

  // 7) Contractor can view all active RFQs
  const contractorRfqs = await contractorX.client.get('/api/contractors/rfqs/all').then(r=>r.data);
  if (!contractorRfqs.some(d => d.rfq && d.rfq.id === rfq.id)) throw new Error('Contractor cannot see active RFQ from Org A');
  console.log('✅ Contractor sees active RFQs across orgs');

  // 8) Contractor bid privacy: create one bid for contractorX via storage
  const bid = await storage.createBid({
    id: uuidv4(),
    rfqId: rfq.id,
    contractorId: (await storage.getContractorByUserId(contractorX.userId)).id,
    bidAmount: '10000',
    proposalText: 'We will do it',
    timeline: '2 weeks',
    status: 'submitted',
    notes: null,
    bidContactName: 'X Person',
    bidContactEmail: `${contractorX.userId}@example.com`,
    bidContactPhone: null,
    extractedAmount: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    submittedAt: new Date(),
    reviewedAt: null,
    reviewedBy: null,
    reviewNotes: null,
  });

  // As contractor: GET bids for RFQ returns only own
  const contractorBids = await contractorX.client.get(`/api/rfqs/${rfq.id}/bids`).then(r=>r.data);
  if (!Array.isArray(contractorBids) || !contractorBids.find(b => b.id === bid.id)) throw new Error('Contractor did not see own bid');
  console.log('✅ Contractor bid scoping enforced');

  console.log('\n🎉 Multi-tenant Privacy Test PASSED');
}

if (require.main === module) {
  run().catch(err => { console.error('💥 Test failed:', err && err.response ? err.response.data || err.response.status : err); process.exit(1); });
}

module.exports = { run };
