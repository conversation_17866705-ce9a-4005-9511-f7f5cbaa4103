
import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DateTimePicker } from "@/components/ui/date-time-picker";
import { Calendar, MapPin, FileText, Tag, AlertTriangle } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { toast as toastify } from "react-toastify";
import { validateDeadlineOrder } from "@/utils/countdown";

interface RFQEditFormProps {
  rfq: any;
  onSave: () => void;
  onCancel: () => void;
}

export function RFQEditForm({ rfq, onSave, onCancel }: RFQEditFormProps) {
  const [formData, setFormData] = useState({
    projectName: rfq.projectName || "",
    projectLocation: rfq.projectLocation || "",
    description: rfq.description || "",
    tradeCategory: rfq.tradeCategory || "general",
    status: rfq.status || "Draft",
    dueDate: rfq.dueDate ? new Date(rfq.dueDate).toISOString().split('T')[0] : "",
    bidProposalDeadlineAt: rfq.bidProposalDeadlineAt ? new Date(rfq.bidProposalDeadlineAt) : null,
  });

  const queryClient = useQueryClient();

  const updateRfqMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch(`/api/rfqs/${rfq.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          dueDate: data.dueDate ? new Date(data.dueDate).toISOString() : null,
          bidProposalDeadlineAt: data.bidProposalDeadlineAt ? data.bidProposalDeadlineAt.toISOString() : null,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update RFQ');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/rfqs"] });
      toast({
        title: "RFQ Updated",
        description: "The RFQ has been successfully updated.",
      });
      onSave();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update RFQ. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate deadline order
    if (formData.bidProposalDeadlineAt && formData.dueDate) {
      const rfqDeadline = new Date(formData.dueDate);
      if (!validateDeadlineOrder(formData.bidProposalDeadlineAt, rfqDeadline)) {
        toastify.error("Bid proposal deadline must be before RFQ deadline");
        return;
      }
    }
    
    updateRfqMutation.mutate(formData);
  };

  const handleChange = (field: string, value: string | Date | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Project Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Project Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="projectName">Project Name</Label>
              <Input
                id="projectName"
                value={formData.projectName}
                onChange={(e) => handleChange("projectName", e.target.value)}
                placeholder="Enter project name"
                required
              />
            </div>
            
            <div>
              <Label htmlFor="projectLocation">Location</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="projectLocation"
                  value={formData.projectLocation}
                  onChange={(e) => handleChange("projectLocation", e.target.value)}
                  placeholder="Enter project location"
                  className="pl-10"
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                placeholder="Enter project description"
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="tradeCategory">Trade Category</Label>
              <Select value={formData.tradeCategory} onValueChange={(value) => handleChange("tradeCategory", value)}>
                <SelectTrigger>
                  <Tag className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select trade category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="electrical">Electrical</SelectItem>
                  <SelectItem value="plumbing">Plumbing</SelectItem>
                  <SelectItem value="hvac">HVAC</SelectItem>
                  <SelectItem value="concrete">Concrete</SelectItem>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="site_work">Site Work</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Status & Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Status & Timeline
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleChange("status", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Draft">Draft</SelectItem>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Review">Review</SelectItem>
                  <SelectItem value="Closed">Closed</SelectItem>
                  <SelectItem value="Awarded">Awarded</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="bidProposalDeadlineAt">Bid Proposal Deadline</Label>
              <DateTimePicker
                value={formData.bidProposalDeadlineAt}
                onChange={(date) => handleChange("bidProposalDeadlineAt", date)}
                placeholder="Select bid proposal deadline"
                label=""
                maxDate={formData.dueDate ? new Date(formData.dueDate) : undefined}
              />
              {formData.bidProposalDeadlineAt && formData.dueDate && (
                <div className="flex items-center gap-2 mt-1">
                  {!validateDeadlineOrder(formData.bidProposalDeadlineAt, new Date(formData.dueDate)) && (
                    <>
                      <AlertTriangle className="h-4 w-4 text-destructive" />
                      <span className="text-sm text-destructive">
                        Must be before RFQ deadline
                      </span>
                    </>
                  )}
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="dueDate">RFQ Deadline</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="dueDate"
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => handleChange("dueDate", e.target.value)}
                  className="pl-10"
                  required
                />
              </div>
            </div>

            <div className="pt-4">
              <Label className="text-sm font-medium text-muted-foreground">Created</Label>
              <p className="mt-1 text-sm">
                {new Date(rfq.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-2 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={updateRfqMutation.isPending}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={updateRfqMutation.isPending}
        >
          {updateRfqMutation.isPending ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </form>
  );
}
