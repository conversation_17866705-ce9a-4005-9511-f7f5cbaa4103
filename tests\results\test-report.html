
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bidaible Unified File Processing Test Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .summary-card.success { border-left-color: #28a745; }
        .summary-card.warning { border-left-color: #ffc107; }
        .summary-card.danger { border-left-color: #dc3545; }
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .summary-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        .suite {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        .suite-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .suite-header h3 {
            margin: 0;
            color: #2c3e50;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pass { background: #d4edda; color: #155724; }
        .status-fail { background: #f8d7da; color: #721c24; }
        .status-error { background: #f8d7da; color: #721c24; }
        .test-details {
            padding: 20px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-name {
            font-weight: 500;
            color: #2c3e50;
        }
        .test-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-duration {
            color: #6c757d;
            font-size: 0.9em;
        }
        .performance-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .performance-item {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Bidaible Unified File Processing Test Report</h1>
            <p>Wasabi Storage Integration & AI Processing Pipeline</p>
            <p><strong>Generated:</strong> 2025-08-15T16:32:27.383Z</p>
            <p><strong>Duration:</strong> 24s</p>
        </div>

        <div class="summary">
            <div class="summary-card danger">
                <h3>Success Rate</h3>
                <div class="value">25.0%</div>
            </div>
            <div class="summary-card success">
                <h3>Tests Passed</h3>
                <div class="value">3</div>
            </div>
            <div class="summary-card danger">
                <h3>Tests Failed</h3>
                <div class="value">9</div>
            </div>
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="value">12</div>
            </div>
        </div>

        
        <div class="suite">
            <div class="suite-header">
                <h3>Wasabi Storage Integration</h3>
                <span class="status-badge status-fail">FAIL</span>
            </div>
            <div class="test-details">
                <p><strong>Duration:</strong> 3s | 
                   <strong>Tests:</strong> 6 | 
                   <strong>Passed:</strong> 1 | 
                   <strong>Failed:</strong> 5</p>
                
                
                <div class="test-item">
                    <div class="test-name">Basic File Upload to Wasabi</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">2858ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">Large File Chunked Upload</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">18ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">Multi-file Batch Upload</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">9ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">SSE Progress Tracking</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">3ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">File Validation & Security</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">4ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">Wasabi Storage Operations</div>
                    <div class="test-status">
                        <span class="status-badge status-pass">PASS</span>
                        <span class="test-duration">8ms</span>
                    </div>
                </div>
                
                
                
            </div>
        </div>
        
        <div class="suite">
            <div class="suite-header">
                <h3>AI Processing & PDF Extraction</h3>
                <span class="status-badge status-fail">FAIL</span>
            </div>
            <div class="test-details">
                <p><strong>Duration:</strong> 21s | 
                   <strong>Tests:</strong> 6 | 
                   <strong>Passed:</strong> 2 | 
                   <strong>Failed:</strong> 4</p>
                
                
                <div class="test-item">
                    <div class="test-name">PDF.js Primary Extraction</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">1722ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">AI Processing - Groq Integration</div>
                    <div class="test-status">
                        <span class="status-badge status-pass">PASS</span>
                        <span class="test-duration">1987ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">Multi-file AI Processing with Priorities</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">9916ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">AI Processing Fallback Mechanisms</div>
                    <div class="test-status">
                        <span class="status-badge status-pass">PASS</span>
                        <span class="test-duration">1772ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">Large File AI Processing Performance</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">2397ms</span>
                    </div>
                </div>
                
                <div class="test-item">
                    <div class="test-name">PDF Extraction Confidence Scoring</div>
                    <div class="test-status">
                        <span class="status-badge status-fail">FAIL</span>
                        <span class="test-duration">3281ms</span>
                    </div>
                </div>
                
                
                
            </div>
        </div>
        

        
        <div class="performance-section">
            <h3>📈 Performance Metrics</h3>
            <div class="performance-grid">
                
                    <div class="performance-item">
                        <h4>AI Processing - Groq Integration</h4>
                        
                        
                        
                        <p><strong>AI Summary:</strong> 1050 chars</p>
                    </div>
                    
            </div>
        </div>
        

        <div class="footer">
            <p>Generated by Bidaible Test Suite | Node.js v22.15.0 | win32</p>
        </div>
    </div>
</body>
</html>
  