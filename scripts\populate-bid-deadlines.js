#!/usr/bin/env node

/**
 * <PERSON>ript to populate bid_proposal_deadline_at for existing RFQs
 * Sets bid proposal deadline to 7 days before due_date (with minimum of 1 day)
 */

import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from "ws";
import { rfqs } from '../shared/schema.js';
import { eq, isNull } from 'drizzle-orm';

// Load environment variables
config();

neonConfig.webSocketConstructor = ws;

const pool = new Pool({ connectionString: process.env.DATABASE_URL });
const db = drizzle({ client: pool });

async function populateBidDeadlines() {
  console.log('🔄 Populating bid proposal deadlines for existing RFQs...');
  
  try {
    // Get all RFQs without bid proposal deadlines
    const rfqsToUpdate = await db
      .select({ id: rfqs.id, dueDate: rfqs.dueDate, projectName: rfqs.projectName })
      .from(rfqs)
      .where(isNull(rfqs.bidProposalDeadlineAt));

    console.log(`📊 Found ${rfqsToUpdate.length} RFQs to update`);

    let updated = 0;
    for (const rfq of rfqsToUpdate) {
      // Calculate bid proposal deadline (7 days before due date, minimum 1 day)
      const dueDate = new Date(rfq.dueDate);
      const sevenDaysBefore = new Date(dueDate);
      sevenDaysBefore.setDate(sevenDaysBefore.getDate() - 7);
      
      // Ensure it's at least 1 day from now
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      const bidProposalDeadline = sevenDaysBefore > tomorrow ? sevenDaysBefore : tomorrow;
      
      await db
        .update(rfqs)
        .set({ bidProposalDeadlineAt: bidProposalDeadline })
        .where(eq(rfqs.id, rfq.id));
      
      console.log(`✅ Updated "${rfq.projectName}": ${bidProposalDeadline.toISOString()}`);
      updated++;
    }

    console.log(`🎉 Successfully updated ${updated} RFQs with bid proposal deadlines`);
    
  } catch (error) {
    console.error('❌ Error populating bid deadlines:', error);
    process.exit(1);
  }
}

// Run the script
populateBidDeadlines()
  .then(() => {
    console.log('✨ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
