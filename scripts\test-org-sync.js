/**
 * Test Organization Sync Service
 * 
 * Simple test to verify the sync service works correctly
 */

import * as dotenv from 'dotenv';

dotenv.config();

async function testOrgSync() {
  console.log('🧪 Testing Organization Sync Service...\n');

  if (!process.env.CLERK_SECRET_KEY) {
    console.error('❌ CLERK_SECRET_KEY environment variable is required');
    return;
  }

  try {
    // Import the sync service
    const { default: OrganizationSyncService } = await import('../server/services/organizationSyncService.js');
    
    // Test syncing for existing users
    const testUsers = [
      'user_319LPVLDMzru79fF2TZXTX5xZ6i', // <EMAIL>  
      'user_31Y2mzQu3ZpajwZNMimrkAjufsX'  // <EMAIL>
    ];

    for (const userId of testUsers) {
      console.log(`🔄 Testing sync for user: ${userId}`);
      
      try {
        await OrganizationSyncService.ensureUserOrganizationSync(userId);
        console.log(`✅ Sync completed for user: ${userId}`);
      } catch (error) {
        console.error(`❌ Sync failed for user ${userId}:`, error.message);
      }
      
      console.log('');
    }

    console.log('✅ Organization sync service test completed');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testOrgSync().catch(console.error);
