# Bidaible Technical Context

## Technology Stack

### Frontend Technologies
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite with Hot Module Replacement (HMR)
- **Routing**: Wouter for lightweight client-side navigation
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query for server state, React Context for global state
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React icons
- **UI Components**: shadcn/ui design system

### Backend Technologies
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: <PERSON> Auth + JWT API keys
- **File Storage**: Replit Object Storage
- **Caching**: In-memory caching with TTL and LRU eviction
- **Session Management**: Clerk-managed sessions with JWTs

### AI & Processing
- **Primary AI**: Groq API (openai/gpt-oss-120b model)
- **Fallback AI**: OpenAI GPT-4.1-mini
- **Vision AI**: Gemini 2.5-pro for OCR fallback
- **Document Processing**: PDF.js, pdf-parse, Gemini Vision
- **Text Extraction**: UnifiedPDFExtractor with 3-tier fallback
- **Progress Tracking**: Server-Sent Events (SSE) for real-time updates

## Development Environment

### Required Tools
- **Node.js**: Version 18+ with npm
- **Database**: PostgreSQL 13+ (Neon serverless recommended)
- **IDE**: VSCode with TypeScript support
- **Git**: Version control with GitHub integration

### Environment Variables
```env
# Database
DATABASE_URL=postgresql://username:password@host:port/database

# AI Services
GROQ_API_KEY=your_groq_key
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key

# Authentication
CLERK_SECRET_KEY=your_clerk_secret
CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key

# Application
NODE_ENV=development|production
PRIMARY_MODEL=groq
SESSION_SECRET=your_session_secret
JWT_SECRET=your_jwt_secret
```

### Development Commands
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Database operations
npm run db:push        # Push schema changes
npm run db:studio      # Open Drizzle Studio

# Testing
npm test              # Run unit tests
npm run test:e2e      # Run end-to-end tests
```

## Architecture Constraints

### Performance Requirements
- **AI Processing**: Sub-3-second analysis generation
- **File Upload**: Support up to 50MB per file, 8 files per RFQ
- **Concurrent Users**: Support 10,000+ concurrent users
- **Response Time**: <1 second for API responses
- **Uptime**: 99.9% availability target

### Security Constraints
- **Data Isolation**: Complete tenant separation at database level
- **Authentication**: Dual system (Clerk + JWT API keys)
- **Encryption**: In-transit and at-rest data encryption
- **Audit Logging**: Comprehensive activity tracking
- **Rate Limiting**: Per-user and per-API-key throttling

### Scalability Constraints
- **Database**: PostgreSQL with 30+ strategic indexes
- **File Storage**: Cloud-based object storage (Replit)
- **Caching**: In-memory with TTL and LRU eviction
- **Processing**: 8-processor concurrent limit for AI operations
- **Memory**: Stream processing for large files (<100MB per operation)

## Dependencies

### Core Dependencies
```json
{
  "react": "^18.0.0",
  "typescript": "^5.0.0",
  "express": "^4.18.0",
  "drizzle-orm": "^0.28.0",
  "postgres": "^3.3.0",
  "@clerk/express": "^1.0.0",
  "zod": "^3.22.0",
  "tailwindcss": "^3.3.0"
}
```

### AI Processing Dependencies
```json
{
  "openai": "^4.0.0",
  "@google/generative-ai": "^0.2.0",
  "groq-sdk": "^0.5.0",
  "pdf-parse": "^1.1.1",
  "pdfjs-dist": "^3.11.0"
}
```

### Development Dependencies
```json
{
  "vite": "^4.4.0",
  "@types/node": "^20.0.0",
  "@types/react": "^18.0.0",
  "jest": "^29.0.0",
  "cypress": "^13.0.0",
  "eslint": "^8.0.0",
  "prettier": "^3.0.0"
}
```

## Deployment Architecture

### Production Environment
- **Platform**: Replit Deployments
- **Database**: Neon PostgreSQL (serverless)
- **File Storage**: Replit Object Storage
- **CDN**: Cloudflare for static assets
- **Monitoring**: Built-in health checks and logging

### Development Workflow
1. **Local Development**: Vite dev server + Express backend
2. **Version Control**: Git with feature branch workflow
3. **CI/CD**: Automatic deployment via Replit
4. **Testing**: Unit tests + integration tests + E2E tests
5. **Code Quality**: ESLint + Prettier + TypeScript strict mode

## Tool Usage Patterns

### Database Management
- **ORM**: Drizzle for type-safe database operations
- **Migrations**: Automatic schema migrations via `drizzle-kit`
- **Studio**: Drizzle Studio for database visualization
- **Indexing**: Strategic indexes for query optimization

### API Development
- **Validation**: Zod schemas for request/response validation
- **Error Handling**: Centralized error management with logging
- **Rate Limiting**: Express middleware for API throttling
- **Documentation**: OpenAPI/Swagger for API documentation

### Frontend Development
- **Component Library**: shadcn/ui for consistent design
- **State Management**: TanStack Query for server state caching
- **Form Handling**: React Hook Form with Zod validation
- **Testing**: React Testing Library + Jest for unit tests

### File Processing
- **Upload Handling**: Multer middleware for multipart/form-data
- **Text Extraction**: Multiple libraries with fallback strategy
- **Progress Tracking**: Server-Sent Events for real-time updates
- **Storage**: Object storage with secure access patterns

## Integration Patterns

### External Services
- **Clerk**: Authentication and user management
- **Groq**: Primary AI processing for speed
- **OpenAI**: Fallback AI processing for reliability
- **Gemini**: Vision AI for OCR capabilities
- **Replit**: Hosting, storage, and deployment

### Internal Services
- **AI Service**: Centralized AI processing with provider abstraction
- **File Service**: Document upload and processing coordination
- **Cache Service**: In-memory caching with intelligent invalidation
- **Audit Service**: Comprehensive activity logging
- **Progress Service**: Real-time progress tracking via SSE

## Development Best Practices

### Code Quality
- **TypeScript Strict Mode**: Full type safety enforcement
- **ESLint Configuration**: Consistent code style and error prevention
- **Prettier Integration**: Automatic code formatting
- **Import Organization**: Consistent import ordering and grouping

### Testing Strategy
- **Unit Tests**: Service layer and utility functions (Jest)
- **Integration Tests**: API endpoints with database (Supertest)
- **E2E Tests**: Critical user workflows (Cypress)
- **Performance Tests**: Load testing for scalability validation

### Security Practices
- **Input Validation**: All API inputs validated with Zod schemas
- **SQL Injection Prevention**: Parameterized queries via ORM
- **XSS Protection**: Content sanitization and CSP headers
- **Error Handling**: Secure error messages without system exposure

This technical foundation enables Bidaible to deliver enterprise-grade performance, security, and scalability while maintaining developer productivity and code quality.
