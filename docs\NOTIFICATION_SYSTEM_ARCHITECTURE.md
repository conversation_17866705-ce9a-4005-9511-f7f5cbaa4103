# Real-Time Notification System Architecture

**Date:** August 20, 2025  
**Version:** 1.0 - Initial Documentation  
**Focus:** Real-time notification system with deadline management integration

## Overview

The Bidaible notification system provides comprehensive real-time notifications with a focus on RFQ deadline management, bid status updates, and system alerts. The system features a live notification bell in the main navigation, automated scheduling for deadline reminders, and full notification management capabilities.

## System Components

### Frontend Components

#### NotificationDropdown.tsx
**Location:** `client/src/components/NotificationDropdown.tsx`

**Features:**
- Bell icon with unread count badge in main navigation
- Dropdown menu displaying live notification data
- Real-time updates via polling every 30-60 seconds
- Click navigation to relevant pages (RFQs, bids)
- Mark as read functionality (individual and batch)
- Support for all notification types including RFQ deadline reminders
- Priority-based visual indicators with color-coded styling

**Key Functions:**
```typescript
export function NotificationDropdown() {
  const { data: unreadData, isLoading } = useUnreadNotifications();
  const markAsReadMutation = useMarkNotificationRead();
  const markAllAsReadMutation = useMarkAllNotificationsRead();
  
  const handleNotificationClick = (notification: Notification) => {
    // Mark as read and navigate to relevant page
  };
}
```

#### useNotifications.ts Hook
**Location:** `client/src/hooks/useNotifications.ts`

**Features:**
- Real-time polling for live updates using TanStack Query
- Query invalidation for immediate updates after actions
- Separate hooks for all notifications vs unread-only
- Optimistic updates for mark-as-read operations

**Hook Types:**
- `useUnreadNotifications()` - For bell dropdown with count
- `useNotifications()` - For full notification management page
- `useMarkNotificationRead()` - Individual notification marking
- `useMarkAllNotificationsRead()` - Batch operations

#### Notifications.tsx Page
**Location:** `client/src/pages/Notifications.tsx`

**Features:**
- Comprehensive notifications page with tabbed interface
- Unread/All notifications tabs with filtering
- Full notification history and management
- Advanced search and filtering capabilities
- Detailed notification display with metadata

### Backend Services

#### Notification Service
**Location:** `server/services/notificationService.ts`

**Core Functions:**
```typescript
class NotificationService {
  // RFQ deadline reminder notification type
  static readonly NotificationTypes = {
    RFQ_DEADLINE_REMINDER: 'RFQ_DEADLINE_REMINDER',
    BID_SUBMITTED: 'BID_SUBMITTED',
    BID_ACCEPTED: 'BID_ACCEPTED',
    // ... other types
  };

  // Create deadline reminder notifications
  static async createRfqDeadlineReminderNotification(params: {
    userId: string;
    rfqId: string;
    projectName: string;
    deadlineDate: string;
    reminderType: string;
  }) {
    // Implementation creates notification and delivery records
  }
}
```

#### Scheduled Notification Processor
**Location:** `server/services/scheduledNotificationProcessor.ts`

**Features:**
- Background processing running every minute
- Processes pending scheduled notifications
- Email delivery integration
- Status tracking and error handling
- Audit logging for all notification activities

**Core Implementation:**
```typescript
export class ScheduledNotificationProcessor {
  private processingInterval?: NodeJS.Timeout;
  private readonly intervalMs = 60000; // Check every minute

  start() {
    this.processScheduledNotifications();
    this.processingInterval = setInterval(() => {
      this.processScheduledNotifications();
    }, this.intervalMs);
  }

  private async processScheduledNotifications() {
    const pendingNotifications = await storage.getPendingScheduledNotifications();
    
    for (const notification of pendingNotifications) {
      await this.processNotification(notification);
    }
  }
}
```

### Database Schema

#### Notifications Table
```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR REFERENCES users(id) NOT NULL,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  priority notification_priority NOT NULL DEFAULT 'medium',
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE notification_priority AS ENUM ('low', 'medium', 'high', 'urgent');

-- Indexes for performance
CREATE INDEX IDX_notifications_user_id_read_at ON notifications(user_id, read_at);
CREATE INDEX IDX_notifications_user_id_created_at ON notifications(user_id, created_at DESC);
CREATE INDEX IDX_notifications_type ON notifications(type);
```

#### Scheduled Notifications Table
```sql
CREATE TABLE scheduled_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_type TEXT NOT NULL,
  user_id VARCHAR REFERENCES users(id) NOT NULL,
  rfq_id UUID REFERENCES rfqs(id),
  scheduled_for TIMESTAMPTZ NOT NULL,
  payload JSONB NOT NULL DEFAULT '{}',
  status scheduled_notification_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE scheduled_notification_status AS ENUM ('pending', 'sent', 'failed', 'cancelled');

-- Indexes for performance
CREATE INDEX IDX_scheduled_notifications_status_scheduled ON scheduled_notifications(status, scheduled_for);
CREATE INDEX IDX_scheduled_notifications_user_id ON scheduled_notifications(user_id);
CREATE INDEX IDX_scheduled_notifications_rfq_id ON scheduled_notifications(rfq_id);
```

## API Endpoints

### Real-time Notification Endpoints
```typescript
// Get user notifications with filtering
GET /api/notifications?filter=all|unread

// Get unread notifications with count (for bell dropdown)
GET /api/notifications/unread

// Mark individual notification as read
PATCH /api/notifications/:id/read

// Mark all user notifications as read (batch operation)
PATCH /api/notifications/mark-all-read

// Get scheduled deadline notifications for RFQs
GET /api/notifications/scheduled
```

### Integration with RFQ Routes
```typescript
// RFQ creation automatically schedules deadline notifications
POST /api/rfqs
// Triggers: scheduleRfqDeadlineReminders(rfq)

// RFQ update may reschedule notifications
PUT /api/rfqs/:id
// May trigger notification rescheduling if deadlines change
```

## RFQ Deadline Management Integration

### Automatic Scheduling
When an RFQ is created, the system automatically schedules three deadline reminder notifications:

```typescript
async function scheduleRfqDeadlineReminders(rfq: any) {
  const reminderTimes = [
    { days: 7, type: '1_week_before' },
    { days: 3, type: '3_days_before' },
    { days: 1, type: '1_day_before' }
  ];

  for (const reminder of reminderTimes) {
    const scheduledFor = new Date(rfq.bidProposalDeadlineAt);
    scheduledFor.setDate(scheduledFor.getDate() - reminder.days);

    if (scheduledFor > new Date()) {
      await storage.createScheduledNotification({
        notificationType: NotificationService.NotificationTypes.RFQ_DEADLINE_REMINDER,
        userId: rfq.createdBy,
        rfqId: rfq.id,
        scheduledFor,
        payload: {
          rfqId: rfq.id,
          projectName: rfq.projectName,
          deadlineDate: rfq.bidProposalDeadlineAt,
          reminderType: reminder.type
        }
      });
    }
  }
}
```

### Notification Types and Visual Indicators

#### Notification Type Mapping
```typescript
const getNotificationIcon = (type: string) => {
  switch (type) {
    case "bid_submitted":
      return <FileText className="h-4 w-4 text-green-600" />;
    case "bid_accepted":
      return <CheckCircle2 className="h-4 w-4 text-green-700" />;
    case "bid_rejected":
      return <Trash2 className="h-4 w-4 text-red-600" />;
    case "rfq_deadline_reminder":
      return <Calendar className="h-4 w-4 text-orange-600" />;
    // ... other types
  }
};
```

#### Priority Color Coding
```typescript
const getPriorityColor = (priority: "low" | "medium" | "high" | "urgent") => {
  switch (priority) {
    case "urgent":
      return "border-l-red-600";
    case "high":
      return "border-l-red-500";
    case "medium":
      return "border-l-orange-500";
    case "low":
      return "border-l-blue-500";
  }
};
```

## Real-time Updates Architecture

### Polling Strategy
The notification system uses polling instead of WebSockets for simplicity and reliability:

```typescript
// TanStack Query configuration for real-time updates
export const useUnreadNotifications = () => {
  return useQuery({
    queryKey: ['notifications', 'unread'],
    queryFn: () => getQueryFn({ on401: "returnNull" })('/api/notifications/unread'),
    refetchInterval: 30000, // Poll every 30 seconds
    refetchIntervalInBackground: true,
  });
};
```

**Benefits of Polling Approach:**
- Simple implementation and debugging
- Automatic reconnection handling
- Works reliably across different network conditions
- No WebSocket connection management complexity
- Efficient with reasonable polling intervals (30-60 seconds)

### Performance Optimization
- **Query Deduplication**: TanStack Query automatically deduplicates identical queries
- **Background Refetching**: Updates continue when tab is not active
- **Optimistic Updates**: Mark-as-read operations update UI immediately
- **Selective Re-rendering**: Only components using notification data re-render

## Integration Points

### Main Navigation
```typescript
// Navbar.tsx integration
<NotificationDropdown /> // Always visible in top navigation
```

### Routing Integration
```typescript
// Click navigation from notifications
const handleNotificationClick = (notification: Notification) => {
  if (notification.data) {
    const { rfqId, bidId } = notification.data;
    
    if (rfqId && bidId) {
      setLocation(`/rfqs/${rfqId}/bids`);
    } else if (rfqId) {
      setLocation(`/rfq/${rfqId}`);
    }
  }
};
```

### Settings Integration
Users can manage notification preferences in Settings:
- Email delivery toggle
- Reminder frequency options  
- Notification method preferences (email, in-app)

## Security and Privacy

### Access Control
- All notifications are user-scoped (users only see their own notifications)
- Organization-based data isolation maintained
- API endpoints require authentication

### Data Protection
- Notification content respects user privacy settings
- No sensitive information exposed in notification metadata
- Audit trails for all notification activities

## Performance Considerations

### Database Optimization
- Strategic indexes on user_id, read_at, and created_at columns
- Efficient queries for unread count calculation
- Proper pagination for large notification lists

### Memory Management
- Lightweight polling implementation
- Efficient React component re-rendering
- Optimized query caching strategies

### Scalability
- Background processor handles high notification volumes
- Database queries optimized for concurrent users
- Notification batching for improved performance

## Monitoring and Analytics

### Delivery Tracking
- Complete audit trail of notification delivery attempts
- Status tracking (pending, sent, failed)
- Performance metrics for notification processing

### User Engagement
- Click-through rates on notifications
- Mark-as-read patterns and timing
- Notification preference analytics

## Future Enhancements

### Planned Features
1. **WebSocket Integration**: Real-time notifications without polling
2. **Push Notifications**: Browser push notifications for urgent alerts
3. **SMS Integration**: Text message notifications for critical deadlines
4. **Custom Templates**: User-configurable notification templates
5. **Advanced Filtering**: More sophisticated notification filtering options

### Scalability Improvements
1. **Event Queue System**: Replace polling with event-driven architecture
2. **Notification Channels**: Multiple delivery channels (email, SMS, push)
3. **User Preference Engine**: Advanced notification preference management
4. **Analytics Dashboard**: Comprehensive notification analytics

---

## Related Documentation

- [RFQ Deadline Management](./RFQ_DEADLINE_MANAGEMENT.md) - Detailed deadline system documentation
- [Technical Architecture](./TECHNICAL_ARCHITECTURE.md) - Overall system architecture
- [API Documentation](./API_CONSOLIDATED.md) - Complete API reference
- [Main AGENT.md](../AGENT.md) - Development guide and system overview

**Last Updated**: August 20, 2025  
**Maintainer**: Development Team  
**Status**: Production Ready ✅
