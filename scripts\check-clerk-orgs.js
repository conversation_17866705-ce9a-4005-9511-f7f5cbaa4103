/**
 * Check Clerk Organizations Script
 * 
 * This script checks what organizations the legitimate users belong to in Clerk
 * and compares them with the Bidaible database.
 */

import { Pool, neonConfig } from '@neondatabase/serverless';
import { clerkClient } from '@clerk/express';
import ws from 'ws';
import * as dotenv from 'dotenv';

dotenv.config();
neonConfig.webSocketConstructor = ws;

const pool = new Pool({ connectionString: process.env.DATABASE_URL });

const LEGITIMATE_USERS = [
  '<EMAIL>',
  '<EMAIL>'
];

async function getUserByEmail(email) {
  try {
    const users = await clerkClient.users.getUserList({ emailAddress: [email] });
    return users.data.length > 0 ? users.data[0] : null;
  } catch (error) {
    console.error(`Error fetching user ${email} from Clerk:`, error.message);
    return null;
  }
}

async function getUserOrganizations(userId) {
  try {
    const memberships = await clerkClient.users.getOrganizationMembershipList({ userId });
    return memberships.data;
  } catch (error) {
    console.error(`Error fetching organizations for user ${userId}:`, error.message);
    return [];
  }
}

async function checkClerkOrganizations() {
  console.log('🔍 Checking Clerk organizations for legitimate users...\n');
  
  for (const email of LEGITIMATE_USERS) {
    console.log(`👤 User: ${email}`);
    
    // Get user from Clerk
    const clerkUser = await getUserByEmail(email);
    if (!clerkUser) {
      console.log('  ❌ User not found in Clerk\n');
      continue;
    }
    
    console.log(`  📧 Clerk User ID: ${clerkUser.id}`);
    console.log(`  📝 Name: ${clerkUser.firstName} ${clerkUser.lastName}`);
    
    // Get user from Bidaible database
    const dbUserQuery = `SELECT id, organization_id FROM users WHERE email = $1`;
    const dbUserResult = await pool.query(dbUserQuery, [email]);
    const dbUser = dbUserResult.rows[0];
    
    if (dbUser) {
      console.log(`  🗄️  Bidaible DB User ID: ${dbUser.id}`);
      console.log(`  🏢 Bidaible Organization ID: ${dbUser.organization_id || 'NULL'}`);
    } else {
      console.log('  ❌ User not found in Bidaible database');
    }
    
    // Get Clerk organizations
    const memberships = await getUserOrganizations(clerkUser.id);
    
    if (memberships.length === 0) {
      console.log('  🏢 No Clerk organizations found');
    } else {
      console.log(`  🏢 Clerk Organizations (${memberships.length}):`);
      
      for (const membership of memberships) {
        const org = membership.organization;
        console.log(`    - ${org.name} (ID: ${org.id})`);
        console.log(`      Role: ${membership.role}`);
        console.log(`      Slug: ${org.slug || 'N/A'}`);
        console.log(`      Created: ${new Date(org.createdAt).toLocaleDateString()}`);
        
        // Check if this org exists in Bidaible database (by slug since Clerk IDs aren't UUIDs)
        const dbOrgQuery = `SELECT id, name, slug FROM organizations WHERE slug = $1 OR name = $2`;
        const dbOrgResult = await pool.query(dbOrgQuery, [org.slug, org.name]);
        
        if (dbOrgResult.rows.length > 0) {
          const dbOrg = dbOrgResult.rows[0];
          console.log(`      ✅ Exists in Bidaible DB: ${dbOrg.name} (${dbOrg.slug})`);
        } else {
          console.log(`      ❌ Missing from Bidaible DB - needs sync`);
        }
        console.log('');
      }
    }
    
    console.log('');
  }
}

async function main() {
  console.log('🔍 Clerk Organization Checker');
  console.log('==============================\n');

  if (!process.env.CLERK_SECRET_KEY) {
    console.error('❌ CLERK_SECRET_KEY environment variable is required');
    process.exit(1);
  }

  try {
    await checkClerkOrganizations();
    
    console.log('✅ Clerk organization check completed');
    
  } catch (error) {
    console.error('❌ Check failed:', error);
    console.error('Stack:', error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

main().catch(console.error);
