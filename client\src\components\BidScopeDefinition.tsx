import { useState } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Trash2, Plus, CheckCircle, XCircle, Package } from "lucide-react";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

export interface ScopeItem {
  type: 'included' | 'excluded';
  description: string;
  category: string;
}

interface BidScopeDefinitionProps {
  includedName: string;
  excludedName: string;
}

// Common scope categories for construction bids
const scopeCategories = [
  "Materials",
  "Labor", 
  "Equipment",
  "Permits & Fees",
  "Testing & Inspections",
  "Coordination",
  "Protection",
  "Cleanup",
  "Warranty",
  "Other",
];

// Template suggestions for common inclusions/exclusions
const inclusionTemplates = [
  { category: "Materials", description: "All materials specified in the project documents" },
  { category: "Labor", description: "All labor required for installation and completion" },
  { category: "Equipment", description: "All equipment and tools necessary for the work" },
  { category: "Permits & Fees", description: "Trade-specific permits and inspection fees" },
  { category: "Testing & Inspections", description: "Required testing and inspections for our trade" },
  { category: "Warranty", description: "One-year warranty on materials and workmanship" },
  { category: "Coordination", description: "Coordination with other trades as required" },
  { category: "Protection", description: "Protection of work during construction" },
];

const exclusionTemplates = [
  { category: "Other", description: "Work by other trades not specifically listed" },
  { category: "Materials", description: "Owner-furnished materials or equipment" },
  { category: "Other", description: "General conditions and overhead costs" },
  { category: "Other", description: "Bonds, insurance, and general contractor fees" },
  { category: "Equipment", description: "Temporary power, lighting, and water" },
  { category: "Cleanup", description: "General cleanup and debris removal" },
  { category: "Other", description: "Design or engineering services" },
  { category: "Permits & Fees", description: "Building permits and general fees" },
];

export function BidScopeDefinition({ includedName, excludedName }: BidScopeDefinitionProps) {
  const { control, watch } = useFormContext();
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  
  const { fields: includedFields, append: appendIncluded, remove: removeIncluded } = useFieldArray({
    control,
    name: includedName,
  });

  const { fields: excludedFields, append: appendExcluded, remove: removeExcluded } = useFieldArray({
    control,
    name: excludedName,
  });

  const includedItems = watch(includedName) || [];
  const excludedItems = watch(excludedName) || [];

  const addIncludedItem = (template?: { category: string; description: string }) => {
    appendIncluded({
      type: 'included',
      description: template?.description || "",
      category: template?.category || "Other",
    });
  };

  const addExcludedItem = (template?: { category: string; description: string }) => {
    appendExcluded({
      type: 'excluded',
      description: template?.description || "",
      category: template?.category || "Other",
    });
  };

  const addFromTemplate = (template: { category: string; description: string }, type: 'included' | 'excluded') => {
    if (type === 'included') {
      addIncludedItem(template);
    } else {
      addExcludedItem(template);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Scope Definition
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Define what is included and excluded in your bid to ensure clear expectations
        </p>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="included" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="included" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Included ({includedItems.length})
            </TabsTrigger>
            <TabsTrigger value="excluded" className="flex items-center gap-2">
              <XCircle className="h-4 w-4" />
              Excluded ({excludedItems.length})
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Templates
            </TabsTrigger>
          </TabsList>

          {/* Included Items Tab */}
          <TabsContent value="included" className="space-y-4">
            {includedFields.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No inclusions added yet</p>
                <p className="text-sm">Add items that are included in your bid scope</p>
              </div>
            )}

            {includedFields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 border rounded-lg">
                {/* Category */}
                <div className="md:col-span-3">
                  <FormField
                    control={control}
                    name={`${includedName}.${index}.category`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {scopeCategories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Description */}
                <div className="md:col-span-8">
                  <FormField
                    control={control}
                    name={`${includedName}.${index}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            placeholder="Describe what is included in your bid..."
                            rows={2}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Remove Button */}
                <div className="md:col-span-1 flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeIncluded(index)}
                    className="w-full"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={() => addIncludedItem()}
              className="w-full"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Inclusion
            </Button>
          </TabsContent>

          {/* Excluded Items Tab */}
          <TabsContent value="excluded" className="space-y-4">
            {excludedFields.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <XCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No exclusions added yet</p>
                <p className="text-sm">Add items that are excluded from your bid scope</p>
              </div>
            )}

            {excludedFields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 border rounded-lg">
                {/* Category */}
                <div className="md:col-span-3">
                  <FormField
                    control={control}
                    name={`${excludedName}.${index}.category`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {scopeCategories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Description */}
                <div className="md:col-span-8">
                  <FormField
                    control={control}
                    name={`${excludedName}.${index}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            placeholder="Describe what is excluded from your bid..."
                            rows={2}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Remove Button */}
                <div className="md:col-span-1 flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeExcluded(index)}
                    className="w-full"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={() => addExcludedItem()}
              className="w-full"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Exclusion
            </Button>
          </TabsContent>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <div>
              <h4 className="text-lg font-semibold mb-4 text-green-700 dark:text-green-300">
                Common Inclusions
              </h4>
              <div className="grid grid-cols-1 gap-3">
                {inclusionTemplates.map((template, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {template.category}
                        </Badge>
                      </div>
                      <p className="text-sm">{template.description}</p>
                    </div>
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      onClick={() => addFromTemplate(template, 'included')}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4 text-red-700 dark:text-red-300">
                Common Exclusions
              </h4>
              <div className="grid grid-cols-1 gap-3">
                {exclusionTemplates.map((template, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {template.category}
                        </Badge>
                      </div>
                      <p className="text-sm">{template.description}</p>
                    </div>
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      onClick={() => addFromTemplate(template, 'excluded')}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}