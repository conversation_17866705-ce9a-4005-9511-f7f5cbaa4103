import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { storage } from '../storage';
import { clerkClient } from '../clerkAuth';
import type { InsertAccessAuditLog } from '@shared/schema';

export const requireOrgAdmin = (): RequestHandler => {
  return async (req: any, res, next) => {
    try {
      const userId = req.user?.claims?.sub;
      const user = userId ? await storage.getUser(userId) : null;

      if (!user || !user.organizationId) {
        await storage.createAccessAuditLog({
          userId: userId || 'unknown',
          action: `${req.method} ${req.path}`,
          resource: req.path,
          organizationId: user?.organizationId,
          success: false,
          reason: `No organization`,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
        } as InsertAccessAuditLog);
        return res.status(403).json({ message: 'Organization required' });
      }

      const memberships = await clerkClient.organizations.getOrganizationMembershipList({
        organizationId: user.organizationId,
        userId: [userId]
      });
      const isAdmin = memberships.data.some(m => m.role === 'org:admin');
      if (!isAdmin) {
        await storage.createAccessAuditLog({
          userId,
          action: `${req.method} ${req.path}`,
          resource: req.path,
          organizationId: user.organizationId,
          success: false,
          reason: `Not an organization admin`,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
        } as InsertAccessAuditLog);
        return res.status(403).json({ message: 'Admin role required' });
      }

      await storage.createAccessAuditLog({
        userId,
        action: `${req.method} ${req.path}`,
        resource: req.path,
        organizationId: user.organizationId,
        success: true,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      } as InsertAccessAuditLog);

      req.currentUser = user;
      next();
    } catch (error) {
      console.error('Org admin check error:', error);
      res.status(500).json({ message: 'Authorization check failed' });
    }
  };
};
