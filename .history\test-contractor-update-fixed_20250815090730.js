import path from 'path';

// We need to use the compiled JavaScript version, not the TypeScript source
async function testUpdate() {
  try {
    console.log('🧪 Testing contractor update directly...');
    
    // Import the compiled storage module
    const { storage } = await import('./dist/index.js');
    
    // First, let's see if we can find any existing contractors
    const contractors = await storage.getContractors();
    console.log('✅ Found', contractors.length, 'contractors in database');
    
    if (contractors.length > 0) {
      const testContractor = contractors[0];
      console.log('🔍 Testing update on contractor:', testContractor.id, testContractor.companyName);
      
      const result = await storage.updateContractor(testContractor.id, {
        companyName: 'Test Update - ' + Date.now()
      });
      
      if (result) {
        console.log('✅ Update successful:', result.companyName);
      } else {
        console.log('❌ Update returned null/undefined');
      }
    } else {
      console.log('ℹ️ No contractors found to test with');
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
  process.exit(0);
}

testUpdate();
