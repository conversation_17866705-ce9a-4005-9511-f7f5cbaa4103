#!/usr/bin/env node

/**
 * Simplified Query Performance Testing Script
 * Tests database performance using raw SQL queries
 */

import { config } from 'dotenv';
import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';

// Load environment variables
config();

// Configure Neon WebSocket
neonConfig.webSocketConstructor = ws;

// Database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

const pool = new Pool({ connectionString });

// Test queries that should benefit from new indexes
const testQueries = [
  {
    name: 'API Key Usage UPSERT (high frequency)',
    description: 'Tests the new UPSERT pattern for API key tracking',
    query: `
      INSERT INTO api_key_usage (api_key_id, endpoint, method, request_count, request_date, last_request_at)
      VALUES ('test-api-key-id', '/api/test', 'GET', 1, CURRENT_DATE, NOW())
      ON CONFLICT (api_key_id, request_date, endpoint, method) 
      DO UPDATE SET 
        request_count = api_key_usage.request_count + 1,
        last_request_at = NOW()
    `
  },
  {
    name: 'Bids by RFQ (multi-tenant lookup)',
    description: 'Tests bid lookups that should use new indexes',
    query: `
      SELECT b.id, b.rfq_id, b.contractor_id, b.status, b.total_amount
      FROM bids b
      WHERE b.rfq_id IN (
        SELECT id FROM rfqs ORDER BY created_at DESC LIMIT 5
      )
      LIMIT 20
    `
  },
  {
    name: 'Unread Notifications Count',
    description: 'Tests notification bell count queries',
    query: `
      SELECT COUNT(*) as unread_count
      FROM notifications 
      WHERE read_at IS NULL 
        AND created_at >= NOW() - INTERVAL '30 days'
    `
  },
  {
    name: 'RFQ Distribution Lookup',
    description: 'Tests contractor RFQ distribution queries',
    query: `
      SELECT rd.id, rd.rfq_id, rd.contractor_id, rd.viewed_at
      FROM rfq_distribution rd
      WHERE rd.viewed_at IS NULL
      LIMIT 10
    `
  },
  {
    name: 'Active RFQs Count',
    description: 'Tests dashboard active RFQ queries',
    query: `
      SELECT COUNT(*) as active_count
      FROM rfqs 
      WHERE status = 'Active'
    `
  }
];

/**
 * Run EXPLAIN ANALYZE on a query and return performance metrics
 */
async function analyzeQuery(name, description, queryText) {
  console.log(`\n📊 Testing: ${name}`);
  console.log(`   ${description}`);
  console.log('   ' + '─'.repeat(60));
  
  try {
    // Run EXPLAIN ANALYZE
    const client = await pool.connect();
    const result = await client.query(`EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${queryText}`);
    const plan = result.rows[0]['QUERY PLAN'][0];
    
    // Extract key metrics
    const executionTime = plan['Execution Time'];
    const totalCost = plan.Plan['Total Cost'];
    const actualRows = plan.Plan['Actual Rows'];
    const planningTime = plan['Planning Time'];
    
    // Check for sequential scans (bad for performance)
    const planText = JSON.stringify(plan);
    const hasSeqScan = planText.includes('Seq Scan');
    const hasIndexScan = planText.includes('Index Scan') || planText.includes('Index Only Scan');
    
    console.log(`   ⏱️  Execution Time: ${executionTime.toFixed(2)}ms`);
    console.log(`   💰 Total Cost: ${totalCost.toFixed(2)}`);
    console.log(`   📊 Rows Returned: ${actualRows}`);
    console.log(`   🧠 Planning Time: ${planningTime.toFixed(2)}ms`);
    
    if (hasSeqScan) {
      console.log(`   ⚠️  SEQUENTIAL SCAN detected - may need index`);
    }
    if (hasIndexScan) {
      console.log(`   ✅ Index usage detected`);
    }
    
    // Performance rating
    let rating = '🟢 Excellent';
    if (executionTime > 50) rating = '🟡 Good';
    if (executionTime > 200) rating = '🟠 Fair';
    if (executionTime > 500) rating = '🔴 Poor';
    if (hasSeqScan && actualRows > 1000) rating = '🔴 Poor (Seq Scan)';
    
    console.log(`   📈 Performance: ${rating}`);
    
    client.release();
    
    return {
      name,
      executionTime,
      totalCost,
      actualRows,
      planningTime,
      hasSeqScan,
      hasIndexScan,
      rating
    };
    
  } catch (error) {
    console.log(`   ❌ Query failed: ${error.message}`);
    return {
      name,
      error: error.message,
      rating: '❌ Failed'
    };
  }
}

/**
 * Check if indexes exist and their usage statistics
 */
async function checkIndexes() {
  console.log('\n🔍 Checking Index Status');
  console.log('═'.repeat(70));
  
  const indexChecks = [
    'idx_bids_rfq_id',
    'idx_bids_contractor_id',
    'idx_bids_rfq_contractor',
    'idx_api_key_usage_unique_daily',
    'idx_notifications_user_unread',
    'idx_rfq_distribution_contractor_unviewed',
    'idx_rfqs_active_status'
  ];
  
  try {
    const client = await pool.connect();
    
    for (const indexName of indexChecks) {
      try {
        const result = await client.query(`
          SELECT 
            schemaname, 
            tablename, 
            indexname,
            COALESCE(idx_scan, 0) as idx_scan,
            COALESCE(idx_tup_read, 0) as idx_tup_read
          FROM pg_stat_user_indexes 
          WHERE indexname = $1
        `, [indexName]);
        
        if (result.rows.length > 0) {
          const idx = result.rows[0];
          console.log(`   ✅ ${indexName}: ${idx.idx_scan} scans, ${idx.idx_tup_read} rows`);
        } else {
          console.log(`   ❌ ${indexName}: NOT FOUND`);
        }
      } catch (error) {
        console.log(`   ⚠️  ${indexName}: Error checking - ${error.message}`);
      }
    }
    
    client.release();
  } catch (error) {
    console.log(`   ❌ Could not check indexes: ${error.message}`);
  }
}

/**
 * Get basic database statistics
 */
async function getDatabaseStats() {
  console.log('\n📈 Database Statistics');
  console.log('═'.repeat(70));
  
  try {
    const client = await pool.connect();
    
    // Check if tables exist and get basic stats
    const tables = ['rfqs', 'bids', 'bid_line_items', 'api_key_usage', 'notifications'];
    
    for (const tableName of tables) {
      try {
        const result = await client.query(`
          SELECT 
            COUNT(*) as row_count
          FROM ${tableName}
        `);
        
        const rowCount = result.rows[0].row_count;
        console.log(`   📋 ${tableName}: ${rowCount} rows`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: Table not found or error - ${error.message}`);
      }
    }
    
    client.release();
  } catch (error) {
    console.log(`   ❌ Could not fetch database stats: ${error.message}`);
  }
}

/**
 * Main test execution
 */
async function runPerformanceTests() {
  console.log('🚀 Bidaible Database Performance Test Suite');
  console.log('═'.repeat(70));
  console.log(`📅 Started at: ${new Date().toISOString()}`);
  
  // Check database connection
  try {
    const client = await pool.connect();
    await client.query('SELECT 1');
    client.release();
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
  
  // Get database stats
  await getDatabaseStats();
  
  // Check indexes
  await checkIndexes();
  
  // Run query tests
  console.log('\n🧪 Query Performance Tests');
  console.log('═'.repeat(70));
  
  const results = [];
  for (const test of testQueries) {
    const result = await analyzeQuery(test.name, test.description, test.query);
    results.push(result);
  }
  
  // Summary report
  console.log('\n📋 Performance Summary');
  console.log('═'.repeat(70));
  
  const successful = results.filter(r => !r.error);
  const failed = results.filter(r => r.error);
  const withSeqScans = successful.filter(r => r.hasSeqScan);
  
  if (successful.length > 0) {
    const avgExecutionTime = successful.reduce((sum, r) => sum + r.executionTime, 0) / successful.length;
    console.log(`⏱️  Average execution time: ${avgExecutionTime.toFixed(2)}ms`);
  }
  
  console.log(`✅ Successful queries: ${successful.length}/${results.length}`);
  console.log(`⚠️  Queries with sequential scans: ${withSeqScans.length}`);
  
  if (failed.length > 0) {
    console.log(`❌ Failed queries: ${failed.length}`);
    failed.forEach(f => console.log(`   - ${f.name}: ${f.error}`));
  }
  
  if (withSeqScans.length > 0) {
    console.log(`\n⚠️  Queries needing index optimization:`);
    withSeqScans.forEach(q => console.log(`   - ${q.name} (${q.executionTime.toFixed(2)}ms)`));
  }
  
  console.log('\n🎯 Next Steps:');
  if (withSeqScans.length > 0) {
    console.log('   1. Deploy the missing indexes from migrations/0012_add_missing_indexes.sql');
  }
  if (successful.length > 0) {
    const maxTime = Math.max(...successful.map(r => r.executionTime));
    if (maxTime > 100) {
      console.log('   2. Consider adding Redis caching for frequently accessed data');
    }
  }
  console.log('   3. Run: npm run db:push to apply the new indexes');
  console.log('   4. Re-run this test after deploying indexes to measure improvements');
  
  console.log(`\n🏁 Test completed at: ${new Date().toISOString()}`);
}

// Handle cleanup on exit
process.on('SIGINT', async () => {
  console.log('\n🛑 Test interrupted, cleaning up...');
  await pool.end();
  process.exit(0);
});

// Run the tests
runPerformanceTests()
  .catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  })
  .finally(async () => {
    await pool.end();
  });
