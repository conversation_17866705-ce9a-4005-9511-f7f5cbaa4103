/**
 * Scheduled Notification Processor
 * Processes pending scheduled notifications and sends them
 */

import { storage } from '../storage';
import { NotificationService } from './notificationService';
import logger from '../utils/logger';

export class ScheduledNotificationProcessor {
  private processingInterval?: NodeJS.Timeout;
  private readonly intervalMs = 60000; // Check every minute

  /**
   * Start the scheduled notification processor
   */
  start() {
    if (this.processingInterval) {
      logger.warn('ScheduledNotificationProcessor already running');
      return;
    }

    logger.info('Starting ScheduledNotificationProcessor');
    
    // Process immediately on start
    this.processScheduledNotifications();
    
    // Then process every minute
    this.processingInterval = setInterval(() => {
      this.processScheduledNotifications();
    }, this.intervalMs);
  }

  /**
   * Stop the scheduled notification processor
   */
  stop() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
      logger.info('ScheduledNotificationProcessor stopped');
    }
  }

  /**
   * Process all pending scheduled notifications
   */
  private async processScheduledNotifications() {
    try {
      const pendingNotifications = await storage.getPendingScheduledNotifications();
      
      if (pendingNotifications.length === 0) {
        return; // No notifications to process
      }

      logger.info(`Processing ${pendingNotifications.length} scheduled notifications`);

      for (const scheduledNotification of pendingNotifications) {
        await this.processNotification(scheduledNotification);
      }
    } catch (error) {
      console.error('Error processing scheduled notifications:', error);
    }
  }

  /**
   * Process a single scheduled notification
   */
  private async processNotification(scheduledNotification: any) {
    try {
      const { id, notificationType, userId, payload } = scheduledNotification;

      logger.info(`Processing scheduled notification ${id} of type ${notificationType}`);

      // Handle different notification types
      switch (notificationType) {
        case NotificationService.NotificationTypes.RFQ_DEADLINE_REMINDER:
          await NotificationService.createRfqDeadlineReminderNotification({
            userId: userId as string,
            rfqId: payload?.rfqId as string,
            projectName: payload?.projectName as string,
            deadlineDate: payload?.deadlineDate as string,
            reminderType: payload?.reminderType as string
          });
          break;
        
        default:
          logger.warn(`Unknown scheduled notification type: ${notificationType}`);
          break;
      }

      // Mark the scheduled notification as sent
      await this.markNotificationSent(id as string);
      
      logger.info(`Successfully processed scheduled notification ${id}`);
      
    } catch (error) {
      console.error(`Error processing scheduled notification ${scheduledNotification.id}:`, error);
      await this.markNotificationFailed(scheduledNotification.id as string);
    }
  }

  /**
   * Mark a scheduled notification as sent
   */
  private async markNotificationSent(notificationId: string) {
    await storage.updateScheduledNotificationStatus(notificationId, 'sent');
  }

  /**
   * Mark a scheduled notification as failed
   */
  private async markNotificationFailed(notificationId: string) {
    await storage.updateScheduledNotificationStatus(notificationId, 'failed');
  }
}

export const scheduledNotificationProcessor = new ScheduledNotificationProcessor();
