# Debug Archive

This folder contains debugging and fix scripts used during development but no longer actively needed.

## Archived Files

### Fix Scripts (Database Repairs)
- `debug-user-context.cjs` - User authentication context analysis (August 2025)
- `fix-test-rfq-user.cjs` - Database script to associate test RFQs with real users
- `fix-user-organization.cjs` - Organization sync repair between Clerk and database

### Purpose
These scripts were created to resolve specific issues during development:
1. **User Context Issues** - Test RFQs created with mock users not showing in UI
2. **Organization Sync** - Clerk organization not syncing with database
3. **Database Repairs** - Updating ownership of test data

### Usage Note
Keep these files for reference in case similar issues occur. Most functionality is now covered by the organized test suite in `tests/` directory.

---
*Archived: August 15, 2025*
