import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useToast } from "@/hooks/use-toast";
import { Bug, Lightbulb, Send } from "lucide-react";

interface UserFeedbackModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UserFeedbackModal({ open, onOpenChange }: UserFeedbackModalProps) {
  const [feedbackType, setFeedbackType] = useState<"bug" | "suggestion" | "">("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!feedbackType || !message.trim()) {
      toast({
        title: "Please fill in all fields",
        description: "Both feedback type and message are required.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/user-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: feedbackType,
          message: message.trim(),
          status: 'open',
          priority: 'medium',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit feedback');
      }
      
      toast({
        title: "Feedback submitted successfully",
        description: "Thank you for your feedback! We'll review it and get back to you if needed.",
      });
      
      // Reset form
      setFeedbackType("");
      setMessage("");
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast({
        title: "Error submitting feedback",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFeedbackType("");
    setMessage("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>User Feedback</DialogTitle>
          <DialogDescription>
            Help us improve Bidaible by sharing your feedback. We value your input!
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label className="text-sm font-medium">Feedback Type</Label>
            <RadioGroup 
              value={feedbackType} 
              onValueChange={(value) => setFeedbackType(value as "bug" | "suggestion")}
              className="mt-2"
            >
              <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted/50">
                <RadioGroupItem value="bug" id="bug" />
                <Bug className="h-4 w-4 text-red-500" />
                <Label htmlFor="bug" className="cursor-pointer flex-1">
                  <div className="font-medium">Bug Report</div>
                  <div className="text-sm text-muted-foreground">
                    Report an issue or problem you encountered
                  </div>
                </Label>
              </div>
              
              <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted/50">
                <RadioGroupItem value="suggestion" id="suggestion" />
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                <Label htmlFor="suggestion" className="cursor-pointer flex-1">
                  <div className="font-medium">Suggestion</div>
                  <div className="text-sm text-muted-foreground">
                    Share ideas for improvements or new features
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </div>
          
          <div>
            <Label htmlFor="message" className="text-sm font-medium">
              Message
            </Label>
            <Textarea
              id="message"
              placeholder={
                feedbackType === "bug" 
                  ? "Please describe the bug, including steps to reproduce it..."
                  : feedbackType === "suggestion"
                  ? "Please describe your suggestion and how it would improve your experience..."
                  : "Please describe your feedback..."
              }
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="mt-2"
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Submit Feedback
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}