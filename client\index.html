<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>Bidaible</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Lora:ital,wght@0,400..700;1,400..700&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="root">
      <div id="fallback-loading" style="display: flex; align-items: center; justify-content: center; min-height: 100vh; font-family: system-ui; text-align: center;">
        <div>
          <div style="width: 32px; height: 32px; background: #f97316; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px;">
            <span style="color: white; font-weight: bold; font-size: 18px;">B</span>
          </div>
          <p style="color: #6b7280; margin: 0;">Loading Bidaible...</p>
          <p style="color: #9ca3af; font-size: 14px; margin: 8px 0 0;">Thanks for using Bidaible. Let us know how we can improve.</p>
        </div>
      </div>
    </div>
    <script>
      // Simple detection script that runs before React
      console.log("🔥 HTML loaded, checking environment...");
      console.log("🌍 Current URL:", window.location.href);
      console.log("🔍 User Agent:", navigator.userAgent.substring(0, 100));
      
      // Listen for CSP violations
      document.addEventListener('securitypolicyviolation', function(e) {
        console.error("🚨 CSP Violation:", {
          directive: e.violatedDirective,
          policy: e.originalPolicy,
          blockedURI: e.blockedURI,
          lineNumber: e.lineNumber,
          sourceFile: e.sourceFile
        });
      });
      
      // Hide fallback when React takes over
      window.hideFallback = function() {
        const fallback = document.getElementById('fallback-loading');
        if (fallback) fallback.style.display = 'none';
      };
      
      // Show error if React doesn't load within 10 seconds
      setTimeout(function() {
        const fallback = document.getElementById('fallback-loading');
        if (fallback && fallback.style.display !== 'none') {
          fallback.innerHTML = `
            <div>
              <div style="width: 32px; height: 32px; background: #ef4444; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px;">
                <span style="color: white; font-weight: bold; font-size: 18px;">!</span>
              </div>
              <h2 style="color: #ef4444; margin: 0 0 16px;">Loading Error</h2>
              <p style="color: #6b7280; margin: 0 0 16px;">The application failed to load. This could be due to:</p>
              <ul style="color: #6b7280; text-align: left; max-width: 400px;">
                <li>Network connectivity issues</li>
                <li>JavaScript execution blocked</li>
                <li>Browser compatibility issues</li>
                <li>Server-side problems</li>
              </ul>
              <button onclick="window.location.reload()" style="background: #f97316; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; margin-top: 16px;">
                Reload Page
              </button>
            </div>
          `;
        }
      }, 10000);
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>