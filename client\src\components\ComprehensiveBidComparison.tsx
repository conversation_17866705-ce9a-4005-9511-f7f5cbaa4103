import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BidAnalysisCard } from '@/components/BidAnalysisCard';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Loader2, Users, Calculator, Award, Download } from 'lucide-react';

interface BidAnalysisData {
  bidId: string;
  contractorId: string;
  contractorInfo: {
    companyName: string;
    primaryContactName: string;
    yearsInBusiness: number;
    licenseNumber?: string;
    generalLiability?: string;
    bondingSingle?: string;
    bondingAggregate?: string;
  };
  bidAmount: number;
  timeline: string;
  status: string;
  lineItems: Array<{
    id: string;
    costCode: string;
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    unitOfMeasure: string;
    category: string;
  }>;
  proposalText?: string;
  notes?: string;
  includedItems: string[];
  excludedItems: string[];
}

interface ComprehensiveBidComparisonProps {
  rfqId: string;
}

// Function to process comprehensive bid analysis data into BidAnalysisData format
const processBidData = (rawData: any[]): BidAnalysisData[] => {
  console.log('🎯 Processing comprehensive bid data:', rawData);
  
  return rawData.map((bid) => {
    // Process line items to match expected format
    const processedLineItems = (bid.lineItems || []).map((item: any) => ({
      id: item.id || Math.random().toString(),
      costCode: item.costCode || 'N/A',
      description: item.description || 'No description',
      quantity: parseFloat(item.quantity || '0'),
      unitPrice: parseFloat(item.unitPrice || '0'),
      totalPrice: parseFloat(item.totalPrice || '0'),
      unitOfMeasure: item.unitOfMeasure || 'EA',
      category: item.category || 'Other',
    }));

    // Calculate total from line items (more accurate than bid amount which might be null)
    const calculatedTotal = processedLineItems.reduce((sum, item) => sum + item.totalPrice, 0);
    
    console.log(`📊 Bid ${bid.companyName}: ${processedLineItems.length} line items, total: $${calculatedTotal}`);

    return {
      bidId: bid.bidId,
      contractorId: bid.contractorId,
      contractorInfo: {
        companyName: bid.companyName || 'Unknown Company',
        primaryContactName: bid.contractorName || 'Unknown Contact',
        yearsInBusiness: bid.contractorInfo?.yearsInBusiness || 15,
        licenseNumber: bid.contractorInfo?.licenseNumber,
        generalLiability: bid.contractorInfo?.generalLiability,
        bondingSingle: bid.contractorInfo?.bondingSingle,
        bondingAggregate: bid.contractorInfo?.bondingAggregate,
      },
      bidAmount: calculatedTotal || parseFloat(bid.bidAmount) || 0,
      timeline: bid.timeline || 'Not specified',
      status: bid.status || 'submitted',
      lineItems: processedLineItems,
      proposalText: bid.proposalText,
      notes: bid.notes,
      includedItems: [], // Will be extracted by BidAnalysisCard
      excludedItems: [], // Will be extracted by BidAnalysisCard
    };
  });
};

export function ComprehensiveBidComparison({ rfqId }: ComprehensiveBidComparisonProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch comprehensive bid analysis data (dedicated endpoint)
  const { data: rawBidData = [], isLoading } = useQuery<any[]>({
    queryKey: ['/api/rfqs', rfqId, 'comprehensive-bid-analysis'],
  });

  // Process the raw data into our card format
  const bidAnalysisData = processBidData(rawBidData);

  // Mutation to accept/reject bids
  const acceptBidMutation = useMutation({
    mutationFn: async ({ bidId, action }: { bidId: string; action: 'accept' | 'reject' }) => {
      const status = action === 'accept' ? 'accepted' : 'rejected';
      return apiRequest('PATCH', `/api/bids/${bidId}`, {
        status,
        notes: `Bid ${status} via comprehensive bid comparison`
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/rfqs', rfqId, 'comprehensive-bid-analysis'] });
      queryClient.invalidateQueries({ queryKey: ['/api/rfqs', rfqId, 'category-analysis'] });
      queryClient.invalidateQueries({ queryKey: ['/api/rfqs', rfqId, 'bids'] });
      toast({ 
        title: "Success", 
        description: "Bid status updated successfully" 
      });
    },
    onError: () => {
      toast({ 
        title: "Error", 
        description: "Failed to update bid status",
        variant: "destructive"
      });
    }
  });

  const handleAcceptBid = (bidId: string) => {
    acceptBidMutation.mutate({ bidId, action: 'accept' });
  };

  const handleRejectBid = (bidId: string) => {
    acceptBidMutation.mutate({ bidId, action: 'reject' });
  };

  // Calculate summary statistics
  const totalBids = bidAnalysisData.length;
  const acceptedBids = bidAnalysisData.filter(bid => bid.status === 'accepted' || bid.status === 'accept').length;
  const averageBid = totalBids > 0 ? bidAnalysisData.reduce((sum, bid) => sum + (bid.bidAmount || 0), 0) / totalBids : 0;
  const lowestBid = totalBids > 0 ? Math.min(...bidAnalysisData.map(bid => bid.bidAmount || Infinity)) : 0;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading comprehensive bid analysis...</span>
      </div>
    );
  }

  if (bidAnalysisData.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">No Bids Available</h3>
          <p className="text-muted-foreground">
            No bid responses have been submitted for this RFQ yet.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Total Bids</p>
                <p className="text-2xl font-bold">{totalBids}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Award className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">Accepted</p>
                <p className="text-2xl font-bold">{acceptedBids}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calculator className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Average Bid</p>
                <p className="text-2xl font-bold">
                  {new Intl.NumberFormat('en-US', { 
                    style: 'currency', 
                    currency: 'USD',
                    minimumFractionDigits: 0
                  }).format(averageBid)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calculator className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Lowest Bid</p>
                <p className="text-2xl font-bold">
                  {new Intl.NumberFormat('en-US', { 
                    style: 'currency', 
                    currency: 'USD',
                    minimumFractionDigits: 0
                  }).format(lowestBid)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Comprehensive Bid Comparison Cards */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold">Comprehensive Bid Analysis</h3>
            <p className="text-sm text-muted-foreground">
              Side-by-side comparison of all contractor proposals with detailed cost breakdowns
            </p>
          </div>
          <div className="flex gap-2">
            {acceptedBids > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={async () => {
                  try {
                    const { exportAcceptedBidDetailsCSV } = await import("@/lib/reportUtils");
                    await exportAcceptedBidDetailsCSV(rfqId);
                    toast({ 
                      title: "Success", 
                      description: "Accepted bid details exported successfully" 
                    });
                  } catch (error) {
                    console.error('Export failed:', error);
                    toast({ 
                      title: "Export failed", 
                      description: "Please try again",
                      variant: "destructive" 
                    });
                  }
                }}
                className="bg-green-50 hover:bg-green-100 border-green-200"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Accepted Bids CSV
              </Button>
            )}
          </div>
        </div>

        {/* Side-by-side bid cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {bidAnalysisData.map((bid) => (
            <BidAnalysisCard
              key={`bid-card-${bid.bidId}`}
              bidData={bid}
              onAccept={handleAcceptBid}
              onReject={handleRejectBid}
              isSelected={bid.status === 'accepted' || bid.status === 'accept'}
            />
          ))}
        </div>
      </div>

      {acceptBidMutation.isPending && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background p-6 rounded-lg flex items-center gap-3">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Updating bid status...</span>
          </div>
        </div>
      )}
    </div>
  );
}