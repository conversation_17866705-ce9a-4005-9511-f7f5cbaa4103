
CREATE TABLE "waitlist" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar UNIQUE NOT NULL,
	"company_name" varchar,
	"first_name" varchar,
	"last_name" varchar,
	"job_title" varchar,
	"source" varchar DEFAULT 'landing_page',
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE INDEX "IDX_waitlist_email" ON "waitlist" USING btree ("email");--> statement-breakpoint
CREATE INDEX "IDX_waitlist_created_at" ON "waitlist" USING btree ("created_at");
