[{"file": "server\\routes.ts", "method": "POST", "path": "/api/admin/api-keys/rotate"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/api-keys/rotation-needed"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/api-keys/security-stats"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/audit/access"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/audit/business"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/admin/audit/business/backfill"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/audit/roles"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/admin/backup/create"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/backup/stats"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/admin/cache/clear"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/performance/cache"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/performance/files"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/security/audit"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/admin/security/invalidate-sessions"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/admin/users"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/admin/users/:userId/role"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/auth/api-keys"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/auth/api-keys"}, {"file": "server\\routes.ts", "method": "DELETE", "path": "/api/auth/api-keys/:id"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/auth/api-keys/:id"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/auth/api-keys/:id/stats"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/auth/user"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/auth/user"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/bids/:bidId"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/bids/:bidId"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/bids/:bidId/action"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/bids/:bidId/analyze"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/bids/:bidId/documents"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/bids/:bidId/structured-data"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/clerk/accept-terms"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/clerk/user"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/contractors"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/contractors"}, {"file": "server\\routes.ts", "method": "DELETE", "path": "/api/contractors/:contractorId/favorite"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/contractors/:contractorId/favorite"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/contractors/:id"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/contractors/bids"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/contractors/favorites"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/contractors/performance-stats"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/contractors/profile"}, {"file": "server\\routes.ts", "method": "PUT", "path": "/api/contractors/profile"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/contractors/rfqs"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/contractors/rfqs/all"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/cost-codes"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/cost-codes/stats"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/dashboard/stats"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/dashboard/stats"}, {"file": "server\\index.ts", "method": "GET", "path": "/api/debug/assets"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/files/:documentId"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/forecast/materials"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/forecast/materials"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/forecast/search"}, {"file": "server\\routes\\health.ts", "method": "GET", "path": "/api/health"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/integrations/export/rfqs"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/integrations/project-budget/:rfqId/:bidId"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/integrations/quickbooks"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/integrations/sage"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/logout"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/notifications"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/notifications/:id/read"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/notifications/preferences"}, {"file": "server\\routes.ts", "method": "PUT", "path": "/api/notifications/preferences/:type"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/notifications/send-custom-email"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/notifications/test"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/notifications/unread"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/organizations"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/organizations/:orgId/invitations"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/organizations/:orgId/invitations"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/organizations/:orgId/users"}, {"file": "server\\routes.ts", "method": "DELETE", "path": "/api/organizations/:orgId/users/:targetUserId"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/organizations/:orgId/users/:targetUserId/role"}, {"file": "server\\routes\\processingStats.ts", "method": "GET", "path": "/api/processing/stats"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/processing/stats"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/reports/email"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/rfqs"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:id"}, {"file": "server\\routes.ts", "method": "PUT", "path": "/api/rfqs/:id"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:id/documents"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:rfqId/bid-comparison"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:rfqId/bid-comparison/export"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:rfqId/bids"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/rfqs/:rfqId/bids"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:rfqId/bids/analysis"}, {"file": "server\\routes.ts", "method": "PUT", "path": "/api/rfqs/:rfqId/buffer"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:rfqId/category-analysis"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:rfqId/comprehensive-bid-analysis"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/rfqs/:rfqId/decline"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/rfqs/:rfqId/decline"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/rfqs/:rfqId/distribute"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/rfqs/:rfqId/distributions"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/rfqs/:rfqId/generate-master-summary"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/rfqs/:rfqId/view"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/rfqs/process-documents"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/test/api-key"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/test/contractor"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/test/distribute"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/test/org"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/test/rfq"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/test/rfq-document"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/test/rfqs"}, {"file": "server\\routes\\testUnifiedExtractor.ts", "method": "GET", "path": "/api/test/unified-pdf-extractor"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/test/user"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/upload/progress/:sessionId"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/upload/start-session"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/user-feedback"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/user-feedback"}, {"file": "server\\routes.ts", "method": "PATCH", "path": "/api/user-feedback/:id"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/user-feedback/stats"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/user/activity"}, {"file": "server\\routes.ts", "method": "POST", "path": "/api/waitlist"}, {"file": "server\\routes.ts", "method": "GET", "path": "/api/waitlist/count"}, {"file": "server\\routes.ts", "method": "GET", "path": "/attached_assets/:fileName"}, {"file": "server\\routes\\analytics.ts", "method": "GET", "path": "/bid-analysis/:rfqId"}, {"file": "server\\routes\\analytics.ts", "method": "GET", "path": "/competitive-intelligence/:rfqId"}, {"file": "server\\routes\\analytics.ts", "method": "GET", "path": "/default-criteria"}, {"file": "server\\routes\\analytics.ts", "method": "POST", "path": "/evaluation-scores/:rfqId"}, {"file": "server\\routes\\analytics.ts", "method": "GET", "path": "/market-intelligence/:region/:trade"}, {"file": "server\\routes\\analytics.ts", "method": "GET", "path": "/predictive-analytics/:rfqId"}, {"file": "server\\routes\\analytics.ts", "method": "GET", "path": "/risk-assessment/:bidId"}, {"file": "server\\index.ts", "method": "GET", "path": "env"}]