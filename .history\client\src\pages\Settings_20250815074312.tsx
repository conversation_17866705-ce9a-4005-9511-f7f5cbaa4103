import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useAuth } from "@/hooks/useAuth";
import { useOrganization, useOrganizationList, OrganizationProfile, UserProfile, CreateOrganization } from "@clerk/clerk-react";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import OnboardingOrganizationSetup from "@/components/OnboardingOrganizationSetup";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Save, User, Key, Bell, Activity, Users, Building, ChevronDown, ChevronRight } from "lucide-react";
import { useUserRole } from "@/hooks/useUserRole";

// Form schemas
const contractorFormSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  companyWebsite: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  legalStructure: z.string().optional(),
  taxId: z.string().optional(),
  dba: z.string().optional(),
  primaryAddress: z.string().optional(),
  mailingAddress: z.string().optional(),
  primaryContactName: z.string().optional(),
  primaryContactEmail: z.string().email().optional().or(z.literal("")),
  primaryContactPhone: z.string().optional(),
  primaryContactTitle: z.string().optional(),
  tradeTypes: z.array(z.string()).min(1, "At least one trade type is required"),
  unionStatus: z.string().optional(),
  unionAffiliations: z.string().optional(),
  certifications: z.array(z.string()).optional(),
  serviceAreas: z.string().optional(),
  licenseNumber: z.string().optional(),
  licenseState: z.string().optional(),
  licenseExpiration: z.string().optional(),
  generalLiability: z.string().optional(),
  workersComp: z.string().optional(),
  autoInsurance: z.string().optional(),
  bondingSingle: z.number().optional(),
  bondingAggregate: z.number().optional(),
  emr: z.number().optional(),
  bankReference: z.string().optional(),
  suretyReference: z.string().optional(),
  creditRating: z.string().optional(),
  paymentTerms: z.string().optional(),
  litigationHistory: z.string().optional(),
  projectReferences: z.string().optional(),
  yearsInBusiness: z.number().optional(),
  specializations: z.array(z.string()).optional(),
  awards: z.string().optional(),
  environmentalPrograms: z.string().optional(),
  workforceSize: z.number().optional(),
  workforceBreakdown: z.string().optional(),
  equipment: z.string().optional(),
  availability: z.string().optional(),
  keywordTags: z.array(z.string()).optional(),
  preferredProjectTypes: z.array(z.string()).optional(),
});

// Trade types and other options
const tradeOptions = [
  { value: "General Contractor", label: "General Contractor" },
  { value: "electrical", label: "Electrical" },
  { value: "plumbing", label: "Plumbing" },
  { value: "hvac", label: "HVAC" },
  { value: "concrete", label: "Concrete" },
  { value: "sitework", label: "Site Work/Excavation" },
  { value: "masonry", label: "Masonry" },
  { value: "structural_steel", label: "Structural Steel" },
  { value: "carpentry", label: "Carpentry" },
  { value: "roofing", label: "Roofing" },
  { value: "waterproofing", label: "Waterproofing" },
  { value: "insulation", label: "Insulation" },
  { value: "drywall", label: "Drywall" },
  { value: "flooring", label: "Flooring" },
  { value: "painting", label: "Painting" },
  { value: "fire_protection", label: "Fire Protection" },
  { value: "security_systems", label: "Security Systems" },
  { value: "landscaping", label: "Landscaping" },
  { value: "asphalt_paving", label: "Asphalt/Paving" },
  { value: "surveying", label: "Surveying" },
  { value: "environmental", label: "Environmental Services" },
  { value: "demolition", label: "Demolition" },
  { value: "utilities", label: "Utilities" },
  { value: "telecommunications", label: "Telecommunications" },
  { value: "glazing", label: "Glazing/Windows" },
  { value: "metal_fabrication", label: "Metal Fabrication" },
  { value: "elevator", label: "Elevator/Escalator" },
  { value: "architectural_millwork", label: "Architectural Millwork" },
];

const certificationOptions = [
  { value: "mbe", label: "MBE (Minority Business Enterprise)" },
  { value: "wbe", label: "WBE (Women Business Enterprise)" },
  { value: "vbe", label: "VBE (Veteran Business Enterprise)" },
  { value: "dbe", label: "DBE (Disadvantaged Business Enterprise)" },
  { value: "sbe", label: "SBE (Small Business Enterprise)" },
];

const specializationOptions = [
  { value: "design_build", label: "Design/Build" },
  { value: "bim", label: "BIM" },
  { value: "prefab", label: "Prefabrication" },
  { value: "sustainable", label: "Sustainable Construction" },
  { value: "heavy_civil", label: "Heavy Civil" },
  { value: "emergency", label: "Emergency Work" },
];

const projectTypeOptions = [
  { value: "residential", label: "Residential" },
  { value: "commercial", label: "Commercial" },
  { value: "infrastructure", label: "Infrastructure" },
  { value: "industrial", label: "Industrial" },
  { value: "institutional", label: "Institutional" },
  { value: "healthcare", label: "Healthcare" },
  { value: "education", label: "Education" },
];

export default function Settings() {
  const { user } = useAuth();
  const { organization, membership, isLoaded } = useOrganization();
  const { userMemberships } = useOrganizationList();
  const { toast } = useToast();
  const { role: userRole } = useUserRole();
  const [location] = useLocation();
  
  // Check if user is in onboarding mode
  const isOnboardingMode = location.includes('onboarding=true');
  
  // State
  const [activeTab, setActiveTab] = useState(isOnboardingMode ? "user-settings" : "user-settings");
  
  // Check permissions
  const isOrgAdmin = membership?.role === 'org:admin';
  const hasOrganizations = userMemberships?.data && userMemberships.data.length > 0;
  const isSuperUserOrAdmin = userRole === 'general_contractor' || userRole === 'contractor';

  // Data queries
  const { data: contractorProfile } = useQuery({
    queryKey: ["/api/contractors/profile"],
    enabled: !!user,
  });

  // Form initialization
  const contractorForm = useForm<z.infer<typeof contractorFormSchema>>({
    resolver: zodResolver(contractorFormSchema),
    defaultValues: {
      companyName: (contractorProfile as any)?.companyName || "",
      tradeTypes: (contractorProfile as any)?.tradeTypes || [],
    },
  });

  // Update form values when contractor profile data loads
  useEffect(() => {
    if (contractorProfile) {
      contractorForm.reset({
        companyName: (contractorProfile as any)?.companyName || "",
        tradeTypes: (contractorProfile as any)?.tradeTypes || [],
      });
    }
  }, [contractorProfile, contractorForm]);

  // Mutations
  const updateContractorMutation = useMutation({
    mutationFn: (data: any) => fetch("/api/contractors/profile", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    }).then(res => res.json()),
    onSuccess: () => {
      toast({ title: "Profile updated successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/contractors/profile"] });
      
      if (isOnboardingMode) {
        // Mark onboarding as complete and redirect to dashboard
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      }
    },
  });

  // Handlers
  const onContractorSubmit = (data: any) => {
    updateContractorMutation.mutate(data);
  };

  if (!isLoaded) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account, profile, and system preferences
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="user-settings">
            <User className="h-4 w-4 mr-2" />
            User Settings
          </TabsTrigger>
          <TabsTrigger value="api-keys">
            <Key className="h-4 w-4 mr-2" />
            API Keys
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          {isSuperUserOrAdmin && (
            <TabsTrigger value="audit-logs">
              <Activity className="h-4 w-4 mr-2" />
              Audit Logs
            </TabsTrigger>
          )}
        </TabsList>

        {/* User Settings Tab - Enhanced with Onboarding */}
        <TabsContent value="user-settings" className="space-y-6">
          {isOnboardingMode && !hasOrganizations && (
            <OnboardingOrganizationSetup 
              onComplete={() => {
                // Refresh user data and continue to profile setup
                queryClient.invalidateQueries({ queryKey: ['/api/auth/user'] });
                toast({
                  title: "Organization Created!",
                  description: "Now let's complete your profile setup.",
                });
              }}
            />
          )}

          {!isOnboardingMode && !hasOrganizations && (
            <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <CardContent className="pt-6">
                <div className="flex items-start gap-4">
                  <Users className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="font-semibold text-blue-900">Create Your Organization</h3>
                    <p className="text-sm text-blue-700 mb-4">
                      Create an organization to access team features and user management.
                    </p>
                    <CreateOrganization 
                      appearance={{
                        elements: {
                          rootBox: "w-full",
                          card: "shadow-none border-0 bg-transparent",
                        }
                      }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* User Profile Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Account
              </CardTitle>
              <CardDescription>
                Manage your personal account settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserProfile 
                routing="hash"
                appearance={{
                  elements: {
                    rootBox: "w-full",
                    card: "shadow-none border border-gray-200 rounded-lg",
                    headerTitle: "text-lg font-semibold",
                    headerSubtitle: "text-sm text-muted-foreground",
                  }
                }}
              />
            </CardContent>
          </Card>

          {/* Contractor Profile Section - Moved from Profile Tab */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {isOnboardingMode && (
                  <div className="flex items-center gap-2 mr-4">
                    <Badge variant="outline">Step 3 of 3</Badge>
                    <div className="h-2 w-2 bg-primary rounded-full animate-pulse" />
                  </div>
                )}
                <Building className="h-5 w-5" />
                Contractor Profile
              </CardTitle>
              <CardDescription>
                {isOnboardingMode 
                  ? "Complete your profile to finish onboarding - Company Name and Trade Types are required"
                  : "Complete your contractor profile to participate in RFQ processes"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...contractorForm}>
                <form onSubmit={contractorForm.handleSubmit(onContractorSubmit)} className="space-y-6">
                  
                  {/* Business Identity & Contact - Required Fields */}
                  <div className={`p-4 rounded-lg ${isOnboardingMode ? 'bg-blue-50 border-l-4 border-l-blue-500' : 'bg-gray-50'}`}>
                    <h3 className="text-lg font-semibold mb-4">Business Identity & Contact</h3>
                    {isOnboardingMode && (
                      <p className="text-sm text-red-600 mb-4">
                        ★ Company Name and Trade Types are required to complete onboarding
                      </p>
                    )}
                    
                    <div className="space-y-4">
                      <FormField
                        control={contractorForm.control}
                        name="companyName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={isOnboardingMode ? "text-red-600 font-semibold" : ""}>
                              Company Name *
                              {isOnboardingMode && <span className="ml-1 text-red-500">★</span>}
                            </FormLabel>
                            <FormControl>
                              <Input 
                                {...field} 
                                placeholder="Enter company name" 
                                className={isOnboardingMode && !field.value ? "border-red-300" : ""}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={contractorForm.control}
                        name="tradeTypes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={isOnboardingMode ? "text-red-600 font-semibold" : ""}>
                              Trade Types *
                              {isOnboardingMode && <span className="ml-1 text-red-500">★</span>}
                            </FormLabel>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                              {tradeOptions.map((trade) => (
                                <div key={trade.value} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={trade.value}
                                    checked={field.value?.includes(trade.value)}
                                    onCheckedChange={(checked) => {
                                      const currentValue = field.value || [];
                                      if (checked) {
                                        field.onChange([...currentValue, trade.value]);
                                      } else {
                                        field.onChange(currentValue.filter((v: string) => v !== trade.value));
                                      }
                                    }}
                                  />
                                  <Label 
                                    htmlFor={trade.value} 
                                    className={`text-sm ${isOnboardingMode && field.value?.length === 0 ? 'text-red-600' : ''}`}
                                  >
                                    {trade.label}
                                  </Label>
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                            {isOnboardingMode && field.value?.length === 0 && (
                              <p className="text-red-500 text-xs">Please select at least one trade type to continue</p>
                            )}
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end pt-6 border-t">
                    <Button 
                      type="submit" 
                      disabled={updateContractorMutation.isPending}
                      className="flex items-center gap-2"
                    >
                      <Save className="h-4 w-4" />
                      {updateContractorMutation.isPending 
                        ? "Saving..." 
                        : isOnboardingMode 
                          ? "Complete Onboarding" 
                          : "Save Profile"
                      }
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API Keys Tab */}
        <TabsContent value="api-keys" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>
                Manage your API keys for programmatic access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-center py-8">
                API Keys functionality available here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-center py-8">
                Notification settings functionality available here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audit Logs Tab */}
        {isSuperUserOrAdmin && (
          <TabsContent value="audit-logs" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Audit Logs</CardTitle>
                <CardDescription>
                  System activity and security audit trail
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-center py-8">
                  Audit logs functionality available here.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
