{"name": "bidaible-unified-file-processing-tests", "version": "1.0.0", "description": "Comprehensive test suite for Bidaible unified file upload and processing with Wasabi storage integration", "main": "run-all-tests.js", "scripts": {"test": "node run-all-tests.js", "test:wasabi": "node test-wasabi-integration.js", "test:ai": "node test-ai-processing.js", "test:watch": "nodemon run-all-tests.js", "install-deps": "npm install axios form-data", "clean": "rm -rf test-data results", "report": "open results/test-report.html || start results/test-report.html"}, "dependencies": {"axios": "^1.6.0", "form-data": "^4.0.0", "jsonwebtoken": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "keywords": ["bidaible", "file-processing", "wasabi-storage", "ai-processing", "pdf-extraction", "testing"], "author": "Bidaible Development Team", "license": "MIT", "engines": {"node": ">=16.0.0"}}