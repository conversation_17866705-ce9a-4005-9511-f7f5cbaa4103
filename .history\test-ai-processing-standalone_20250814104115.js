import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load .env from root directory
config({ path: path.join(__dirname, '.env') });

console.log('🔧 Testing AI Processing with Environment Variables');
console.log('Environment variables loaded:');
console.log('GROQ_API_KEY:', !!process.env.GROQ_API_KEY, process.env.GROQ_API_KEY?.length);
console.log('OPENAI_API_KEY:', !!process.env.OPENAI_API_KEY, process.env.OPENAI_API_KEY?.length);
console.log('GEMINI_API_KEY:', !!process.env.GEMINI_API_KEY, process.env.GEMINI_API_KEY?.length);
console.log('PRIMARY_MODEL:', process.env.PRIMARY_MODEL);

// Test AI processing with a simple text
async function testAIProcessing() {
  try {
    // Import the AI service
    const { processRfqDocumentOptimized } = await import('./server/services/aiOptimizedService.ts');
    
    // Create a test file with some content
    const fs = await import('fs');
    const testContent = `
PROJECT: Test Construction Project
LOCATION: 123 Main Street, Portland, OR
DESCRIPTION: This is a test RFQ for electrical work including wiring and fixtures.
DUE DATE: 2025-09-15
CONTACT: John Smith, <EMAIL>
REQUIREMENTS:
- Licensed electrician required
- Must provide materials list
- 30-day completion timeline
    `;
    
    const testFilePath = './test-rfq.txt';
    fs.writeFileSync(testFilePath, testContent);
    
    console.log('\n🤖 Testing AI processing...');
    const result = await processRfqDocumentOptimized(testFilePath, 'test-rfq.txt');
    
    console.log('\n✅ AI Processing Result:');
    console.log('Success:', result.success);
    console.log('Model used:', result.model);
    console.log('Processing time:', result.processingTime + 'ms');
    console.log('Retry count:', result.retryCount);
    console.log('Extracted text length:', result.extractedText?.length || 0);
    console.log('Structured data keys:', result.structuredData ? Object.keys(result.structuredData) : []);
    
    if (result.structuredData?.aiSummary) {
      console.log('\n📋 AI Summary Preview:');
      console.log(result.structuredData.aiSummary.substring(0, 200) + '...');
    }
    
    // Clean up test file
    fs.unlinkSync(testFilePath);
    
    return result.success;
    
  } catch (error) {
    console.error('\n❌ AI Processing Test Failed:', error.message);
    return false;
  }
}

// Run the test
testAIProcessing().then(success => {
  console.log('\n🎯 Test Result:', success ? 'PASSED' : 'FAILED');
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});
