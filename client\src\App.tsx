import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ThemeProvider";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./styles/toast.css";
import { ClerkProvider, OrganizationSwitcher, SignedIn } from "@clerk/clerk-react";
import { useAuth } from "@/hooks/useAuth";
import TermsProvider from "@/providers/TermsProvider";
import { Navbar } from "@/components/Navbar";
import { Sidebar } from "@/components/Sidebar";
import ErrorBoundary from "@/components/ErrorBoundary";
import NotFound from "@/pages/not-found";
import Landing from "@/pages/Landing";
import Dashboard from "@/pages/Dashboard";
import RFQs from "@/pages/RFQs";
import Contractors from "@/pages/Contractors";
import Settings from "@/pages/Settings";
import HelpSupport from "@/pages/HelpSupport";
import Templates from "@/pages/Templates";
import BidResponses from "@/pages/BidResponses";
import BidManagement from "@/pages/BidManagement";
import RfqBidManagement from "@/pages/RfqBidManagement";
import ContractorRfqs from "@/pages/ContractorRfqs";
import RfqDetail from "@/pages/RfqDetail";
import FavoritesManagement from "@/pages/FavoritesManagement";
import Analytics from "@/pages/Analytics";
import MatIQ from "@/pages/Forecast";
import DocumentLibrary from "@/pages/DocumentLibrary";
import Notifications from "@/pages/Notifications";

import { useEffect } from "react";

function Router() {
  const { isAuthenticated, isLoading } = useAuth();

  return (
    <Switch>
      {isLoading ? (
        <Route path="*">
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-foreground font-bold text-lg">B</span>
              </div>
              <p className="text-muted-foreground">Loading...</p>
            </div>
          </div>
        </Route>
      ) : !isAuthenticated ? (
        <Route path="/" component={Landing} />
      ) : (
        <>
          <Route path="/" component={Dashboard} />
          <Route path="/rfqs" component={RFQs} />
          <Route path="/rfq/:rfqId" component={RfqDetail} />
          <Route path="/rfqs/:rfqId/bids" component={RfqBidManagement} />
          <Route path="/contractors" component={Contractors} />
          <Route path="/contractors/favorites" component={FavoritesManagement} />
          <Route path="/contractors/rfqs" component={ContractorRfqs} />
          <Route path="/bids" component={BidManagement} />
          <Route path="/bid-responses/:rfqId" component={BidResponses} />
          <Route path="/analytics" component={Analytics} />
          <Route path="/forecast" component={MatIQ} />
          <Route path="/documents" component={DocumentLibrary} />
          <Route path="/notifications" component={Notifications} />
          <Route path="/settings" component={Settings} />

          <Route path="/help" component={HelpSupport} />
          <Route path="/templates" component={Templates} />
          <Route component={NotFound} />
        </>
      )}
      <Route component={NotFound} />
    </Switch>
  );
}

function AuthenticatedApp() {
  const { isAuthenticated, isLoading } = useAuth();

  console.log("🔄 AuthenticatedApp: Render state", { isAuthenticated, isLoading });

  if (isLoading) {
    console.log("⏳ AuthenticatedApp: Showing loading state");
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-primary-foreground font-bold text-lg">B</span>
          </div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  console.log("✅ AuthenticatedApp: Rendering main layout");
  return (
    <TermsProvider>
      <div className="h-screen bg-background">
        {isAuthenticated && <Navbar />}
        <div className="h-full flex">
          {isAuthenticated && <Sidebar />}
          <main className={`flex-1 overflow-auto ${isAuthenticated ? 'pt-16 pl-4 pr-6 pb-6' : 'p-0'}`}>
            <Router />
          </main>
        </div>
      </div>
    </TermsProvider>
  );
}

function App() {
console.log("🚀 App component rendering");

// Handle runtime errors globally
useEffect(() => {
const handleError = (event: ErrorEvent) => {
console.error("🚨 Runtime error caught:", event.error);
// Prevent the error from causing a white screen
event.preventDefault();
return true;
};

const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
console.error("🚨 Unhandled promise rejection:", event.reason);
// Prevent unhandled rejections from crashing the app
event.preventDefault();
};

window.addEventListener('error', handleError);
window.addEventListener('unhandledrejection', handleUnhandledRejection);

return () => {
window.removeEventListener('error', handleError);
window.removeEventListener('unhandledrejection', handleUnhandledRejection);
};
}, []);

const publishableKey =
    import.meta.env.VITE_CLERK_PUBLISHABLE_KEY ||
    (typeof window !== 'undefined' && (window as any).__PUBLIC_ENV__?.VITE_CLERK_PUBLISHABLE_KEY) ||
    (typeof window !== 'undefined' && (window as any).__PUBLIC_ENV__?.CLERK_PUBLISHABLE_KEY);

// Guard: render a friendly diagnostics page if the key is missing.
if (!publishableKey) {
return (
<div style={{
minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center',
fontFamily: 'system-ui', padding: 24
}}>
<div style={{ maxWidth: 680, textAlign: 'left' }}>
  <h1 style={{ margin: 0, marginBottom: 8 }}>Configuration required</h1>
    <p style={{ color: '#6b7280', marginTop: 0 }}>The app can’t start because the frontend env var is missing.</p>
      <ol style={{ lineHeight: 1.6 }}>
          <li>Set <code>VITE_CLERK_PUBLISHABLE_KEY</code> in your deployment environment (Railway).</li>
            <li>Use the same value as <code>CLERK_PUBLISHABLE_KEY</code> (pk_...).</li>
            <li>Deploy again. Optional: add your domain to Clerk Allowed Origins.</li>
          </ol>
          <p style={{ color: '#9ca3af', fontSize: 14 }}>Tip: see .env.example for required variables.</p>
        </div>
      </div>
    );
  }

  return (
    <ClerkProvider publishableKey={publishableKey}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <ErrorBoundary>
            <AuthenticatedApp />
            <Toaster />
            <ToastContainer
              position="top-right"
              autoClose={3000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="light"
              toastClassName="bg-background border border-border text-foreground"
              progressClassName="bg-primary"
            />
          </ErrorBoundary>
        </ThemeProvider>
      </QueryClientProvider>
    </ClerkProvider>
  );
}

export default App;