/**
 * Test Real AI Processing Pipeline
 * Tests the actual RFQ creation endpoint with AI processing
 */

import fs from 'fs';
import FormData from 'form-data';
import fetch from 'node-fetch';

const SERVER_URL = 'http://localhost:5000';
const TEST_FILE = './attached_assets/RFQ #2021-301 Response - ACCENT ELECTRICAL_1753707304170.pdf';

async function testRealAIProcessing() {
  console.log('🧪 Testing Real AI Processing Pipeline...');
  
  try {
    // Check if test file exists
    if (!fs.existsSync(TEST_FILE)) {
      console.log('❌ Test file not found:', TEST_FILE);
      return;
    }
    
    console.log('✅ Test file found:', TEST_FILE);
    const fileStats = fs.statSync(TEST_FILE);
    console.log(`📊 File size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);
    
    // Create form data for real RFQ creation
    const formData = new FormData();
    formData.append('documents', fs.createReadStream(TEST_FILE));
    formData.append('projectName', 'AI Processing Test RFQ');
    formData.append('projectLocation', 'Test Location for AI');
    formData.append('description', 'Testing real AI processing pipeline with actual document');
    
    console.log('📤 Uploading to REAL RFQ endpoint (with AI processing)...');
    console.log('⚠️ Note: This will require authentication - may fail without proper auth');
    
    // Use the real RFQ endpoint that includes AI processing
    const response = await fetch(`${SERVER_URL}/api/rfqs`, {
      method: 'POST',
      body: formData,
      headers: {
        ...formData.getHeaders(),
        // Add a simple test authorization header
        'Authorization': 'Bearer test-token'
      }
    });
    
    console.log('📥 Response status:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Real AI processing successful!');
      console.log('📋 Result keys:', Object.keys(result));
      
      if (result.aiSummary) {
        console.log('🤖 AI Summary generated:', result.aiSummary.substring(0, 300) + '...');
      } else {
        console.log('⚠️ No AI Summary in result');
      }
      
      if (result.extractedData) {
        console.log('📊 Extracted data keys:', Object.keys(result.extractedData));
        if (result.extractedData.aiSummary) {
          console.log('🤖 AI Summary in extractedData:', result.extractedData.aiSummary.substring(0, 300) + '...');
        }
      } else {
        console.log('⚠️ No extracted data in result');
      }
      
      console.log('📋 Full result structure:', JSON.stringify(result, null, 2));
      
    } else {
      const errorText = await response.text();
      console.log('❌ Real AI processing failed:', response.status);
      console.log('📄 Error response:', errorText);
      
      if (response.status === 401 || response.status === 403) {
        console.log('🔐 Authentication issue - this is expected for the real endpoint');
        console.log('💡 The AI processing pipeline would work with proper authentication');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.message.includes('ECONNREFUSED')) {
      console.log('🔌 Server connection failed - make sure server is running on localhost:5000');
    }
  }
}

// Run the test
testRealAIProcessing().catch(console.error);
