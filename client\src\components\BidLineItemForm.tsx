import { useState, useEffect } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trash2, Plus, Calculator, Loader2 } from "lucide-react";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { loadCostCodes, getCostCodesByCategory, type CostCode } from "@/utils/costCodes";

export interface BidLineItem {
  costCode: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  unitOfMeasure: string;
  category: string;
}

interface BidLineItemFormProps {
  name: string;
  onTotalChange?: (total: number) => void;
}

// Unit options remain the same

const unitOptions = [
  "Each", "Linear Foot", "Square Foot", "Cubic Foot", "Square Yard", "Cubic Yard",
  "Pound", "Ton", "Hour", "Day", "Lump Sum", "Allowance"
];

export function BidLineItemForm({ name, onTotalChange }: BidLineItemFormProps) {
  const { control, watch, setValue } = useFormContext();
  const { fields, append, remove } = useFieldArray({
    control,
    name,
  });

  const [costCodes, setCostCodes] = useState<CostCode[]>([]);
  const [costCodesByCategory, setCostCodesByCategory] = useState<Record<string, CostCode[]>>({});
  const [isLoadingCostCodes, setIsLoadingCostCodes] = useState(true);

  const lineItems = watch(name) || [];

  // Load cost codes from CSV on component mount
  useEffect(() => {
    const loadCodes = async () => {
      try {
        setIsLoadingCostCodes(true);
        const codes = await loadCostCodes();
        setCostCodes(codes);
        setCostCodesByCategory(getCostCodesByCategory(codes));
      } catch (error) {
        console.error('Failed to load cost codes:', error);
      } finally {
        setIsLoadingCostCodes(false);
      }
    };

    loadCodes();
  }, []);

  // Calculate total when line items change
  const calculateTotal = () => {
    const total = lineItems.reduce((sum: number, item: BidLineItem) => {
      return sum + (item.totalPrice || 0);
    }, 0);
    return total;
  };

  // Update total price when quantity or unit price changes
  const updateItemTotal = (index: number, quantity: number, unitPrice: number) => {
    const total = quantity * unitPrice;
    setValue(`${name}.${index}.totalPrice`, total);
  };

  const addLineItem = () => {
    append({
      costCode: "",
      description: "",
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0,
      unitOfMeasure: "Each",
      category: "General Requirements",
    });
  };

  const removeLineItem = (index: number) => {
    remove(index);
  };

  const total = calculateTotal();

  // Use effect to notify parent of total changes
  useEffect(() => {
    if (onTotalChange) {
      onTotalChange(total);
    }
  }, [total, onTotalChange]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Line Item Breakdown
          </div>
          <div className="text-lg font-bold text-primary">
            Total: ${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {fields.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">No line items added yet</p>
            <p className="text-sm">Add line items to provide detailed cost breakdown</p>
          </div>
        )}

        {fields.map((field, index) => (
          <div key={field.id} className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 border rounded-lg">
            {/* Cost Code */}
            <div className="md:col-span-2">
              <FormField
                control={control}
                name={`${name}.${index}.costCode`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost Code</FormLabel>
                    <Select onValueChange={(value) => {
                      field.onChange(value);
                      const costCode = costCodes.find(cc => cc.code === value);
                      if (costCode) {
                        setValue(`${name}.${index}.category`, costCode.category);
                        setValue(`${name}.${index}.description`, costCode.description);
                      }
                    }} value={field.value} disabled={isLoadingCostCodes}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={isLoadingCostCodes ? "Loading..." : "Select Code"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-60">
                        {isLoadingCostCodes ? (
                          <SelectItem value="loading" disabled>
                            <div className="flex items-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Loading cost codes...
                            </div>
                          </SelectItem>
                        ) : (
                          costCodes.map((code) => (
                            <SelectItem key={code.code} value={code.code}>
                              {code.code} - {code.description}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Description */}
            <div className="md:col-span-4">
              <FormField
                control={control}
                name={`${name}.${index}.description`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Work item description" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Quantity */}
            <div className="md:col-span-1">
              <FormField
                control={control}
                name={`${name}.${index}.quantity`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Qty</FormLabel>
                    <FormControl>
                      <Input 
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        onChange={(e) => {
                          const quantity = parseFloat(e.target.value) || 0;
                          field.onChange(quantity);
                          const unitPrice = watch(`${name}.${index}.unitPrice`) || 0;
                          updateItemTotal(index, quantity, unitPrice);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Unit of Measure */}
            <div className="md:col-span-1">
              <FormField
                control={control}
                name={`${name}.${index}.unitOfMeasure`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {unitOptions.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {unit}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Unit Price */}
            <div className="md:col-span-2">
              <FormField
                control={control}
                name={`${name}.${index}.unitPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit Price</FormLabel>
                    <FormControl>
                      <Input 
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        onChange={(e) => {
                          const unitPrice = parseFloat(e.target.value) || 0;
                          field.onChange(unitPrice);
                          const quantity = watch(`${name}.${index}.quantity`) || 0;
                          updateItemTotal(index, quantity, unitPrice);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Total Price */}
            <div className="md:col-span-1">
              <FormField
                control={control}
                name={`${name}.${index}.totalPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total</FormLabel>
                    <FormControl>
                      <Input 
                        {...field}
                        type="number"
                        step="0.01"
                        readOnly
                        className="bg-muted"
                        value={field.value?.toFixed(2) || "0.00"}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Remove Button */}
            <div className="md:col-span-1 flex items-end">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeLineItem(index)}
                className="w-full"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}

        <Button
          type="button"
          variant="outline"
          onClick={addLineItem}
          className="w-full"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Line Item
        </Button>
      </CardContent>
    </Card>
  );
}