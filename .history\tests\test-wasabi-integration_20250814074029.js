/**
 * Wasabi Storage Integration Test Suite
 * Tests the unified file upload and processing system with Wasabi storage
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');
const { testAuth } = require('./test-auth-helper');

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:5000';
const TEST_DATA_DIR = path.join(__dirname, 'test-data');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Utility functions
function log(message, type = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${type}] ${message}`);
}

function recordTest(testName, passed, duration, notes = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    log(`✅ ${testName} - PASSED (${duration}ms)`, 'PASS');
  } else {
    testResults.failed++;
    log(`❌ ${testName} - FAILED (${duration}ms)`, 'FAIL');
  }
  
  testResults.details.push({
    test: testName,
    status: passed ? 'PASS' : 'FAIL',
    duration,
    notes,
    timestamp: new Date().toISOString()
  });
}

// Create test data directory if it doesn't exist
function ensureTestDataDir() {
  if (!fs.existsSync(TEST_DATA_DIR)) {
    fs.mkdirSync(TEST_DATA_DIR, { recursive: true });
    log('Created test data directory');
  }
}

// Generate test PDF file
function createTestPDF(filename, sizeKB = 100) {
  const filePath = path.join(TEST_DATA_DIR, filename);
  
  // Create a simple PDF-like file for testing
  const pdfHeader = '%PDF-1.4\n';
  const pdfContent = `1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n
2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n
3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n
4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n100 700 Td\n(Test RFQ Document) Tj\nET\nendstream\nendobj\n
xref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000206 00000 n \n
trailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n299\n%%EOF\n`;
  
  // Pad to desired size
  const targetSize = sizeKB * 1024;
  const currentSize = pdfHeader.length + pdfContent.length;
  const padding = targetSize > currentSize ? 'X'.repeat(targetSize - currentSize) : '';
  
  const fullContent = pdfHeader + pdfContent + padding;
  fs.writeFileSync(filePath, fullContent);
  
  log(`Created test PDF: ${filename} (${Math.round(fullContent.length / 1024)}KB)`);
  return filePath;
}

// Test 1.1: Basic File Upload to Wasabi
async function testBasicFileUpload() {
  const testName = 'Basic File Upload to Wasabi';
  const startTime = Date.now();
  
  try {
    // Create test file
    const testFile = createTestPDF('test-basic-upload.pdf', 50);
    
    // Create form data
    const form = new FormData();
    form.append('documents', fs.createReadStream(testFile));
    form.append('projectName', 'Test RFQ Project');
    form.append('projectLocation', 'Test Location');
    form.append('description', 'Test RFQ for Wasabi integration');
    form.append('tradeCategory', 'general');
    
    // Make upload request with proper authentication
    const authHeaders = await testAuth.getFormHeaders(form);
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: authHeaders,
      timeout: 30000
    });
    
    const duration = Date.now() - startTime;
    
    // Validate response
    if (response.status === 201 && response.data.id) {
      // Test file retrieval with authentication
      const rfqId = response.data.id;
      const authClient = await testAuth.createAuthenticatedClient();
      const documentsResponse = await authClient.get(`/api/rfqs/${rfqId}/documents`);
      
      if (documentsResponse.data.length > 0) {
        const document = documentsResponse.data[0];
        const expectedKeyPattern = /^rfq-documents\/\d+-test-basic-upload\.pdf$/;
        
        if (expectedKeyPattern.test(document.objectKey)) {
          recordTest(testName, true, duration, `RFQ created: ${rfqId}, Document: ${document.objectKey}`);
        } else {
          recordTest(testName, false, duration, `Invalid object key format: ${document.objectKey}`);
        }
      } else {
        recordTest(testName, false, duration, 'No documents found in response');
      }
    } else {
      recordTest(testName, false, duration, `Unexpected response: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Test 1.2: Large File Chunked Upload
async function testLargeFileUpload() {
  const testName = 'Large File Chunked Upload';
  const startTime = Date.now();
  
  try {
    // Create large test file (15MB)
    const testFile = createTestPDF('test-large-upload.pdf', 15 * 1024);
    
    // Create form data
    const form = new FormData();
    form.append('documents', fs.createReadStream(testFile));
    form.append('projectName', 'Large File Test RFQ');
    form.append('projectLocation', 'Test Location');
    form.append('description', 'Large file test for chunked upload');
    form.append('tradeCategory', 'general');
    
    // Make upload request with proper authentication and longer timeout
    const authHeaders = await testAuth.getFormHeaders(form);
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: authHeaders,
      timeout: 60000 // 60 second timeout for large files
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === 201 && response.data.id) {
      recordTest(testName, true, duration, `Large file uploaded successfully: ${response.data.id}`);
    } else {
      recordTest(testName, false, duration, `Upload failed: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Test 1.3: Multi-file Batch Upload
async function testMultiFileUpload() {
  const testName = 'Multi-file Batch Upload';
  const startTime = Date.now();
  
  try {
    // Create multiple test files
    const pdfFile = createTestPDF('test-multi-main.pdf', 100);
    const txtFile = path.join(TEST_DATA_DIR, 'test-specs.txt');
    const csvFile = path.join(TEST_DATA_DIR, 'test-costs.csv');
    
    // Create text file
    fs.writeFileSync(txtFile, 'Test specifications document\nThis is a test specification file for multi-file upload testing.');
    
    // Create CSV file
    fs.writeFileSync(csvFile, 'Cost Code,Description,Unit Price\n03.01,Concrete Work,150.00\n05.01,Steel Work,200.00');
    
    // Create form data with multiple files
    const form = new FormData();
    form.append('documents', fs.createReadStream(pdfFile));
    form.append('documents', fs.createReadStream(txtFile));
    form.append('documents', fs.createReadStream(csvFile));
    form.append('projectName', 'Multi-file Test RFQ');
    form.append('projectLocation', 'Test Location');
    form.append('description', 'Multi-file batch upload test');
    form.append('tradeCategory', 'general');
    form.append('uploadBatchId', `batch_${Date.now()}`);
    
    // Add file metadata
    form.append('fileMetadata[0]', JSON.stringify({
      id: 'file-1',
      fileType: 'main',
      sequence: 1,
      fileName: 'test-multi-main.pdf'
    }));
    form.append('fileMetadata[1]', JSON.stringify({
      id: 'file-2',
      fileType: 'specifications',
      sequence: 2,
      fileName: 'test-specs.txt'
    }));
    form.append('fileMetadata[2]', JSON.stringify({
      id: 'file-3',
      fileType: 'supporting',
      sequence: 3,
      fileName: 'test-costs.csv'
    }));
    
    const authHeaders = await testAuth.getFormHeaders(form);
    const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
      headers: authHeaders,
      timeout: 45000
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === 201 && response.data.id) {
      // Verify all files were uploaded with authentication
      const rfqId = response.data.id;
      const authClient = await testAuth.createAuthenticatedClient();
      const documentsResponse = await authClient.get(`/api/rfqs/${rfqId}/documents`);
      
      if (documentsResponse.data.length === 3) {
        recordTest(testName, true, duration, `All 3 files uploaded successfully: ${rfqId}`);
      } else {
        recordTest(testName, false, duration, `Expected 3 files, got ${documentsResponse.data.length}`);
      }
    } else {
      recordTest(testName, false, duration, `Upload failed: ${response.status}`);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Test Progress Tracking
async function testProgressTracking() {
  const testName = 'SSE Progress Tracking';
  const startTime = Date.now();
  
  try {
    // Create upload session with proper authentication
    const authClient = await testAuth.createAuthenticatedClient();
    const sessionResponse = await authClient.post('/api/upload/start-session', {});
    
    if (sessionResponse.data.sessionId) {
      const sessionId = sessionResponse.data.sessionId;
      log(`Created upload session: ${sessionId}`);
      
      // Note: Full SSE testing would require a more complex setup
      // For now, we'll just verify the session creation
      recordTest(testName, true, Date.now() - startTime, `Session created: ${sessionId}`);
    } else {
      recordTest(testName, false, Date.now() - startTime, 'Failed to create session');
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Test File Validation
async function testFileValidation() {
  const testName = 'File Validation & Security';
  const startTime = Date.now();
  
  try {
    // Create invalid file (executable)
    const invalidFile = path.join(TEST_DATA_DIR, 'malicious.exe');
    fs.writeFileSync(invalidFile, 'This is not a valid document file');
    
    const form = new FormData();
    form.append('documents', fs.createReadStream(invalidFile));
    form.append('projectName', 'Invalid File Test');
    form.append('projectLocation', 'Test Location');
    form.append('tradeCategory', 'general');
    
    try {
      const authHeaders = await testAuth.getFormHeaders(form);
      const response = await axios.post(`${BASE_URL}/api/rfqs`, form, {
        headers: authHeaders,
        timeout: 10000
      });
      
      // Should not reach here - upload should be rejected
      recordTest(testName, false, Date.now() - startTime, 'Invalid file was accepted');
      
    } catch (uploadError) {
      // Expected to fail - this is good
      if (uploadError.response && uploadError.response.status === 400) {
        recordTest(testName, true, Date.now() - startTime, 'Invalid file correctly rejected');
      } else {
        recordTest(testName, false, Date.now() - startTime, `Unexpected error: ${uploadError.message}`);
      }
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Setup error: ${error.message}`);
  }
}

// Test Wasabi Storage Operations
async function testWasabiOperations() {
  const testName = 'Wasabi Storage Operations';
  const startTime = Date.now();
  
  try {
    // This would test the objectStorageService directly
    // For now, we'll test via the API endpoints
    
    // Test file serving endpoint with authentication
    const authClient = await testAuth.createAuthenticatedClient();
    const response = await authClient.get('/api/debug/assets');
    
    if (response.status === 200) {
      recordTest(testName, true, Date.now() - startTime, 'Storage operations accessible');
    } else {
      recordTest(testName, false, Date.now() - startTime, 'Storage operations failed');
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    recordTest(testName, false, duration, `Error: ${error.message}`);
  }
}

// Main test runner
async function runTests() {
  log('🚀 Starting Wasabi Storage Integration Tests');
  log('================================================');
  
  // Setup authentication
  log('🔑 Setting up test authentication...');
  await testAuth.testAuthentication();
  
  // Setup test data directory
  ensureTestDataDir();
  
  // Run tests
  await testBasicFileUpload();
  await testLargeFileUpload();
  await testMultiFileUpload();
  await testProgressTracking();
  await testFileValidation();
  await testWasabiOperations();
  
  // Generate report
  log('================================================');
  log('📊 Test Results Summary');
  log(`Total Tests: ${testResults.total}`);
  log(`Passed: ${testResults.passed}`);
  log(`Failed: ${testResults.failed}`);
  log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  // Save detailed results
  const reportPath = path.join(__dirname, 'test-results-wasabi.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  log(`📄 Detailed results saved to: ${reportPath}`);
  
  // Cleanup test files
  if (fs.existsSync(TEST_DATA_DIR)) {
    fs.rmSync(TEST_DATA_DIR, { recursive: true, force: true });
    log('🧹 Cleaned up test data files');
  }
  
  return testResults;
}

// Export for use in other test files
module.exports = {
  runTests,
  testResults
};

// Run tests if called directly
if (require.main === module) {
  runTests().then(results => {
    process.exit(results.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}
