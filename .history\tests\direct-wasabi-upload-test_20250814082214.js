/**
 * Direct Wasabi Upload Test
 * Simple pass/fail test for file upload to Wasabi
 */

// Load environment variables from root .env file
require('dotenv').config({ path: '.env' });

const path = require('path');
const fs = require('fs');

async function directWasabiUploadTest() {
  console.log('🚀 Direct Wasabi Upload Test');
  console.log('============================');
  
  try {
    // Import the object storage service
    const { uploadFile } = require('../server/services/objectStorageService.ts');
    
    // Create a simple test file buffer
    const testContent = `Test file uploaded at ${new Date().toISOString()}`;
    const testBuffer = Buffer.from(testContent, 'utf8');
    const testFileName = `test-upload-${Date.now()}.txt`;
    
    console.log('📄 Test file details:');
    console.log(`  - Name: ${testFileName}`);
    console.log(`  - Size: ${testBuffer.length} bytes`);
    console.log(`  - Content: ${testContent}`);
    
    console.log('\n⬆️ Uploading to Wasabi...');
    
    // Attempt direct upload to Wasabi
    const result = await uploadFile(
      testBuffer,
      testFileName,
      'text/plain'
    );
    
    console.log('\n✅ UPLOAD SUCCESSFUL!');
    console.log('📊 Upload result:');
    console.log(`  - File Name: ${result.fileName}`);
    console.log(`  - Object Key: ${result.objectKey}`);
    console.log(`  - File Size: ${result.fileSize} bytes`);
    console.log(`  - MIME Type: ${result.mimeType}`);
    console.log(`  - Uploaded At: ${result.uploadedAt}`);
    
    return {
      success: true,
      result: result,
      message: 'File uploaded successfully to Wasabi'
    };
    
  } catch (error) {
    console.log('\n❌ UPLOAD FAILED!');
    console.log('💥 Error details:');
    console.log(`  - Error: ${error.message}`);
    console.log(`  - Type: ${error.constructor.name}`);
    
    // Log additional error details if available
    if (error.code) {
      console.log(`  - Code: ${error.code}`);
    }
    if (error.statusCode) {
      console.log(`  - Status Code: ${error.statusCode}`);
    }
    
    return {
      success: false,
      error: error.message,
      errorCode: error.code,
      statusCode: error.statusCode
    };
  }
}

// Run the test
if (require.main === module) {
  directWasabiUploadTest()
    .then(result => {
      console.log('\n🎯 FINAL RESULT:');
      console.log('================');
      
      if (result.success) {
        console.log('✅ PASS - Wasabi upload working correctly');
        console.log('🎉 Your Wasabi integration is functional!');
        process.exit(0);
      } else {
        console.log('❌ FAIL - Wasabi upload not working');
        console.log('🔧 Issue:', result.error);
        
        // Provide specific troubleshooting guidance
        if (result.errorCode === 'InvalidAccessKeyId') {
          console.log('💡 Fix: Check WASABI_ACCESS_KEY_ID environment variable');
        } else if (result.errorCode === 'SignatureDoesNotMatch') {
          console.log('💡 Fix: Check WASABI_SECRET_ACCESS_KEY environment variable');
        } else if (result.errorCode === 'NoSuchBucket') {
          console.log('💡 Fix: Check WASABI_BUCKET_NAME or create the bucket');
        } else if (result.statusCode === 403) {
          console.log('💡 Fix: Check bucket permissions and access key permissions');
        } else {
          console.log('💡 Check network connectivity and Wasabi service status');
        }
        
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test setup failed:', error);
      process.exit(1);
    });
}

module.exports = { directWasabiUploadTest };
