# Security Integration Test Plan

Run from `tests/` with the dev server running on http://localhost:5000.

- RFQ Update Access: `node test-rfq-update-access.js`
- Contractor Docs/Files Access: `node test-docs-files-access.js`
- Sensitive Endpoint Rate Limits: `node test-rate-limits.js`
- Admin Routes Denial: `node test-admin-routes.js`

Notes:
- Tests create orgs/users directly via storage and authenticate via generated API keys.
- Admin routes use Clerk org membership and may return 500 locally if <PERSON> isn’t configured; denial (403/500) is acceptable for this test.
