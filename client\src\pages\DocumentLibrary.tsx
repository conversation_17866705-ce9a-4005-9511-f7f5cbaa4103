import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { FileText, Search, Upload, Download, Filter, Calendar, User, Archive } from "lucide-react";
import { useState } from "react";

export default function DocumentLibrary() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");

  // Mock document data for now
  const documents = [
    {
      id: "1",
      name: "Standard Construction Contract Template.docx",
      type: "Contract Template",
      uploadedBy: "System Admin",
      uploadedAt: "2024-01-15",
      size: "125 KB",
      category: "templates"
    },
    {
      id: "2", 
      name: "Safety Requirements Checklist.pdf",
      type: "Safety Document",
      uploadedBy: "Safety Manager",
      uploadedAt: "2024-01-12",
      size: "89 KB",
      category: "safety"
    },
    {
      id: "3",
      name: "Bid Proposal Template.docx", 
      type: "Bid Template",
      uploadedBy: "Operations Manager",
      uploadedAt: "2024-01-10",
      size: "156 KB",
      category: "templates"
    },
    {
      id: "4",
      name: "Insurance Requirements Guide.pdf",
      type: "Insurance Guide",
      uploadedBy: "Legal Department",
      uploadedAt: "2024-01-08",
      size: "203 KB", 
      category: "legal"
    }
  ];

  const filterOptions = [
    { value: "all", label: "All Documents" },
    { value: "templates", label: "Templates" },
    { value: "safety", label: "Safety Documents" },
    { value: "legal", label: "Legal Documents" }
  ];

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = selectedFilter === "all" || doc.category === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "templates":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "safety":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "legal":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Document Library</h1>
          <p className="text-muted-foreground mt-2">
            Centralized access to templates, forms, and reference documents
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Upload className="h-4 w-4" />
          Upload Document
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents by name or type..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              {filterOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedFilter === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedFilter(option.value)}
                  className="flex items-center gap-2"
                >
                  <Filter className="h-3 w-3" />
                  {option.label}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents List */}
      <div className="grid gap-4">
        {filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Archive className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No documents found</h3>
              <p className="text-muted-foreground">
                {searchTerm || selectedFilter !== "all" 
                  ? "Try adjusting your search or filters" 
                  : "Upload your first document to get started"}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredDocuments.map((doc) => (
            <Card key={doc.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                      <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground">{doc.name}</h3>
                      <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {doc.uploadedBy}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(doc.uploadedAt).toLocaleDateString()}
                        </div>
                        <span>{doc.size}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className={getCategoryColor(doc.category)}>
                      {doc.type}
                    </Badge>
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Download className="h-3 w-3" />
                      Download
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Library Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{documents.length}</div>
              <div className="text-sm text-muted-foreground">Total Documents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {documents.filter(d => d.category === "templates").length}
              </div>
              <div className="text-sm text-muted-foreground">Templates</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {documents.filter(d => d.category === "safety").length}
              </div>
              <div className="text-sm text-muted-foreground">Safety Docs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {documents.filter(d => d.category === "legal").length}
              </div>
              <div className="text-sm text-muted-foreground">Legal Docs</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}