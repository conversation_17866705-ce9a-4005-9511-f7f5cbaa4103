/**
 * Test File Upload and AI Processing
 * Tests the complete file processing pipeline via HTTP request
 */

import fs from 'fs';
import FormData from 'form-data';
import fetch from 'node-fetch';

const SERVER_URL = 'http://localhost:5000';
const TEST_FILE = './attached_assets/RFQ #2021-301 Response - ACCENT ELECTRICAL_1753707304170.pdf';

async function testFileUpload() {
  console.log('🧪 Testing File Upload and AI Processing...');
  
  try {
    // Check if test file exists
    if (!fs.existsSync(TEST_FILE)) {
      console.log('❌ Test file not found:', TEST_FILE);
      return;
    }
    
    console.log('✅ Test file found:', TEST_FILE);
    const fileStats = fs.statSync(TEST_FILE);
    console.log(`📊 File size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);
    
    // Create form data
    const formData = new FormData();
    formData.append('documents', fs.createReadStream(TEST_FILE));
    formData.append('projectName', 'Test RFQ Processing');
    formData.append('projectLocation', 'Test Location');
    formData.append('description', 'Testing AI processing pipeline');
    
    console.log('📤 Uploading file to test endpoint...');
    
    // Use the test endpoint that bypasses authentication
    const response = await fetch(`${SERVER_URL}/api/test/rfqs`, {
      method: 'POST',
      body: formData,
      headers: {
        ...formData.getHeaders()
      }
    });
    
    console.log('📥 Response status:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Upload successful!');
      console.log('📋 Result:', JSON.stringify(result, null, 2));
      
      if (result.aiSummary) {
        console.log('🤖 AI Summary generated:', result.aiSummary.substring(0, 200) + '...');
      }
      
      if (result.extractedData) {
        console.log('📊 Extracted data keys:', Object.keys(result.extractedData));
      }
    } else {
      const errorText = await response.text();
      console.log('❌ Upload failed:', response.status, errorText);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testFileUpload().catch(console.error);
