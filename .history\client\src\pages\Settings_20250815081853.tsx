import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useAuth } from "@/hooks/useAuth";
import { useOrganization, useOrganizationList, UserProfile, CreateOrganization } from "@clerk/clerk-react";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import OnboardingOrganizationSetup from "@/components/OnboardingOrganizationSetup";
import { queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Save, User, Key, Bell, Activity, Users, Building } from "lucide-react";
import { useUserRole } from "@/hooks/useUserRole";

const contractorFormSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  tradeTypes: z.array(z.string()).min(1, "At least one trade type is required"),
});

const tradeOptions = [
  { value: "General Contractor", label: "General Contractor" },
  { value: "electrical", label: "Electrical" },
  { value: "plumbing", label: "Plumbing" },
  { value: "hvac", label: "HVAC" },
  { value: "concrete", label: "Concrete" },
  { value: "sitework", label: "Site Work/Excavation" },
  { value: "masonry", label: "Masonry" },
  { value: "carpentry", label: "Carpentry" },
  { value: "roofing", label: "Roofing" },
  { value: "painting", label: "Painting" },
];

export default function Settings() {
  const { user } = useAuth();
  const { organization, membership, isLoaded } = useOrganization();
  const { userMemberships } = useOrganizationList();
  const { toast } = useToast();
  const { role: userRole } = useUserRole();
  const [location] = useLocation();
  
  const isOnboardingMode = location.includes('onboarding=true');
  const [activeTab, setActiveTab] = useState("user-settings");
  
  const isOrgAdmin = membership?.role === 'org:admin';
  const hasOrganizations = userMemberships?.data && userMemberships.data.length > 0;
  const isSuperUserOrAdmin = userRole === 'general_contractor' || userRole === 'contractor';

  const { data: contractorProfile } = useQuery({
    queryKey: ["/api/contractors/profile"],
    enabled: !!user,
  });

  const contractorForm = useForm<z.infer<typeof contractorFormSchema>>({
    resolver: zodResolver(contractorFormSchema),
    defaultValues: {
      companyName: "",
      tradeTypes: [],
    },
  });

  useEffect(() => {
    if (contractorProfile) {
      const profile = contractorProfile as any;
      contractorForm.reset({
        companyName: profile?.companyName || "",
        tradeTypes: profile?.tradeTypes || [],
      });
    }
  }, [contractorProfile, contractorForm]);

  const updateContractorMutation = useMutation({
    mutationFn: (data: any) => fetch("/api/contractors/profile", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    }).then(res => res.json()),
    onSuccess: () => {
      toast({ title: "Profile updated successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/contractors/profile"] });
      
      if (isOnboardingMode) {
        toast({ title: "Onboarding Complete!", description: "Welcome to Bidaible!" });
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      }
    },
  });

  const onContractorSubmit = (data: any) => {
    updateContractorMutation.mutate(data);
  };

  if (!isLoaded) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account, profile, and system preferences
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="user-settings">
            <User className="h-4 w-4 mr-2" />
            User Settings
          </TabsTrigger>
          <TabsTrigger value="api-keys">
            <Key className="h-4 w-4 mr-2" />
            API Keys
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          {isSuperUserOrAdmin && (
            <TabsTrigger value="audit-logs">
              <Activity className="h-4 w-4 mr-2" />
              Audit Logs
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="user-settings" className="space-y-6">
          {isOnboardingMode && !hasOrganizations && (
            <OnboardingOrganizationSetup 
              onComplete={() => {
                queryClient.invalidateQueries({ queryKey: ['/api/auth/user'] });
                toast({
                  title: "Organization Created!",
                  description: "Now let's complete your profile setup.",
                });
              }}
            />
          )}

          {!isOnboardingMode && !hasOrganizations && (
            <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <CardContent className="pt-6">
                <div className="flex items-start gap-4">
                  <Users className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="font-semibold text-blue-900">Create Your Organization</h3>
                    <p className="text-sm text-blue-700 mb-4">
                      Create an organization to access team features and user management.
                    </p>
                    <CreateOrganization 
                      appearance={{
                        elements: {
                          rootBox: "w-full",
                          card: "shadow-none border-0 bg-transparent",
                        }
                      }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Account
              </CardTitle>
              <CardDescription>
                Manage your personal account settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserProfile 
                routing="hash"
                appearance={{
                  elements: {
                    rootBox: "w-full",
                    card: "shadow-none border border-gray-200 rounded-lg",
                    headerTitle: "text-lg font-semibold",
                    headerSubtitle: "text-sm text-muted-foreground",
                  }
                }}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {isOnboardingMode && (
                  <div className="flex items-center gap-2 mr-4">
                    <Badge variant="outline">Step 3 of 3</Badge>
                    <div className="h-2 w-2 bg-primary rounded-full animate-pulse" />
                  </div>
                )}
                <Building className="h-5 w-5" />
                Contractor Profile
              </CardTitle>
              <CardDescription>
                {isOnboardingMode 
                  ? "Complete your profile to finish onboarding - Company Name and Trade Types are required"
                  : "Complete your contractor profile to participate in RFQ processes (all Profile content has been moved here)"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...contractorForm}>
                <form onSubmit={contractorForm.handleSubmit(onContractorSubmit)} className="space-y-6">
                  
                  <div className={`p-4 rounded-lg space-y-4 ${isOnboardingMode ? 'bg-blue-50 border-l-4 border-l-blue-500' : 'bg-gray-50'}`}>
                    <h3 className="text-lg font-semibold">Business Identity & Contact</h3>
                    {isOnboardingMode && (
                      <p className="text-sm text-red-600">
                        ★ Company Name and Trade Types are required to complete onboarding
                      </p>
                    )}
                    
                    <FormField
                      control={contractorForm.control}
                      name="companyName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={isOnboardingMode ? "text-red-600 font-semibold" : ""}>
                            Company Name *
                            {isOnboardingMode && <span className="ml-1 text-red-500">★</span>}
                          </FormLabel>
                          <FormControl>
                            <Input 
                              {...field} 
                              placeholder="Enter company name" 
                              className={isOnboardingMode && !field.value ? "border-red-300" : ""}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={contractorForm.control}
                      name="tradeTypes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={isOnboardingMode ? "text-red-600 font-semibold" : ""}>
                            Trade Types *
                            {isOnboardingMode && <span className="ml-1 text-red-500">★</span>}
                          </FormLabel>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {tradeOptions.map((trade) => (
                              <div key={trade.value} className="flex items-center space-x-2">
                                <Checkbox
                                  id={trade.value}
                                  checked={field.value?.includes(trade.value)}
                                  onCheckedChange={(checked) => {
                                    const currentValue = field.value || [];
                                    if (checked) {
                                      field.onChange([...currentValue, trade.value]);
                                    } else {
                                      field.onChange(currentValue.filter((v: string) => v !== trade.value));
                                    }
                                  }}
                                />
                                <Label 
                                  htmlFor={trade.value} 
                                  className={`text-sm ${isOnboardingMode && field.value?.length === 0 ? 'text-red-600' : ''}`}
                                >
                                  {trade.label}
                                </Label>
                              </div>
                            ))}
                          </div>
                          <FormMessage />
                          {isOnboardingMode && field.value?.length === 0 && (
                            <p className="text-red-500 text-xs">Please select at least one trade type to continue</p>
                          )}
                        </FormItem>
                      )}
                    />

                  </div>

                  {/* Additional Profile Sections */}
                  <div className="space-y-4 mt-6">
                    <div className="border-t pt-6">
                      <h3 className="text-lg font-semibold mb-4">Additional Business Information</h3>
                      
                      <Accordion type="multiple" className="w-full space-y-2">
                        {/* Business Details */}
                        <AccordionItem value="business-details" className="border rounded-lg">
                          <AccordionTrigger className="px-4 py-3 hover:no-underline">
                            <span className="font-medium text-base">Business Details</span>
                          </AccordionTrigger>
                          <AccordionContent className="px-4 pb-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="companyWebsite">Company Website</Label>
                                <Input
                                  id="companyWebsite"
                                  type="text"
                                  placeholder="https://www.example.com"
                                />
                              </div>
                              
                              <div className="space-y-2">
                                <Label htmlFor="legalStructure">Legal Structure</Label>
                                <select className="w-full p-2 border rounded-md">
                                  <option value="">Select structure</option>
                                  <option value="corporation">Corporation</option>
                                  <option value="llc">LLC</option>
                                  <option value="partnership">Partnership</option>
                                  <option value="sole_proprietorship">Sole Proprietorship</option>
                                </select>
                              </div>
                              
                              <div className="space-y-2">
                                <Label htmlFor="taxId">Tax ID/EIN</Label>
                                <Input
                                  id="taxId"
                                  placeholder="Enter tax ID or EIN"
                                />
                              </div>
                              
                              <div className="space-y-2">
                                <Label htmlFor="contactEmail">Primary Contact Email</Label>
                                <Input
                                  id="contactEmail"
                                  type="email"
                                  placeholder="Enter contact email"
                                />
                              </div>
                              
                              <div className="space-y-2">
                                <Label htmlFor="contactPhone">Contact Phone</Label>
                                <Input
                                  id="contactPhone"
                                  placeholder="Enter contact phone"
                                />
                              </div>
                              
                              <div className="space-y-2">
                                <Label htmlFor="unionStatus">Union Status</Label>
                                <select className="w-full p-2 border rounded-md">
                                  <option value="">Select status</option>
                                  <option value="union">Union</option>
                                  <option value="non-union">Non-Union</option>
                                  <option value="open-shop">Open Shop</option>
                                </select>
                              </div>
                              
                              <div className="space-y-2">
                                <Label htmlFor="yearsInBusiness">Years in Business</Label>
                                <Input
                                  id="yearsInBusiness"
                                  type="number"
                                  placeholder="Enter years"
                                />
                              </div>
                              
                              <div className="md:col-span-2 space-y-2">
                                <Label htmlFor="businessAddress">Business Address</Label>
                                <textarea
                                  id="businessAddress"
                                  className="w-full p-2 border rounded-md"
                                  placeholder="No P.O. boxes allowed"
                                  rows={3}
                                />
                              </div>
                            </div>
                          </AccordionContent>
                        </AccordionItem>

                        {/* Credentials & Compliance */}
                        <AccordionItem value="credentials-compliance" className="border rounded-lg">
                          <AccordionTrigger className="px-4 py-3 hover:no-underline">
                            <span className="font-medium text-base">Credentials & Compliance</span>
                          </AccordionTrigger>
                          <AccordionContent className="px-4 pb-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="licenseNumber">Contractor License Number</Label>
                            <Input
                              id="licenseNumber"
                              placeholder="Enter license number"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="licenseExpiration">License Expiration Date</Label>
                            <Input
                              id="licenseExpiration"
                              type="date"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="bondingCapacity">Bonding Capacity</Label>
                            <Input
                              id="bondingCapacity"
                              placeholder="Enter bonding capacity"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="safetyRating">Safety Rating</Label>
                            <Input
                              id="safetyRating"
                              placeholder="Enter safety rating"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="insuranceLimits">Insurance Limits</Label>
                            <Input
                              id="insuranceLimits"
                              placeholder="General/Auto/Workers Comp"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="certifications">Certifications</Label>
                            <Input
                              id="certifications"
                              placeholder="OSHA, EPA, etc."
                            />
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label>License Documents</Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <p className="text-sm text-gray-600">Upload license documents</p>
                            <p className="text-xs text-gray-500 mt-1">Supported formats: PDF, JPG, PNG (Max 10MB per file)</p>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label>Insurance Certificates</Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <p className="text-sm text-gray-600">Upload insurance certificates</p>
                            <p className="text-xs text-gray-500 mt-1">Supported formats: PDF (Max 10MB per file)</p>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label>Bonding Documents</Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <p className="text-sm text-gray-600">Upload bonding documents</p>
                            <p className="text-xs text-gray-500 mt-1">Supported formats: PDF (Max 10MB per file)</p>
                          </div>
                        </div>
                          </AccordionContent>
                        </AccordionItem>

                      {/* Financial & Reference Data */}
                      <div className="p-4 bg-gray-50 rounded-lg space-y-4">
                        <h4 className="font-medium text-base">Financial & Reference Data</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="dunsNumber">DUNS Number</Label>
                            <Input
                              id="dunsNumber"
                              placeholder="Enter DUNS number"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="cageCode">CAGE Code</Label>
                            <Input
                              id="cageCode"
                              placeholder="Enter CAGE code"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="annualRevenue">Annual Revenue</Label>
                            <select className="w-full p-2 border rounded-md">
                              <option value="">Select range</option>
                              <option value="under_1m">Under $1M</option>
                              <option value="1m_5m">$1M - $5M</option>
                              <option value="5m_25m">$5M - $25M</option>
                              <option value="25m_100m">$25M - $100M</option>
                              <option value="over_100m">Over $100M</option>
                            </select>
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="creditRating">Credit Rating</Label>
                            <select className="w-full p-2 border rounded-md">
                              <option value="">Select rating</option>
                              <option value="excellent">Excellent</option>
                              <option value="good">Good</option>
                              <option value="fair">Fair</option>
                              <option value="poor">Poor</option>
                            </select>
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="bankReference">Bank Reference</Label>
                            <Input
                              id="bankReference"
                              placeholder="Primary bank name"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="suretryCompany">Surety Company</Label>
                            <Input
                              id="suretryCompany"
                              placeholder="Surety company name"
                            />
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="tradeReferences">Trade References</Label>
                          <textarea
                            id="tradeReferences"
                            className="w-full p-2 border rounded-md"
                            placeholder="List recent clients with contact information"
                            rows={3}
                          />
                        </div>
                      </div>

                      {/* Performance & Experience */}
                      <div className="p-4 bg-gray-50 rounded-lg space-y-4">
                        <h4 className="font-medium text-base">Performance & Experience</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="largestProjectValue">Largest Project Value</Label>
                            <Input
                              id="largestProjectValue"
                              placeholder="Enter dollar amount"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="currentBacklog">Current Backlog</Label>
                            <Input
                              id="currentBacklog"
                              placeholder="Enter dollar amount"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="projectsCompleted">Projects Completed (Last 3 Years)</Label>
                            <Input
                              id="projectsCompleted"
                              type="number"
                              placeholder="Number of projects"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="averageProjectSize">Average Project Size</Label>
                            <select className="w-full p-2 border rounded-md">
                              <option value="">Select range</option>
                              <option value="under_100k">Under $100K</option>
                              <option value="100k_500k">$100K - $500K</option>
                              <option value="500k_2m">$500K - $2M</option>
                              <option value="2m_10m">$2M - $10M</option>
                              <option value="over_10m">Over $10M</option>
                            </select>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="specialtyExperience">Specialty Experience</Label>
                          <textarea
                            id="specialtyExperience"
                            className="w-full p-2 border rounded-md"
                            placeholder="Describe specific project types, industries, or specialized work"
                            rows={3}
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="notableProjects">Notable Projects</Label>
                          <textarea
                            id="notableProjects"
                            className="w-full p-2 border rounded-md"
                            placeholder="List significant projects with descriptions"
                            rows={3}
                          />
                        </div>
                      </div>

                      {/* Operational Details */}
                      <div className="p-4 bg-gray-50 rounded-lg space-y-4">
                        <h4 className="font-medium text-base">Operational Details</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="employeeCount">Employee Count</Label>
                            <select className="w-full p-2 border rounded-md">
                              <option value="">Select range</option>
                              <option value="1_10">1-10</option>
                              <option value="11_50">11-50</option>
                              <option value="51_200">51-200</option>
                              <option value="201_500">201-500</option>
                              <option value="over_500">500+</option>
                            </select>
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="serviceRadius">Service Radius (miles)</Label>
                            <Input
                              id="serviceRadius"
                              type="number"
                              placeholder="Geographic reach"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="equipmentOwned">Major Equipment Owned</Label>
                            <textarea
                              id="equipmentOwned"
                              className="w-full p-2 border rounded-md"
                              placeholder="List major equipment and machinery"
                              rows={2}
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="workingHours">Working Hours</Label>
                            <select className="w-full p-2 border rounded-md">
                              <option value="">Select schedule</option>
                              <option value="standard">Standard Business Hours</option>
                              <option value="extended">Extended Hours Available</option>
                              <option value="24_7">24/7 Emergency Service</option>
                              <option value="weekends">Weekends Available</option>
                            </select>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="serviceAreas">Primary Service Areas</Label>
                          <textarea
                            id="serviceAreas"
                            className="w-full p-2 border rounded-md"
                            placeholder="List cities, counties, or regions served"
                            rows={2}
                          />
                        </div>
                      </div>

                      {/* Custom Tags & Preferences */}
                      <div className="p-4 bg-gray-50 rounded-lg space-y-4">
                        <h4 className="font-medium text-base">Custom Tags & Preferences</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="preferredProjectTypes">Preferred Project Types</Label>
                            <textarea
                              id="preferredProjectTypes"
                              className="w-full p-2 border rounded-md"
                              placeholder="Commercial, residential, industrial, etc."
                              rows={2}
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="customTags">Custom Tags</Label>
                            <textarea
                              id="customTags"
                              className="w-full p-2 border rounded-md"
                              placeholder="Keywords for better matching (comma separated)"
                              rows={2}
                            />
                          </div>
                        </div>
                        
                        <div className="space-y-3">
                          <Label>Notification Preferences</Label>
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Checkbox id="emailNotifications" />
                              <Label htmlFor="emailNotifications">Email notifications for new RFQs</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="smsNotifications" />
                              <Label htmlFor="smsNotifications">SMS notifications for urgent RFQs</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="weeklyDigest" />
                              <Label htmlFor="weeklyDigest">Weekly digest of opportunities</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="autoMatch" />
                              <Label htmlFor="autoMatch">Auto-match to relevant RFQs</Label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end pt-6 border-t">
                    <Button 
                      type="submit" 
                      disabled={updateContractorMutation.isPending}
                      className="flex items-center gap-2"
                    >
                      <Save className="h-4 w-4" />
                      {updateContractorMutation.isPending 
                        ? "Saving..." 
                        : isOnboardingMode 
                          ? "Complete Onboarding" 
                          : "Save Profile"
                      }
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api-keys" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>Manage your API keys for programmatic access</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-center py-8">API Keys functionality available here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>Configure how you receive notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-center py-8">Notification settings functionality available here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        {isSuperUserOrAdmin && (
          <TabsContent value="audit-logs" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Audit Logs</CardTitle>
                <CardDescription>System activity and security audit trail</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-center py-8">Audit logs functionality available here.</p>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
