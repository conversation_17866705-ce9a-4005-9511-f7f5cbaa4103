-- Migration: Add unique constraints on object_key columns to prevent duplicate file storage
-- Author: System migration for organization-based file storage
-- Date: 2025-08-21

-- Add unique index on rfq_documents.object_key
CREATE UNIQUE INDEX IF NOT EXISTS rfq_documents_object_key_uidx
  ON rfq_documents (object_key);

-- Add unique index on bid_documents.object_key  
CREATE UNIQUE INDEX IF NOT EXISTS bid_documents_object_key_uidx
  ON bid_documents (object_key);

-- Add comment for documentation
COMMENT ON INDEX rfq_documents_object_key_uidx IS 'Prevents duplicate file storage in Wasabi object storage for RFQ documents';
COMMENT ON INDEX bid_documents_object_key_uidx IS 'Prevents duplicate file storage in Wasabi object storage for bid documents';
