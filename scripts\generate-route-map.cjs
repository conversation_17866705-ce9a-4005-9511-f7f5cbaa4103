#!/usr/bin/env node
/**
 * Route map generator: scans server/routes.ts and server/routes/* for app/router endpoints
 * Outputs docs/route-map.json and docs/route-map.md
 */
const fs = require('fs');
const path = require('path');

const ROOT = path.resolve(__dirname, '..');
const SERVER_DIR = path.join(ROOT, 'server');

function readFileSafe(p) {
  try { return fs.readFileSync(p, 'utf-8'); } catch { return ''; }
}

function scanFile(file, acc) {
  const content = readFileSafe(file);
  if (!content) return;
  const appRegex = /\bapp\.(get|post|put|patch|delete)\(\s*["'`]([^"'`]+)["'`]/g;
  const routerRegex = /\brouter\.(get|post|put|patch|delete)\(\s*["'`]([^"'`]+)["'`]/g;
  let m;
  while ((m = appRegex.exec(content))) {
    acc.push({ file: path.relative(ROOT, file), method: m[1].toUpperCase(), path: m[2] });
  }
  while ((m = routerRegex.exec(content))) {
    acc.push({ file: path.relative(ROOT, file), method: m[1].toUpperCase(), path: m[2] });
  }
}

function walk(dir, files = []) {
  for (const entry of fs.readdirSync(dir)) {
    const p = path.join(dir, entry);
    const st = fs.statSync(p);
    if (st.isDirectory()) walk(p, files);
    else if (p.endsWith('.ts')) files.push(p);
  }
  return files;
}

(function main(){
  const files = walk(SERVER_DIR, []);
  const routes = [];
  for (const f of files) scanFile(f, routes);
  routes.sort((a,b)=> a.path.localeCompare(b.path) || a.method.localeCompare(b.method));
  const outDir = path.join(ROOT, 'docs');
  if (!fs.existsSync(outDir)) fs.mkdirSync(outDir, { recursive: true });
  fs.writeFileSync(path.join(outDir, 'route-map.json'), JSON.stringify(routes, null, 2));
  const md = ['# Route Map', '', '| Method | Path | File |', '|---|---|---|', ...routes.map(r=>`| ${r.method} | ${r.path} | ${r.file} |`) ].join('\n');
  fs.writeFileSync(path.join(outDir, 'route-map.md'), md);
  console.log(`Wrote ${routes.length} routes to docs/route-map.(json|md)`);
})();
