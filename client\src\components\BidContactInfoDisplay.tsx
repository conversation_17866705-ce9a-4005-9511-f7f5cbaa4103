import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Mail, Phone, AlertCircle } from "lucide-react";

interface BidContactInfoDisplayProps {
  contactInfo: {
    name?: string;
    email?: string;
    phone?: string;
  };
  hasContactInfo: boolean;
}

export function BidContactInfoDisplay({ contactInfo, hasContactInfo }: BidContactInfoDisplayProps) {
  if (!hasContactInfo || (!contactInfo.name && !contactInfo.email && !contactInfo.phone)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Contact Information
          </CardTitle>
          <CardDescription>
            No structured contact information available for this bid.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Contact information may be available in the bid documents</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Contact Information
        </CardTitle>
        <CardDescription>
          Primary contact for this bid submission
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Contact Summary Card */}
        <div className="bg-muted p-4 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-primary" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-lg">
                {contactInfo.name || 'Name not provided'}
              </h3>
              <div className="flex items-center gap-4 mt-1">
                {contactInfo.email && (
                  <Badge variant="outline" className="text-xs">
                    Primary Contact
                  </Badge>
                )}
                <Badge variant="secondary" className="text-xs">
                  Bid Submission Contact
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Email */}
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <Mail className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm text-muted-foreground">Email Address</p>
              {contactInfo.email ? (
                <a 
                  href={`mailto:${contactInfo.email}`}
                  className="font-medium hover:underline text-blue-600"
                >
                  {contactInfo.email}
                </a>
              ) : (
                <p className="text-muted-foreground">Not provided</p>
              )}
            </div>
          </div>

          {/* Phone */}
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="h-10 w-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <Phone className="h-5 w-5 text-green-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm text-muted-foreground">Phone Number</p>
              {contactInfo.phone ? (
                <a 
                  href={`tel:${contactInfo.phone}`}
                  className="font-medium hover:underline text-green-600"
                >
                  {contactInfo.phone}
                </a>
              ) : (
                <p className="text-muted-foreground">Not provided</p>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        {(contactInfo.email || contactInfo.phone) && (
          <div className="flex gap-2">
            {contactInfo.email && (
              <a
                href={`mailto:${contactInfo.email}?subject=Regarding Your Bid Submission`}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg text-sm font-medium transition-colors"
              >
                Send Email
              </a>
            )}
            {contactInfo.phone && (
              <a
                href={`tel:${contactInfo.phone}`}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-lg text-sm font-medium transition-colors"
              >
                Call Now
              </a>
            )}
          </div>
        )}

        {/* Data Quality Note */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <p className="text-sm text-blue-700 dark:text-blue-300">
            <strong>Note:</strong> This contact information was provided during the structured bid submission process.
            {!contactInfo.email && !contactInfo.phone && " Additional contact details may be available in the bid documents."}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}