import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  DollarSign, 
  Calendar, 
  FileText, 
  TrendingUp, 
  TrendingDown, 
  CheckCircle,
  XCircle,
  Package,
  Clock,
  Building2
} from 'lucide-react';

interface BidLineItem {
  id: string;
  costCode: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  unitOfMeasure: string;
  category: string;
}

interface ContractorBid {
  bidId: string;
  contractorId: string;
  contractorName: string;
  companyName: string;
  bidAmount: number;
  timeline: string;
  status: string;
  competitiveScore?: number;
  lineItems: BidLineItem[];
  proposalText?: string;
  notes?: string;
}

interface CategorySummary {
  mainCostCode: string;
  categoryName: string;
  contractorBids: ContractorBid[];
  totalContractors: number;
  priceRange: {
    lowest: number;
    highest: number;
    average: number;
  };
}

interface CategoryDetailModalProps {
  categoryData: CategorySummary;
  selectedBidId?: string;
}

export function CategoryDetailModal({ categoryData, selectedBidId }: CategoryDetailModalProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(num);
  };

  const getPriceVarianceColor = (amount: number, average: number) => {
    const variance = (amount - average) / average;
    if (variance > 0.1) return 'text-red-600 dark:text-red-400';
    if (variance < -0.1) return 'text-green-600 dark:text-green-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accepted':
      case 'accept':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
      case 'reject':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    }
  };

  // Get all unique cost codes from all bids for comparison
  const allCostCodes = Array.from(
    new Set(
      categoryData.contractorBids.flatMap(bid => 
        bid.lineItems.map(item => item.costCode)
      )
    )
  ).sort();

  return (
    <div className="space-y-6">
      {/* Category Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {categoryData.mainCostCode} - {categoryData.categoryName}
          </CardTitle>
          <CardDescription>
            Detailed comparison of {categoryData.totalContractors} contractor bids
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(categoryData.priceRange.lowest)}
              </div>
              <div className="text-sm text-muted-foreground">Lowest Bid</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {formatCurrency(categoryData.priceRange.average)}
              </div>
              <div className="text-sm text-muted-foreground">Average</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(categoryData.priceRange.highest)}
              </div>
              <div className="text-sm text-muted-foreground">Highest Bid</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="comparison" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="comparison">Side-by-Side Comparison</TabsTrigger>
          <TabsTrigger value="details">Detailed Breakdown</TabsTrigger>
          <TabsTrigger value="analysis">Analysis & Notes</TabsTrigger>
        </TabsList>

        {/* Side-by-Side Comparison */}
        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Line Item Comparison</CardTitle>
              <CardDescription>
                Compare cost codes and pricing across all contractors
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[120px]">Cost Code</TableHead>
                      <TableHead className="min-w-[200px]">Description</TableHead>
                      {categoryData.contractorBids.map((bid) => (
                        <TableHead key={bid.bidId} className="text-center min-w-[120px]">
                          <div>
                            <div className="font-medium">{bid.companyName}</div>
                            <Badge className={`mt-1 ${getStatusColor(bid.status)}`}>
                              {bid.status === 'accept' ? 'Accepted' : 
                               bid.status === 'reject' ? 'Rejected' : 
                               bid.status}
                            </Badge>
                          </div>
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allCostCodes.map((costCode) => {
                      // Find the description from any bid that has this cost code
                      const sampleItem = categoryData.contractorBids
                        .flatMap(bid => bid.lineItems)
                        .find(item => item.costCode === costCode);
                      
                      return (
                        <TableRow key={costCode}>
                          <TableCell className="font-medium">{costCode}</TableCell>
                          <TableCell className="text-sm">
                            {sampleItem?.description || 'N/A'}
                          </TableCell>
                          {categoryData.contractorBids.map((bid) => {
                            const lineItem = bid.lineItems.find(item => item.costCode === costCode);
                            
                            if (!lineItem) {
                              return (
                                <TableCell key={bid.bidId} className="text-center text-muted-foreground">
                                  Not included
                                </TableCell>
                              );
                            }

                            return (
                              <TableCell key={bid.bidId} className="text-center">
                                <div className="space-y-1">
                                  <div className="font-medium">
                                    {formatCurrency(lineItem.totalPrice)}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {formatNumber(lineItem.quantity)} {lineItem.unitOfMeasure}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    @ {formatCurrency(lineItem.unitPrice)}
                                  </div>
                                </div>
                              </TableCell>
                            );
                          })}
                        </TableRow>
                      );
                    })}
                    
                    {/* Totals row */}
                    <TableRow className="bg-muted/50 font-medium">
                      <TableCell colSpan={2} className="font-bold">
                        Total for Category
                      </TableCell>
                      {categoryData.contractorBids.map((bid) => (
                        <TableCell key={bid.bidId} className="text-center">
                          <div className={`font-bold text-lg ${getPriceVarianceColor(bid.bidAmount, categoryData.priceRange.average)}`}>
                            {formatCurrency(bid.bidAmount)}
                          </div>
                          {bid.bidAmount < categoryData.priceRange.average && (
                            <div className="flex items-center justify-center text-green-600 text-xs">
                              <TrendingDown className="h-3 w-3 mr-1" />
                              Below Average
                            </div>
                          )}
                          {bid.bidAmount > categoryData.priceRange.average && (
                            <div className="flex items-center justify-center text-red-600 text-xs">
                              <TrendingUp className="h-3 w-3 mr-1" />
                              Above Average
                            </div>
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Detailed Breakdown */}
        <TabsContent value="details" className="space-y-4">
          {categoryData.contractorBids.map((bid) => (
            <Card key={bid.bidId} className={selectedBidId === bid.bidId ? 'border-primary' : ''}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      {bid.companyName}
                    </CardTitle>
                    <CardDescription>{bid.contractorName}</CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold">
                      {formatCurrency(bid.bidAmount)}
                    </div>
                    <Badge className={getStatusColor(bid.status)}>
                      {bid.status === 'accept' ? 'Accepted' : 
                       bid.status === 'reject' ? 'Rejected' : 
                       bid.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Timeline and Score */}
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{bid.timeline || 'Timeline not specified'}</span>
                    </div>
                    {bid.competitiveScore && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">Competitive Score:</span>
                        <span className="font-medium">{Math.round(bid.competitiveScore * 100)}%</span>
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Line Items */}
                  <div>
                    <h4 className="font-medium mb-3">Line Items Breakdown</h4>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Cost Code</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead className="text-right">Qty</TableHead>
                          <TableHead className="text-right">Unit Price</TableHead>
                          <TableHead className="text-right">Total</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {bid.lineItems.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.costCode}</TableCell>
                            <TableCell>{item.description}</TableCell>
                            <TableCell className="text-right">
                              {formatNumber(item.quantity)} {item.unitOfMeasure}
                            </TableCell>
                            <TableCell className="text-right">
                              {formatCurrency(item.unitPrice)}
                            </TableCell>
                            <TableCell className="text-right font-medium">
                              {formatCurrency(item.totalPrice)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Analysis & Notes */}
        <TabsContent value="analysis" className="space-y-4">
          {categoryData.contractorBids.map((bid) => (
            <Card key={bid.bidId}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  {bid.companyName} - Proposal & Notes
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {bid.proposalText && (
                  <div>
                    <h4 className="font-medium mb-2">Proposal Summary</h4>
                    <div className="text-sm bg-muted p-3 rounded-lg">
                      {bid.proposalText}
                    </div>
                  </div>
                )}
                
                {bid.notes && (
                  <div>
                    <h4 className="font-medium mb-2">Additional Notes</h4>
                    <div className="text-sm bg-muted p-3 rounded-lg">
                      {bid.notes}
                    </div>
                  </div>
                )}

                {bid.competitiveScore && (
                  <div>
                    <h4 className="font-medium mb-2">AI Analysis</h4>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-sm">Competitive Score:</span>
                        <Badge variant="outline">
                          {Math.round(bid.competitiveScore * 100)}%
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}