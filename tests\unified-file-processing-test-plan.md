# Unified File Upload & Processing Test Plan - Wasabi Storage Integration

**Date:** August 14, 2025  
**Version:** 1.0  
**Status:** Ready for Execution

## Test Environment Setup

### Prerequisites
- ✅ Wasabi S3-compatible storage configured
- ✅ AI services (Groq, OpenAI, Gemini) API keys
- ✅ PostgreSQL database with latest schema
- ✅ Server running on localhost:5000
- ✅ Clerk authentication working

### Environment Variables Required
```bash
WASABI_ACCESS_KEY_ID=your_wasabi_access_key_id
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_access_key
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com
WASABI_REGION=us-east-1
GROQ_API_KEY=gsk_your_groq_api_key
OPENAI_API_KEY=sk-your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
```

## Test Categories

### 🗂️ Category 1: Wasabi Storage Integration Tests

#### Test 1.1: Basic File Upload to Wasabi
**Objective:** Validate basic file upload functionality to Wasabi storage

**Steps:**
1. Upload single PDF file (< 10MB) via `/api/rfqs` endpoint
2. Verify file stored in Wasabi with correct object key format
3. Check file metadata in database
4. Test file retrieval via `/api/files/:documentId`

**Expected Results:**
- File uploaded successfully to Wasabi
- Object key format: `rfq-documents/{timestamp}-{filename}`
- Database record created with correct metadata
- File retrievable via API endpoint

**Test Data:** Sample construction RFQ PDF (5MB)

#### Test 1.2: Large File Chunked Upload
**Objective:** Test chunked upload for large files

**Steps:**
1. Upload large PDF file (> 10MB, up to 50MB)
2. Monitor progress tracking during upload
3. Verify file integrity after upload
4. Test download and compare checksums

**Expected Results:**
- Chunked upload completes successfully
- Progress updates received via SSE
- File integrity maintained
- Download matches original file

**Test Data:** Large construction specification PDF (25MB+)

#### Test 1.3: Multi-file Batch Upload
**Objective:** Test simultaneous upload of multiple files

**Steps:**
1. Upload batch of 5 files with different types and sizes
2. Monitor individual file progress
3. Verify all files stored with unique object keys
4. Check batch completion status

**Expected Results:**
- All files uploaded successfully
- Unique object keys generated
- Progress tracked per file
- Batch processing completes

**Test Data:** Mixed file batch (PDF, TXT, CSV)

### 🤖 Category 2: Unified PDF Processing Tests

#### Test 2.1: PDF.js Primary Extraction
**Objective:** Validate primary PDF text extraction method

**Steps:**
1. Upload standard construction RFQ PDF
2. Monitor extraction process
3. Verify extracted text quality and length
4. Check confidence scoring

**Expected Results:**
- Text extracted successfully (>50 characters)
- Confidence score calculated
- Processing time logged
- Structured data extracted

#### Test 2.2: Fallback Strategy Testing
**Objective:** Test PDF extraction fallback mechanisms

**Steps:**
1. Test with PDF that may fail PDF.js
2. Verify pdf-parse fallback activation
3. Test Gemini Vision fallback (if available)
4. Test graceful failure handling

**Expected Results:**
- Fallback methods activate when needed
- Error logging captures failure reasons
- Processing continues with available methods
- User receives appropriate feedback

### 🧠 Category 3: AI Processing Pipeline Tests

#### Test 3.1: AI Model Integration
**Objective:** Test AI processing with multiple providers

**Steps:**
1. Upload RFQ document for AI processing
2. Verify Groq API processing (primary)
3. Test OpenAI fallback scenario
4. Validate structured data extraction

**Expected Results:**
- AI processing completes successfully
- Structured data extracted correctly
- Model fallback works when needed
- Processing time within acceptable limits

#### Test 3.2: Batch AI Processing
**Objective:** Test AI processing of multiple files with priorities

**Steps:**
1. Upload batch with main RFQ + supporting documents
2. Verify main file gets comprehensive analysis
3. Check supporting files get appropriate processing
4. Validate AI summary generation

**Expected Results:**
- Main files processed with full analysis
- Supporting files processed appropriately
- AI summaries generated
- File priorities respected

### 📊 Category 4: Progress Tracking & Real-time Updates

#### Test 4.1: SSE Progress Tracking
**Objective:** Validate real-time progress updates

**Steps:**
1. Create upload session via `/api/upload/start-session`
2. Connect to SSE endpoint `/api/upload/progress/:sessionId`
3. Upload file and monitor progress events
4. Verify session cleanup

**Expected Results:**
- SSE connection established
- Progress events received in real-time
- Progress percentages accurate
- Session cleaned up properly

#### Test 4.2: Multi-file Progress Coordination
**Objective:** Test progress tracking for batch uploads

**Steps:**
1. Start batch upload session
2. Upload multiple files simultaneously
3. Monitor individual and overall progress
4. Test progress persistence

**Expected Results:**
- Individual file progress tracked
- Overall batch progress calculated
- Progress updates coordinated
- State persisted correctly

### 🛡️ Category 5: Error Handling & Resilience Tests

#### Test 5.1: Network Failure Scenarios
**Objective:** Test system resilience under network issues

**Steps:**
1. Simulate Wasabi connection failures
2. Test AI API timeouts
3. Verify error logging and user feedback
4. Test retry mechanisms

**Expected Results:**
- Graceful error handling
- Appropriate user feedback
- Error logging captures details
- Retry mechanisms work

#### Test 5.2: File Validation & Security
**Objective:** Test file validation and security measures

**Steps:**
1. Attempt upload of unsupported file types
2. Test oversized files (>250MB)
3. Try malicious filename patterns
4. Verify MIME type validation

**Expected Results:**
- Invalid files rejected
- Security measures prevent malicious uploads
- Appropriate error messages
- System remains stable

### ⚡ Category 6: Performance & Load Tests

#### Test 6.1: Single File Performance
**Objective:** Benchmark single file processing performance

**Steps:**
1. Upload large PDF (50+ pages)
2. Measure extraction time
3. Measure AI processing time
4. Monitor memory usage

**Expected Results:**
- Processing time < 5 seconds for extraction
- AI processing completes within timeout
- Memory usage stays reasonable
- Performance metrics logged

#### Test 6.2: Concurrent Load Testing
**Objective:** Test system under concurrent load

**Steps:**
1. Simulate 8+ concurrent uploads
2. Monitor system stability
3. Verify processing queue management
4. Check Wasabi storage performance

**Expected Results:**
- System handles concurrent load
- Processing queue works correctly
- No memory leaks or crashes
- Storage performance acceptable

## Test Execution Checklist

### Pre-Test Setup
- [ ] Environment variables configured
- [ ] Test data files prepared
- [ ] Database schema up to date
- [ ] Server running and accessible
- [ ] Authentication working

### Test Execution Order
1. [ ] **Category 1:** Wasabi Storage Integration
2. [ ] **Category 2:** PDF Processing
3. [ ] **Category 3:** AI Processing Pipeline
4. [ ] **Category 4:** Progress Tracking
5. [ ] **Category 5:** Error Handling
6. [ ] **Category 6:** Performance Testing

### Post-Test Validation
- [ ] All uploaded files cleaned up
- [ ] Database state verified
- [ ] Performance metrics documented
- [ ] Issues logged and prioritized

## Success Criteria

### Functional Requirements
- ✅ 100% file upload success rate to Wasabi
- ✅ >95% PDF text extraction success rate
- ✅ Real-time progress updates with <1s latency
- ✅ Multi-file batch processing completion
- ✅ Proper error handling and user feedback

### Performance Requirements
- ✅ Large file processing <5 seconds
- ✅ Concurrent processing up to 8 files
- ✅ Memory usage <100MB per file
- ✅ Progress tracking accuracy >99%

### Security Requirements
- ✅ Multi-tenant isolation maintained
- ✅ File validation prevents malicious uploads
- ✅ Presigned URLs expire correctly
- ✅ Organization-scoped access control

## Test Results Documentation

### Test Results Template
```
Test: [Test Name]
Date: [Date]
Status: [PASS/FAIL/PARTIAL]
Duration: [Time]
Notes: [Observations]
Issues: [Any problems found]
```

### Performance Metrics Template
```
File Size: [Size]
Extraction Time: [Time]
AI Processing Time: [Time]
Total Processing Time: [Time]
Memory Usage: [Peak Memory]
Confidence Score: [Score]
```

## Next Steps After Testing

1. **Document Results:** Record all test outcomes
2. **Address Issues:** Fix any identified problems
3. **Performance Optimization:** Optimize based on benchmarks
4. **Production Readiness:** Validate system ready for deployment
5. **Monitoring Setup:** Implement production monitoring

---

## Command Recipes (Bash and PowerShell)

These commands help execute tests quickly. Replace placeholders like <API_KEY>, <SESSION_ID>, and file paths as needed.

### Start the dev server
```bash
npm run dev
```

### Single file upload (authenticated)
```bash
# Bash (macOS/Linux/WSL/Git Bash on Windows)
curl -X POST \
  -H "Authorization: Bearer <API_KEY>" \
  -F "documents=@test.pdf;type=application/pdf" \
  -F "projectName=Test RFQ" \
  http://localhost:5000/api/rfqs
```

### Single file upload (no auth, test endpoint)
```bash
curl -X POST \
  -F "documents=@test.pdf;type=application/pdf" \
  -F "projectName=Test RFQ (No Auth)" \
  http://localhost:5000/api/test/rfqs
```

You can use the sample [test.pdf](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/test.pdf).

### Multi-file batch upload
```bash
curl -X POST \
  -H "Authorization: Bearer <API_KEY>" \
  -F "documents=@rfq.pdf;type=application/pdf" \
  -F "documents=@specs.txt;type=text/plain" \
  -F "documents=@costs.csv;type=text/csv" \
  -F "projectName=Mixed Batch" \
  http://localhost:5000/api/rfqs
```

### Large file upload
```bash
# Example with a large PDF (25MB+)
curl -X POST \
  -H "Authorization: Bearer <API_KEY>" \
  -F "documents=@large.pdf;type=application/pdf" \
  -F "projectName=Large File" \
  http://localhost:5000/api/rfqs
```

### Start an upload session and stream SSE progress
```bash
# Create a session
SESSION_JSON=$(curl -s -X POST http://localhost:5000/api/upload/start-session)
SESSION_ID=$(echo "$SESSION_JSON" | jq -r .sessionId)

# Stream progress (open a new terminal)
curl -N http://localhost:5000/api/upload/progress/$SESSION_ID

# Upload while session is open
curl -X POST \
  -H "Authorization: Bearer <API_KEY>" \
  -F "sessionId=$SESSION_ID" \
  -F "documents=@test.pdf;type=application/pdf" \
  -F "projectName=SSE Session Test" \
  http://localhost:5000/api/rfqs
```

PowerShell equivalents:
```powershell
# Create session
$sessionJson = Invoke-RestMethod -Method Post -Uri http://localhost:5000/api/upload/start-session
$sessionId = $sessionJson.sessionId

# Stream progress (PowerShell doesn't stream SSE well). Prefer Git Bash:
#   curl -N http://localhost:5000/api/upload/progress/$sessionId

# Upload with session
Invoke-RestMethod -Method Post -Uri http://localhost:5000/api/rfqs \
  -Headers @{ Authorization = "Bearer <API_KEY>" } \
  -Form @{ sessionId = $sessionId; projectName = 'SSE Session Test'; documents = Get-Item './test.pdf' }
```

### Verify stored file via API
```bash
# Replace with a real documentId from the upload response
curl -I http://localhost:5000/api/files/<documentId>
```

## Endpoint Reference (used in this plan)

- POST /api/rfqs — Create RFQ + upload documents (auth required)
- POST /api/test/rfqs — Create RFQ + upload (no auth; for testing)
- POST /api/upload/start-session — Initialize upload session
- GET /api/upload/progress/:sessionId — Stream SSE progress
- GET /api/files/:documentId — Download or redirect to stored file

## Notes and Constraints

- Use Git Bash or WSL on Windows for reliable SSE streaming with `curl -N`.
- Ensure environment variables match those in the Environment Variables section.
- For performance tests, run with a warm server to avoid cold-start skew.
- Clean up uploaded test files in Wasabi after tests via console or admin scripts.

---

**Test Plan Status:** Ready for Execution  
**Estimated Duration:** 2-3 days for complete test suite  
**Prerequisites:** All environment setup completed
