import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';
import * as dotenv from 'dotenv';

dotenv.config();
neonConfig.webSocketConstructor = ws;

const pool = new Pool({ connectionString: process.env.DATABASE_URL });

try {
  // Check for any remaining test-pattern users
  const testUserQuery = `
    SELECT id, email, first_name, last_name, organization_id, created_at
    FROM users 
    WHERE email ILIKE '%@example.com'
       OR email ILIKE '%@test.com'
       OR email ILIKE '%test%'
       OR email ILIKE '%demo%'
       OR first_name ILIKE 'test%'
       OR first_name ILIKE 'demo%'
    ORDER BY created_at DESC
  `;
  
  const result = await pool.query(testUserQuery);
  
  console.log('Remaining test users:');
  if (result.rows.length === 0) {
    console.log('✅ No test users found');
  } else {
    result.rows.forEach(user => {
      console.log(`  - ${user.email} (${user.first_name} ${user.last_name}) - Org: ${user.organization_id} - Created: ${new Date(user.created_at).toLocaleDateString()}`);
    });
    
    console.log(`\n📊 Found ${result.rows.length} test users`);
  }
  
  // Also check all users to see the current state
  const allUsersQuery = `SELECT COUNT(*) as count FROM users`;
  const allUsers = await pool.query(allUsersQuery);
  console.log(`\n📋 Total users in database: ${allUsers.rows[0].count}`);
  
  // Check legitimate users
  const legitQuery = `
    SELECT email, first_name, last_name, organization_id 
    FROM users 
    WHERE email IN ('<EMAIL>', '<EMAIL>')
  `;
  const legitUsers = await pool.query(legitQuery);
  
  console.log('\n👤 Legitimate production users:');
  legitUsers.rows.forEach(user => {
    console.log(`  - ${user.email} (${user.first_name} ${user.last_name}) - Org: ${user.organization_id || 'None'}`);
  });
  
} catch (error) {
  console.error('Error:', error);
} finally {
  await pool.end();
}
