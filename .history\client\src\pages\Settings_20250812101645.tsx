import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useAuth } from "@/hooks/useAuth";
import { useOrganization, useOrganizationList, OrganizationProfile, UserProfile, CreateOrganization } from "@clerk/clerk-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { ChevronDown, ChevronRight, Save, User, Key, Plus, Eye, EyeOff, Copy, Check, Trash2, Edit, Calendar, Activity, Users, Shield, AlertTriangle, Clock, Bell, Mail } from "lucide-react";
import { useUserRole } from "@/hooks/useUserRole";

// Form schemas
const contractorFormSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  companyWebsite: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  legalStructure: z.string().optional(),
  taxId: z.string().optional(),
  dba: z.string().optional(),
  primaryAddress: z.string().optional(),
  mailingAddress: z.string().optional(),
  primaryContactName: z.string().optional(),
  primaryContactEmail: z.string().email().optional().or(z.literal("")),
  primaryContactPhone: z.string().optional(),
  primaryContactTitle: z.string().optional(),
  tradeTypes: z.array(z.string()).min(1, "At least one trade type is required"),
  unionStatus: z.string().optional(),
  unionAffiliations: z.string().optional(),
  certifications: z.array(z.string()).optional(),
  serviceAreas: z.string().optional(),
  licenseNumber: z.string().optional(),
  licenseState: z.string().optional(),
  licenseExpiration: z.string().optional(),
  generalLiability: z.string().optional(),
  workersComp: z.string().optional(),
  autoInsurance: z.string().optional(),
  bondingSingle: z.number().optional(),
  bondingAggregate: z.number().optional(),
  emr: z.number().optional(),
  bankReference: z.string().optional(),
  suretyReference: z.string().optional(),
  creditRating: z.string().optional(),
  paymentTerms: z.string().optional(),
  litigationHistory: z.string().optional(),
  projectReferences: z.string().optional(),
  yearsInBusiness: z.number().optional(),
  specializations: z.array(z.string()).optional(),
  awards: z.string().optional(),
  environmentalPrograms: z.string().optional(),
  workforceSize: z.number().optional(),
  workforceBreakdown: z.string().optional(),
  equipment: z.string().optional(),
  availability: z.string().optional(),
  keywordTags: z.array(z.string()).optional(),
  preferredProjectTypes: z.array(z.string()).optional(),
});

const apiKeyFormSchema = z.object({
  name: z.string().min(1, "API key name is required"),
  permissions: z.enum(["read-only", "upload-only", "full-access"]),
  rateLimit: z.number().min(1).max(10000),
});

const notificationSettingsSchema = z.object({
  emailNotifications: z.boolean(),
  smsNotifications: z.boolean(),
  pushNotifications: z.boolean(),
  bidAlerts: z.boolean(),
  rfqAlerts: z.boolean(),
  systemAlerts: z.boolean(),
  weeklyDigest: z.boolean(),
  marketingEmails: z.boolean(),
});

// Trade types and other options
const tradeOptions = [
  { value: "General Contractor", label: "General Contractor" },
  { value: "electrical", label: "Electrical" },
  { value: "plumbing", label: "Plumbing" },
  { value: "hvac", label: "HVAC" },
  { value: "concrete", label: "Concrete" },
  { value: "sitework", label: "Site Work/Excavation" },
  { value: "masonry", label: "Masonry" },
  { value: "structural_steel", label: "Structural Steel" },
  { value: "carpentry", label: "Carpentry" },
  { value: "roofing", label: "Roofing" },
  { value: "waterproofing", label: "Waterproofing" },
  { value: "insulation", label: "Insulation" },
  { value: "drywall", label: "Drywall" },
  { value: "flooring", label: "Flooring" },
  { value: "painting", label: "Painting" },
  { value: "fire_protection", label: "Fire Protection" },
  { value: "security_systems", label: "Security Systems" },
  { value: "landscaping", label: "Landscaping" },
  { value: "asphalt_paving", label: "Asphalt/Paving" },
  { value: "surveying", label: "Surveying" },
  { value: "environmental", label: "Environmental Services" },
  { value: "demolition", label: "Demolition" },
  { value: "utilities", label: "Utilities" },
  { value: "telecommunications", label: "Telecommunications" },
  { value: "glazing", label: "Glazing/Windows" },
  { value: "metal_fabrication", label: "Metal Fabrication" },
  { value: "elevator", label: "Elevator/Escalator" },
  { value: "architectural_millwork", label: "Architectural Millwork" },
];

const certificationOptions = [
  { value: "mbe", label: "MBE (Minority Business Enterprise)" },
  { value: "wbe", label: "WBE (Women Business Enterprise)" },
  { value: "vbe", label: "VBE (Veteran Business Enterprise)" },
  { value: "dbe", label: "DBE (Disadvantaged Business Enterprise)" },
  { value: "sbe", label: "SBE (Small Business Enterprise)" },
];

const specializationOptions = [
  { value: "design_build", label: "Design/Build" },
  { value: "bim", label: "BIM" },
  { value: "prefab", label: "Prefabrication" },
  { value: "sustainable", label: "Sustainable Construction" },
  { value: "heavy_civil", label: "Heavy Civil" },
  { value: "emergency", label: "Emergency Work" },
];

const projectTypeOptions = [
  { value: "residential", label: "Residential" },
  { value: "commercial", label: "Commercial" },
  { value: "infrastructure", label: "Infrastructure" },
  { value: "industrial", label: "Industrial" },
  { value: "institutional", label: "Institutional" },
  { value: "healthcare", label: "Healthcare" },
  { value: "education", label: "Education" },
];

export default function Settings() {
  const { user } = useAuth();
  const { organization, membership, isLoaded } = useOrganization();
  const { userMemberships } = useOrganizationList();
  const { toast } = useToast();
  const { role: userRole } = useUserRole();
  
  // State
  const [activeTab, setActiveTab] = useState("profile");
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({});
  const [isApiKeyDialogOpen, setIsApiKeyDialogOpen] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({});
  
  // Check permissions
  const isOrgAdmin = membership?.role === 'org:admin';
  const hasOrganizations = userMemberships?.data && userMemberships.data.length > 0;
  const isSuperUserOrAdmin = userRole === 'general_contractor' || userRole === 'contractor';

  // Data queries
  const { data: contractorProfile } = useQuery({
    queryKey: ["/api/contractors/profile"],
    enabled: !!user,
  });

  const { data: apiKeys } = useQuery({
    queryKey: ["/api/api-keys"],
    enabled: !!user,
  });

  const { data: notificationSettings } = useQuery({
    queryKey: ["/api/notification-settings"],
    enabled: !!user,
  });

  const { data: auditLogs } = useQuery({
    queryKey: ["/api/audit-logs"],
    enabled: isSuperUserOrAdmin,
  });

  const { data: users } = useQuery({
    queryKey: ["/api/users"],
    enabled: isSuperUserOrAdmin,
  });

  // Form initialization
  const contractorForm = useForm<z.infer<typeof contractorFormSchema>>({
    resolver: zodResolver(contractorFormSchema),
    defaultValues: {
      companyName: contractorProfile?.companyName || "",
      companyWebsite: contractorProfile?.companyWebsite || "",
      legalStructure: contractorProfile?.legalStructure || "",
      taxId: contractorProfile?.taxId || "",
      dba: contractorProfile?.dba || "",
      primaryAddress: contractorProfile?.primaryAddress || "",
      mailingAddress: contractorProfile?.mailingAddress || "",
      primaryContactName: contractorProfile?.primaryContactName || "",
      primaryContactEmail: contractorProfile?.primaryContactEmail || "",
      primaryContactPhone: contractorProfile?.primaryContactPhone || "",
      tradeTypes: contractorProfile?.tradeTypes || [],
      unionStatus: contractorProfile?.unionStatus || "",
      unionAffiliations: contractorProfile?.unionAffiliations || "",
      certifications: contractorProfile?.certifications || [],
      serviceAreas: contractorProfile?.serviceAreas || "",
      licenseNumber: contractorProfile?.licenseNumber || "",
      licenseState: contractorProfile?.licenseState || "",
      licenseExpiration: contractorProfile?.licenseExpiration || "",
      generalLiability: contractorProfile?.generalLiability || "",
      workersComp: contractorProfile?.workersComp || "",
      autoInsurance: contractorProfile?.autoInsurance || "",
      bondingSingle: contractorProfile?.bondingSingle,
      bondingAggregate: contractorProfile?.bondingAggregate,
      emr: contractorProfile?.emr,
      bankReference: contractorProfile?.bankReference || "",
      suretyReference: contractorProfile?.suretyReference || "",
      creditRating: contractorProfile?.creditRating || "",
      paymentTerms: contractorProfile?.paymentTerms || "",
      litigationHistory: contractorProfile?.litigationHistory || "",
      projectReferences: contractorProfile?.projectReferences || "",
      yearsInBusiness: contractorProfile?.yearsInBusiness,
      specializations: contractorProfile?.specializations || [],
      awards: contractorProfile?.awards || "",
      environmentalPrograms: contractorProfile?.environmentalPrograms || "",
      workforceSize: contractorProfile?.workforceSize,
      workforceBreakdown: contractorProfile?.workforceBreakdown || "",
      equipment: contractorProfile?.equipment || "",
      availability: contractorProfile?.availability || "",
      keywordTags: contractorProfile?.keywordTags || [],
      preferredProjectTypes: contractorProfile?.preferredProjectTypes || [],
    },
  });

  const apiKeyForm = useForm({
    resolver: zodResolver(apiKeyFormSchema),
    defaultValues: {
      name: "",
      permissions: "read-only" as const,
      rateLimit: 100,
    },
  });

  const notificationForm = useForm({
    resolver: zodResolver(notificationSettingsSchema),
    defaultValues: {
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      bidAlerts: true,
      rfqAlerts: true,
      systemAlerts: true,
      weeklyDigest: false,
      marketingEmails: false,
    },
  });

  // Update form values when contractor profile data loads
  useEffect(() => {
    if (contractorProfile) {
      contractorForm.reset({
        companyName: contractorProfile.companyName || "",
        companyWebsite: contractorProfile.companyWebsite || "",
        legalStructure: contractorProfile.legalStructure || "",
        taxId: contractorProfile.taxId || "",
        dba: contractorProfile.dba || "",
        primaryAddress: contractorProfile.primaryAddress || "",
        mailingAddress: contractorProfile.mailingAddress || "",
        primaryContactName: contractorProfile.primaryContactName || "",
        primaryContactEmail: contractorProfile.primaryContactEmail || "",
        primaryContactPhone: contractorProfile.primaryContactPhone || "",
        tradeTypes: contractorProfile.tradeTypes || [],
        unionStatus: contractorProfile.unionStatus || "",
        unionAffiliations: contractorProfile.unionAffiliations || "",
        certifications: contractorProfile.certifications || [],
        serviceAreas: contractorProfile.serviceAreas || "",
        licenseNumber: contractorProfile.licenseNumber || "",
        licenseState: contractorProfile.licenseState || "",
        licenseExpiration: contractorProfile.licenseExpiration || "",
        generalLiability: contractorProfile.generalLiability || "",
        workersComp: contractorProfile.workersComp || "",
        autoInsurance: contractorProfile.autoInsurance || "",
        bondingSingle: contractorProfile.bondingSingle,
        bondingAggregate: contractorProfile.bondingAggregate,
        emr: contractorProfile.emr,
        bankReference: contractorProfile.bankReference || "",
        suretyReference: contractorProfile.suretyReference || "",
        creditRating: contractorProfile.creditRating || "",
        paymentTerms: contractorProfile.paymentTerms || "",
        litigationHistory: contractorProfile.litigationHistory || "",
        projectReferences: contractorProfile.projectReferences || "",
        yearsInBusiness: contractorProfile.yearsInBusiness,
        specializations: contractorProfile.specializations || [],
        awards: contractorProfile.awards || "",
        environmentalPrograms: contractorProfile.environmentalPrograms || "",
        workforceSize: contractorProfile.workforceSize,
        workforceBreakdown: contractorProfile.workforceBreakdown || "",
        equipment: contractorProfile.equipment || "",
        availability: contractorProfile.availability || "",
        keywordTags: contractorProfile.keywordTags || [],
        preferredProjectTypes: contractorProfile.preferredProjectTypes || [],
      });
    }
  }, [contractorProfile, contractorForm]);

  // Mutations
  const updateContractorMutation = useMutation({
    mutationFn: (data: any) => fetch("/api/contractors/profile", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    }).then(res => res.json()),
    onSuccess: () => {
      toast({ title: "Profile updated successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/contractors/profile"] });
    },
  });

  const createApiKeyMutation = useMutation({
    mutationFn: (data: any) => fetch("/api/api-keys", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    }).then(res => res.json()),
    onSuccess: (response: any) => {
      setNewApiKey(response.apiKey);
      setIsApiKeyDialogOpen(true);
      toast({ title: "API key created successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/api-keys"] });
      apiKeyForm.reset();
    },
  });

  const deleteApiKeyMutation = useMutation({
    mutationFn: (keyId: string) => fetch(`/api/api-keys/${keyId}`, {
      method: "DELETE",
    }).then(res => res.json()),
    onSuccess: () => {
      toast({ title: "API key deleted successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/api-keys"] });
    },
  });

  const updateNotificationsMutation = useMutation({
    mutationFn: (data: any) => fetch("/api/notification-settings", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    }).then(res => res.json()),
    onSuccess: () => {
      toast({ title: "Notification settings updated" });
      queryClient.invalidateQueries({ queryKey: ["/api/notification-settings"] });
    },
  });

  // Handlers
  const onContractorSubmit = (data: any) => {
    updateContractorMutation.mutate(data);
  };

  const onApiKeySubmit = (data: any) => {
    createApiKeyMutation.mutate(data);
  };

  const onNotificationSubmit = (data: any) => {
    updateNotificationsMutation.mutate(data);
  };

  const toggleCollapse = (section: string) => {
    setCollapsedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({ title: "Copied to clipboard" });
  };

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKey(prev => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  if (!isLoaded) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account, profile, and system preferences
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile">
            <User className="h-4 w-4 mr-2" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="api-keys">
            <Key className="h-4 w-4 mr-2" />
            API Keys
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="user-settings">
            <User className="h-4 w-4 mr-2" />
            User Settings
          </TabsTrigger>
          {/* {isSuperUserOrAdmin && (
            <TabsTrigger value="user-management">
              <Users className="h-4 w-4 mr-2" />
              Users
            </TabsTrigger>
          )} */}
          {isSuperUserOrAdmin && (
            <TabsTrigger value="audit-logs">
              <Activity className="h-4 w-4 mr-2" />
              Audit Logs
            </TabsTrigger>
          )}
          {isOrgAdmin && (
            <TabsTrigger value="organization">
              <Users className="h-4 w-4 mr-2" />
              Organization
            </TabsTrigger>
          )}
        </TabsList>

        {/* Contractor Profile Tab */}
        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Contractor Profile</CardTitle>
              <CardDescription>
                Complete your contractor profile to participate in RFQ processes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...contractorForm}>
                <form onSubmit={contractorForm.handleSubmit(onContractorSubmit)} className="space-y-6">
                  
                  {/* Business Identity & Contact - 10 fields */}
                  <Collapsible>
                    <CollapsibleTrigger
                      className="flex items-center justify-between w-full p-4 bg-gray-50 rounded-lg hover:bg-gray-100"
                      onClick={() => toggleCollapse('business')}
                    >
                      <h3 className="text-lg font-semibold">Business Identity & Contact</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">10 fields</span>
                        {collapsedSections.business ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="companyName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Company Name *</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Enter company name" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="companyWebsite"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Company Website</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="https://example.com" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="legalStructure"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Legal Structure</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select structure" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="llc">LLC</SelectItem>
                                  <SelectItem value="corporation">Corporation</SelectItem>
                                  <SelectItem value="partnership">Partnership</SelectItem>
                                  <SelectItem value="sole_proprietorship">Sole Proprietorship</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="taxId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Tax ID / EIN</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="XX-XXXXXXX" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="dba"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>DBA (Doing Business As)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Enter DBA name" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="primaryContactName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Contact Name</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Contact person name" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="primaryContactEmail"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Contact Email</FormLabel>
                              <FormControl>
                                <Input {...field} type="email" placeholder="<EMAIL>" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="primaryContactPhone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Contact Phone</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="(*************" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="space-y-4">
                        <FormField
                          control={contractorForm.control}
                          name="primaryAddress"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Business Address</FormLabel>
                              <FormControl>
                                <Textarea {...field} placeholder="Full business address" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="mailingAddress"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Mailing Address (if different)</FormLabel>
                              <FormControl>
                                <Textarea {...field} placeholder="Mailing address" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Classification & Capability - 5 fields */}
                  <Collapsible>
                    <CollapsibleTrigger
                      className="flex items-center justify-between w-full p-4 bg-gray-50 rounded-lg hover:bg-gray-100"
                      onClick={() => toggleCollapse('classification')}
                    >
                      <h3 className="text-lg font-semibold">Classification & Capability</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">5 fields</span>
                        {collapsedSections.classification ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <FormField
                        control={contractorForm.control}
                        name="tradeTypes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Trade Types *</FormLabel>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                              {tradeOptions.map((trade) => (
                                <div key={trade.value} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={trade.value}
                                    checked={field.value?.includes(trade.value)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        field.onChange([...field.value, trade.value]);
                                      } else {
                                        field.onChange(field.value?.filter((v: string) => v !== trade.value));
                                      }
                                    }}
                                  />
                                  <Label htmlFor={trade.value} className="text-sm">
                                    {trade.label}
                                  </Label>
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="unionStatus"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Union Status</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="union">Union</SelectItem>
                                  <SelectItem value="non_union">Non-Union</SelectItem>
                                  <SelectItem value="mixed">Mixed</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="unionAffiliations"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Union Affiliations</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Union names/local numbers" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={contractorForm.control}
                        name="certifications"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Certifications</FormLabel>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {certificationOptions.map((cert) => (
                                <div key={cert.value} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={cert.value}
                                    checked={field.value?.includes(cert.value)}
                                    onCheckedChange={(checked) => {
                                      const currentValue = field.value || [];
                                      if (checked) {
                                        field.onChange([...currentValue, cert.value]);
                                      } else {
                                        field.onChange(currentValue.filter((v: string) => v !== cert.value));
                                      }
                                    }}
                                  />
                                  <Label htmlFor={cert.value} className="text-sm">
                                    {cert.label}
                                  </Label>
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={contractorForm.control}
                        name="serviceAreas"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Service Areas</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Geographic areas you serve (cities, counties, states)" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Credentials & Compliance - 9 fields */}
                  <Collapsible>
                    <CollapsibleTrigger
                      className="flex items-center justify-between w-full p-4 bg-gray-50 rounded-lg hover:bg-gray-100"
                      onClick={() => toggleCollapse('credentials')}
                    >
                      <h3 className="text-lg font-semibold">Credentials & Compliance</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">9 fields</span>
                        {collapsedSections.credentials ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="licenseNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>License Number</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="License #" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="licenseState"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>License State</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="State" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="licenseExpiration"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>License Expiration</FormLabel>
                              <FormControl>
                                <Input {...field} type="date" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="generalLiability"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>General Liability ($)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="e.g., 2,000,000" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="workersComp"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Workers' Comp ($)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="e.g., 1,000,000" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="autoInsurance"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Auto Insurance ($)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="e.g., 1,000,000" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="bondingSingle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bonding Single ($)</FormLabel>
                              <FormControl>
                                <Input 
                                  {...field} 
                                  type="number" 
                                  placeholder="e.g., 5000000"
                                  onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="bondingAggregate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bonding Aggregate ($)</FormLabel>
                              <FormControl>
                                <Input 
                                  {...field} 
                                  type="number" 
                                  placeholder="e.g., 10000000"
                                  onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="emr"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>EMR (Experience Mod Rate)</FormLabel>
                              <FormControl>
                                <Input 
                                  {...field} 
                                  type="number" 
                                  step="0.01"
                                  placeholder="e.g., 0.95"
                                  onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Financial & Reference Data - 6 fields */}
                  <Collapsible>
                    <CollapsibleTrigger
                      className="flex items-center justify-between w-full p-4 bg-gray-50 rounded-lg hover:bg-gray-100"
                      onClick={() => toggleCollapse('financial')}
                    >
                      <h3 className="text-lg font-semibold">Financial & Reference Data</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">6 fields</span>
                        {collapsedSections.financial ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="bankReference"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bank Reference</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Bank name and contact" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="suretyReference"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Surety Reference</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Surety company and contact" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="creditRating"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Credit Rating</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="D&B rating or credit score" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="paymentTerms"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Payment Terms</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="e.g., Net 30, 2/10 Net 30" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={contractorForm.control}
                        name="litigationHistory"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Litigation History</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Any significant legal matters or disputes" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={contractorForm.control}
                        name="projectReferences"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Project References</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Recent project references with contact information" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Performance & Experience - 4 fields */}
                  <Collapsible>
                    <CollapsibleTrigger
                      className="flex items-center justify-between w-full p-4 bg-gray-50 rounded-lg hover:bg-gray-100"
                      onClick={() => toggleCollapse('performance')}
                    >
                      <h3 className="text-lg font-semibold">Performance & Experience</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">4 fields</span>
                        {collapsedSections.performance ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="yearsInBusiness"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Years in Business</FormLabel>
                              <FormControl>
                                <Input 
                                  {...field} 
                                  type="number" 
                                  placeholder="Number of years"
                                  onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="specializations"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Specializations</FormLabel>
                              <div className="grid grid-cols-1 gap-2">
                                {specializationOptions.map((spec) => (
                                  <div key={spec.value} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={spec.value}
                                      checked={field.value?.includes(spec.value)}
                                      onCheckedChange={(checked) => {
                                        const currentValue = field.value || [];
                                        if (checked) {
                                          field.onChange([...currentValue, spec.value]);
                                        } else {
                                          field.onChange(currentValue.filter((v: string) => v !== spec.value));
                                        }
                                      }}
                                    />
                                    <Label htmlFor={spec.value} className="text-sm">
                                      {spec.label}
                                    </Label>
                                  </div>
                                ))}
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={contractorForm.control}
                        name="awards"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Awards & Recognition</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Industry awards, certifications, recognition" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={contractorForm.control}
                        name="environmentalPrograms"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Environmental Programs</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Environmental initiatives, green building programs" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Operational Details - 4 fields */}
                  <Collapsible>
                    <CollapsibleTrigger
                      className="flex items-center justify-between w-full p-4 bg-gray-50 rounded-lg hover:bg-gray-100"
                      onClick={() => toggleCollapse('operational')}
                    >
                      <h3 className="text-lg font-semibold">Operational Details</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">4 fields</span>
                        {collapsedSections.operational ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={contractorForm.control}
                          name="workforceSize"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Workforce Size</FormLabel>
                              <FormControl>
                                <Input 
                                  {...field} 
                                  type="number" 
                                  placeholder="Number of employees"
                                  onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={contractorForm.control}
                          name="availability"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Current Availability</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select availability" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="immediately">Immediately Available</SelectItem>
                                  <SelectItem value="30_days">Available in 30 Days</SelectItem>
                                  <SelectItem value="60_days">Available in 60 Days</SelectItem>
                                  <SelectItem value="90_days">Available in 90+ Days</SelectItem>
                                  <SelectItem value="limited">Limited Availability</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={contractorForm.control}
                        name="workforceBreakdown"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Workforce Breakdown</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Employee breakdown by role/trade (e.g., 10 carpenters, 5 electricians)" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={contractorForm.control}
                        name="equipment"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Major Equipment</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="List of major equipment and machinery owned" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Custom Tags & Preferences - 2 fields */}
                  <Collapsible>
                    <CollapsibleTrigger
                      className="flex items-center justify-between w-full p-4 bg-gray-50 rounded-lg hover:bg-gray-100"
                      onClick={() => toggleCollapse('custom')}
                    >
                      <h3 className="text-lg font-semibold">Custom Tags & Preferences</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">2 fields</span>
                        {collapsedSections.custom ? <ChevronRight /> : <ChevronDown />}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4">
                      <FormField
                        control={contractorForm.control}
                        name="keywordTags"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Keyword Tags</FormLabel>
                            <FormControl>
                              <Input 
                                {...field} 
                                placeholder="Enter tags separated by commas (e.g., green building, historic renovation)"
                                onChange={(e) => {
                                  const tags = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag);
                                  field.onChange(tags);
                                }}
                                value={field.value?.join(', ') || ''}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={contractorForm.control}
                        name="preferredProjectTypes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Preferred Project Types</FormLabel>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                              {projectTypeOptions.map((type) => (
                                <div key={type.value} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={type.value}
                                    checked={field.value?.includes(type.value)}
                                    onCheckedChange={(checked) => {
                                      const currentValue = field.value || [];
                                      if (checked) {
                                        field.onChange([...currentValue, type.value]);
                                      } else {
                                        field.onChange(currentValue.filter((v: string) => v !== type.value));
                                      }
                                    }}
                                  />
                                  <Label htmlFor={type.value} className="text-sm">
                                    {type.label}
                                  </Label>
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CollapsibleContent>
                  </Collapsible>

                  <div className="flex justify-end pt-6 border-t">
                    <Button 
                      type="submit" 
                      disabled={updateContractorMutation.isPending}
                      className="flex items-center gap-2"
                    >
                      <Save className="h-4 w-4" />
                      {updateContractorMutation.isPending ? "Saving..." : "Save Profile"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API Keys Tab */}
        <TabsContent value="api-keys" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>API Keys</CardTitle>
                <CardDescription>
                  Manage your API keys for programmatic access
                </CardDescription>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Create API Key
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New API Key</DialogTitle>
                    <DialogDescription>
                      Generate a new API key for external integrations
                    </DialogDescription>
                  </DialogHeader>
                  <Form {...apiKeyForm}>
                    <form onSubmit={apiKeyForm.handleSubmit(onApiKeySubmit)} className="space-y-4">
                      <FormField
                        control={apiKeyForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Key Name</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="e.g., Production API" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={apiKeyForm.control}
                        name="permissions"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Permissions</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="read-only">Read Only</SelectItem>
                                <SelectItem value="upload-only">Upload Only</SelectItem>
                                <SelectItem value="full-access">Full Access</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={apiKeyForm.control}
                        name="rateLimit"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Rate Limit (requests/hour)</FormLabel>
                            <FormControl>
                              <Input 
                                {...field} 
                                type="number" 
                                onChange={(e) => field.onChange(parseInt(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <DialogFooter>
                        <Button type="submit" disabled={createApiKeyMutation.isPending}>
                          {createApiKeyMutation.isPending ? "Creating..." : "Create Key"}
                        </Button>
                      </DialogFooter>
                    </form>
                  </Form>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {!apiKeys || apiKeys.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  No API keys created yet. Create your first API key to get started.
                </p>
              ) : (
                <div className="space-y-4">
                  {apiKeys && apiKeys.map((key: any) => (
                    <div key={key.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">{key.name}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline">{key.permissions}</Badge>
                            <span className="text-sm text-muted-foreground">
                              {key.rateLimit} req/hour
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleApiKeyVisibility(key.id)}
                          >
                            {showApiKey[key.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(key.keyPreview)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-600">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete API Key</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete this API key? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteApiKeyMutation.mutate(key.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                      {showApiKey[key.id] && (
                        <div className="mt-2 p-2 bg-gray-50 rounded font-mono text-sm">
                          {key.keyPreview}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...notificationForm}>
                <form onSubmit={notificationForm.handleSubmit(onNotificationSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <FormField
                      control={notificationForm.control}
                      name="emailNotifications"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between">
                          <div>
                            <FormLabel>Email Notifications</FormLabel>
                            <p className="text-sm text-muted-foreground">
                              Receive notifications via email
                            </p>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="bidAlerts"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between">
                          <div>
                            <FormLabel>Bid Alerts</FormLabel>
                            <p className="text-sm text-muted-foreground">
                              Get notified about new bid opportunities
                            </p>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="rfqAlerts"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between">
                          <div>
                            <FormLabel>RFQ Alerts</FormLabel>
                            <p className="text-sm text-muted-foreground">
                              Get notified about RFQ updates and responses
                            </p>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="systemAlerts"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between">
                          <div>
                            <FormLabel>System Alerts</FormLabel>
                            <p className="text-sm text-muted-foreground">
                              Important system notifications and updates
                            </p>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex justify-end">
                    <Button type="submit" disabled={updateNotificationsMutation.isPending}>
                      {updateNotificationsMutation.isPending ? "Saving..." : "Save Settings"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Settings Tab - Clerk Component */}
        <TabsContent value="user-settings" className="space-y-6">
          {!hasOrganizations && (
            <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <CardContent className="pt-6">
                <div className="flex items-start gap-4">
                  <Users className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="font-semibold text-blue-900">Create Your Organization</h3>
                    <p className="text-sm text-blue-700 mb-4">
                      Create an organization to access team features and user management.
                    </p>
                    <CreateOrganization 
                      appearance={{
                        elements: {
                          rootBox: "w-full",
                          card: "shadow-none border-0 bg-transparent",
                        }
                      }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Settings
              </CardTitle>
              <CardDescription>
                Manage your personal account settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserProfile 
                routing="hash"
                appearance={{
                  elements: {
                    rootBox: "w-full",
                    card: "shadow-none border border-gray-200 rounded-lg",
                    headerTitle: "text-lg font-semibold",
                    headerSubtitle: "text-sm text-muted-foreground",
                  }
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Management Tab */}
        {/* {isSuperUserOrAdmin && (
          <TabsContent value="user-management" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
                <CardDescription>
                  Manage system users and their permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!users || users.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    No users found.
                  </p>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Active</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users && users.map((user: any) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            {user.firstName} {user.lastName}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{user.role || 'User'}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">Active</Badge>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {user.lastActive ? new Date(user.lastActive).toLocaleDateString() : 'Never'}
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )} */}

        {/* Audit Logs Tab */}
        {isSuperUserOrAdmin && (
          <TabsContent value="audit-logs" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Audit Logs</CardTitle>
                <CardDescription>
                  System activity and security audit trail
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!auditLogs || auditLogs.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    No audit logs found.
                  </p>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Resource</TableHead>
                        <TableHead>IP Address</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {auditLogs && auditLogs.map((log: any) => (
                        <TableRow key={log.id}>
                          <TableCell className="text-sm">
                            {new Date(log.createdAt).toLocaleString()}
                          </TableCell>
                          <TableCell>{log.userEmail}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{log.action}</Badge>
                          </TableCell>
                          <TableCell>{log.resourceType}</TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {log.ipAddress || '-'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Organization Tab - Clerk Component */}
        {isOrgAdmin && (
          <TabsContent value="organization" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Organization Management
                </CardTitle>
                <CardDescription>
                  Manage organization settings, members, and permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OrganizationProfile 
                  routing="hash"
                  appearance={{
                    elements: {
                      rootBox: "w-full",
                      card: "shadow-none border border-gray-200 rounded-lg",
                      headerTitle: "text-lg font-semibold",
                      headerSubtitle: "text-sm text-muted-foreground",
                    }
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>

      {/* API Key Display Dialog */}
      <Dialog open={isApiKeyDialogOpen} onOpenChange={setIsApiKeyDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>API Key Created</DialogTitle>
            <DialogDescription>
              Your new API key has been generated. Please copy and store it securely.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium mb-2">API Key:</p>
              <div className="flex items-center gap-2">
                <code className="flex-1 p-2 bg-white rounded border text-sm font-mono break-all">
                  {newApiKey}
                </code>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => newApiKey && copyToClipboard(newApiKey)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Important:</p>
                <p>This is the only time you'll see this key. Make sure to copy and store it securely.</p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsApiKeyDialogOpen(false)}>
              I've Saved the Key
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}