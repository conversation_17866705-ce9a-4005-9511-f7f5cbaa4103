import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Download, ExternalLink } from 'lucide-react';

interface SimplePDFViewerProps {
  documentId: string;
  fileName: string;
  onDownload?: () => void;
}

export function SimplePDFViewer({ documentId, fileName, onDownload }: SimplePDFViewerProps) {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Instead of fetching and creating blob URL, use direct URL to iframe
    const directUrl = `/api/files/${documentId}?view=true`;
    setPdfUrl(directUrl);
    setLoading(false);
  }, [documentId]);

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      // Default download behavior
      const link = document.createElement('a');
      link.href = `/api/files/${documentId}`;
      link.download = fileName;
      link.click();
    }
  };

  const handleOpenInNewTab = () => {
    if (pdfUrl) {
      window.open(pdfUrl, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading PDF...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <Button onClick={handleDownload} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download File Instead
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Compact PDF Controls */}
      <div className="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium truncate">
            {fileName}
          </span>
        </div>

        <div className="flex items-center gap-1">
          <Button onClick={handleDownload} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Download
          </Button>
          
          <Button onClick={handleOpenInNewTab} variant="ghost" size="sm">
            <ExternalLink className="h-4 w-4 mr-1" />
            New Tab
          </Button>
        </div>
      </div>

      {/* PDF Document - Full Height */}
      <div className="flex-1 overflow-hidden bg-gray-100 dark:bg-gray-900">
        {pdfUrl ? (
          <iframe
            src={`${pdfUrl}#view=FitH`}
            className="w-full h-full border-0"
            title={`PDF Viewer - ${fileName}`}
            style={{ height: '100%', minHeight: '600px' }}
            sandbox="allow-same-origin allow-scripts"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 text-gray-400">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
              </div>
              <p className="text-gray-600 dark:text-gray-300">Loading PDF...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}