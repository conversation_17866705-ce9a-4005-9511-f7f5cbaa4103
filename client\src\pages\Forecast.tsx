import { useState, useEffect } from "react";
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Search, RefreshCw } from "lucide-react";
import { TrendingUp, TrendingDown, Filter, ChevronDown } from "lucide-react";

// Construction materials organized by standard cost codes
const materialsData = [
  {
    category: "03 – Concrete",
    items: [
      {
        id: "03.01",
        name: "READY-MIX CONCRETE",
        unit: "[yd³]",
        value: "$165.00",
        netChange: "+$2.50",
        percentChange: "+1.54%",
        time: "11:15",
        ytd: "18.5%",
        ytdC: "18.5%",
        trend: "up",
        chartPoints: "0,20 10,18 20,22 30,19 40,17 50,15 60,18 70,16 80,14 90,12 100,10"
      },
      {
        id: "03.02",
        name: "HIGH STRENGTH CONCRETE",
        unit: "[yd³]",
        value: "$195.00",
        netChange: "+$3.75",
        percentChange: "+1.96%",
        time: "11:17",
        ytd: "22.1%",
        ytdC: "22.1%",
        trend: "up",
        chartPoints: "0,15 10,14 20,16 30,15 40,14 50,13 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "03.03",
        name: "CONCRETE ADMIXTURE",
        unit: "[gal]",
        value: "$15.50",
        netChange: "+$0.45",
        percentChange: "+2.99%",
        time: "11:27",
        ytd: "16.4%",
        ytdC: "16.4%",
        trend: "up",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,18 60,16 70,17 80,19 90,20 100,22"
      },
      {
        id: "03.04",
        name: "PRECAST CONCRETE PANEL",
        unit: "[sq ft]",
        value: "$12.50",
        netChange: "+$0.65",
        percentChange: "+5.48%",
        time: "11:21",
        ytd: "22.1%",
        ytdC: "22.1%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,16 40,15 50,14 60,13 70,14 80,15 90,16 100,17"
      }
    ]
  },
  {
    category: "04 – Masonry",
    items: [
      {
        id: "04.01",
        name: "CMU BLOCK 8IN",
        unit: "[block]",
        value: "$2.85",
        netChange: "+$0.12",
        percentChange: "+4.40%",
        time: "11:17",
        ytd: "12.8%",
        ytdC: "12.8%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,16 40,15 50,14 60,13 70,14 80,15 90,16 100,17"
      },
      {
        id: "04.02",
        name: "CMU BLOCK 12IN",
        unit: "[block]",
        value: "$4.25",
        netChange: "+$0.18",
        percentChange: "+4.42%",
        time: "11:19",
        ytd: "15.2%",
        ytdC: "15.2%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "04.03",
        name: "FACE BRICK",
        unit: "[thousand]",
        value: "$485.00",
        netChange: "+$18.50",
        percentChange: "+3.97%",
        time: "11:13",
        ytd: "19.8%",
        ytdC: "19.8%",
        trend: "up",
        chartPoints: "0,15 10,14 20,16 30,15 40,14 50,13 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "04.04",
        name: "GROUT MIX",
        unit: "[bag]",
        value: "$8.95",
        netChange: "+$0.25",
        percentChange: "+2.87%",
        time: "11:25",
        ytd: "9.7%",
        ytdC: "9.7%",
        trend: "up",
        chartPoints: "0,12 10,11 20,13 30,14 40,15 50,16 60,17 70,18 80,17 90,16 100,15"
      }
    ]
  },
  {
    category: "05 – Metals",
    items: [
      {
        id: "05.01",
        name: "W14x48 WIDE FLANGE",
        unit: "[lb/ft]",
        value: "$0.98",
        netChange: "-$0.14",
        percentChange: "-12.5%",
        time: "11:20",
        ytd: "8.5%",
        ytdC: "8.5%",
        trend: "down",
        chartPoints: "0,20 10,18 20,22 30,19 40,17 50,15 60,18 70,16 80,14 90,12 100,10"
      },
      {
        id: "05.02",
        name: "W16x26 WIDE FLANGE",
        unit: "[lb/ft]",
        value: "$1.05",
        netChange: "+$0.08",
        percentChange: "+8.25%",
        time: "11:22",
        ytd: "12.3%",
        ytdC: "12.3%",
        trend: "up",
        chartPoints: "0,15 10,14 20,16 30,15 40,14 50,13 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "05.03",
        name: "STEEL DECK 20GA",
        unit: "[sq ft]",
        value: "$2.34",
        netChange: "+$0.12",
        percentChange: "+5.41%",
        time: "11:25",
        ytd: "15.2%",
        ytdC: "15.2%",
        trend: "up",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,18 60,16 70,17 80,19 90,20 100,22"
      },
      {
        id: "05.04",
        name: "STEEL STUDS 16GA",
        unit: "[ft]",
        value: "$0.85",
        netChange: "+$0.03",
        percentChange: "+3.66%",
        time: "11:19",
        ytd: "7.4%",
        ytdC: "7.4%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,16 40,15 50,14 60,13 70,14 80,15 90,16 100,17"
      },
      {
        id: "05.05",
        name: "STRUCTURAL BOLTS A325",
        unit: "[lb]",
        value: "$3.45",
        netChange: "+$0.15",
        percentChange: "+4.55%",
        time: "11:17",
        ytd: "11.2%",
        ytdC: "11.2%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "05.06",
        name: "STEEL MESH 6X6",
        unit: "[sq ft]",
        value: "$0.65",
        netChange: "-$0.02",
        percentChange: "-2.99%",
        time: "11:21",
        ytd: "4.8%",
        ytdC: "4.8%",
        trend: "down",
        chartPoints: "0,15 10,14 20,13 30,12 40,11 50,10 60,11 70,12 80,11 90,10 100,9"
      }
    ]
  },
  {
    category: "06 – Woods, Plastics, and Composites",
    items: [
      {
        id: "06.01",
        name: "2x4 SPF STUD",
        unit: "[board ft]",
        value: "$0.95",
        netChange: "-$0.08",
        percentChange: "-7.78%",
        time: "11:16",
        ytd: "-5.3%",
        ytdC: "-5.3%",
        trend: "down",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,18 60,16 70,17 80,19 90,20 100,22"
      },
      {
        id: "06.02",
        name: "2x8 SPF",
        unit: "[board ft]",
        value: "$1.45",
        netChange: "+$0.12",
        percentChange: "+9.02%",
        time: "11:24",
        ytd: "3.2%",
        ytdC: "3.2%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "06.03",
        name: "OSB 7/16",
        unit: "[sq ft]",
        value: "$0.85",
        netChange: "-$0.03",
        percentChange: "-3.41%",
        time: "11:14",
        ytd: "-8.1%",
        ytdC: "-8.1%",
        trend: "down",
        chartPoints: "0,15 10,16 20,17 30,18 40,17 50,16 60,15 70,14 80,13 90,12 100,11"
      },
      {
        id: "06.04",
        name: "PLYWOOD 3/4",
        unit: "[sq ft]",
        value: "$2.15",
        netChange: "+$0.07",
        percentChange: "+3.36%",
        time: "11:26",
        ytd: "4.5%",
        ytdC: "4.5%",
        trend: "up",
        chartPoints: "0,15 10,14 20,13 30,12 40,11 50,10 60,11 70,12 80,11 90,10 100,9"
      }
    ]
  },
  {
    category: "07 – Thermal and Moisture Protection",
    items: [
      {
        id: "07.01",
        name: "RIGID FOAM XPS 2IN",
        unit: "[board ft]",
        value: "$1.25",
        netChange: "+$0.08",
        percentChange: "+6.84%",
        time: "11:14",
        ytd: "13.5%",
        ytdC: "13.5%",
        trend: "up",
        chartPoints: "0,15 10,14 20,13 30,12 40,11 50,10 60,11 70,12 80,11 90,10 100,9"
      },
      {
        id: "07.02",
        name: "FIBERGLASS BATT R-13",
        unit: "[sq ft]",
        value: "$0.45",
        netChange: "-$0.02",
        percentChange: "-4.26%",
        time: "11:16",
        ytd: "6.2%",
        ytdC: "6.2%",
        trend: "down",
        chartPoints: "0,12 10,11 20,13 30,14 40,15 50,16 60,17 70,18 80,17 90,16 100,15"
      },
      {
        id: "07.03",
        name: "TPO ROOFING MEMBRANE",
        unit: "[sq ft]",
        value: "$3.85",
        netChange: "+$0.18",
        percentChange: "+4.90%",
        time: "11:20",
        ytd: "14.3%",
        ytdC: "14.3%",
        trend: "up",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,18 60,16 70,17 80,19 90,20 100,22"
      },
      {
        id: "07.04",
        name: "WATERPROOFING MEMBRANE",
        unit: "[sq ft]",
        value: "$2.95",
        netChange: "+$0.07",
        percentChange: "+2.43%",
        time: "11:22",
        ytd: "8.9%",
        ytdC: "8.9%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,16 40,15 50,14 60,13 70,14 80,15 90,16 100,17"
      }
    ]
  },
  {
    category: "08 – Openings",
    items: [
      {
        id: "08.01",
        name: "CURTAIN WALL SYSTEM",
        unit: "[sq ft]",
        value: "$45.50",
        netChange: "+$1.85",
        percentChange: "+4.24%",
        time: "11:12",
        ytd: "24.7%",
        ytdC: "24.7%",
        trend: "up",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,22 60,21 70,20 80,19 90,18 100,17"
      },
      {
        id: "08.02",
        name: "INSULATED GLASS UNITS",
        unit: "[sq ft]",
        value: "$8.95",
        netChange: "+$0.35",
        percentChange: "+4.07%",
        time: "11:14",
        ytd: "17.2%",
        ytdC: "17.2%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "08.03",
        name: "ALUMINUM STOREFRONT",
        unit: "[ft]",
        value: "$28.50",
        netChange: "+$1.25",
        percentChange: "+4.58%",
        time: "11:18",
        ytd: "21.3%",
        ytdC: "21.3%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,18 40,17 50,16 60,15 70,14 80,13 90,12 100,11"
      }
    ]
  },
  {
    category: "09 – Finishes",
    items: [
      {
        id: "09.01",
        name: "DRYWALL 1/2\"",
        unit: "[sheet]",
        value: "$12.85",
        netChange: "+$0.22",
        percentChange: "+1.74%",
        time: "09:50",
        ytd: "8.95%",
        ytdC: "11.20%",
        trend: "up",
        chartPoints: "0,15 10,14 20,13 30,12 40,11 50,10 60,11 70,12 80,11 90,10 100,9"
      },
      {
        id: "09.02",
        name: "PAINT INTERIOR",
        unit: "[gal]",
        value: "$42.75",
        netChange: "+$0.85",
        percentChange: "+2.03%",
        time: "10:21",
        ytd: "14.20%",
        ytdC: "18.75%",
        trend: "up",
        chartPoints: "0,12 10,11 20,13 30,14 40,15 50,16 60,17 70,18 80,17 90,16 100,15"
      },
      {
        id: "09.03",
        name: "CARPET COMMERCIAL",
        unit: "[sq yd]",
        value: "$18.50",
        netChange: "+$0.35",
        percentChange: "+1.93%",
        time: "10:45",
        ytd: "7.65%",
        ytdC: "9.85%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "09.04",
        name: "CERAMIC TILE",
        unit: "[sq ft]",
        value: "$8.95",
        netChange: "+$0.12",
        percentChange: "+1.36%",
        time: "10:52",
        ytd: "11.40%",
        ytdC: "14.85%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,18 40,17 50,16 60,15 70,14 80,13 90,12 100,11"
      }
    ]
  },
  {
    category: "10 – Specialties",
    items: [
      {
        id: "10.01",
        name: "RAISED ACCESS FLOORING",
        unit: "[sq ft]",
        value: "$8.95",
        netChange: "+$0.35",
        percentChange: "+4.07%",
        time: "11:10",
        ytd: "20.1%",
        ytdC: "20.1%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,16 40,15 50,14 60,13 70,14 80,15 90,16 100,17"
      },
      {
        id: "10.02",
        name: "SUSPENDED CEILING GRID",
        unit: "[sq ft]",
        value: "$1.85",
        netChange: "+$0.08",
        percentChange: "+4.51%",
        time: "11:12",
        ytd: "12.3%",
        ytdC: "12.3%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "10.03",
        name: "FIREPROOFING SPRAY",
        unit: "[sq ft]",
        value: "$2.45",
        netChange: "+$0.12",
        percentChange: "+5.15%",
        time: "11:14",
        ytd: "17.5%",
        ytdC: "17.5%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "10.04",
        name: "RAISED ACCESS FLOORING",
        unit: "[sq ft]",
        value: "$8.95",
        netChange: "+$0.35",
        percentChange: "+4.07%",
        time: "11:10",
        ytd: "20.1%",
        ytdC: "20.1%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,16 40,15 50,14 60,13 70,14 80,15 90,16 100,17"
      }
    ]
  },
  {
    category: "22 – Plumbing",
    items: [
      {
        id: "22.01",
        name: "COPPER PIPE 3/4\"",
        unit: "[ft]",
        value: "$4.62",
        netChange: "-$0.08",
        percentChange: "-1.70%",
        time: "11:15",
        ytd: "-8.90%",
        ytdC: "-12.45%",
        trend: "down",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,22 60,21 70,20 80,19 90,18 100,17"
      },
      {
        id: "22.02",
        name: "PVC PIPE 4\"",
        unit: "[ft]",
        value: "$8.45",
        netChange: "+$0.12",
        percentChange: "+1.44%",
        time: "11:17",
        ytd: "12.30%",
        ytdC: "15.85%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "22.03",
        name: "FIRE SPRINKLER PIPE",
        unit: "[ft]",
        value: "$4.85",
        netChange: "+$0.18",
        percentChange: "+3.85%",
        time: "11:19",
        ytd: "15.4%",
        ytdC: "15.4%",
        trend: "up",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,18 60,16 70,17 80,19 90,20 100,22"
      }
    ]
  },
  {
    category: "23 – HVAC",
    items: [
      {
        id: "23.01",
        name: "GALVANIZED DUCTWORK",
        unit: "[lb]",
        value: "$2.85",
        netChange: "+$0.12",
        percentChange: "+4.39%",
        time: "11:11",
        ytd: "16.8%",
        ytdC: "16.8%",
        trend: "up",
        chartPoints: "0,15 10,14 20,13 30,12 40,11 50,10 60,11 70,12 80,11 90,10 100,9"
      },
      {
        id: "23.02",
        name: "RTU 10 TON",
        unit: "[unit]",
        value: "$1,850.00",
        netChange: "+$85.00",
        percentChange: "+4.81%",
        time: "11:13",
        ytd: "25.2%",
        ytdC: "25.2%",
        trend: "up",
        chartPoints: "0,15 10,14 20,13 30,12 40,11 50,10 60,11 70,12 80,11 90,10 100,9"
      },
      {
        id: "23.03",
        name: "CHILLED WATER PIPE",
        unit: "[ft]",
        value: "$15.50",
        netChange: "+$0.65",
        percentChange: "+4.37%",
        time: "11:15",
        ytd: "18.9%",
        ytdC: "18.9%",
        trend: "up",
        chartPoints: "0,12 10,11 20,13 30,14 40,15 50,16 60,17 70,18 80,17 90,16 100,15"
      }
    ]
  },
  {
    category: "26 – Electrical",
    items: [
      {
        id: "26.01",
        name: "12 AWG THHN WIRE",
        unit: "[ft]",
        value: "$0.89",
        netChange: "+$0.05",
        percentChange: "+5.95%",
        time: "11:13",
        ytd: "15.8%",
        ytdC: "15.8%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,18 40,17 50,16 60,15 70,14 80,13 90,12 100,11"
      },
      {
        id: "26.02",
        name: "1/2 EMT CONDUIT",
        unit: "[ft]",
        value: "$1.25",
        netChange: "-$0.08",
        percentChange: "-6.02%",
        time: "11:27",
        ytd: "8.9%",
        ytdC: "8.9%",
        trend: "down",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,22 60,21 70,20 80,19 90,18 100,17"
      },
      {
        id: "26.03",
        name: "PANEL 200A MAIN",
        unit: "[unit]",
        value: "$485.00",
        netChange: "+$12.50",
        percentChange: "+2.65%",
        time: "11:12",
        ytd: "18.2%",
        ytdC: "18.2%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "26.04",
        name: "SOLAR PANEL 300W",
        unit: "[watt]",
        value: "$0.68",
        netChange: "-$0.02",
        percentChange: "-2.86%",
        time: "11:20",
        ytd: "-3.2%",
        ytdC: "-3.2%",
        trend: "down",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,22 60,21 70,20 80,19 90,18 100,17"
      }
    ]
  },
  {
    category: "31 – Earthwork",
    items: [
      {
        id: "31.01",
        name: "CRUSHED STONE #57",
        unit: "[ton]",
        value: "$28.50",
        netChange: "+$0.75",
        percentChange: "+2.70%",
        time: "11:09",
        ytd: "12.8%",
        ytdC: "12.8%",
        trend: "up",
        chartPoints: "0,15 10,14 20,16 30,15 40,14 50,13 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "31.02",
        name: "STRUCTURAL FILL SAND",
        unit: "[yd³]",
        value: "$35.50",
        netChange: "+$1.25",
        percentChange: "+3.65%",
        time: "11:15",
        ytd: "16.2%",
        ytdC: "16.2%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "31.03",
        name: "GEOTEXTILE FABRIC",
        unit: "[sq yd]",
        value: "$1.25",
        netChange: "+$0.05",
        percentChange: "+4.17%",
        time: "11:17",
        ytd: "8.9%",
        ytdC: "8.9%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,18 40,17 50,16 60,15 70,14 80,13 90,12 100,11"
      },
      {
        id: "26.04",
        name: "SOLAR PANEL 300W",
        unit: "[watt]",
        value: "$0.68",
        netChange: "-$0.02",
        percentChange: "-2.86%",
        time: "11:20",
        ytd: "-3.2%",
        ytdC: "-3.2%",
        trend: "down",
        chartPoints: "0,10 10,12 20,15 30,18 40,20 50,22 60,21 70,20 80,19 90,18 100,17"
      }
    ]
  },
  {
    category: "32 – Exterior Improvements",
    items: [
      {
        id: "32.01",
        name: "ASPHALT PAVING",
        unit: "[ton]",
        value: "$85.00",
        netChange: "+$3.25",
        percentChange: "+3.98%",
        time: "11:11",
        ytd: "18.9%",
        ytdC: "18.9%",
        trend: "up",
        chartPoints: "0,20 10,19 20,18 30,17 40,16 50,15 60,14 70,13 80,12 90,11 100,10"
      },
      {
        id: "32.02",
        name: "CONCRETE PAVING",
        unit: "[sq ft]",
        value: "$4.85",
        netChange: "+$0.18",
        percentChange: "+3.85%",
        time: "11:13",
        ytd: "14.7%",
        ytdC: "14.7%",
        trend: "up",
        chartPoints: "0,15 10,16 20,17 30,18 40,17 50,16 60,15 70,14 80,13 90,12 100,11"
      }
    ]
  }
];

export default function MatIQ() {
  const [activeFilters, setActiveFilters] = useState({
    trending: false,
    volatility: false,
    regional: false
  });
  const [timeRange, setTimeRange] = useState("1W");
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch stored forecast materials from database
  const { data: storedMaterials, isLoading } = useQuery({
    queryKey: ["/api/forecast/materials"],
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
  });

  // Search materials using Perplexity API
  const searchMutation = useMutation({
    mutationFn: async ({ materialName, unit }: { materialName: string; unit: string }) => {
      return await apiRequest("POST", `/api/forecast/search`, { materialName, unit });
    },
    onSuccess: (data) => {
      toast({
        title: "Material Search Complete",
        description: `Found pricing data for ${searchTerm}`,
      });
    },
    onError: (error) => {
      toast({
        title: "Search Failed",
        description: "Unable to fetch real-time pricing data",
        variant: "destructive",
      });
    },
  });

  // Save materials to database
  const saveMaterialsMutation = useMutation({
    mutationFn: async () => {
      const materialPromises = materialsData.flatMap(section => 
        section.items.map(item => 
          apiRequest("POST", `/api/forecast/materials`, {
            id: item.id,
            costCode: item.id, // Use the item ID as cost code (e.g., "03.01")
            category: section.category,
            name: item.name,
            unit: item.unit,
            currentPrice: item.value.replace('$', '').replace(',', ''),
            previousPrice: (parseFloat(item.value.replace('$', '').replace(',', '')) - parseFloat(item.netChange.replace(/[+$,]/g, ''))).toString(),
            changePercent: item.percentChange.replace(/[+%]/g, ''),
            volume: "N/A",
            time: "N/A",
            ytdChange: item.ytd.replace(/[+%]/g, ''),
            ytdChangePercent: item.ytd.replace(/[+%]/g, '') // Use ytd since ytdC was removed
          })
        )
      );
      return Promise.all(materialPromises);
    },
    onSuccess: () => {
      toast({
        title: "Materials Saved",
        description: "All materials have been saved to the database with cost codes",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/forecast/materials"] });
    },
    onError: (error) => {
      toast({
        title: "Save Failed",
        description: "Unable to save materials to database",
        variant: "destructive",
      });
    },
  });

  const handleSearch = () => {
    if (!searchTerm.trim()) return;
    
    setIsSearching(true);
    // Extract material name and unit from search term
    const materialName = searchTerm.trim();
    const unit = "sq ft"; // Default unit, could be made dynamic
    
    searchMutation.mutate({ materialName, unit });
    setIsSearching(false);
  };

  const toggleFilter = (filter: keyof typeof activeFilters) => {
    setActiveFilters(prev => ({
      ...prev,
      [filter]: !prev[filter]
    }));
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up": return "#27ae60";
      case "down": return "#e74c3c";
      default: return "#95a5a6";
    }
  };

  const getChangeClass = (change: string) => {
    if (change.startsWith("+")) return "text-green-500";
    if (change.startsWith("-")) return "text-red-500";
    return "text-gray-500";
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-5">
      <div className="max-w-7xl mx-auto">
        {/* Page Title */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-foreground">MatIQ</h1>
          <p className="text-muted-foreground mt-1">Material Intelligence & Forecasting</p>
        </div>
        
        {/* Header with Search */}
        <div className="flex items-center justify-between gap-4 mb-5 pb-3 border-b border-border">
          <div className="flex items-center gap-4">
            {/* Search Bar */}
            <div className="flex items-center gap-2 bg-muted/50 rounded-lg px-3 py-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search materials (e.g., steel rebar, concrete)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="bg-transparent border-none outline-none text-sm w-64"
              />
              <Button
                size="sm"
                onClick={handleSearch}
                disabled={isSearching || !searchTerm.trim()}
                className="ml-2"
              >
                {isSearching ? <RefreshCw className="h-3 w-3 animate-spin" /> : "Search"}
              </Button>
            </div>
            
            {/* Save Materials Button */}
            <Button
              onClick={() => saveMaterialsMutation.mutate()}
              disabled={saveMaterialsMutation.isPending}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {saveMaterialsMutation.isPending ? <RefreshCw className="h-3 w-3 animate-spin mr-2" /> : null}
              Save to Database
            </Button>
          </div>
          
          <div className="flex items-center gap-4">
          <Button 
            variant={!activeFilters.trending && !activeFilters.volatility && !activeFilters.regional ? "default" : "outline"}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            Standard
          </Button>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
          
          <Button 
            variant={activeFilters.trending ? "default" : "outline"}
            onClick={() => toggleFilter('trending')}
            className="flex items-center gap-2"
          >
            <div className="w-3 h-3 border border-current" />
            Trending
          </Button>
          
          <Button 
            variant={activeFilters.volatility ? "default" : "outline"}
            onClick={() => toggleFilter('volatility')}
            className="flex items-center gap-2"
          >
            <div className="w-3 h-3 border border-current" />
            Volatility
          </Button>
          
          <Button 
            variant={activeFilters.regional ? "default" : "outline"}
            onClick={() => toggleFilter('regional')}
            className="flex items-center gap-2"
          >
            <div className="w-3 h-3 border border-current" />
            Regional
          </Button>

          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-20 bg-primary text-primary-foreground font-bold">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1W">1W</SelectItem>
              <SelectItem value="1M">1M</SelectItem>
              <SelectItem value="3M">3M</SelectItem>
              <SelectItem value="YTD">YTD</SelectItem>
            </SelectContent>
          </Select>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
          
          <Button variant="outline">%Chg YTD</Button>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
          
          <Button variant="outline">USD</Button>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted/50 sticky top-0 z-10">
                <th className="text-left p-3 w-12 text-sm font-normal text-muted-foreground border-b-2 border-border">Cost Code</th>
                <th className="text-left p-3 w-48 text-sm font-normal text-muted-foreground border-b-2 border-border">Description</th>
                <th className="text-left p-3 w-24 text-sm font-normal text-muted-foreground border-b-2 border-border">30Day</th>
                <th className="text-left p-3 w-28 text-sm font-normal text-muted-foreground border-b-2 border-border">Price</th>
                <th className="text-left p-3 w-24 text-sm font-normal text-muted-foreground border-b-2 border-border">Net Chg</th>
                <th className="text-left p-3 w-20 text-sm font-normal text-muted-foreground border-b-2 border-border">%Chg</th>
                <th className="text-left p-3 w-20 text-sm font-normal text-muted-foreground border-b-2 border-border">%Ytd</th>
              </tr>
            </thead>
            <tbody>
              {materialsData.map((section) => (
                <React.Fragment key={section.category}>
                  <tr>
                    <td colSpan={7} className="bg-muted font-bold text-primary p-2 border-t-2 border-border border-b border-border">
                      {section.category}
                    </td>
                  </tr>
                  {section.items.map((item) => (
                    <tr key={item.id} className="border-b border-border hover:bg-muted/30">
                      <td className="p-3 text-sm">{item.id}</td>
                      <td className="p-3 text-sm">
                        <span className="text-primary font-medium">{item.name}</span>{" "}
                        <span className="text-muted-foreground text-xs">{item.unit}</span>
                      </td>
                      <td className="p-3 w-24 h-8 relative flex items-center justify-center">
                        <div className="flex items-center gap-2">
                          <svg className="w-16 h-6" viewBox="0 0 100 30">
                            <polyline 
                              points={item.chartPoints}
                              fill="none" 
                              stroke={getTrendColor(item.trend)} 
                              strokeWidth="1.5"
                            />
                          </svg>
                          {item.trend === "up" && (
                            <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 3l7 7H3l7-7z" clipRule="evenodd" />
                            </svg>
                          )}
                          {item.trend === "down" && (
                            <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 17L3 10h14l-7 7z" clipRule="evenodd" />
                            </svg>
                          )}
                          {item.trend === "stable" && (
                            <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 10h14v1H3v-1z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </td>
                      <td className="p-3 text-sm font-mono">{item.value}</td>
                      <td className={`p-3 text-sm ${getChangeClass(item.netChange)}`}>{item.netChange}</td>
                      <td className={`p-3 text-sm ${getChangeClass(item.percentChange)}`}>{item.percentChange}</td>
                      <td className={`p-3 text-sm ${getChangeClass(item.ytd)}`}>{item.ytd}</td>
                    </tr>
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}