const axios = require('axios');

function attachDebugInterceptors(client, label = 'client') {
  client.interceptors.response.use(
    (res) => res,
    (error) => {
      const status = error.response && error.response.status;
      if (status === 401 || status === 403 || status === 429) {
        const data = error.response.data;
        console.error(`🔎 ${label} error ${status}:`, typeof data === 'string' ? data.slice(0, 300) : data);
      }
      return Promise.reject(error);
    }
  );
}

async function getApiKey(baseUrl, userId) {
  const resp = await axios.post(`${baseUrl}/api/test/api-key`, { userId, permissions: 'full-access', rateLimit: 1000 }, { validateStatus: () => true });
  const ct = resp.headers['content-type'] || '';
  console.log('🔐 /api/test/api-key status:', resp.status, 'CT:', ct);
  if (typeof resp.data === 'string') {
    console.log('🔐 Raw body (first 200):', resp.data.slice(0, 200));
    throw new Error('API key endpoint returned non-JSON');
  }
  if (!resp.data || !resp.data.apiKey) {
    console.log('🔐 Body:', resp.data);
    throw new Error('API key missing in response');
  }
  if (!String(resp.data.apiKey).startsWith('bda_')) {
    throw new Error('API key missing expected prefix');
  }
  return resp.data.apiKey;
}

async function whoAmI(baseUrl, apiKey) {
  const who = await axios.get(`${baseUrl}/api/test/whoami`, {
    headers: { Authorization: `Bearer ${apiKey}` },
    validateStatus: () => true,
  });
  console.log('🆔 whoami status:', who.status, 'data:', typeof who.data === 'string' ? who.data.slice(0, 200) : who.data);
  return who;
}

module.exports = { attachDebugInterceptors, getApiKey, whoAmI };
