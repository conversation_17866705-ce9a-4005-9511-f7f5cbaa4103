import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, TrendingDown, Equal, Package, CheckCircle, XCircle, Trophy, AlertTriangle } from "lucide-react";
import { useStructuredBidData } from "@/hooks/useStructuredBidData";

interface BidComparisonModalProps {
  bids: Array<{
    id: string;
    bidAmount: number | null;
    extractedAmount: number | null;
    timeline: string | null;
    extractedTimeline: string | null;
    scope: string | null;
    extractedScope: string | null;
    conditions: string | null;
    extractedConditions: string | null;
    extractionConfidence: number | null;
    status: string;
    submittedAt: string;
    contractorId: string;
    contractorName?: string;
    aiSummary: string | null;
  }>;
  rfq: {
    title: string;
    projectLocation: string;
    dueDate: string;
  };
  isOpen: boolean;
  onClose: () => void;
}

export default function BidComparisonModal({ bids, rfq, isOpen, onClose }: BidComparisonModalProps) {
  // Fetch structured data for all bids
  const structuredDataQueries = bids.map(bid => 
    useStructuredBidData(bid.id)
  );

  const formatCurrency = (amount: number | null) => {
    if (!amount) return "Not specified";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accepted': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getCompetitivePosition = (currentAmount: number, allAmounts: number[]) => {
    const validAmounts = allAmounts.filter(Boolean).sort((a, b) => a - b);
    if (validAmounts.length === 0) return null;
    
    const position = validAmounts.indexOf(currentAmount) + 1;
    const total = validAmounts.length;
    const isLowest = position === 1;
    const isHighest = position === total;
    
    return { position, total, isLowest, isHighest };
  };

  // Calculate competitive insights
  const validBidAmounts = bids.map(bid => bid.bidAmount || bid.extractedAmount).filter(Boolean) as number[];
  const averageBid = validBidAmounts.length > 0 ? validBidAmounts.reduce((a, b) => a + b, 0) / validBidAmounts.length : 0;
  const minBid = Math.min(...validBidAmounts);
  const maxBid = Math.max(...validBidAmounts);

  // Collect all categories from structured data
  const allCategories = new Set<string>();
  structuredDataQueries.forEach(query => {
    if (query.data?.lineItems.categories) {
      query.data.lineItems.categories.forEach(cat => allCategories.add(cat));
    }
  });

  const categoryArray = Array.from(allCategories).sort();

  // Category comparison data
  const getCategoryData = (bidIndex: number, category: string) => {
    const structuredData = structuredDataQueries[bidIndex]?.data;
    if (!structuredData) return { items: 0, total: 0 };
    
    const categoryItems = structuredData.lineItems.items.filter(item => item.category === category);
    const total = categoryItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
    
    return { items: categoryItems.length, total };
  };

  if (bids.length === 0) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Bid Comparison - {rfq.title}
            <Badge variant="outline" className="ml-2">
              {bids.length} Bids
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="scope">Scope Comparison</TabsTrigger>
            <TabsTrigger value="insights">Competitive Insights</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-sm text-muted-foreground">Total Bids</p>
                  <p className="text-2xl font-bold">{bids.length}</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-sm text-muted-foreground">Average Bid</p>
                  <p className="text-2xl font-bold text-blue-600">{formatCurrency(averageBid)}</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-sm text-muted-foreground">Lowest Bid</p>
                  <p className="text-2xl font-bold text-green-600">{formatCurrency(minBid)}</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-sm text-muted-foreground">Price Range</p>
                  <p className="text-2xl font-bold text-purple-600">{formatCurrency(maxBid - minBid)}</p>
                </CardContent>
              </Card>
            </div>

            {/* Bid Comparison Table */}
            <Card>
              <CardHeader>
                <CardTitle>Side-by-Side Comparison</CardTitle>
                <CardDescription>
                  Compare key metrics across all submitted bids
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Contractor</TableHead>
                        <TableHead>Bid Amount</TableHead>
                        <TableHead>Position</TableHead>
                        <TableHead>Timeline</TableHead>
                        <TableHead>Data Quality</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {bids.map((bid, index) => {
                        const bidAmount = bid.bidAmount || bid.extractedAmount;
                        const position = bidAmount ? getCompetitivePosition(bidAmount, validBidAmounts) : null;
                        const structuredData = structuredDataQueries[index]?.data;
                        const qualityScore = structuredData?.summary.hasStructuredData ? 85 : Math.round((bid.extractionConfidence || 0) * 100);
                        
                        return (
                          <TableRow key={bid.id}>
                            <TableCell className="font-medium">
                              {bid.contractorName || `Contractor ${index + 1}`}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <span className="font-semibold">
                                  {formatCurrency(bidAmount)}
                                </span>
                                {position && (
                                  <>
                                    {position.isLowest && <TrendingDown className="h-4 w-4 text-green-600" />}
                                    {position.isHighest && <TrendingUp className="h-4 w-4 text-red-600" />}
                                    {!position.isLowest && !position.isHighest && <Equal className="h-4 w-4 text-gray-500" />}
                                  </>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {position ? (
                                <Badge className={position.isLowest ? 'bg-green-100 text-green-800' : position.isHighest ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}>
                                  #{position.position} of {position.total}
                                </Badge>
                              ) : (
                                <span className="text-muted-foreground">N/A</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {bid.timeline || bid.extractedTimeline || 'Not specified'}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Progress value={qualityScore} className="w-16 h-2" />
                                <span className="text-sm">{qualityScore}%</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(bid.status)}>
                                {bid.status.replace('_', ' ')}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Category-by-Category Comparison
                </CardTitle>
                <CardDescription>
                  Compare cost breakdowns across different categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                {categoryArray.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No structured category data available for comparison</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Category</TableHead>
                          {bids.map((bid, index) => (
                            <TableHead key={bid.id} className="text-center">
                              {bid.contractorName || `Contractor ${index + 1}`}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {categoryArray.map(category => (
                          <TableRow key={category}>
                            <TableCell className="font-medium">{category}</TableCell>
                            {bids.map((bid, bidIndex) => {
                              const categoryData = getCategoryData(bidIndex, category);
                              return (
                                <TableCell key={bid.id} className="text-center">
                                  {categoryData.total > 0 ? (
                                    <div>
                                      <div className="font-semibold">{formatCurrency(categoryData.total)}</div>
                                      <div className="text-xs text-muted-foreground">
                                        {categoryData.items} items
                                      </div>
                                    </div>
                                  ) : (
                                    <span className="text-muted-foreground">-</span>
                                  )}
                                </TableCell>
                              );
                            })}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Scope Comparison Tab */}
          <TabsContent value="scope" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {bids.map((bid, index) => {
                const structuredData = structuredDataQueries[index]?.data;
                return (
                  <Card key={bid.id}>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {bid.contractorName || `Contractor ${index + 1}`}
                      </CardTitle>
                      <CardDescription>
                        Scope definition and inclusions/exclusions
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {structuredData?.scopeDefinition ? (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                              <CheckCircle className="h-6 w-6 text-green-600 mx-auto mb-1" />
                              <p className="font-semibold">{structuredData.scopeDefinition.included.count}</p>
                              <p className="text-sm text-muted-foreground">Included Items</p>
                            </div>
                            <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                              <XCircle className="h-6 w-6 text-red-600 mx-auto mb-1" />
                              <p className="font-semibold">{structuredData.scopeDefinition.excluded.count}</p>
                              <p className="text-sm text-muted-foreground">Excluded Items</p>
                            </div>
                          </div>
                          
                          {structuredData.scopeDefinition.included.items.slice(0, 3).map(item => (
                            <div key={item.id} className="flex items-start gap-2 text-sm">
                              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>{item.description}</span>
                            </div>
                          ))}
                          
                          {structuredData.scopeDefinition.included.count > 3 && (
                            <p className="text-sm text-muted-foreground">
                              ...and {structuredData.scopeDefinition.included.count - 3} more included items
                            </p>
                          )}
                        </>
                      ) : (
                        <div className="text-center py-4 text-muted-foreground">
                          <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No structured scope data available</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          {/* Competitive Insights Tab */}
          <TabsContent value="insights" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Price Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Bid Spread:</span>
                      <span className="font-semibold">{formatCurrency(maxBid - minBid)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Coefficient of Variation:</span>
                      <span className="font-semibold">
                        {validBidAmounts.length > 1 ? 
                          `${Math.round((Math.sqrt(validBidAmounts.reduce((acc, val) => acc + Math.pow(val - averageBid, 2), 0) / validBidAmounts.length) / averageBid) * 100)}%` 
                          : 'N/A'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Lowest vs Average:</span>
                      <span className="font-semibold text-green-600">
                        {averageBid > 0 ? `${Math.round(((averageBid - minBid) / averageBid) * 100)}% lower` : 'N/A'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Trophy className="h-5 w-5" />
                    Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {bids.length < 3 && (
                      <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <p className="text-sm text-yellow-700 dark:text-yellow-300">
                          <strong>Consider:</strong> Seeking additional bids for better price comparison
                        </p>
                      </div>
                    )}
                    
                    {(maxBid - minBid) / averageBid > 0.3 && (
                      <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          <strong>High Price Variation:</strong> Review scope definitions for consistency
                        </p>
                      </div>
                    )}
                    
                    <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <p className="text-sm text-green-700 dark:text-green-300">
                        <strong>Best Value:</strong> Consider structured data completeness alongside price
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}