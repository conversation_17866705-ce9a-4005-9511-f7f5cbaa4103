-- Add user classification enum and onboarding fields
CREATE TYPE user_classification AS ENUM ('general_contractor', 'contractor');

-- Add user classification and onboarding tracking fields to users table
ALTER TABLE users ADD COLUMN user_classification user_classification;
ALTER TABLE users ADD COLUMN onboarding_completed BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN onboarding_completed_at TIMESTAMP;

-- Add index for efficient user classification queries
CREATE INDEX IDX_users_classification ON users(user_classification);
CREATE INDEX IDX_users_onboarding ON users(onboarding_completed);
