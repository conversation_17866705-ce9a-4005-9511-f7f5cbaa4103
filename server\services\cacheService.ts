/**
 * Caching Service for Performance Optimization
 * Implements in-memory caching with TTL for frequently accessed data
 */

interface CacheItem<T> {
  data: T;
  expires: number;
  hits: number;
}

class CacheService {
  private cache = new Map<string, CacheItem<any>>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes
  private maxSize = 1000; // Maximum cache entries
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
  };

  /**
   * Set cache entry with optional TTL
   */
  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    // Evict expired entries if cache is getting too large
    if (this.cache.size >= this.maxSize) {
      this.evictExpired();
      
      // If still too large, evict least recently used items
      if (this.cache.size >= this.maxSize) {
        this.evictLRU();
      }
    }

    this.cache.set(key, {
      data,
      expires: Date.now() + ttl,
      hits: 0,
    });
  }

  /**
   * Get cache entry
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      this.stats.misses++;
      return null;
    }

    if (Date.now() > item.expires) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    item.hits++;
    this.stats.hits++;
    return item.data;
  }

  /**
   * Delete cache entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear entire cache
   */
  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, evictions: 0 };
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): typeof this.stats & { size: number; hitRate: number } {
    const total = this.stats.hits + this.stats.misses;
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: total > 0 ? this.stats.hits / total : 0,
    };
  }

  /**
   * Evict expired entries
   */
  private evictExpired(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expires) {
        this.cache.delete(key);
        this.stats.evictions++;
      }
    }
  }

  /**
   * Evict least recently used entries
   */
  private evictLRU(): void {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].hits - b[1].hits);
    
    // Remove bottom 10% of entries
    const toRemove = Math.ceil(entries.length * 0.1);
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i][0]);
      this.stats.evictions++;
    }
  }

  /**
   * Cache wrapper for async functions
   */
  async cached<T>(
    key: string,
    fn: () => Promise<T>,
    ttl: number = this.defaultTTL
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const result = await fn();
    this.set(key, result, ttl);
    return result;
  }

  /**
   * Invalidate cache patterns (useful for related data)
   */
  invalidatePattern(pattern: string): number {
    let count = 0;
    const regex = new RegExp(pattern);
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        count++;
      }
    }
    
    return count;
  }
}

// Cache TTL constants for different data types
export const CACHE_TTL = {
  USER_PROFILE: 10 * 60 * 1000, // 10 minutes
  CONTRACTOR_LIST: 5 * 60 * 1000, // 5 minutes
  RFQ_LIST: 2 * 60 * 1000, // 2 minutes
  DASHBOARD_STATS: 30 * 1000, // 30 seconds
  BID_SUMMARY: 1 * 60 * 1000, // 1 minute
  DOCUMENT_METADATA: 15 * 60 * 1000, // 15 minutes
  BIDS: 2 * 60 * 1000, // 2 minutes
} as const;

// Global cache instance
export const cache = new CacheService();

// Cache key generators
export const CACHE_KEYS = {
  userProfile: (userId: string) => `user:${userId}`,
  contractorProfile: (contractorId: string) => `contractor:${contractorId}`,
  contractorsByUser: (userId: string) => `contractors:user:${userId}`,
  rfqsByUser: (userId: string) => `rfqs:user:${userId}`,
  rfqDetails: (rfqId: string) => `rfq:${rfqId}`,
  bidsByRfq: (rfqId: string) => `bids:rfq:${rfqId}`,
  bidsByContractor: (contractorId: string) => `bids:contractor:${contractorId}`,
  dashboardStats: (userId: string) => `dashboard:stats:${userId}`,
  favoriteContractors: (userId: string) => `favorites:${userId}`,
  rfqDistribution: (rfqId: string) => `distribution:${rfqId}`,
  BID_LINE_ITEMS: `bid:lineItems`,
  BID_SCOPE: `bid:scope`,
  BIDS: `bids`
} as const;