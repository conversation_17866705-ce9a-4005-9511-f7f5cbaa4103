import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import ReactMarkdown from 'react-markdown';
import { 
  DollarSign,
  CheckCircle,
  FileText,
  Filter,
  ArrowUpDown,
  Eye,
  Settings,
  Calculator,
  TrendingUp,
  Brain,
  BarChart3
} from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";

interface BidAnalysisData {
  rfq: any;
  bidSummary: {
    totalBids: number;
    acceptedBids: number;
    rejectedBids: number;
    pendingBids: number;
    baseTotal: number;
    bufferAmount: number;
    totalWithBuffer: number;
  };
  bids: Array<{
    bid: {
      id: string;
      bidAmount: number | null;
      extractedAmount: number | null;
      timeline: string | null;
      status: string;
      submittedAt: string;
      aiSummary: string | null;
      competitiveScore: number | null;
    };
    contractor: {
      companyName: string;
      primaryContactName: string;
    };
  }>;
}

interface BidManagementWidgetProps {
  rfqId: string;
}

export function BidManagementWidget({ rfqId }: BidManagementWidgetProps) {
  const [sortBy, setSortBy] = useState("submittedAt");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedBid, setSelectedBid] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("overview");
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch bid analysis data
  const { data: analysisData, isLoading } = useQuery<BidAnalysisData>({
    queryKey: ['/api/rfqs', rfqId, 'bids', 'analysis'],
    enabled: !!rfqId,
  });

  // Bid action mutation
  const bidActionMutation = useMutation({
    mutationFn: async ({ bidId, action }: any) => {
      return apiRequest("PATCH", `/api/bids/${bidId}`, {
        status: action,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/rfqs', rfqId, 'bids', 'analysis'] });
      toast({ title: "Bid action completed successfully" });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    },
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Bid Management Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="grid grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-20 bg-muted rounded-lg"></div>
              ))}
            </div>
            <div className="h-40 bg-muted rounded-lg"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analysisData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Bid Management Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No RFQ data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { rfq, bidSummary, bids } = analysisData;

  // Debug: Log when component renders and data changes
  console.log(`🚀 BidManagementWidget render - Total bids: ${bids?.length || 0}, Status filter: "${statusFilter}"`);
  if (bids?.length > 0) {
    console.log('📋 Bid statuses:', bids.map(({bid}) => `${bid.id.slice(0,8)}: "${bid.status}"`));
  }

  // Status normalization function
  const normalizeStatus = (status: string) => {
    const normalized = status.toLowerCase();
    switch (normalized) {
      case 'accept':
      case 'accepted':
        return 'accepted';
      case 'reject':
      case 'rejected':
        return 'rejected';
      case 'submitted':
      case 'pending':
        return 'pending';
      case 'request info':
      case 'requestinfo':
        return 'request info';
      default:
        return normalized;
    }
  };

  // Format status for display
  const formatStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accept':
        return 'Accepted';
      case 'reject':
        return 'Rejected';
      case 'request info':
        return 'Request Info';
      case 'submitted':
      case 'pending':
        return 'Pending';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Filter and sort bids
  const filteredBids = bids.filter(({ bid }) => {
    if (statusFilter === "all") return true;
    
    const normalizedBidStatus = normalizeStatus(bid.status);
    const normalizedFilter = statusFilter.toLowerCase();
    
    // Debug logging - temporarily enabled
    console.log(`🔍 Filter Debug - Bid ID: ${bid.id.slice(0,8)}, Status: "${bid.status}" -> normalized: "${normalizedBidStatus}", filter: "${normalizedFilter}", match: ${normalizedBidStatus === normalizedFilter}`);
    
    // Handle special cases
    if (statusFilter === "pending") {
      // Pending includes submitted and any non-accepted/rejected status
      return !['accepted', 'rejected'].includes(normalizedBidStatus);
    }
    
    return normalizedBidStatus === normalizedFilter;
  });

  const sortedBids = [...filteredBids].sort((a, b) => {
    const aValue = a.bid[sortBy as keyof typeof a.bid];
    const bValue = b.bid[sortBy as keyof typeof b.bid];
    
    if (sortBy === "submittedAt") {
      return new Date(bValue as string).getTime() - new Date(aValue as string).getTime();
    }
    
    if (typeof aValue === "number" && typeof bValue === "number") {
      return bValue - aValue;
    }
    
    return String(bValue).localeCompare(String(aValue));
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Bid Management Dashboard</CardTitle>
            <p className="text-sm text-muted-foreground truncate max-w-md">
              {rfq.projectName}
            </p>
          </div>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-1" />
            Buffer Settings
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="bids" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Bid Analysis
            </TabsTrigger>
            <TabsTrigger value="ai-analysis" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              AI Analysis
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 mt-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-4 gap-4">
          <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
            <div className="p-2 bg-blue-500/10 rounded-lg">
              <FileText className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium">Total Bids</p>
              <p className="text-2xl font-bold">{bidSummary.totalBids}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
            <div className="p-2 bg-green-500/10 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium">Accepted Bids</p>
              <p className="text-2xl font-bold">{bidSummary.acceptedBids}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
            <div className="p-2 bg-green-500/10 rounded-lg">
              <DollarSign className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium">Base Total</p>
              <p className="text-2xl font-bold">${bidSummary.baseTotal.toLocaleString()}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
            <div className="p-2 bg-orange-500/10 rounded-lg">
              <Calculator className="h-4 w-4 text-orange-600" />
            </div>
            <div>
              <p className="text-sm font-medium">Total + Buffer</p>
              <p className="text-2xl font-bold text-orange-600">
                ${bidSummary.totalWithBuffer.toLocaleString()}
              </p>
              <p className="text-xs text-muted-foreground">
                +${bidSummary.bufferAmount.toLocaleString()} ({((bidSummary.bufferAmount / bidSummary.baseTotal) * 100).toFixed(1)}%)
              </p>
            </div>
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Label htmlFor="status-filter" className="text-sm">Filter by Status:</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Bids</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="accepted">Accepted</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
              <Label htmlFor="sort-by" className="text-sm">Sort by:</Label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="submittedAt">Submission Date</SelectItem>
                  <SelectItem value="extractedAmount">Bid Amount</SelectItem>
                  <SelectItem value="competitiveScore">AI Score</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Bids Table */}
        <div className="border rounded-lg">
          <div className="p-4 border-b bg-muted/30">
            <h3 className="font-semibold">Bid Submissions ({sortedBids.length})</h3>
          </div>
          
          {sortedBids.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No bids match your current filters</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Contractor</TableHead>
                  <TableHead>Bid Amount</TableHead>
                  <TableHead>Timeline</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>AI Score</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedBids.map(({ bid, contractor }) => (
                  <TableRow key={bid.id} className="hover:bg-muted/30">
                    <TableCell>
                      <div>
                        <p className="font-medium">{contractor.companyName}</p>
                        <p className="text-sm text-muted-foreground">{contractor.primaryContactName}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">
                        ${(bid.extractedAmount || bid.bidAmount || 0).toLocaleString()}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{bid.timeline || '—'}</span>
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${
                        normalizeStatus(bid.status) === 'accepted' 
                          ? 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-200'
                          : normalizeStatus(bid.status) === 'rejected'
                          ? 'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-200'
                          : 'border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-200'
                      }`}>
                        {formatStatus(bid.status)}
                      </span>
                    </TableCell>
                    <TableCell>
                      {bid.competitiveScore ? (
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="h-3 w-3 text-green-600" />
                          <span className="text-sm font-medium">{bid.competitiveScore}/100</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">—</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {formatDistanceToNow(new Date(bid.submittedAt), { addSuffix: true })}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedBid({ bid, contractor })}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Review
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>AI Bid Analysis</DialogTitle>
                          </DialogHeader>
                          {selectedBid && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium mb-2">Bid Details</h4>
                                  <p><strong>Amount:</strong> ${(selectedBid.bid.extractedAmount || 0).toLocaleString()}</p>
                                  <p><strong>Timeline:</strong> {selectedBid.bid.timeline || 'Not specified'}</p>
                                  <p><strong>Contractor:</strong> {selectedBid.contractor.companyName}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium mb-2">AI Assessment</h4>
                                  <p><strong>Competitive Score:</strong> {selectedBid.bid.competitiveScore || 'N/A'}/100</p>
                                  <p><strong>Status:</strong> {selectedBid.bid.status}</p>
                                </div>
                              </div>
                              
                              {selectedBid.bid.aiSummary && (
                                <div>
                                  <h4 className="font-medium mb-2">AI Analysis Summary</h4>
                                  <div className="bg-muted/50 p-3 rounded-lg max-h-60 overflow-y-auto">
                                    <ReactMarkdown className="prose prose-sm max-w-none">
                                      {selectedBid.bid.aiSummary}
                                    </ReactMarkdown>
                                  </div>
                                </div>
                              )}
                              
                              <div className="flex space-x-2 pt-4">
                                {normalizeStatus(selectedBid.bid.status) === 'pending' && (
                                  <>
                                    <Button
                                      onClick={() => bidActionMutation.mutate({ 
                                        bidId: selectedBid.bid.id, 
                                        action: 'accept' 
                                      })}
                                      disabled={bidActionMutation.isPending}
                                      className="bg-green-600 hover:bg-green-700"
                                    >
                                      <CheckCircle className="h-4 w-4 mr-1" />
                                      Accept Bid
                                    </Button>
                                    <Button
                                      variant="destructive"
                                      onClick={() => bidActionMutation.mutate({ 
                                        bidId: selectedBid.bid.id, 
                                        action: 'reject' 
                                      })}
                                      disabled={bidActionMutation.isPending}
                                    >
                                      Reject Bid
                                    </Button>
                                  </>
                                )}
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
          </TabsContent>

          <TabsContent value="bids" className="space-y-6 mt-6">
            <div className="text-center py-8 text-muted-foreground">
              <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <h3 className="font-medium">Bid Analysis</h3>
              <p className="text-sm">Detailed bid comparison and market analysis coming soon.</p>
            </div>
          </TabsContent>

          <TabsContent value="ai-analysis" className="space-y-6 mt-6">
            <div className="text-center py-8 text-muted-foreground">
              <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <h3 className="font-medium">AI Bid Analysis</h3>
              <p className="text-sm">AI-powered executive summary and bid ranking coming soon.</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}