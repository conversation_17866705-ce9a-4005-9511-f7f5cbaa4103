/**
 * Enhanced Bid Analytics Service
 * Provides advanced analytics, risk assessment, and evaluation scoring
 */

import { GoogleGenerativeAI } from "@google/generative-ai";
import { storage } from "../storage";
import type { 
  Bid, 
  Contractor, 
  Rfq
} from "@shared/schema";

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

// Enhanced Analytics Interfaces
export interface CompetitiveIntelligence {
  marketPosition: {
    rank: number;
    percentile: number;
    priceAdvantage: number;
    competitiveGap: number;
  };
  pricingAnalysis: {
    vsAverageBid: number;
    vsLowestBid: number;
    vsHighestBid: number;
    marketMedian: number;
  };
  competitorInsights: {
    strongestCompetitor: string;
    weakestCompetitor: string;
    pricingStrategy: 'aggressive' | 'conservative' | 'market-rate';
    marketShare: number;
  };
}

export interface EnhancedRiskAssessment {
  financialRisk: {
    score: number;
    factors: string[];
    priceDeviation: number;
    budgetRisk: 'low' | 'medium' | 'high';
  };
  technicalRisk: {
    score: number;
    complexityLevel: number;
    skillRequirements: string[];
    technicalChallenges: string[];
  };
  timelineRisk: {
    score: number;
    feasibilityRating: number;
    criticalPath: string[];
    delayProbability: number;
  };
  contractorRisk: {
    score: number;
    reliabilityIndex: number;
    historicalPerformance: number;
    capacityAssessment: string;
  };
}

export interface PredictiveAnalytics {
  successProbability: number;
  riskFactors: {
    budget: number;
    timeline: number;
    technical: number;
    contractor: number;
    market: number;
  };
  predictiveInsights: {
    likelyOutcome: string;
    confidenceLevel: number;
    keyRisks: string[];
    recommendations: string[];
  };
  historicalComparison: {
    similarProjects: number;
    successRate: number;
    avgPerformance: number;
    lessons: string[];
  };
}

export interface EvaluationCriteria {
  technical: {
    weight: number;
    maxScore: number;
    factors: {
      experience: number;
      expertise: number;
      methodology: number;
      innovation: number;
    };
  };
  financial: {
    weight: number;
    maxScore: number;
    factors: {
      pricing: number;
      costBreakdown: number;
      valueEngineering: number;
      paymentTerms: number;
    };
  };
  timeline: {
    weight: number;
    maxScore: number;
    factors: {
      duration: number;
      milestones: number;
      resourceAvailability: number;
      riskMitigation: number;
    };
  };
  compliance: {
    weight: number;
    maxScore: number;
    factors: {
      licensing: number;
      insurance: number;
      safety: number;
      certifications: number;
    };
  };
}

/**
 * Generate comprehensive competitive intelligence for an RFQ
 */
export async function generateCompetitiveIntelligence(rfqId: string): Promise<CompetitiveIntelligence> {
  const bids = await storage.getBidsByRfq(rfqId);
  const contractors = await Promise.all(
    bids.map(bid => storage.getContractor(bid.contractorId!))
  );

  const bidAmounts = bids
    .map(bid => bid.bidAmount || bid.extractedAmount)
    .filter(amount => amount !== null)
    .map(amount => Number(amount));

  if (bidAmounts.length === 0) {
    throw new Error("No bid amounts available for analysis");
  }

  const sortedAmounts = bidAmounts.sort((a, b) => a - b);
  const averageBid = bidAmounts.reduce((sum, amount) => sum + amount, 0) / bidAmounts.length;
  const medianBid = sortedAmounts[Math.floor(sortedAmounts.length / 2)];
  const lowestBid = sortedAmounts[0];
  const highestBid = sortedAmounts[sortedAmounts.length - 1];

  // For demonstration, using the first bid as target
  const targetBid = bidAmounts[0];
  const rank = sortedAmounts.indexOf(targetBid) + 1;
  const percentile = ((bidAmounts.length - rank + 1) / bidAmounts.length) * 100;

  return {
    marketPosition: {
      rank,
      percentile,
      priceAdvantage: ((averageBid - targetBid) / averageBid) * 100,
      competitiveGap: targetBid - lowestBid,
    },
    pricingAnalysis: {
      vsAverageBid: targetBid - averageBid,
      vsLowestBid: targetBid - lowestBid,
      vsHighestBid: targetBid - highestBid,
      marketMedian: medianBid,
    },
    competitorInsights: {
      strongestCompetitor: contractors[0]?.companyName || "Unknown",
      weakestCompetitor: contractors[contractors.length - 1]?.companyName || "Unknown",
      pricingStrategy: targetBid < averageBid ? 'aggressive' : targetBid > averageBid ? 'conservative' : 'market-rate',
      marketShare: (1 / bidAmounts.length) * 100,
    },
  };
}

/**
 * Generate enhanced risk assessment for a bid
 */
export async function generateEnhancedRiskAssessment(bidId: string): Promise<EnhancedRiskAssessment> {
  const bid = await storage.getBid(bidId);
  if (!bid) throw new Error("Bid not found");

  const contractor = await storage.getContractor(bid.contractorId!);
  if (!contractor) throw new Error("Contractor not found");

  const rfq = await storage.getRfq(bid.rfqId!);
  if (!rfq) throw new Error("RFQ not found");

  // Get all bids for competitive context
  const allBids = await storage.getBidsByRfq(bid.rfqId!);
  const bidAmounts = allBids
    .map(b => b.bidAmount || b.extractedAmount)
    .filter(amount => amount !== null)
    .map(amount => Number(amount));

  const averageBid = bidAmounts.reduce((sum, amount) => sum + amount, 0) / bidAmounts.length;
  const targetAmount = Number(bid.bidAmount || bid.extractedAmount || 0);
  const priceDeviation = Math.abs(targetAmount - averageBid) / averageBid * 100;

  // Generate AI-powered risk analysis
  const riskAnalysis = await generateAIRiskAnalysis(bid, contractor, rfq, allBids);

  return {
    financialRisk: {
      score: riskAnalysis.financialRiskScore,
      factors: riskAnalysis.financialRiskFactors,
      priceDeviation,
      budgetRisk: priceDeviation > 20 ? 'high' : priceDeviation > 10 ? 'medium' : 'low',
    },
    technicalRisk: {
      score: riskAnalysis.technicalRiskScore,
      complexityLevel: riskAnalysis.technicalComplexity,
      skillRequirements: riskAnalysis.skillRequirements,
      technicalChallenges: riskAnalysis.technicalChallenges,
    },
    timelineRisk: {
      score: riskAnalysis.timelineRiskScore,
      feasibilityRating: riskAnalysis.timelineFeasibility,
      criticalPath: riskAnalysis.criticalPath,
      delayProbability: riskAnalysis.delayProbability,
    },
    contractorRisk: {
      score: riskAnalysis.contractorRiskScore,
      reliabilityIndex: riskAnalysis.contractorReliability,
      historicalPerformance: riskAnalysis.historicalPerformance,
      capacityAssessment: riskAnalysis.capacityAssessment,
    },
  };
}

/**
 * Generate predictive analytics for project success
 */
export async function generatePredictiveAnalytics(rfqId: string): Promise<PredictiveAnalytics> {
  const rfq = await storage.getRfq(rfqId);
  if (!rfq) throw new Error("RFQ not found");

  const bids = await storage.getBidsByRfq(rfqId);
  const contractors = await Promise.all(
    bids.map(bid => storage.getContractor(bid.contractorId!))
  );

  // Generate AI-powered predictive analysis
  const predictiveAnalysis = await generateAIPredictiveAnalysis(rfq, bids, contractors);

  return {
    successProbability: predictiveAnalysis.successProbability,
    riskFactors: {
      budget: predictiveAnalysis.budgetRisk,
      timeline: predictiveAnalysis.timelineRisk,
      technical: predictiveAnalysis.technicalRisk,
      contractor: predictiveAnalysis.contractorRisk,
      market: predictiveAnalysis.marketRisk,
    },
    predictiveInsights: {
      likelyOutcome: predictiveAnalysis.likelyOutcome,
      confidenceLevel: predictiveAnalysis.confidenceLevel,
      keyRisks: predictiveAnalysis.keyRisks,
      recommendations: predictiveAnalysis.recommendations,
    },
    historicalComparison: {
      similarProjects: predictiveAnalysis.similarProjects,
      successRate: predictiveAnalysis.successRate,
      avgPerformance: predictiveAnalysis.avgPerformance,
      lessons: predictiveAnalysis.lessons,
    },
  };
}

/**
 * Calculate weighted evaluation scores for all bids in an RFQ
 */
export async function calculateWeightedEvaluationScores(
  rfqId: string,
  criteria: EvaluationCriteria
): Promise<any[]> {
  const bids = await storage.getBidsByRfq(rfqId);
  const evaluationScores: any[] = [];

  for (const bid of bids) {
    const contractor = await storage.getContractor(bid.contractorId!);
    if (!contractor) continue;

    const scores = await calculateBidEvaluationScore(bid, contractor, criteria);
    evaluationScores.push(scores);
  }

  // Rank bids by weighted score
  evaluationScores.sort((a, b) => (b.weightedScore || 0) - (a.weightedScore || 0));
  
  // Update ranks
  evaluationScores.forEach((score, index) => {
    score.rfqRank = index + 1;
    score.percentileRank = Number(((evaluationScores.length - index) / evaluationScores.length * 100).toFixed(2));
  });

  return evaluationScores;
}

/**
 * Calculate evaluation score for a single bid
 */
async function calculateBidEvaluationScore(
  bid: Bid,
  contractor: Contractor,
  criteria: EvaluationCriteria
): Promise<any> {
  // Calculate individual criterion scores
  const technicalScore = await calculateTechnicalScore(bid, contractor, criteria.technical);
  const financialScore = await calculateFinancialScore(bid, criteria.financial);
  const timelineScore = await calculateTimelineScore(bid, criteria.timeline);
  const complianceScore = await calculateComplianceScore(contractor, criteria.compliance);

  // Calculate weighted score
  const weightedScore = 
    (technicalScore * criteria.technical.weight) +
    (financialScore * criteria.financial.weight) +
    (timelineScore * criteria.timeline.weight) +
    (complianceScore * criteria.compliance.weight);

  const normalizedScore = weightedScore / 100;

  return {
    id: '',
    bidId: bid.id,
    rfqId: bid.rfqId!,
    evaluationTemplateId: null,
    technicalScore: Number(technicalScore.toFixed(2)),
    financialScore: Number(financialScore.toFixed(2)),
    timelineScore: Number(timelineScore.toFixed(2)),
    complianceScore: Number(complianceScore.toFixed(2)),
    qualityScore: Number(((technicalScore + complianceScore) / 2).toFixed(2)),
    weightedScore: Number(weightedScore.toFixed(2)),
    normalizedScore: Number(normalizedScore.toFixed(4)),
    scoreBreakdown: {
      technical: technicalScore,
      financial: financialScore,
      timeline: timelineScore,
      compliance: complianceScore,
    },
    strengths: await identifyStrengths(bid, contractor),
    weaknesses: await identifyWeaknesses(bid, contractor),
    recommendations: await generateRecommendations(bid, contractor),
    rfqRank: 0,
    percentileRank: Number("0"),
    evaluatedAt: new Date(),
    evaluatedBy: 'AI_SYSTEM_v1.0',
  };
}

// Helper functions for scoring
async function calculateTechnicalScore(bid: Bid, contractor: Contractor, criteria: any): Promise<number> {
  let score = 0;
  
  // Experience factor
  const yearsInBusiness = contractor.yearsInBusiness || 0;
  score += Math.min(yearsInBusiness * 2, 25); // Max 25 points for experience
  
  // Expertise factor (based on trade types match)
  const hasRelevantTrades = contractor.tradeTypes ? 25 : 0;
  score += hasRelevantTrades;
  
  // Methodology factor (based on bid detail)
  const methodologyScore = bid.extractedScope ? 25 : 15;
  score += methodologyScore;
  
  // Innovation factor (based on unique conditions)
  const innovationScore = bid.extractedConditions ? 25 : 15;
  score += innovationScore;
  
  return Math.min(score, 100);
}

async function calculateFinancialScore(bid: Bid, criteria: any): Promise<number> {
  let score = 0;
  
  // Pricing competitiveness
  const hasBidAmount = bid.bidAmount || bid.extractedAmount;
  score += hasBidAmount ? 40 : 0;
  
  // Cost breakdown clarity
  const hasCostBreakdown = bid.extractedData ? 30 : 15;
  score += hasCostBreakdown;
  
  // Value engineering
  const hasValueEngineering = bid.extractedConditions ? 20 : 10;
  score += hasValueEngineering;
  
  // Payment terms
  const hasPaymentTerms = bid.notes ? 10 : 5;
  score += hasPaymentTerms;
  
  return Math.min(score, 100);
}

async function calculateTimelineScore(bid: Bid, criteria: any): Promise<number> {
  let score = 0;
  
  // Duration feasibility
  const hasTimeline = bid.timeline || bid.extractedTimeline;
  score += hasTimeline ? 40 : 0;
  
  // Milestone planning
  const hasMilestones = bid.extractedData ? 30 : 15;
  score += hasMilestones;
  
  // Resource availability
  const hasResourcePlan = bid.extractedScope ? 20 : 10;
  score += hasResourcePlan;
  
  // Risk mitigation
  const hasRiskMitigation = bid.extractedConditions ? 10 : 5;
  score += hasRiskMitigation;
  
  return Math.min(score, 100);
}

async function calculateComplianceScore(contractor: Contractor, criteria: any): Promise<number> {
  let score = 0;
  
  // Licensing
  const hasLicense = contractor.licenseNumber ? 30 : 0;
  score += hasLicense;
  
  // Insurance
  const hasInsurance = contractor.generalLiability ? 30 : 0;
  score += hasInsurance;
  
  // Safety
  const hasSafety = contractor.emr ? 20 : 10;
  score += hasSafety;
  
  // Certifications
  const hasCertifications = contractor.certifications ? 20 : 10;
  score += hasCertifications;
  
  return Math.min(score, 100);
}

async function identifyStrengths(bid: Bid, contractor: Contractor): Promise<string[]> {
  const strengths = [];
  
  if (contractor.yearsInBusiness && contractor.yearsInBusiness > 10) {
    strengths.push("Extensive industry experience");
  }
  
  if (contractor.certifications) {
    strengths.push("Professional certifications");
  }
  
  if (bid.extractedScope) {
    strengths.push("Detailed scope of work");
  }
  
  if (bid.extractedConditions) {
    strengths.push("Clear project conditions");
  }
  
  return strengths;
}

async function identifyWeaknesses(bid: Bid, contractor: Contractor): Promise<string[]> {
  const weaknesses = [];
  
  if (!contractor.licenseNumber) {
    weaknesses.push("Missing license information");
  }
  
  if (!contractor.generalLiability) {
    weaknesses.push("Insurance details not provided");
  }
  
  if (!bid.timeline && !bid.extractedTimeline) {
    weaknesses.push("No timeline specified");
  }
  
  return weaknesses;
}

async function generateRecommendations(bid: Bid, contractor: Contractor): Promise<string[]> {
  const recommendations = [];
  
  if (!contractor.licenseNumber) {
    recommendations.push("Verify contractor licensing");
  }
  
  if (!contractor.generalLiability) {
    recommendations.push("Request insurance certificates");
  }
  
  if (!bid.timeline && !bid.extractedTimeline) {
    recommendations.push("Clarify project timeline");
  }
  
  recommendations.push("Conduct reference checks");
  
  return recommendations;
}

// AI-powered analysis functions
async function generateAIRiskAnalysis(bid: Bid, contractor: Contractor, rfq: Rfq, allBids: Bid[]) {
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
  
  const prompt = `
    Analyze this construction bid for risk assessment:
    
    BID DETAILS:
    - Bid Amount: ${bid.bidAmount || bid.extractedAmount || 'Not specified'}
    - Timeline: ${bid.timeline || bid.extractedTimeline || 'Not specified'}
    - Scope: ${bid.extractedScope || 'Not specified'}
    
    CONTRACTOR PROFILE:
    - Company: ${contractor.companyName}
    - Years in Business: ${contractor.yearsInBusiness || 'Not specified'}
    - Trade Types: ${JSON.stringify(contractor.tradeTypes)}
    - License: ${contractor.licenseNumber || 'Not specified'}
    
    PROJECT CONTEXT:
    - Project: ${rfq.projectName}
    - Location: ${rfq.projectLocation}
    - Total Bids: ${allBids.length}
    
    Return a JSON object with risk scores (0-100) and analysis:
    {
      "financialRiskScore": number,
      "technicalRiskScore": number,
      "timelineRiskScore": number,
      "contractorRiskScore": number,
      "financialRiskFactors": ["factor1", "factor2"],
      "technicalComplexity": number,
      "skillRequirements": ["skill1", "skill2"],
      "technicalChallenges": ["challenge1", "challenge2"],
      "timelineFeasibility": number,
      "criticalPath": ["milestone1", "milestone2"],
      "delayProbability": number,
      "contractorReliability": number,
      "historicalPerformance": number,
      "capacityAssessment": "assessment description"
    }
  `;

  try {
    const result = await model.generateContent([{ text: prompt }]);
    const content = result.response.text();
    return JSON.parse(content);
  } catch (error) {
    console.error("AI Risk Analysis failed:", error);
    return {
      financialRiskScore: 50,
      technicalRiskScore: 50,
      timelineRiskScore: 50,
      contractorRiskScore: 50,
      financialRiskFactors: ["Unable to analyze"],
      technicalComplexity: 50,
      skillRequirements: ["Standard construction skills"],
      technicalChallenges: ["Standard project challenges"],
      timelineFeasibility: 50,
      criticalPath: ["Planning", "Execution", "Completion"],
      delayProbability: 0.3,
      contractorReliability: 50,
      historicalPerformance: 50,
      capacityAssessment: "Unable to assess"
    };
  }
}

async function generateAIPredictiveAnalysis(rfq: Rfq, bids: Bid[], contractors: (Contractor | null)[]) {
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
  
  const prompt = `
    Analyze this RFQ for predictive success modeling:
    
    PROJECT DETAILS:
    - Title: ${rfq.projectName}
    - Location: ${rfq.projectLocation}
    - Due Date: ${rfq.dueDate}
    - Total Bids: ${bids.length}
    
    BID SUMMARY:
    ${bids.map((bid, index) => `
    Bid ${index + 1}:
    - Amount: ${bid.bidAmount || bid.extractedAmount || 'Not specified'}
    - Timeline: ${bid.timeline || bid.extractedTimeline || 'Not specified'}
    - Contractor: ${contractors[index]?.companyName || 'Unknown'}
    `).join('\n')}
    
    Return a JSON object with predictive analysis:
    {
      "successProbability": number,
      "budgetRisk": number,
      "timelineRisk": number,
      "technicalRisk": number,
      "contractorRisk": number,
      "marketRisk": number,
      "likelyOutcome": "description",
      "confidenceLevel": number,
      "keyRisks": ["risk1", "risk2"],
      "recommendations": ["rec1", "rec2"],
      "similarProjects": number,
      "successRate": number,
      "avgPerformance": number,
      "lessons": ["lesson1", "lesson2"]
    }
  `;

  try {
    const result = await model.generateContent([{ text: prompt }]);
    const content = result.response.text();
    return JSON.parse(content);
  } catch (error) {
    console.error("AI Predictive Analysis failed:", error);
    return {
      successProbability: 0.75,
      budgetRisk: 30,
      timelineRisk: 25,
      technicalRisk: 20,
      contractorRisk: 15,
      marketRisk: 10,
      likelyOutcome: "Project likely to succeed with proper management",
      confidenceLevel: 0.7,
      keyRisks: ["Budget overrun", "Schedule delays"],
      recommendations: ["Regular monitoring", "Clear communication"],
      similarProjects: 10,
      successRate: 0.8,
      avgPerformance: 85,
      lessons: ["Proper planning is crucial", "Regular communication prevents issues"]
    };
  }
}

/**
 * Save risk assessment to database
 */
export async function saveRiskAssessment(bidId: string, assessment: EnhancedRiskAssessment): Promise<void> {
  const bid = await storage.getBid(bidId);
  if (!bid) throw new Error("Bid not found");

  const riskAssessment: any = {
    bidId,
    rfqId: bid.rfqId!,
    financialRiskScore: assessment.financialRisk.score.toString(),
    technicalRiskScore: assessment.technicalRisk.score.toString(),
    timelineRiskScore: assessment.timelineRisk.score.toString(),
    contractorRiskScore: assessment.contractorRisk.score.toString(),
    overallRiskScore: ((assessment.financialRisk.score + assessment.technicalRisk.score + assessment.timelineRisk.score + assessment.contractorRisk.score) / 4).toString(),
    riskFactors: {
      financial: assessment.financialRisk.factors,
      technical: assessment.technicalRisk.technicalChallenges,
      timeline: assessment.timelineRisk.criticalPath,
      contractor: [assessment.contractorRisk.capacityAssessment]
    },
    riskLevel: assessment.financialRisk.budgetRisk,
    successProbability: (1 - (assessment.financialRisk.score + assessment.technicalRisk.score + assessment.timelineRisk.score + assessment.contractorRisk.score) / 400).toString(),
    budgetOverrunProbability: (assessment.financialRisk.score / 100).toString(),
    scheduleDelayProbability: (assessment.timelineRisk.delayProbability).toString(),
    calculatedBy: 'AI_SYSTEM_v1.0'
  };

  // Save to database (you'll need to implement this in storage service)
  // await storage.createRiskAssessment(riskAssessment);
}