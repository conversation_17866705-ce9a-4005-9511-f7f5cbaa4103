@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #e7e5e4;
  --foreground: #1e293b;
  --card: #f5f5f4;
  --card-foreground: #1e293b;
  --popover: #f5f5f4;
  --popover-foreground: #1e293b;
  --primary: #f75100;
  --primary-foreground: #ffffff;
  --secondary: #ffffff;
  --secondary-foreground: #4b5563;
  --muted: #e7e5e4;
  --muted-foreground: #6b7280;
  --accent: #f3e5f5;
  --accent-foreground: #374151;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #bfbfbf;
  --input: #d6d3d1;
  --ring: #6366f1;
  --chart-1: #5d9aea;
  --chart-2: #352ce8;
  --chart-3: #a226c5;
  --chart-4: #28264b;
  --chart-5: #4d9f3c;
  --sidebar: #e5e2e0;
  --sidebar-foreground: #1e293b;
  --sidebar-primary: #6366f1;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f3e5f5;
  --sidebar-accent-foreground: #374151;
  --sidebar-border: #d6d3d1;
  --sidebar-ring: #6366f1;
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 1.25rem;
  --shadow-2xs: 2px 2px 10px 4px hsl(240 4% 60% / 0.09);
  --shadow-xs: 2px 2px 10px 4px hsl(240 4% 60% / 0.09);
  --shadow-sm: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 1px 2px 3px hsl(240 4% 60% / 0.18);
  --shadow: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 1px 2px 3px hsl(240 4% 60% / 0.18);
  --shadow-md: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 2px 4px 3px hsl(240 4% 60% / 0.18);
  --shadow-lg: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 4px 6px 3px hsl(240 4% 60% / 0.18);
  --shadow-xl: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 8px 10px 3px hsl(240 4% 60% / 0.18);
  --shadow-2xl: 2px 2px 10px 4px hsl(240 4% 60% / 0.45);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: #1e1b18;
  --foreground: #e2e8f0;
  --card: #2c2825;
  --card-foreground: #e2e8f0;
  --popover: #2c2825;
  --popover-foreground: #e2e8f0;
  --primary: #f38620;
  --primary-foreground: #1e1b18;
  --secondary: #3a3633;
  --secondary-foreground: #d1d5db;
  --muted: #2c2825;
  --muted-foreground: #9ca3af;
  --accent: #484441;
  --accent-foreground: #d1d5db;
  --destructive: #ef4444;
  --destructive-foreground: #1e1b18;
  --border: #3a3633;
  --input: #3a3633;
  --ring: #818cf8;
  --chart-1: #f0690f;
  --chart-2: #64f2b0;
  --chart-3: #45b7d3;
  --chart-4: #ac64e8;
  --chart-5: #3730a3;
  --sidebar: #3a3633;
  --sidebar-foreground: #e2e8f0;
  --sidebar-primary: #e68333;
  --sidebar-primary-foreground: #1e1b18;
  --sidebar-accent: #484441;
  --sidebar-accent-foreground: #d1d5db;
  --sidebar-border: #3a3633;
  --sidebar-ring: #818cf8;
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 1.25rem;
  --shadow-2xs: 2px 2px 10px 4px hsl(0 0% 0% / 0.09);
  --shadow-xs: 2px 2px 10px 4px hsl(0 0% 0% / 0.09);
  --shadow-sm: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18);
  --shadow: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18);
  --shadow-md: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 2px 4px 3px hsl(0 0% 0% / 0.18);
  --shadow-lg: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 4px 6px 3px hsl(0 0% 0% / 0.18);
  --shadow-xl: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 8px 10px 3px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 2px 2px 10px 4px hsl(0 0% 0% / 0.45);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply antialiased bg-background text-foreground;
    font-family: var(--font-sans);
  }
}

@layer components {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}

@layer utilities {
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
}
