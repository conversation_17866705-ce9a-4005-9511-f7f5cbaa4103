/**
 * RFQ Update Access Test
 * - GC_A creates RFQ in Org A
 * - GC_B attempts to update -> 403
 * - GC_A updates -> 200
 */
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const { attachDebugInterceptors, getApiKey, whoAmI } = require('./test-utils');

const BASE_URL = process.env.BASE_URL || 'http://localhost:5000';

async function createUserWithKey({ userId, classification, organizationId }) {
  // Create user via dev fixtures (no auth)
  await axios.post(`${BASE_URL}/api/test/user`, { id: userId, organizationId, userClassification: classification });
  // Create API key via dev fixture
  const apiKey = await getApiKey(BASE_URL, userId);
  const client = axios.create({ baseURL: BASE_URL, headers: { Authorization: `Bearer ${apiKey}` }, timeout: 30000 });
  attachDebugInterceptors(client, `client:${userId}`);
  await whoAmI(BASE_URL, apiKey);
  return { userId, client };
}

async function run() {
  console.log('🧪 RFQ Update Access Test');
  const { data: orgA } = await axios.post(`${BASE_URL}/api/test/org`, { id: uuidv4(), name: 'Org A', slug: `org-a-${Date.now()}` });
  const { data: orgB } = await axios.post(`${BASE_URL}/api/test/org`, { id: uuidv4(), name: 'Org B', slug: `org-b-${Date.now()}` });
   console.log('✅ Orgs created', { orgA: orgA.id, orgB: orgB.id });
  
  const gcA = await createUserWithKey({ userId: `gc_a_${Date.now()}`, classification: 'general_contractor', organizationId: orgA.id });
   const gcB = await createUserWithKey({ userId: `gc_b_${Date.now()}`, classification: 'general_contractor', organizationId: orgB.id });
  
  let rfq;
  try {
    const resp = await axios.post(`${BASE_URL}/api/test/rfq-simple`, {
      createdBy: gcA.userId,
      organizationId: orgA.id
    }, { validateStatus: () => true });
    console.log('📨 rfq-simple status:', resp.status, 'body:', typeof resp.data === 'string' ? resp.data.slice(0, 200) : resp.data);
    if (resp.status !== 200) throw new Error(resp.data && resp.data.message ? resp.data.message : 'rfq-simple failed');
    rfq = resp.data;
  } catch (e) {
    console.error('❌ rfq-simple error:', e && e.response ? e.response.data || e.response.status : e);
    throw e;
  }
  console.log('✅ RFQ created', rfq.id);
 
  // GC_B attempt -> 403 or 404 (deny without leaking existence)
  try {
    await gcB.client.put(`/api/rfqs/${rfq.id}`, { projectName: 'Hacked' });
    throw new Error('Expected 403/404 for GC_B update');
  } catch (e) {
    const s = e.response && e.response.status;
    if (!(s === 403 || s === 404)) throw e;
    console.log(`✅ GC_B denied updating Org A RFQ (status ${s})`);
  }

  // GC_A update -> 200
  const res = await gcA.client.put(`/api/rfqs/${rfq.id}`, { description: 'Updated description' });
  if (res.status !== 200 || res.data.description !== 'Updated description') throw new Error('GC_A update failed');
  console.log('✅ GC_A successfully updated own RFQ');

  console.log('\n🎉 RFQ Update Access Test PASSED');
}

if (require.main === module) {
  run().catch(err => { console.error('💥 Test failed:', err && err.response ? err.response.data || err.response.status : err); process.exit(1); });
}

module.exports = { run };
