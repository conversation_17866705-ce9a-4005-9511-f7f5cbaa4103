#!/usr/bin/env node

/**
 * Apply database indexes from migration file
 */

import { config } from 'dotenv';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { readFileSync } from 'fs';
import ws from 'ws';

// Load environment variables
config();

// Configure Neon WebSocket
neonConfig.webSocketConstructor = ws;

// Database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

const pool = new Pool({ connectionString });

async function applyIndexes() {
  console.log('🚀 Applying Database Indexes');
  console.log('═'.repeat(50));
  
  try {
    // Read the SQL file
    const sqlContent = readFileSync('migrations/0012_add_missing_indexes.sql', 'utf8');
    
    // Split into individual statements (rough parsing)
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--') && !stmt.startsWith('/*'));
    
    const client = await pool.connect();
    
    let successCount = 0;
    let skipCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (!statement || statement.length < 10) continue;
      
      try {
        console.log(`📄 Executing statement ${i + 1}/${statements.length}...`);
        
        // Extract index name for better logging
        const indexMatch = statement.match(/CREATE\s+(?:UNIQUE\s+)?INDEX\s+(?:CONCURRENTLY\s+)?(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
        const indexName = indexMatch ? indexMatch[1] : `statement-${i + 1}`;
        
        const startTime = Date.now();
        await client.query(statement + ';');
        const duration = Date.now() - startTime;
        
        console.log(`   ✅ ${indexName} created (${duration}ms)`);
        successCount++;
        
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`   ⚠️  Index already exists - skipping`);
          skipCount++;
        } else {
          console.error(`   ❌ Failed: ${error.message}`);
        }
      }
    }
    
    client.release();
    
    console.log('\n📊 Summary:');
    console.log(`   ✅ Successfully created: ${successCount} indexes`);
    console.log(`   ⚠️  Already existed: ${skipCount} indexes`);
    console.log(`   📄 Total statements processed: ${statements.length}`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
  
  console.log('\n🎉 Index migration completed!');
  console.log('   Run: node test-query-performance-simple.js to test improvements');
}

applyIndexes().catch(console.error);
