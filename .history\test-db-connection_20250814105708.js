import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from "ws";
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configure Neon WebSocket
neonConfig.webSocketConstructor = ws;

async function testConnection() {
  console.log('🔍 Testing Neon database connection...');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    process.exit(1);
  }

  // Parse and display connection info (without password)
  const url = new URL(process.env.DATABASE_URL);
  console.log(`📡 Connecting to: ${url.hostname}:${url.port}/${url.pathname.slice(1)}`);
  console.log(`👤 User: ${url.username}`);
  console.log(`🌍 Region: ${url.hostname.includes('us-east-2') ? 'us-east-2' : 'unknown'}`);

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    // Test basic connection
    console.log('\n⏳ Testing connection...');
    const client = await pool.connect();
    
    // Test basic query
    console.log('✅ Connection successful!');
    
    // Check database version
    const versionResult = await client.query('SELECT version()');
    console.log(`📊 PostgreSQL Version: ${versionResult.rows[0].version.split(' ')[1]}`);
    
    // Check if any tables exist
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log(`\n📋 Existing tables: ${tablesResult.rows.length}`);
    if (tablesResult.rows.length > 0) {
      tablesResult.rows.forEach(row => {
        console.log(`  - ${row.table_name}`);
      });
    } else {
      console.log('  (No tables found - ready for schema deployment)');
    }
    
    client.release();
    console.log('\n🎉 Database connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
    process.exit(1);
  } finally {
    await pool.end();
  }
}

testConnection();
