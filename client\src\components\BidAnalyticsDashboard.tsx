/**
 * Enhanced Bid Analytics Dashboard Component
 * Displays competitive intelligence, risk assessment, and predictive analytics
 */

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Shield, 
  AlertTriangle, 
  Target, 
  Award,
  BarChart3,
  PieChart,
  LineChart,
  RefreshCw
} from 'lucide-react';

interface BidAnalyticsProps {
  rfqId: string;
}

interface CompetitiveIntelligence {
  marketPosition: {
    rank: number;
    percentile: number;
    priceAdvantage: number;
    competitiveGap: number;
  };
  pricingAnalysis: {
    vsAverageBid: number;
    vsLowestBid: number;
    vsHighestBid: number;
    marketMedian: number;
  };
  competitorInsights: {
    strongestCompetitor: string;
    weakestCompetitor: string;
    pricingStrategy: 'aggressive' | 'conservative' | 'market-rate';
    marketShare: number;
  };
}

interface PredictiveAnalytics {
  successProbability: number;
  riskFactors: {
    budget: number;
    timeline: number;
    technical: number;
    contractor: number;
    market: number;
  };
  predictiveInsights: {
    likelyOutcome: string;
    confidenceLevel: number;
    keyRisks: string[];
    recommendations: string[];
  };
  historicalComparison: {
    similarProjects: number;
    successRate: number;
    avgPerformance: number;
    lessons: string[];
  };
}

interface EvaluationScore {
  bidId: string;
  rfqId: string;
  technicalScore: number;
  financialScore: number;
  timelineScore: number;
  complianceScore: number;
  weightedScore: number;
  rfqRank: number;
  percentileRank: number;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

interface BidAnalysisData {
  competitiveIntelligence: CompetitiveIntelligence | null;
  predictiveAnalytics: PredictiveAnalytics | null;
  evaluationScores: EvaluationScore[];
  generatedAt: string;
  rfqId: string;
}

const BidAnalyticsDashboard: React.FC<BidAnalyticsProps> = ({ rfqId }) => {
  const [activeTab, setActiveTab] = useState('competitive');
  const [refreshKey, setRefreshKey] = useState(0);

  const { data: analysisData, isLoading, error, refetch } = useQuery<BidAnalysisData>({
    queryKey: ['bid-analysis', rfqId, refreshKey],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/bid-analysis/${rfqId}`);
      if (!response.ok) throw new Error('Failed to fetch bid analysis');
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Enhanced Bid Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-32 bg-gray-200 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-24 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            Analytics Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">Failed to load analytics data</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const competitiveIntelligence = analysisData?.competitiveIntelligence;
  const predictiveAnalytics = analysisData?.predictiveAnalytics;
  const evaluationScores = analysisData?.evaluationScores || [];

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Enhanced Bid Analytics
          </CardTitle>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="competitive">Competitive Intel</TabsTrigger>
            <TabsTrigger value="predictive">Predictive Analytics</TabsTrigger>
            <TabsTrigger value="evaluation">Evaluation Scores</TabsTrigger>
            <TabsTrigger value="summary">Executive Summary</TabsTrigger>
          </TabsList>

          <TabsContent value="competitive" className="space-y-4">
            <CompetitiveIntelligenceView data={competitiveIntelligence} />
          </TabsContent>

          <TabsContent value="predictive" className="space-y-4">
            <PredictiveAnalyticsView data={predictiveAnalytics} />
          </TabsContent>

          <TabsContent value="evaluation" className="space-y-4">
            <EvaluationScoresView data={evaluationScores} />
          </TabsContent>

          <TabsContent value="summary" className="space-y-4">
            <ExecutiveSummaryView 
              competitive={competitiveIntelligence}
              predictive={predictiveAnalytics}
              evaluation={evaluationScores}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

const CompetitiveIntelligenceView: React.FC<{ data: CompetitiveIntelligence | null }> = ({ data }) => {
  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No competitive intelligence data available</p>
      </div>
    );
  }

  const { marketPosition, pricingAnalysis, competitorInsights } = data;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            Market Position
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Market Rank</span>
            <Badge variant={marketPosition.rank <= 3 ? "default" : "secondary"}>
              #{marketPosition.rank}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Percentile</span>
            <span className="text-sm">{marketPosition.percentile.toFixed(1)}%</span>
          </div>
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Price Advantage</span>
              <span className={`text-sm ${marketPosition.priceAdvantage > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {marketPosition.priceAdvantage > 0 ? '+' : ''}{marketPosition.priceAdvantage.toFixed(1)}%
              </span>
            </div>
            <Progress value={Math.abs(marketPosition.priceAdvantage)} className="h-2" />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Competitive Gap</span>
            <span className="text-sm">${marketPosition.competitiveGap.toLocaleString()}</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="w-5 h-5" />
            Pricing Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">vs Average Bid</span>
            <span className={`text-sm ${pricingAnalysis.vsAverageBid < 0 ? 'text-green-600' : 'text-red-600'}`}>
              {pricingAnalysis.vsAverageBid < 0 ? '' : '+'}${pricingAnalysis.vsAverageBid.toLocaleString()}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">vs Lowest Bid</span>
            <span className={`text-sm ${pricingAnalysis.vsLowestBid < 0 ? 'text-green-600' : 'text-red-600'}`}>
              {pricingAnalysis.vsLowestBid < 0 ? '' : '+'}${pricingAnalysis.vsLowestBid.toLocaleString()}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">vs Highest Bid</span>
            <span className={`text-sm ${pricingAnalysis.vsHighestBid < 0 ? 'text-green-600' : 'text-red-600'}`}>
              {pricingAnalysis.vsHighestBid < 0 ? '' : '+'}${pricingAnalysis.vsHighestBid.toLocaleString()}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Market Median</span>
            <span className="text-sm">${pricingAnalysis.marketMedian.toLocaleString()}</span>
          </div>
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5" />
            Competitor Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span className="text-sm font-medium text-gray-500">Strongest Competitor</span>
              <p className="text-sm font-semibold">{competitorInsights.strongestCompetitor}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Pricing Strategy</span>
              <Badge variant={
                competitorInsights.pricingStrategy === 'aggressive' ? 'destructive' :
                competitorInsights.pricingStrategy === 'conservative' ? 'secondary' : 'default'
              }>
                {competitorInsights.pricingStrategy}
              </Badge>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Market Share</span>
              <p className="text-sm font-semibold">{competitorInsights.marketShare.toFixed(1)}%</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const PredictiveAnalyticsView: React.FC<{ data: PredictiveAnalytics | null }> = ({ data }) => {
  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No predictive analytics data available</p>
      </div>
    );
  }

  const { successProbability, riskFactors, predictiveInsights, historicalComparison } = data;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Success Probability
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <div className="text-4xl font-bold text-green-600 mb-2">
              {(successProbability * 100).toFixed(1)}%
            </div>
            <Progress value={successProbability * 100} className="h-3 mb-4" />
            <p className="text-sm text-gray-600">{predictiveInsights.likelyOutcome}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Risk Factors
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {Object.entries(riskFactors).map(([factor, value]) => (
            <div key={factor} className="flex items-center justify-between">
              <span className="text-sm font-medium capitalize">{factor}</span>
              <div className="flex items-center gap-2">
                <Progress value={value} className="w-16 h-2" />
                <span className="text-sm w-8">{value}%</span>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Key Risks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {predictiveInsights.keyRisks.map((risk, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></div>
                <span className="text-sm">{risk}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LineChart className="w-5 h-5" />
            Historical Comparison
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Similar Projects</span>
            <span className="text-sm">{historicalComparison.similarProjects}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Success Rate</span>
            <span className="text-sm">{(historicalComparison.successRate * 100).toFixed(1)}%</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Avg Performance</span>
            <span className="text-sm">{historicalComparison.avgPerformance}/100</span>
          </div>
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>AI Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {predictiveInsights.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"></div>
                <span className="text-sm">{recommendation}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

const EvaluationScoresView: React.FC<{ data: EvaluationScore[] }> = ({ data }) => {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No evaluation scores available</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {data.map((score, index) => (
        <Card key={score.bidId}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Bid #{index + 1}</span>
              <div className="flex items-center gap-2">
                <Badge variant={score.rfqRank === 1 ? "default" : "secondary"}>
                  Rank #{score.rfqRank}
                </Badge>
                <Badge variant="outline">
                  {score.percentileRank}th percentile
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Technical Score</span>
                    <span className="text-sm">{score.technicalScore}/100</span>
                  </div>
                  <Progress value={score.technicalScore} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Financial Score</span>
                    <span className="text-sm">{score.financialScore}/100</span>
                  </div>
                  <Progress value={score.financialScore} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Timeline Score</span>
                    <span className="text-sm">{score.timelineScore}/100</span>
                  </div>
                  <Progress value={score.timelineScore} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Compliance Score</span>
                    <span className="text-sm">{score.complianceScore}/100</span>
                  </div>
                  <Progress value={score.complianceScore} className="h-2" />
                </div>
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-bold">Weighted Score</span>
                    <span className="text-sm font-bold">{score.weightedScore}/100</span>
                  </div>
                  <Progress value={score.weightedScore} className="h-3" />
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-green-600 mb-2">Strengths</h4>
                  <ul className="space-y-1">
                    {score.strengths.map((strength, i) => (
                      <li key={i} className="text-sm flex items-start gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-1.5 flex-shrink-0"></div>
                        {strength}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-red-600 mb-2">Areas for Improvement</h4>
                  <ul className="space-y-1">
                    {score.weaknesses.map((weakness, i) => (
                      <li key={i} className="text-sm flex items-start gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></div>
                        {weakness}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

const ExecutiveSummaryView: React.FC<{ 
  competitive: CompetitiveIntelligence | null;
  predictive: PredictiveAnalytics | null;
  evaluation: EvaluationScore[];
}> = ({ competitive, predictive, evaluation }) => {
  const topBid = evaluation.find(score => score.rfqRank === 1);
  const avgScore = evaluation.length > 0 ? evaluation.reduce((sum, score) => sum + score.weightedScore, 0) / evaluation.length : 0;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Executive Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 mb-2">
                {evaluation.length}
              </div>
              <p className="text-sm text-gray-600">Total Bids Analyzed</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 mb-2">
                {predictive ? (predictive.successProbability * 100).toFixed(1) : 'N/A'}%
              </div>
              <p className="text-sm text-gray-600">Success Probability</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 mb-2">
                {avgScore.toFixed(1)}
              </div>
              <p className="text-sm text-gray-600">Average Score</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {topBid && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Top Performing Bid
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Weighted Score</p>
                <p className="text-2xl font-bold">{topBid.weightedScore}/100</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Percentile Rank</p>
                <p className="text-2xl font-bold">{topBid.percentileRank}th</p>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-600 mb-2">Key Strengths</p>
              <div className="flex flex-wrap gap-2">
                {topBid.strengths.slice(0, 3).map((strength, index) => (
                  <Badge key={index} variant="secondary">{strength}</Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {competitive && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Market Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Pricing Strategy</p>
                <Badge variant={
                  competitive.competitorInsights.pricingStrategy === 'aggressive' ? 'destructive' :
                  competitive.competitorInsights.pricingStrategy === 'conservative' ? 'secondary' : 'default'
                }>
                  {competitive.competitorInsights.pricingStrategy}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Price Advantage</p>
                <p className={`text-lg font-bold ${competitive.marketPosition.priceAdvantage > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {competitive.marketPosition.priceAdvantage > 0 ? '+' : ''}{competitive.marketPosition.priceAdvantage.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BidAnalyticsDashboard;