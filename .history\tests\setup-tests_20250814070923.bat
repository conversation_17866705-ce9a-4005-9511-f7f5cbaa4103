@echo off
REM Bidaible Unified File Processing Test Suite Setup Script (Windows)
REM This script helps set up the test environment and dependencies

echo 🚀 Setting up Bidaible Unified File Processing Test Suite
echo ========================================================

REM Check if Node.js is installed
echo.
echo ℹ️ Step 1: Checking prerequisites...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 16 or higher.
    pause
    exit /b 1
) else (
    echo ✅ Node.js is installed
    node --version
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm.
    pause
    exit /b 1
) else (
    echo ✅ npm is installed
    npm --version
)

REM Install dependencies
echo.
echo ℹ️ Step 2: Installing dependencies...
if exist package.json (
    npm install
    if %errorlevel% equ 0 (
        echo ✅ Dependencies installed successfully
    ) else (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ❌ package.json not found. Make sure you're in the tests directory.
    pause
    exit /b 1
)

REM Check environment variables
echo.
echo ℹ️ Step 3: Checking environment configuration...
if exist "..\\.env" (
    echo ✅ .env file found in parent directory
    
    REM Check for Wasabi variables
    findstr /C:"WASABI_ACCESS_KEY_ID" "..\\.env" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Wasabi storage credentials configured
    ) else (
        echo ⚠️ Wasabi storage credentials not found in .env file
        echo ℹ️ Please add WASABI_ACCESS_KEY_ID and WASABI_SECRET_ACCESS_KEY to .env
    )
    
    REM Check for AI service keys
    findstr /C:"GROQ_API_KEY" "..\\.env" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ AI service API keys configured
    ) else (
        findstr /C:"OPENAI_API_KEY" "..\\.env" >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✅ AI service API keys configured
        ) else (
            findstr /C:"GEMINI_API_KEY" "..\\.env" >nul 2>&1
            if %errorlevel% equ 0 (
                echo ✅ AI service API keys configured
            ) else (
                echo ⚠️ No AI service API keys found in .env file
                echo ℹ️ Please add at least one of: GROQ_API_KEY, OPENAI_API_KEY, GEMINI_API_KEY
            )
        )
    )
    
    REM Check for database URL
    findstr /C:"DATABASE_URL" "..\\.env" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Database URL configured
    ) else (
        echo ⚠️ DATABASE_URL not found in .env file
    )
    
) else (
    echo ⚠️ .env file not found in parent directory
    echo ℹ️ Please create a .env file with required environment variables
    echo ℹ️ See README.md for required variables
)

REM Check if server is running
echo.
echo ℹ️ Step 4: Checking server status...
curl -s http://localhost:5000/api/debug/assets >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Bidaible server is running on localhost:5000
) else (
    echo ⚠️ Bidaible server is not running on localhost:5000
    echo ℹ️ Please start the server with: npm run dev
)

REM Final instructions
echo.
echo ========================================================
echo ✅ Setup completed successfully!
echo.
echo ℹ️ Next steps:
echo   1. Ensure your .env file has all required variables
echo   2. Start the Bidaible server: npm run dev
echo   3. Run the test suite: npm test
echo.
echo ℹ️ Available test commands:
echo   npm test              # Run all tests
echo   npm run test:wasabi   # Run Wasabi storage tests
echo   npm run test:ai       # Run AI processing tests
echo   npm run test:watch    # Run tests in watch mode
echo   npm run report        # Open HTML test report
echo.
echo ℹ️ For detailed instructions, see README.md
echo ========================================================

pause
