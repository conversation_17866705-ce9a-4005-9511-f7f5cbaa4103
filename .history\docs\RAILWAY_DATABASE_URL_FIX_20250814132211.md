# Railway DATABASE_URL Fix Guide

## Problem Summary

The application is failing to start on Railway with the error:
```
FATAL: DATABASE_URL is not defined.
Application cannot start without a database connection.
```

## Root Cause Analysis

The issue occurs because:

1. **NODE_ENV Mismatch**: Railway has `NODE_ENV=development` instead of `production`, causing the application to try loading a `.env` file that doesn't exist in the Railway deployment.

2. **Environment Detection Logic**: The original code only checked `NODE_ENV` to determine whether to load `.env` files, but Railway can have `NODE_ENV=development` while still being a deployment environment.

3. **Railway Environment Variables Available**: All environment variables (including `DATABASE_URL`) are properly set in Railway dashboard, but the application isn't using them due to the faulty environment detection logic.

**ACTUAL ISSUE**: The app tries to load `.env` file in Railway because `NODE_ENV=development`, but Railway deployments don't have `.env` files - they inject variables directly.

## Diagnostic Tools Added

### 1. Enhanced Debug Logging
- Added comprehensive environment variable logging in `server/index.ts`
- Added detailed database debugging in `server/db.ts`
- Both will show what environment variables are available at runtime

### 2. Railway Debug Script
- Created `railway-debug.js` for comprehensive environment analysis
- Added `npm run debug` command to package.json
- Can be run locally or on Railway to diagnose issues

### 3. Railway Configuration Updates
- Updated `railway.toml` with debug capabilities
- Added healthcheck command for troubleshooting

## Step-by-Step Fix Instructions

### Step 1: Verify Neon Database Connection String
1. Go to your Neon dashboard
2. Copy the connection string from your database
3. Ensure it matches this format:
   ```
   ************************************************************
   ```

### Step 2: Set Environment Variables in Railway
1. Go to your Railway project dashboard
2. Navigate to the **Variables** tab
3. Add the following environment variables:

   **Required Variables:**
   ```
   DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
   NODE_ENV=production
   ```

   **Additional Variables (copy from your .env file):**
   ```
   CLERK_PUBLISHABLE_KEY=pk_test_YXdhaXRlZC1ncmFja2xlLTkwLmNsZXJrLmFjY291bnRzLmRldiQ
   CLERK_SECRET_KEY=sk_test_27mlwUK1sgyTyklq76Q73qsnxLIjaXzQrWq5FjumTd
   GROQ_API_KEY=********************************************************
   OPENAI_API_KEY=********************************************************************************************************************************************************************
   GEMINI_API_KEY=AIzaSyCnWXqrz4s43HEbMj_05T01FoX2xuZeVC0
   RESEND_API_KEY=re_Joui4szb_D5NstaT9RTuCjENXqxY8KKDy
   WASABI_ACCESS_KEY_ID=QMX972IMONHSOMR89UJB
   WASABI_SECRET_ACCESS_KEY=QyYDdnXS55U7jB3DK2LXW8CR2LCRT5DFClRXXnFw
   WASABI_BUCKET_NAME=bidaible-storage
   WASABI_ENDPOINT=https://s3.wasabisys.com
   WASABI_REGION=us-east-1
   PRIMARY_MODEL=groq
   SESSION_SECRET=your_session_secret_here
   JWT_SECRET=your_jwt_secret_here
   PORT=5000
   ```

### Step 3: Deploy and Test
1. After setting the environment variables, trigger a new deployment
2. Monitor the deployment logs for the debug output
3. Look for the "PRODUCTION ENVIRONMENT DEBUG" section in the logs

### Step 4: Verify Database Connection
1. Check the logs for successful database connection
2. If still failing, use the debug script by temporarily changing the start command to:
   ```
   npm run debug && npm start
   ```

## Common Issues and Solutions

### Issue 1: Environment Variables Not Showing Up
**Solution**: Ensure variables are set in the correct Railway environment (production/staging)

### Issue 2: DATABASE_URL Format Issues
**Solution**: Verify the connection string format matches Neon's requirements:
- Must include `sslmode=require`
- Should include `channel_binding=require` for Neon
- Username and password must be URL-encoded if they contain special characters

### Issue 3: Build vs Runtime Environment
**Solution**: Environment variables are only available at runtime, not during build. Ensure critical variables are set for the deployment environment.

## Verification Steps

1. **Check Railway Logs**: Look for the debug output showing environment variables
2. **Test Database Connection**: Verify the DATABASE_URL is properly formatted and accessible
3. **Monitor Application Startup**: Ensure the application starts without the DATABASE_URL error

## Rollback Plan

If issues persist:
1. Revert the debug logging changes
2. Use the original railway.toml configuration
3. Focus on manually verifying Railway environment variable configuration

## Additional Notes

- The `.env` file is only used in development
- Railway provides environment variables directly to the application
- All sensitive variables must be set in Railway's dashboard
- The debug logging will be removed once the issue is resolved

## Testing the Fix

Run locally to test the debug script:
```bash
npm run debug
```

This will show you what environment variables are available and help identify any configuration issues.
