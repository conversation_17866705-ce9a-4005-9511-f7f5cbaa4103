import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";
import * as fs from "fs";
import path from "path";
import { streamProcessor } from "./aiStreamProcessor";
interface ExtractedRfqData {
  text?: string;
  structuredData?: {
    fileName?: string;
    projectDescription?: string;
    contactName?: string;
    contactEmail?: string;
    projectSummary?: string;
    requirements?: string | string[];
    finalAwardDate?: string;
    aiSummary?: string;
    projectLocation?: string;
  };
  extractedText?: string;
}

// Enhanced timeout and retry configuration
const AI_TIMEOUT = 90000; // 90 seconds - increased for large file processing
const MAX_RETRIES = 2;
const RETRY_DELAY = 2000; // 2 seconds

// Initialize AI clients with timeout configuration  
const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY,
  timeout: AI_TIMEOUT
});

let groq = new OpenAI({ 
  apiKey: process.env.GROQ_API_KEY,
  baseURL: "https://api.groq.com/openai/v1",
  timeout: AI_TIMEOUT
});

// Fresh Groq client function
function getGroqClient() {
  return new OpenAI({
    apiKey: process.env.GROQ_API_KEY,
    baseURL: "https://api.groq.com/openai/v1",
    timeout: AI_TIMEOUT
  });
}

// Model configuration
const OPENAI_MODEL = "gpt-4.1-mini";
const GEMINI_MODEL = "gemini-2.5-pro";
const GROQ_MODEL = "openai/gpt-oss-120b";

// Initialize Gemini client
let gemini: any = null;
try {
  if (process.env.GEMINI_API_KEY) {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    gemini = genAI.getGenerativeModel({ model: GEMINI_MODEL });
  }
} catch (error) {
  console.error("Failed to initialize Gemini client:", error);
}

interface AIProcessingOptions {
  onProgress?: (progress: { stage: string; percentage: number; message: string }) => void;
  timeout?: number;
  retries?: number;
  chunkSize?: number;
}

interface AIProcessingResult {
  text?: string;
  extractedText?: string;
  structuredData?: {
    fileName?: string;
    projectDescription?: string;
    contactName?: string;
    contactEmail?: string;
    projectSummary?: string;
    requirements?: string | string[];
    finalAwardDate?: string;
    aiSummary?: string;
    projectLocation?: string;
  };
  processingTime: number;
  model: string;
  success: boolean;
  retryCount: number;
}

/**
 * Enhanced RFQ document processing with stream-based approach and retry logic
 */
export async function processRfqDocumentOptimized(
  filePath: string, 
  fileName: string,
  options: AIProcessingOptions = {}
): Promise<AIProcessingResult> {
  const startTime = Date.now();
  let retryCount = 0;
  const maxRetries = options.retries || MAX_RETRIES;

  console.log(`Starting optimized processing for ${fileName}`);
  options.onProgress?.({ stage: 'initialization', percentage: 5, message: 'Initializing AI processing' });

  // Enhanced text extraction with streaming
  let extractedText = '';
  try {
    const fileExt = path.extname(fileName).toLowerCase();

    if (fileExt === '.pdf') {
      options.onProgress?.({ stage: 'extraction', percentage: 10, message: 'Extracting PDF content' });
      const result = await streamProcessor.processPDFStream(filePath, {
        onProgress: (progress) => {
          const adjustedPercentage = 10 + (progress.percentage * 0.4); // 10-50% range
          options.onProgress?.({ 
            stage: 'extraction', 
            percentage: Math.round(adjustedPercentage), 
            message: progress.message 
          });
        },
        timeout: options.timeout || AI_TIMEOUT,
        chunkSize: options.chunkSize
      });

      if (!result.success) {
        throw new Error(result.error || 'PDF processing failed');
      }
      extractedText = result.extractedText;
      console.log(`Extracted ${extractedText.length} characters from PDF in ${result.processingTime}ms`);

    } else if (['.txt', '.csv'].includes(fileExt)) {
      options.onProgress?.({ stage: 'extraction', percentage: 20, message: 'Reading text file' });
      const result = await streamProcessor.processTextStream(filePath, {
        onProgress: (progress) => {
          const adjustedPercentage = 20 + (progress.percentage * 0.3); // 20-50% range
          options.onProgress?.({ 
            stage: 'extraction', 
            percentage: Math.round(adjustedPercentage), 
            message: progress.message 
          });
        },
        timeout: options.timeout || AI_TIMEOUT,
        chunkSize: options.chunkSize
      });

      if (!result.success) {
        throw new Error(result.error || 'Text processing failed');
      }
      extractedText = result.extractedText;
      console.log(`Extracted ${extractedText.length} characters from text file in ${result.processingTime}ms`);

    } else {
      throw new Error(`Unsupported file type: ${fileExt}`);
    }

    // Smart content prioritization with 150K limit
    if (extractedText.length > 150000) {
      console.log(`Applying smart truncation from ${extractedText.length} to 150000 characters`);
      extractedText = applySmartTruncation(extractedText, fileName);
    }

  } catch (extractionError) {
    console.error(`Text extraction failed for ${fileName}:`, extractionError);

    // For extraction failures, try fallback to original AI service
    console.log(`Falling back to original AI service for ${fileName}`);
    try {
      const { processRfqDocument } = await import('./aiService');
      const fallbackResult = await processRfqDocument(filePath, fileName);

      if (fallbackResult.structuredData && Object.keys(fallbackResult.structuredData).length > 0) {
        console.log(`✅ Fallback extraction succeeded for ${fileName}`);
        const fallbackText = fallbackResult.text || (fallbackResult as any).extractedText || '';
        return {
          text: fallbackText,
          extractedText: fallbackText,
          structuredData: fallbackResult.structuredData,
          processingTime: Date.now() - startTime,
          model: 'fallback',
          success: true,
          retryCount: 0
        };
      }
    } catch (fallbackError) {
      console.error(`Fallback extraction also failed for ${fileName}:`, fallbackError);
    }

    return {
      text: '',
      extractedText: '',
      structuredData: { fileName },
      processingTime: Date.now() - startTime,
      model: 'none',
      success: false,
      retryCount: 0
    };
  }

  // AI processing with enhanced retry logic
  options.onProgress?.({ stage: 'ai_processing', percentage: 60, message: 'Processing with AI models' });

  const processingAttempt = async (attempt: number): Promise<any> => {
    const models = [
      { name: 'groq', client: getGroqClient(), model: GROQ_MODEL },
      { name: 'gemini', client: gemini, model: GEMINI_MODEL },
      { name: 'openai', client: openai, model: OPENAI_MODEL }
    ];

    for (const { name, client, model } of models) {
      if (!client) continue;

      try {
        console.log(`Attempt ${attempt + 1}: Trying ${name} model`);
        options.onProgress?.({ 
          stage: 'ai_processing', 
          percentage: 70 + (attempt * 10), 
          message: `Processing with ${name} AI model (attempt ${attempt + 1})` 
        });

        const structuredData = await processWithModel(client, model, extractedText, name);

        if (structuredData && Object.keys(structuredData).length > 0) {
          console.log(`✅ Success with ${name} model on attempt ${attempt + 1}`);
          return { structuredData, model: name };
        }
      } catch (error) {
        console.error(`❌ ${name} model failed on attempt ${attempt + 1}:`, error);
        continue;
      }
    }

    throw new Error(`All AI models failed on attempt ${attempt + 1}`);
  };

  // Retry logic with exponential backoff
  let result;
  while (retryCount <= maxRetries) {
    try {
      result = await processingAttempt(retryCount);
      break;
    } catch (error) {
      retryCount++;
      if (retryCount > maxRetries) {
        console.error(`All retry attempts exhausted for ${fileName}`);
        return {
          text: extractedText,
          extractedText: extractedText,
          structuredData: { fileName },
          processingTime: Date.now() - startTime,
          model: 'failed',
          success: false,
          retryCount
        };
      }

      // Exponential backoff delay
      const delay = RETRY_DELAY * Math.pow(2, retryCount - 1);
      console.log(`Retrying in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries + 1})`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  const processingTime = Date.now() - startTime;
  options.onProgress?.({ stage: 'complete', percentage: 100, message: 'AI processing complete' });

  return {
    text: extractedText,
    extractedText,
    structuredData: result.structuredData,
    processingTime,
    model: result.model,
    success: true,
    retryCount
  };
}

/**
 * Process text with a specific AI model
 */
async function processWithModel(
  client: any,
  model: string,
  extractedText: string,
  modelName: string
): Promise<any> {
  const constructionOptimizedPrompt = `You are an expert construction project analyst. Extract comprehensive RFQ information from this construction document and return ONLY valid JSON:

{
  "fileName": "filename from document or context",
  "projectDescription": "detailed project description with construction specifics",
  "contactName": "primary contact person name",
  "contactEmail": "primary contact email address", 
  "projectSummary": "concise project overview focusing on construction scope",
  "requirements": ["key requirement 1", "key requirement 2", "key requirement 3"],
  "finalAwardDate": "YYYY-MM-DD format or null",
  "projectLocation": "complete project location/address",
  "aiSummary": "# PROJECT OVERVIEW\n\n**Project:** [Project Name]\n**Location:** [Location]\n**Due Date:** [Date]\n\n## SCOPE OF WORK\n[Detailed analysis of construction scope, trade types involved, and project complexity]\n\n## KEY REQUIREMENTS\n- [Critical requirement 1]\n- [Critical requirement 2]\n- [Critical requirement 3]\n\n## SUBMISSION REQUIREMENTS\n[Bid submission details, deadlines, required documents]\n\n## TIMELINE & MILESTONES\n[Project timeline, key dates, completion requirements]\n\n## CONTACT INFORMATION\n**Primary Contact:** [Name and email]\n**Organization:** [Owner/Client organization]\n\n## ANALYSIS & INSIGHTS\n[Professional assessment of project complexity, potential challenges, and opportunities]"
}

CONSTRUCTION DOCUMENT CONTENT:
${extractedText}

Focus on extracting construction-specific details like trade categories, project phases, materials, specifications, and submission requirements. Generate a comprehensive markdown-formatted aiSummary that provides professional insights for construction contractors.

Return only the JSON object:`;

  const timeout = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`${modelName} timeout`)), AI_TIMEOUT);
  });

  const aiRequest = (async () => {
    if (modelName === 'gemini' && client) {
      const result = await client.generateContent(constructionOptimizedPrompt);
      const response = await result.response;
      return response.text();
    } else if (client) {
      const completion = await client.chat.completions.create({
        model,
        messages: [{ role: "user", content: constructionOptimizedPrompt }],
        temperature: 0.1,
        max_tokens: 3000, // Increased for comprehensive AI summaries
      });
      return completion.choices[0]?.message?.content || '';
    }
    throw new Error('Invalid client configuration');
  })();

  const response = await Promise.race([aiRequest, timeout]);

  // Parse JSON response
  const jsonMatch = response.match(/\{[\s\S]*\}/);
  if (!jsonMatch) {
    throw new Error('No JSON found in response');
  }

  return JSON.parse(jsonMatch[0]);
}

/**
 * Get processing statistics
 */
export function getProcessingStats() {
  return streamProcessor.getProcessingStats();
}

/**
 * Smart content prioritization for large documents
 */
function applySmartTruncation(text: string, fileName: string): string {
  const TARGET_LENGTH = 150000;
  const PRIORITY_BUFFER = 50000; // Reserve 50K for priority content
  const GENERAL_BUFFER = TARGET_LENGTH - PRIORITY_BUFFER;

  console.log(`Smart truncation for ${fileName}: targeting ${TARGET_LENGTH} chars`);

  // Split text by pages to preserve structure
  const pages = text.split(/--- Page \d+ ---/);
  const pageHeaders = text.match(/--- Page \d+ ---/g) || [];

  // Priority section patterns (case-insensitive)
  const priorityPatterns = [
    /project\s+(description|summary|overview|scope|details)/gi,
    /scope\s+of\s+work/gi,
    /requirements?/gi,
    /specifications?/gi,
    /general\s+conditions/gi,
    /special\s+conditions/gi,
    /technical\s+specifications/gi,
    /project\s+manual/gi,
    /bid\s+instructions?/gi,
    /addend(a|um)/gi,
    /schedule/gi,
    /timeline/gi,
    /contact\s+information/gi,
    /owner.*information/gi
  ];

  let priorityContent = '';
  let generalContent = '';
  let structureMarkers = `=== DOCUMENT: ${fileName} ===\n\n`;

  // Extract priority content from each page
  for (let i = 0; i < pages.length; i++) {
    const page = pages[i];
    const header = pageHeaders[i] || `--- Page ${i + 1} ---`;

    if (!page.trim()) continue;

    // Check if page contains priority content
    const hasPriorityContent = priorityPatterns.some(pattern => pattern.test(page));

    if (hasPriorityContent) {
      // Extract priority sections from this page
      const prioritySections = extractPrioritySections(page, priorityPatterns);
      if (prioritySections.length > 0) {
        priorityContent += `\n${header}\n${prioritySections.join('\n\n')}\n`;
      }
    } else {
      // Add to general content
      generalContent += `\n${header}\n${page}\n`;
    }
  }

  // Build final content with smart allocation
  let finalContent = structureMarkers;

  // Add priority content (up to 50K chars)
  if (priorityContent.length > 0) {
    if (priorityContent.length > PRIORITY_BUFFER) {
      priorityContent = priorityContent.substring(0, PRIORITY_BUFFER - 100) + "\n\n[Priority content truncated]";
    }
    finalContent += priorityContent;
  }

  // Fill remaining space with general content
  const remainingSpace = TARGET_LENGTH - finalContent.length - 200; // Reserve space for footer
  if (remainingSpace > 0 && generalContent.length > 0) {
    if (generalContent.length > remainingSpace) {
      generalContent = generalContent.substring(0, remainingSpace - 100) + "\n\n[General content truncated]";
    }
    finalContent += generalContent;
  }

  // Add summary footer
  finalContent += "\n\n=== SMART TRUNCATION SUMMARY ===";
  finalContent += `\nOriginal length: ${text.length} characters`;
  finalContent += `\nFinal length: ${finalContent.length} characters`;
  finalContent += `\nPriority content: ${priorityContent.length} characters`;
  finalContent += `\nGeneral content: ${Math.min(generalContent.length, remainingSpace)} characters`;

  console.log(`Smart truncation completed: ${text.length} → ${finalContent.length} chars`);
  return finalContent;
}

/**
 * Extract priority sections from page content
 */
function extractPrioritySections(pageContent: string, patterns: RegExp[]): string[] {
  const sections = [];
  const lines = pageContent.split('\n');
  let currentSection = '';
  let inPrioritySection = false;

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Check if line starts a priority section
    const isPriorityHeader = patterns.some(pattern => {
      pattern.lastIndex = 0; // Reset regex state
      return pattern.test(trimmedLine);
    });

    if (isPriorityHeader) {
      // Save previous section if exists
      if (currentSection.trim()) {
        sections.push(currentSection.trim());
      }
      currentSection = line + '\n';
      inPrioritySection = true;
    } else if (inPrioritySection) {
      currentSection += line + '\n';

      // End section if we hit a clear break (empty lines or new major heading)
      if (trimmedLine === '' && currentSection.length > 200) {
        // Look ahead to see if next significant content starts new section
        const nextLines = lines.slice(lines.indexOf(line) + 1, lines.indexOf(line) + 5);
        const hasNewSection = nextLines.some(nextLine => 
          nextLine.trim().length > 0 && 
          patterns.some(pattern => {
            pattern.lastIndex = 0;
            return pattern.test(nextLine.trim());
          })
        );

        if (hasNewSection) {
          sections.push(currentSection.trim());
          currentSection = '';
          inPrioritySection = false;
        }
      }
    }
  }

  // Add final section
  if (currentSection.trim()) {
    sections.push(currentSection.trim());
  }

  return sections;
}

/**
 * Clean up resources
 */
export async function cleanupProcessing() {
  await streamProcessor.cleanup();
}