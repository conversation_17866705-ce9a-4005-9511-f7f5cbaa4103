#!/usr/bin/env node

/**
 * Railway Deployment Debug Script
 * This script helps diagnose DATABASE_URL issues in Railway deployments
 */

console.log('=== RAILWAY DEPLOYMENT DEBUG SCRIPT ===');
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);
console.log('Architecture:', process.arch);
console.log('Current working directory:', process.cwd());
console.log('');

// Check NODE_ENV
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('');

// Check for DATABASE_URL and related variables
console.log('=== DATABASE ENVIRONMENT VARIABLES ===');
const dbRelatedVars = Object.keys(process.env).filter(key => 
  key.toLowerCase().includes('database') || 
  key.toLowerCase().includes('db') || 
  key.toLowerCase().includes('pg') ||
  key.toLowerCase().includes('neon')
);

if (dbRelatedVars.length === 0) {
  console.log('❌ No database-related environment variables found!');
} else {
  dbRelatedVars.forEach(key => {
    const value = process.env[key];
    if (key === 'DATABASE_URL' && value) {
      // Show partial DATABASE_URL for security
      const url = new URL(value);
      console.log(`✅ ${key}: ${url.protocol}//${url.username}:***@${url.host}${url.pathname}${url.search}`);
    } else {
      console.log(`${value ? '✅' : '❌'} ${key}: ${value ? '[SET]' : '[NOT SET]'}`);
    }
  });
}
console.log('');

// Check all environment variables count
console.log('=== ENVIRONMENT SUMMARY ===');
console.log('Total environment variables:', Object.keys(process.env).length);
console.log('');

// Test DATABASE_URL parsing if it exists
if (process.env.DATABASE_URL) {
  console.log('=== DATABASE_URL VALIDATION ===');
  try {
    const url = new URL(process.env.DATABASE_URL);
    console.log('✅ DATABASE_URL is a valid URL');
    console.log('  Protocol:', url.protocol);
    console.log('  Host:', url.hostname);
    console.log('  Port:', url.port || '5432 (default)');
    console.log('  Database:', url.pathname.slice(1));
    console.log('  SSL Mode:', url.searchParams.get('sslmode') || 'not specified');
    console.log('  Username:', url.username ? '[SET]' : '[NOT SET]');
    console.log('  Password:', url.password ? '[SET]' : '[NOT SET]');
  } catch (error) {
    console.log('❌ DATABASE_URL is not a valid URL:', error.message);
  }
} else {
  console.log('=== DATABASE_URL VALIDATION ===');
  console.log('❌ DATABASE_URL is not set');
}
console.log('');

// Check for .env file (should not exist in production)
const fs = require('fs');
const path = require('path');

console.log('=== FILE SYSTEM CHECK ===');
const envPath = path.join(process.cwd(), '.env');
try {
  if (fs.existsSync(envPath)) {
    console.log('⚠️  .env file exists in production (this might be the issue!)');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    console.log('  .env file contains', lines.length, 'non-comment lines');
    
    // Check if DATABASE_URL is in .env
    const hasDbUrl = lines.some(line => line.startsWith('DATABASE_URL='));
    console.log('  DATABASE_URL in .env:', hasDbUrl ? '✅ YES' : '❌ NO');
  } else {
    console.log('✅ No .env file found (correct for production)');
  }
} catch (error) {
  console.log('❌ Error checking .env file:', error.message);
}
console.log('');

// Railway-specific checks
console.log('=== RAILWAY ENVIRONMENT CHECK ===');
const railwayVars = Object.keys(process.env).filter(key => 
  key.startsWith('RAILWAY_') || key.startsWith('NIXPACKS_')
);

if (railwayVars.length > 0) {
  console.log('✅ Railway environment detected');
  railwayVars.forEach(key => {
    console.log(`  ${key}: ${process.env[key]}`);
  });
} else {
  console.log('⚠️  No Railway-specific environment variables found');
}
console.log('');

// Final recommendations
console.log('=== RECOMMENDATIONS ===');
if (!process.env.DATABASE_URL) {
  console.log('🔧 CRITICAL: Set DATABASE_URL in Railway dashboard:');
  console.log('   1. Go to your Railway project dashboard');
  console.log('   2. Navigate to Variables tab');
  console.log('   3. Add DATABASE_URL with your Neon database connection string');
  console.log('   4. Redeploy your application');
}

if (process.env.NODE_ENV !== 'production') {
  console.log('🔧 Set NODE_ENV=production in Railway dashboard');
}

console.log('');
console.log('=== END DEBUG SCRIPT ===');
