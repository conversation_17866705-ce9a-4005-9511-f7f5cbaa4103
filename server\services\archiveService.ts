import { Readable } from 'stream';
import archiver from 'archiver';
import { storage } from '../storage';
import { downloadFileStream } from './objectStorageService';
import { logger } from '../utils/logger';
import type { Rfq, RfqDocument, Bid, BidDocument } from '@shared/schema';

export interface ArchiveMetadata {
  rfq: {
    id: string;
    projectName: string;
    projectLocation: string;
    status: string;
    createdAt: string;
    dueDate: string;
    bidProposalDeadlineAt: string | null;
  };
  bids: Array<{
    id: string;
    contractorId: string;
    contractorName: string;
    submittedAt: string;
    totalAmount: number;
    status: string;
    documents: Array<{
      fileName: string;
      fileSize: number;
      objectKey: string;
    }>;
  }>;
  documents: Array<{
    fileName: string;
    fileSize: number;
    objectKey: string;
    fileType: string;
  }>;
  archivedAt: string;
  archivedBy: string;
}

export class ArchiveService {
  /**
   * Create a ZIP archive stream for an RFQ with all related documents and bids
   */
  static async createRfqArchiveStream(rfqId: string, organizationId: string): Promise<{ stream: Readable; metadata: ArchiveMetadata }> {
    try {
      // Get RFQ details
      const rfq = await storage.getRfq(rfqId);
      if (!rfq || rfq.organizationId !== organizationId) {
        throw new Error('RFQ not found or access denied');
      }

      // Get RFQ documents
      const rfqDocuments = await storage.getRfqDocuments(rfqId);
      
      // Get all bids for this RFQ
      const bids = await storage.getBidsByRfq(rfqId);
      
      // Get bid documents for all bids
      const bidDocuments: { [bidId: string]: any[] } = {};
      for (const bid of bids) {
        bidDocuments[bid.id] = await storage.getBidDocuments(bid.id);
      }

      // Get contractor details for bids
      const contractorDetails: { [contractorId: string]: any } = {};
      for (const bid of bids) {
        if (bid.contractorId && !contractorDetails[bid.contractorId]) {
          contractorDetails[bid.contractorId] = await storage.getContractor(bid.contractorId);
        }
      }

      // Create metadata
      const metadata: ArchiveMetadata = {
        rfq: {
          id: rfq.id,
          projectName: rfq.projectName || 'Untitled Project',
          projectLocation: rfq.projectLocation || '',
          status: rfq.status || 'Draft',
          createdAt: rfq.createdAt?.toISOString() || '',
          dueDate: rfq.dueDate?.toISOString() || '',
          bidProposalDeadlineAt: rfq.bidProposalDeadlineAt?.toISOString() || null,
        },
        bids: bids
          .filter(bid => bid.contractorId) // Only include bids with valid contractor IDs
          .map(bid => ({
            id: bid.id,
            contractorId: bid.contractorId!,
            contractorName: contractorDetails[bid.contractorId!]?.companyName || 'Unknown Contractor',
            submittedAt: bid.submittedAt?.toISOString() || '',
            totalAmount: parseFloat(bid.bidAmount || '0'),
            status: bid.status || 'submitted',
            documents: (bidDocuments[bid.id] || []).map((doc: any) => ({
              fileName: doc.fileName,
              fileSize: doc.fileSize || 0,
              objectKey: doc.objectKey,
            })),
          })),
        documents: rfqDocuments.map(doc => ({
          fileName: doc.fileName,
          fileSize: doc.fileSize || 0,
          objectKey: doc.objectKey,
          fileType: doc.fileType || 'supporting',
        })),
        archivedAt: new Date().toISOString(),
        archivedBy: rfq.createdBy || 'unknown',
      };

      // Create ZIP archive stream
      const archive = archiver('zip', {
        zlib: { level: 9 } // Best compression
      });

      // Add metadata file
      archive.append(JSON.stringify(metadata, null, 2), { name: 'archive-metadata.json' });

      // Add RFQ documents
      for (const doc of rfqDocuments) {
        try {
          const fileStreamResult = await downloadFileStream(doc.objectKey);
          archive.append(fileStreamResult.stream, { name: `rfq-documents/original/${doc.fileName}` });
        } catch (error) {
          logger.warn(`Failed to add RFQ document ${doc.fileName} to archive: ${error}`);
       }
      }

      // Add bid documents organized by contractor
      for (const bid of bids) {
        if (!bid.contractorId) continue; // Skip bids without contractor IDs
        
        const contractorName = contractorDetails[bid.contractorId]?.companyName || `contractor-${bid.contractorId}`;
        const safeName = contractorName.replace(/[^a-zA-Z0-9-_]/g, '-');
        
        for (const doc of bidDocuments[bid.id] || []) {
          try {
            const fileStreamResult = await downloadFileStream(doc.objectKey);
            archive.append(fileStreamResult.stream, { name: `bids/${safeName}/${doc.fileName}` });
          } catch (error) {
            logger.warn(`Failed to add bid document ${doc.fileName} to archive: ${error}`);
          }
        }
      }

      // Finalize the archive
      archive.finalize();

      return { stream: archive, metadata };
    } catch (error) {
      logger.error(`Failed to create RFQ archive: ${error}`);
      throw error;
    }
  }

  /**
   * Archive one or more RFQs
   */
  static async archiveRfqs(rfqIds: string[], organizationId: string, userId: string): Promise<{ success: boolean; archivedCount: number }> {
    try {
      const result = await storage.updateRfqsArchiveStatus(rfqIds, organizationId, true);
      
      // Log the archive action for audit
      for (const rfqId of rfqIds) {
        await storage.createBusinessAuditLog({
          userId,
          eventType: 'rfq_archived',
          eventData: { 
            rfqId,
            archivedAt: new Date().toISOString(),
            organizationId 
          },
          resourceId: rfqId,
        });
      }

      return { success: true, archivedCount: result.affectedRows || rfqIds.length };
    } catch (error) {
      logger.error(`Failed to archive RFQs: ${error}`);
      throw error;
    }
  }

  /**
   * Unarchive an RFQ
   */
  static async unarchiveRfq(rfqId: string, organizationId: string, userId: string): Promise<{ success: boolean }> {
    try {
      await storage.updateRfqsArchiveStatus([rfqId], organizationId, false);
      
      // Log the unarchive action for audit
      await storage.createBusinessAuditLog({
        userId,
        eventType: 'rfq_unarchived',
        eventData: { 
          rfqId,
          unarchivedAt: new Date().toISOString(),
          organizationId 
        },
        resourceId: rfqId,
      });

      return { success: true };
    } catch (error) {
      logger.error(`Failed to unarchive RFQ: ${error}`);
      throw error;
    }
  }
}
