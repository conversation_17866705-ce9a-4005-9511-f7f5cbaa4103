CREATE TYPE "public"."rfq_status" AS ENUM('Draft', 'Active', 'Review', 'Closed', 'Awarded');--> statement-breakpoint
CREATE TYPE "public"."trade" AS ENUM('electrical', 'plumbing', 'hvac', 'concrete', 'general', 'site_work');--> statement-breakpoint
CREATE TYPE "public"."user_role" AS ENUM('SuperUser', 'Admin', 'Editor', 'Viewer');--> statement-breakpoint
CREATE TABLE "bid_documents" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"bid_id" uuid,
	"file_name" varchar NOT NULL,
	"file_url" varchar NOT NULL,
	"file_size" integer,
	"mime_type" varchar,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "bids" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"rfq_id" uuid,
	"contractor_id" uuid,
	"bid_amount" numeric(12, 2),
	"proposal_text" text,
	"submitted_at" timestamp DEFAULT now(),
	"status" varchar DEFAULT 'Pending'
);
--> statement-breakpoint
CREATE TABLE "contractors" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" varchar,
	"company_name" varchar NOT NULL,
	"legal_structure" varchar,
	"tax_id" varchar,
	"dba" varchar,
	"primary_address" text,
	"mailing_address" text,
	"primary_contact_name" varchar,
	"primary_contact_email" varchar,
	"primary_contact_phone" varchar,
	"primary_contact_title" varchar,
	"trade_types" jsonb,
	"union_status" varchar,
	"union_affiliations" text,
	"certifications" jsonb,
	"service_areas" text,
	"license_number" varchar,
	"license_state" varchar,
	"license_expiration" timestamp,
	"general_liability" text,
	"workers_comp" text,
	"auto_insurance" text,
	"bonding_single" integer,
	"bonding_aggregate" integer,
	"emr" numeric(4, 2),
	"bank_reference" text,
	"surety_reference" text,
	"credit_rating" varchar,
	"payment_terms" varchar,
	"litigation_history" text,
	"project_references" text,
	"years_in_business" integer,
	"specializations" jsonb,
	"awards" text,
	"environmental_programs" text,
	"workforce_size" integer,
	"workforce_breakdown" text,
	"equipment" text,
	"availability" text,
	"keyword_tags" jsonb,
	"preferred_project_types" jsonb,
	"business_address" text,
	"contact_email" varchar,
	"contact_phone" varchar,
	"is_approved" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "forecast_materials" (
	"id" varchar PRIMARY KEY NOT NULL,
	"cost_code" varchar NOT NULL,
	"category" varchar NOT NULL,
	"name" varchar NOT NULL,
	"unit" varchar NOT NULL,
	"current_price" numeric(10, 2),
	"previous_price" numeric(10, 2),
	"change_percent" numeric(5, 2),
	"volume" varchar,
	"time" varchar,
	"ytd_change" numeric(5, 2),
	"ytd_change_percent" numeric(5, 2),
	"last_updated" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "rfq_documents" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"rfq_id" uuid,
	"file_name" varchar NOT NULL,
	"object_key" varchar NOT NULL,
	"file_size" integer,
	"mime_type" varchar,
	"extracted_text" text,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "rfqs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"created_by" varchar,
	"project_name" varchar NOT NULL,
	"project_location" varchar NOT NULL,
	"trade_category" "trade" NOT NULL,
	"description" text,
	"due_date" timestamp NOT NULL,
	"status" "rfq_status" DEFAULT 'Draft',
	"extracted_data" jsonb,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "sessions" (
	"sid" varchar PRIMARY KEY NOT NULL,
	"sess" jsonb NOT NULL,
	"expire" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" varchar PRIMARY KEY NOT NULL,
	"email" varchar,
	"first_name" varchar,
	"last_name" varchar,
	"profile_image_url" varchar,
	"role" "user_role" DEFAULT 'Viewer',
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "bid_documents" ADD CONSTRAINT "bid_documents_bid_id_bids_id_fk" FOREIGN KEY ("bid_id") REFERENCES "public"."bids"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bids" ADD CONSTRAINT "bids_rfq_id_rfqs_id_fk" FOREIGN KEY ("rfq_id") REFERENCES "public"."rfqs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bids" ADD CONSTRAINT "bids_contractor_id_contractors_id_fk" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contractors" ADD CONSTRAINT "contractors_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rfq_documents" ADD CONSTRAINT "rfq_documents_rfq_id_rfqs_id_fk" FOREIGN KEY ("rfq_id") REFERENCES "public"."rfqs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rfqs" ADD CONSTRAINT "rfqs_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "IDX_session_expire" ON "sessions" USING btree ("expire");