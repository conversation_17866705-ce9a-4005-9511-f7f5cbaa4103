import { useQuery } from '@tanstack/react-query';

export interface BidLineItem {
  id: string;
  bidId: string;
  costCode?: string | null;
  description?: string | null;
  quantity?: number | null;
  unitPrice?: number | null;
  unitOfMeasure?: string | null;
  totalPrice?: number | null;
  category?: string | null;
  createdAt?: string | null;
}

export interface ScopeItem {
  id: string;
  bidId: string;
  itemType: 'included' | 'excluded';
  description: string | null;
  category: string | null;
  createdAt?: string | null;
}

export interface StructuredBidData {
  bid?: {
    id: string;
    rfqId: string | null;
    contractorId: string | null;
    bidAmount?: number | string | null;
    status?: string | null;
    bidContactName?: string | null;
    bidContactEmail?: string | null;
    bidContactPhone?: string | null;
  };
  lineItems: {
    items: BidLineItem[];
    count: number;
    total: number;
    categories: string[];
  };
  scopeDefinition: {
    included: { items: ScopeItem[]; count: number; categories: string[] };
    excluded: { items: ScopeItem[]; count: number; categories: string[] };
  };
  summary: {
    hasStructuredData: boolean;
    dataCompleteness: {
      hasLineItems: boolean;
      hasScopeDefinition: boolean;
      hasContactInfo: boolean;
      calculatedTotal: number;
      bidAmountMatch: boolean;
    };
  };
}

export function useStructuredBidData(bidId: string) {
  return useQuery<StructuredBidData>({
    queryKey: ['/api/bids', bidId, 'structured-data'],
    enabled: !!bidId,
  });
}

export function calculateDataQuality(data: StructuredBidData): {
  score: number;
  completeness: number;
  hasLineItems: boolean;
  hasScope: boolean;
  hasStructuredData: boolean;
} {
  const hasLineItems = !!(data.lineItems && data.lineItems.items && data.lineItems.items.length > 0);
  const hasScope = !!(
    data.scopeDefinition && (
      (data.scopeDefinition.included.items && data.scopeDefinition.included.items.length > 0) ||
      (data.scopeDefinition.excluded.items && data.scopeDefinition.excluded.items.length > 0)
    )
  );
  const hasStructuredData = hasLineItems || hasScope;

  let score = 0;
  if (hasLineItems) score += 50;
  if (hasScope) score += 30;
  if (Number(data.bid?.bidAmount || 0) > 0) score += 20;

  const completeness = (data.summary?.dataCompleteness?.hasLineItems ? 50 : 0) + (data.summary?.dataCompleteness?.hasScopeDefinition ? 50 : 0);

  return {
    score: Math.min(score, 100),
    completeness,
    hasLineItems,
    hasScope,
    hasStructuredData,
  };
}