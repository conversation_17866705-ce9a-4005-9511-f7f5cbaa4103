-- Migration: Add RFQ archive support
-- This migration adds the isArchived field and associated index for RFQ archiving functionality

-- Add isArchived column to rfqs table
ALTER TABLE "rfqs" ADD COLUMN "is_archived" BOOLEAN DEFAULT FALSE NOT NULL;

-- Add strategic index for efficient filtering of archived RFQs by organization
CREATE INDEX "IDX_rfqs_org_archived" ON "rfqs" ("organization_id", "is_archived");

-- Add comment for documentation
COMMENT ON COLUMN "rfqs"."is_archived" IS 'Flag indicating if RFQ has been archived by general contractor';
COMMENT ON INDEX "IDX_rfqs_org_archived" IS 'Composite index for efficient organization-scoped archive filtering';
