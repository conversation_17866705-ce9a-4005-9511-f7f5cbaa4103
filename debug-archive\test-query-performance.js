#!/usr/bin/env node

/**
 * Query Performance Testing Script
 * Tests database performance before/after index optimizations
 * Run with: node test-query-performance.js
 */

import { config } from 'dotenv';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import { sql } from 'drizzle-orm';
import ws from 'ws';
import * as schema from './shared/schema.js';

// Load environment variables
config();

// Configure Neon WebSocket
neonConfig.webSocketConstructor = ws;

// Database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

const pool = new Pool({ connectionString });
const db = drizzle(pool, { schema });

// Test queries that should benefit from new indexes
const testQueries = [
  {
    name: 'API Key Usage UPSERT (high frequency)',
    description: 'Tests the new UPSERT pattern for API key tracking',
    query: `
      INSERT INTO api_key_usage (api_key_id, endpoint, method, request_count, request_date, last_request_at)
      VALUES ('test-api-key-id', '/api/test', 'GET', 1, CURRENT_DATE, NOW())
      ON CONFLICT (api_key_id, request_date, endpoint, method) 
      DO UPDATE SET 
        request_count = api_key_usage.request_count + 1,
        last_request_at = NOW()
    `
  },
  {
    name: 'Bids by RFQ and Contractor (multi-tenant)',
    description: 'Tests contractor bid lookups with new indexes',
    query: `
      SELECT b.*, bl.description, bl.total_price 
      FROM bids b
      LEFT JOIN bid_line_items bl ON b.id = bl.bid_id
      WHERE b.rfq_id IN (SELECT id FROM rfqs LIMIT 10)
        AND b.contractor_id IS NOT NULL
      ORDER BY b.created_at DESC
      LIMIT 50
    `
  },
  {
    name: 'Unread Notifications Count (dashboard)',
    description: 'Tests notification bell count queries',
    query: `
      SELECT user_id, COUNT(*) as unread_count
      FROM notifications 
      WHERE read_at IS NULL 
        AND organization_id IS NOT NULL
      GROUP BY user_id
      LIMIT 100
    `
  },
  {
    name: 'RFQ Distribution for Contractor (high traffic)',
    description: 'Tests contractor RFQ listing with new composite index',
    query: `
      SELECT rd.*, r.project_name, r.due_date
      FROM rfq_distribution rd
      JOIN rfqs r ON rd.rfq_id = r.id
      WHERE rd.contractor_id IN (SELECT id FROM contractors LIMIT 5)
        AND rd.viewed_at IS NULL
      ORDER BY r.due_date ASC
      LIMIT 25
    `
  },
  {
    name: 'Active RFQs by Organization (dashboard)',
    description: 'Tests organization dashboard queries',
    query: `
      SELECT organization_id, COUNT(*) as active_rfqs
      FROM rfqs 
      WHERE status = 'Active' 
        AND organization_id IS NOT NULL
      GROUP BY organization_id
      LIMIT 50
    `
  },
  {
    name: 'API Key Usage Analytics (reporting)',
    description: 'Tests API usage reporting queries',
    query: `
      SELECT 
        ak.name,
        aku.endpoint,
        aku.method,
        SUM(aku.request_count) as total_requests,
        MAX(aku.last_request_at) as last_used
      FROM api_key_usage aku
      JOIN api_keys ak ON aku.api_key_id = ak.id
      WHERE aku.request_date >= CURRENT_DATE - INTERVAL '30 days'
        AND ak.is_active = true
      GROUP BY ak.name, aku.endpoint, aku.method
      ORDER BY total_requests DESC
      LIMIT 20
    `
  }
];

/**
 * Run EXPLAIN ANALYZE on a query and return performance metrics
 */
async function analyzeQuery(name, description, queryText) {
  console.log(`\n📊 Testing: ${name}`);
  console.log(`   ${description}`);
  console.log('   ' + '─'.repeat(60));
  
  try {
    // Run EXPLAIN ANALYZE
    const result = await db.execute(sql.raw(`EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${queryText}`));
    const plan = result[0]['QUERY PLAN'][0];
    
    // Extract key metrics
    const executionTime = plan['Execution Time'];
    const totalCost = plan.Plan['Total Cost'];
    const actualRows = plan.Plan['Actual Rows'];
    const planningTime = plan['Planning Time'];
    
    // Check for sequential scans (bad for performance)
    const planText = JSON.stringify(plan);
    const hasSeqScan = planText.includes('Seq Scan');
    const hasIndexScan = planText.includes('Index Scan') || planText.includes('Index Only Scan');
    
    // Extract buffer statistics
    const sharedHit = extractBufferStat(planText, 'Shared Hit');
    const sharedRead = extractBufferStat(planText, 'Shared Read');
    
    console.log(`   ⏱️  Execution Time: ${executionTime.toFixed(2)}ms`);
    console.log(`   💰 Total Cost: ${totalCost.toFixed(2)}`);
    console.log(`   📊 Rows Returned: ${actualRows}`);
    console.log(`   🧠 Planning Time: ${planningTime.toFixed(2)}ms`);
    
    if (hasSeqScan) {
      console.log(`   ⚠️  SEQUENTIAL SCAN detected - may need index`);
    }
    if (hasIndexScan) {
      console.log(`   ✅ Index usage detected`);
    }
    
    if (sharedHit > 0 || sharedRead > 0) {
      console.log(`   💾 Buffer: ${sharedHit} hit, ${sharedRead} read`);
    }
    
    // Performance rating
    let rating = '🟢 Excellent';
    if (executionTime > 100) rating = '🟡 Good';
    if (executionTime > 500) rating = '🟠 Fair';
    if (executionTime > 1000) rating = '🔴 Poor';
    if (hasSeqScan && actualRows > 1000) rating = '🔴 Poor (Seq Scan)';
    
    console.log(`   📈 Performance: ${rating}`);
    
    return {
      name,
      executionTime,
      totalCost,
      actualRows,
      planningTime,
      hasSeqScan,
      hasIndexScan,
      rating
    };
    
  } catch (error) {
    console.log(`   ❌ Query failed: ${error.message}`);
    return {
      name,
      error: error.message,
      rating: '❌ Failed'
    };
  }
}

/**
 * Extract buffer statistics from plan text
 */
function extractBufferStat(planText, statName) {
  const regex = new RegExp(`"${statName}":\\s*(\\d+)`, 'i');
  const match = planText.match(regex);
  return match ? parseInt(match[1]) : 0;
}

/**
 * Check if required indexes exist
 */
async function checkIndexes() {
  console.log('\n🔍 Checking Index Status');
  console.log('═'.repeat(70));
  
  const indexChecks = [
    'idx_bids_rfq_contractor',
    'idx_bids_contractor_created', 
    'idx_api_key_usage_unique_daily',
    'idx_notifications_user_unread',
    'idx_rfq_distribution_contractor_unviewed',
    'idx_rfqs_active_status'
  ];
  
  for (const indexName of indexChecks) {
    try {
      const result = await db.execute(sql.raw(`
        SELECT 
          schemaname, 
          tablename, 
          indexname,
          idx_scan,
          idx_tup_read
        FROM pg_stat_user_indexes 
        WHERE indexname = '${indexName}'
      `));
      
      if (result.length > 0) {
        const idx = result[0];
        console.log(`   ✅ ${indexName}: ${idx.idx_scan} scans, ${idx.idx_tup_read} rows`);
      } else {
        console.log(`   ❌ ${indexName}: NOT FOUND`);
      }
    } catch (error) {
      console.log(`   ⚠️  ${indexName}: Error checking - ${error.message}`);
    }
  }
}

/**
 * Get database statistics
 */
async function getDatabaseStats() {
  console.log('\n📈 Database Statistics');
  console.log('═'.repeat(70));
  
  try {
    // Table sizes
    const tableSizes = await db.execute(sql.raw(`
      SELECT 
        relname as table_name,
        pg_size_pretty(pg_total_relation_size(relid)) as size,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_tup_hot_upd as hot_updates
      FROM pg_stat_user_tables 
      WHERE relname IN ('rfqs', 'bids', 'bid_line_items', 'api_key_usage', 'notifications')
      ORDER BY pg_total_relation_size(relid) DESC
    `));
    
    console.log('   Table Activity:');
    tableSizes.forEach(table => {
      console.log(`   📋 ${table.table_name}: ${table.size} (${table.inserts}i/${table.updates}u/${table.deletes}d)`);
    });
    
  } catch (error) {
    console.log(`   ❌ Could not fetch database stats: ${error.message}`);
  }
}

/**
 * Main test execution
 */
async function runPerformanceTests() {
  console.log('🚀 Bidaible Database Performance Test Suite');
  console.log('═'.repeat(70));
  console.log(`📅 Started at: ${new Date().toISOString()}`);
  
  // Check database connection
  try {
    await db.execute(sql.raw('SELECT 1'));
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
  
  // Get database stats
  await getDatabaseStats();
  
  // Check indexes
  await checkIndexes();
  
  // Run query tests
  console.log('\n🧪 Query Performance Tests');
  console.log('═'.repeat(70));
  
  const results = [];
  for (const test of testQueries) {
    const result = await analyzeQuery(test.name, test.description, test.query);
    results.push(result);
  }
  
  // Summary report
  console.log('\n📋 Performance Summary');
  console.log('═'.repeat(70));
  
  const successful = results.filter(r => !r.error);
  const failed = results.filter(r => r.error);
  const withSeqScans = successful.filter(r => r.hasSeqScan);
  const avgExecutionTime = successful.reduce((sum, r) => sum + r.executionTime, 0) / successful.length;
  
  console.log(`✅ Successful queries: ${successful.length}/${results.length}`);
  console.log(`⚠️  Queries with sequential scans: ${withSeqScans.length}`);
  console.log(`⏱️  Average execution time: ${avgExecutionTime.toFixed(2)}ms`);
  
  if (failed.length > 0) {
    console.log(`❌ Failed queries: ${failed.length}`);
    failed.forEach(f => console.log(`   - ${f.name}: ${f.error}`));
  }
  
  if (withSeqScans.length > 0) {
    console.log(`\n⚠️  Queries needing index optimization:`);
    withSeqScans.forEach(q => console.log(`   - ${q.name} (${q.executionTime.toFixed(2)}ms)`));
  }
  
  console.log('\n🎯 Recommendations:');
  if (withSeqScans.length > 0) {
    console.log('   1. Deploy the missing indexes from migrations/0012_add_missing_indexes.sql');
  }
  if (avgExecutionTime > 100) {
    console.log('   2. Consider adding Redis caching for frequently accessed data');
  }
  if (successful.some(r => r.actualRows > 10000)) {
    console.log('   3. Add LIMIT clauses to queries returning many rows');
  }
  
  console.log(`\n🏁 Test completed at: ${new Date().toISOString()}`);
}

// Handle cleanup on exit
process.on('SIGINT', async () => {
  console.log('\n🛑 Test interrupted, cleaning up...');
  await pool.end();
  process.exit(0);
});

// Run the tests
runPerformanceTests()
  .catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  })
  .finally(async () => {
    await pool.end();
  });
