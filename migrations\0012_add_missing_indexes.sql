-- Migration: Add missing multi-tenant and high-traffic indexes
-- Created: 2025-08-24
-- Purpose: Optimize query performance for 10k+ concurrent users

-- ==============================================================================
-- MULTI-TENANT FILTERING INDEXES
-- Critical for organization-based data isolation and RLS policies
-- ==============================================================================

-- Bids table indexes (CRITICAL - highest traffic)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bids_rfq_id 
  ON bids(rfq_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bids_contractor_id 
  ON bids(contractor_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bids_rfq_contractor 
  ON bids(rfq_id, contractor_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bids_contractor_created 
  ON bids(contractor_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bids_status 
  ON bids(status) WHERE status IS NOT NULL;

-- Bid Documents indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bid_documents_bid_id 
  ON bid_documents(bid_id);

-- Bid Line Items indexes  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bid_line_items_bid_id 
  ON bid_line_items(bid_id);

-- Bid Inclusions/Exclusions indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bid_inclusions_exclusions_bid_id 
  ON bid_inclusions_exclusions(bid_id);

-- RFQ Documents indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfq_documents_rfq_id 
  ON rfq_documents(rfq_id);

-- RFQ Distribution indexes (high traffic for contractor notifications)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfq_distribution_rfq_contractor 
  ON rfq_distribution(rfq_id, contractor_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfq_distribution_contractor_unviewed 
  ON rfq_distribution(contractor_id) 
  WHERE viewed_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfq_distribution_contractor_created 
  ON rfq_distribution(contractor_id, created_at DESC);

-- ==============================================================================
-- HIGH-TRAFFIC LOOKUP INDEXES
-- Optimize frequent dashboard and listing queries
-- ==============================================================================

-- API Key Usage indexes (hot table - every request)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_key_usage_composite 
  ON api_key_usage(api_key_id, request_date, endpoint, method);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_key_usage_last_request 
  ON api_key_usage(api_key_id, last_request_at DESC);

-- API Keys indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_keys_user_active 
  ON api_keys(user_id, is_active) 
  WHERE is_active = true;

-- Notifications indexes (unread counts, bell notifications)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread 
  ON notifications(user_id, organization_id) 
  WHERE read_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_created 
  ON notifications(user_id, created_at DESC);

-- Notification Deliveries indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notification_deliveries_notification_id 
  ON notification_deliveries(notification_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notification_deliveries_status 
  ON notification_deliveries(delivery_status, scheduled_for);

-- Scheduled Notifications indexes (background processor)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scheduled_notifications_pending 
  ON scheduled_notifications(status, scheduled_for) 
  WHERE status = 'pending';

-- User Feedback indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_feedback_user_created 
  ON user_feedback(user_id, created_at DESC);

-- Contractor Favorites indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contractor_favorites_contractor_rfq 
  ON contractor_favorites(contractor_id, rfq_id);

-- ==============================================================================
-- DASHBOARD AND ANALYTICS INDEXES
-- Optimize common dashboard queries and counts
-- ==============================================================================

-- RFQs active status index (dashboard counters)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfqs_active_status 
  ON rfqs(status) 
  WHERE status = 'Active';

-- RFQs by organization and due date (contractor dashboard)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfqs_org_due_date 
  ON rfqs(organization_id, due_date DESC) 
  WHERE status IN ('Active', 'Draft');

-- Business Audit Log indexes (compliance reporting)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_audit_user_action 
  ON business_audit_log(user_id, action, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_audit_created 
  ON business_audit_log(created_at DESC);

-- Access Audit Log indexes (security monitoring)  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_access_audit_user_created 
  ON access_audit_log(user_id, created_at DESC);

-- ==============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- Support multi-column WHERE clauses and ORDER BY
-- ==============================================================================

-- Users by organization with activity status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_org_active_created 
  ON users(organization_id, is_active, created_at DESC);

-- Contractors by organization with status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contractors_org_status 
  ON contractors(organization_id, status, created_at DESC);

-- Notification Preferences by user (settings queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notification_preferences_user 
  ON notification_preferences(user_id);

-- ==============================================================================
-- UNIQUE CONSTRAINTS AND COMPOSITE INDEXES
-- Prevent duplicates and support UPSERT operations
-- ==============================================================================

-- Ensure unique API key usage records per day/endpoint/method
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_api_key_usage_unique_daily 
  ON api_key_usage(api_key_id, request_date, endpoint, method);

-- Ensure unique RFQ distribution per RFQ/contractor pair
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_rfq_distribution_unique 
  ON rfq_distribution(rfq_id, contractor_id);

-- Ensure unique contractor favorites
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_contractor_favorites_unique 
  ON contractor_favorites(contractor_id, rfq_id);

-- ==============================================================================
-- INDEX MAINTENANCE SETTINGS
-- Optimize for heavy write workloads
-- ==============================================================================

-- Set fillfactor for tables with frequent updates (leave room for HOT updates)
-- Note: These ALTER TABLE commands require ACCESS EXCLUSIVE locks
-- Run during maintenance window or low-traffic periods

-- ALTER TABLE api_key_usage SET (fillfactor = 90);
-- ALTER TABLE business_audit_log SET (fillfactor = 90);  
-- ALTER TABLE access_audit_log SET (fillfactor = 90);
-- ALTER TABLE notification_deliveries SET (fillfactor = 90);

-- ==============================================================================
-- VERIFICATION QUERIES
-- Run these to verify index usage after deployment
-- ==============================================================================

/*
-- Check index usage statistics
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Check for unused indexes
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' AND idx_scan = 0
ORDER BY tablename;

-- Monitor index bloat
SELECT tablename, indexname, 
  pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY pg_relation_size(indexrelid) DESC;
*/
