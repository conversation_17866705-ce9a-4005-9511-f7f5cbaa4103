# RFQ Deadline Management System Documentation

**Date:** August 20, 2025  
**Version:** 1.0  
**Status:** Production Ready

## Overview

The RFQ Deadline Management System is a comprehensive feature that manages dual deadlines for construction project bidding with automated notifications, real-time countdown timers, and background processing. The system ensures proper deadline sequencing and keeps all stakeholders informed about approaching deadlines.

## Key Features

### Dual Deadline System
- **Bid Proposal Deadline**: When contractors must submit their bids
- **RFQ Project Deadline**: Overall project completion deadline
- **Automatic Validation**: Ensures bid proposal deadline precedes project deadline
- **Database Constraints**: SQL-level validation prevents invalid deadline combinations

### Automated Notification System
- **Scheduled Reminders**: 1 week, 3 days, and 1 day before bid proposal deadline
- **Background Processing**: Notification processor runs every minute
- **Email Delivery**: Automatic email notifications to RFQ creators
- **Status Tracking**: Complete audit trail of notification delivery attempts

### Real-time User Experience
- **Countdown Timers**: Live countdown displays throughout the platform
- **Color-coded Urgency**: Visual indicators for deadline urgency levels
- **React-Toastify Integration**: Immediate user feedback for deadline-related actions
- **Auto-refresh Components**: Countdown timers update every second

## Technical Architecture

### Database Schema

#### RFQ Table Updates
```sql
-- Added bid proposal deadline column
ALTER TABLE rfqs ADD COLUMN bid_proposal_deadline_at TIMESTAMPTZ NOT NULL;

-- Validation constraint ensuring proper deadline order
ALTER TABLE rfqs ADD CONSTRAINT chk_bid_proposal_before_rfq_deadline 
CHECK (bid_proposal_deadline_at < due_date);

-- Performance indexes
CREATE INDEX IDX_rfqs_bid_proposal_deadline ON rfqs(bid_proposal_deadline_at);
CREATE INDEX IDX_rfqs_org_bid_deadline ON rfqs(organization_id, bid_proposal_deadline_at);
```

#### Scheduled Notifications Table
```sql
-- New table for managing scheduled deadline notifications
CREATE TABLE scheduled_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_type TEXT NOT NULL,
  user_id VARCHAR REFERENCES users(id) NOT NULL,
  rfq_id UUID REFERENCES rfqs(id),
  scheduled_for TIMESTAMPTZ NOT NULL,
  payload JSONB NOT NULL DEFAULT '{}',
  status scheduled_notification_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Status enum for notification tracking
CREATE TYPE scheduled_notification_status AS ENUM ('pending', 'sent', 'failed', 'cancelled');

-- Performance indexes
CREATE INDEX IDX_scheduled_notifications_status_scheduled ON scheduled_notifications(status, scheduled_for);
CREATE INDEX IDX_scheduled_notifications_user_id ON scheduled_notifications(user_id);
CREATE INDEX IDX_scheduled_notifications_rfq_id ON scheduled_notifications(rfq_id);
```

### Backend Services

#### Scheduled Notification Processor
**File**: `server/services/scheduledNotificationProcessor.ts`

```typescript
export class ScheduledNotificationProcessor {
  private processingInterval?: NodeJS.Timeout;
  private readonly intervalMs = 60000; // Check every minute

  start() {
    this.processScheduledNotifications();
    this.processingInterval = setInterval(() => {
      this.processScheduledNotifications();
    }, this.intervalMs);
  }

  private async processScheduledNotifications() {
    const pendingNotifications = await storage.getPendingScheduledNotifications();
    
    for (const notification of pendingNotifications) {
      await this.processNotification(notification);
    }
  }
}
```

#### Notification Service Integration
**File**: `server/services/notificationService.ts`

```typescript
// RFQ deadline reminder notification type
static readonly NotificationTypes = {
  RFQ_DEADLINE_REMINDER: 'RFQ_DEADLINE_REMINDER',
  // ... other types
};

// Method to create deadline reminder notifications
static async createRfqDeadlineReminderNotification(params: {
  userId: string;
  rfqId: string;
  projectName: string;
  deadlineDate: string;
  reminderType: string;
}) {
  // Implementation creates notification and delivery records
}
```

#### RFQ Route Integration
**File**: `server/routes.ts`

```typescript
// Function to schedule deadline reminders when RFQ is created
async function scheduleRfqDeadlineReminders(rfq: any) {
  const reminderTimes = [
    { days: 7, type: '1_week_before' },
    { days: 3, type: '3_days_before' },
    { days: 1, type: '1_day_before' }
  ];

  for (const reminder of reminderTimes) {
    const scheduledFor = new Date(rfq.bidProposalDeadlineAt);
    scheduledFor.setDate(scheduledFor.getDate() - reminder.days);

    if (scheduledFor > new Date()) {
      await storage.createScheduledNotification({
        notificationType: NotificationService.NotificationTypes.RFQ_DEADLINE_REMINDER,
        userId: rfq.createdBy,
        rfqId: rfq.id,
        scheduledFor,
        payload: {
          rfqId: rfq.id,
          projectName: rfq.projectName,
          deadlineDate: rfq.bidProposalDeadlineAt,
          reminderType: reminder.type
        }
      });
    }
  }
}
```

### Frontend Components

#### Countdown Utilities
**File**: `client/src/utils/countdown.ts`

```typescript
export interface CountdownData {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  isExpired: boolean;
  totalMs: number;
}

export function calculateCountdown(targetDate: string | Date): CountdownData {
  // Implementation calculates time remaining
}

export function getCountdownUrgency(countdown: CountdownData): 'safe' | 'warning' | 'urgent' | 'expired' {
  if (countdown.isExpired) return 'expired';
  
  const totalHours = countdown.totalMs / (1000 * 60 * 60);
  
  if (totalHours <= 24) return 'urgent';
  if (totalHours <= 72) return 'warning';
  return 'safe';
}

export function validateDeadlineOrder(bidProposalDeadline: Date, rfqDeadline: Date): boolean {
  return bidProposalDeadline < rfqDeadline;
}
```

#### Countdown Badge Component
**File**: `client/src/components/CountdownBadge.tsx`

```typescript
export const CountdownBadge: React.FC<{
  targetDate: string;
  label?: string;
  showLabel?: boolean;
}> = ({ targetDate, label, showLabel = true }) => {
  const [countdown, setCountdown] = useState<CountdownData>(
    calculateCountdown(targetDate)
  );

  useEffect(() => {
    const interval = setInterval(() => {
      setCountdown(calculateCountdown(targetDate));
    }, 1000);

    return () => clearInterval(interval);
  }, [targetDate]);

  const urgency = getCountdownUrgency(countdown);
  const colorClasses = getUrgencyColor(urgency);

  // Render countdown badge with color-coded urgency
};
```

#### RFQ Form Validation
**File**: `client/src/components/RFQEditForm.tsx`

```typescript
// Form validation ensures proper deadline ordering
const validateDeadlines = (bidDeadline: Date, projectDeadline: Date) => {
  if (!validateDeadlineOrder(bidDeadline, projectDeadline)) {
    toast.error('Bid proposal deadline must be before project deadline');
    return false;
  }
  return true;
};
```

## User Experience Features

### Visual Urgency Indicators

The system uses a color-coded urgency system for immediate deadline awareness:

- **🟢 Green (Safe)**: More than 3 days remaining
- **🟡 Yellow (Warning)**: 1-3 days remaining  
- **🔴 Red (Urgent)**: Less than 24 hours remaining
- **⚫ Gray (Expired)**: Deadline has passed

### React-Toastify Integration

**File**: `client/src/App.tsx`
```typescript
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./styles/toast.css";

// ToastContainer configured with custom styling
<ToastContainer
  position="top-right"
  autoClose={5000}
  hideProgressBar={false}
  newestOnTop={false}
  closeOnClick
  rtl={false}
  pauseOnFocusLoss
  draggable
  pauseOnHover
/>
```

**Custom Styling**: `client/src/styles/toast.css`
- Integrated with application theme (light/dark mode)
- Custom colors matching the Bidaible brand
- Responsive design for mobile devices

### Notification Preferences

Users can customize deadline notification preferences in their Settings:
- Email delivery toggle
- Reminder frequency options
- Notification method preferences (email, in-app, SMS)

## Database Migrations

### Migration 0010: Add Deadline Management
**File**: `migrations/0010_add_deadline_management.sql`

Key changes:
1. Added `bid_proposal_deadline_at` column to `rfqs` table
2. Updated existing records with calculated default values
3. Added validation constraint ensuring proper deadline order
4. Created `scheduled_notifications` table with status enum
5. Added strategic indexes for query performance

### Backfill Script
**File**: `scripts/populate-bid-deadlines.js`

Script to populate `bid_proposal_deadline_at` for existing RFQs:
- Sets bid deadline to 7 days before project deadline
- Falls back to 1 day before if less than 7 days remain
- Ensures minimum 1-hour buffer for immediate deadlines

## API Integration

### New Endpoints

#### Deadline Management
- **POST /api/rfqs** - Enhanced to schedule deadline notifications automatically
- **PUT /api/rfqs/:id** - Updates deadline notifications when deadlines change
- **GET /api/notifications/scheduled** - Retrieve scheduled deadline notifications

#### Notification Management
- **GET /api/notifications/preferences** - Get user notification preferences
- **PATCH /api/notifications/preferences** - Update deadline notification settings
- **GET /api/notifications/history** - View notification delivery history

### Enhanced Responses

RFQ objects now include:
```json
{
  "id": "rfq-uuid",
  "projectName": "Office Renovation",
  "bidProposalDeadlineAt": "2025-08-27T17:00:00Z",
  "dueDate": "2025-09-15T17:00:00Z",
  "countdownData": {
    "days": 5,
    "hours": 14,
    "minutes": 23,
    "urgency": "warning"
  }
}
```

## Testing & Quality Assurance

### Test Coverage

1. **Database Schema Tests**
   - Constraint validation (deadline ordering)
   - Index performance verification
   - Migration rollback testing

2. **Backend Service Tests**
   - Notification processor functionality
   - Deadline validation logic
   - Background processing reliability

3. **Frontend Component Tests**
   - Countdown calculation accuracy
   - Color-coded urgency display
   - Form validation behavior

4. **Integration Tests**
   - End-to-end deadline notification flow
   - Email delivery verification
   - Real-time countdown updates

### Performance Considerations

- **Database Indexes**: Strategic indexes on deadline columns for fast queries
- **Memory Usage**: Efficient countdown calculations without excessive re-renders
- **Background Processing**: Lightweight 1-minute interval processing
- **Notification Batching**: Optimized batch processing for multiple reminders

## Security & Compliance

### Data Protection
- Deadline information tied to user/organization access controls
- Notification delivery respects user privacy settings
- Audit trails for all deadline-related activities

### Input Validation
- Client-side deadline validation with server-side verification
- SQL injection prevention through parameterized queries
- Type-safe TypeScript interfaces for all deadline operations

## Troubleshooting Guide

### Common Issues

1. **Notifications Not Sending**
   - Check scheduled notification processor is running
   - Verify user email preferences are enabled
   - Review notification delivery status in audit logs

2. **Countdown Timers Not Updating**
   - Ensure React components are properly mounted
   - Check for JavaScript errors in browser console
   - Verify countdown calculation functions

3. **Deadline Validation Failures**
   - Confirm bid deadline is before project deadline
   - Check timezone handling in date comparisons
   - Validate user input format and parsing

### Debugging Commands

```bash
# Check scheduled notifications
curl -X GET "http://localhost:5000/api/notifications/scheduled" \
  -H "Authorization: Bearer your-api-key"

# Test notification processor
curl -X POST "http://localhost:5000/api/test/process-notifications" \
  -H "Authorization: Bearer your-api-key"

# Verify deadline validation
curl -X POST "http://localhost:5000/api/test/validate-deadlines" \
  -H "Content-Type: application/json" \
  -d '{"bidDeadline":"2025-08-27","projectDeadline":"2025-09-15"}'
```

## Future Enhancements

### Planned Features
1. **Custom Notification Templates**: Allow users to customize reminder messages
2. **Multiple Reminder Schedules**: Support for user-defined reminder intervals
3. **SMS Integration**: Text message notifications for urgent deadlines
4. **Calendar Integration**: Export deadlines to external calendar systems
5. **Timezone Support**: Enhanced timezone handling for global users

### Scalability Improvements
1. **Queue-based Processing**: Replace interval processing with event queues
2. **Notification Delivery Optimization**: Batch processing for large user bases
3. **Caching Layer**: Cache countdown calculations for frequently accessed RFQs
4. **Database Partitioning**: Partition scheduled notifications by date ranges

---

## Related Documentation

- [Main AGENT.md](../AGENT.md) - Development guide and system overview
- [Technical Architecture](./TECHNICAL_ARCHITECTURE.md) - Overall system architecture
- [API Documentation](./API_CONSOLIDATED.md) - Complete API reference
- [Database Migration Guide](./DATABASE_MIGRATION_CHECKPOINT_AUGUST_2025.md) - Migration procedures

**Last Updated**: August 20, 2025  
**Maintainer**: Development Team  
**Status**: Production Ready ✅
