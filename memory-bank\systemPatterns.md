# Bidaible System Patterns

## Architecture Overview

### Multi-Tenant Saa<PERSON> Pattern
- **Organization-Based Isolation**: Complete data separation between tenants
- **Automatic Scoping**: All database queries include organization filtering
- **Role-Based Access Control**: 3-tier hierarchy (Org, Admin, User)
- **Audit Trail**: Comprehensive logging for security and compliance

### Dual Authentication Strategy
- **Primary**: Clerk Authentication for web interface
  - JWT-based session management
  - Frontend components handle auth flow
  - Automatic token refresh and validation
- **Secondary**: JWT API Keys for programmatic access
  - Scoped permissions (read-only, upload-only, full-access)
  - Rate limiting and usage tracking
  - SHA-256 hashing for security

## AI Processing Patterns

### Multi-Provider Fallback Architecture
```
Primary (Groq: openai/gpt-oss-120b) 
    ↓ (if fails)
Secondary (OpenAI: gpt-4.1-mini)
    ↓ (if fails)  
Tertiary (Gemini: gemini-2.5-pro)
    ↓ (if fails)
Graceful Degradation (basic text extraction)
```

**Key Benefits**:
- 100% reliability through redundancy
- Sub-3-second processing with Groq primary
- Automatic failover without user intervention
- Cost optimization through intelligent routing

### Document Processing Pipeline
```
File Upload → Validation → Text Extraction → AI Analysis → Data Mapping → Storage
     ↓            ↓             ↓              ↓            ↓           ↓
Multi-format   Size/Type    UnifiedPDF     Multi-model   Schema      Database
Support        Checks       Extractor      Processing    Mapping     + Files
```

**Critical Components**:
- **UnifiedPDFExtractor**: 3-tier fallback (PDF.js → pdf-parse → Gemini Vision)
- **AI Stream Processor**: Concurrent processing with 8-processor limit
- **Progress Service**: Real-time SSE updates for user feedback
- **Batch Management**: Multi-file processing with priority handling

## Database Patterns

### Strategic Indexing (30+ Indexes)
```sql
-- Performance-critical indexes
CREATE INDEX idx_bids_rfq_status ON bids (rfq_id, status);
CREATE INDEX idx_rfqs_created_status ON rfqs (created_by, status, created_at);
CREATE INDEX idx_api_keys_user_active ON api_keys (user_id, is_active, expires_at);
```

### Data Access Layer Pattern
- **Repository Pattern**: Clean separation between business logic and data access
- **Drizzle ORM**: Type-safe database operations with automatic migrations
- **Connection Pooling**: Optimized database connections for scalability
- **Caching Strategy**: In-memory cache with TTL and LRU eviction

## Frontend Architecture Patterns

### Component Hierarchy
```
App (Root)
├── AuthenticatedApp (Protected Routes)
│   ├── Layout (Navigation + Sidebar)
│   ├── Pages (Route Components)
│   └── Components (Business Logic)
└── Landing (Public Routes)
```

### State Management Strategy
- **Server State**: TanStack Query for API data with caching
- **Local State**: React hooks for component-specific state
- **Global State**: React Context for theme, auth status
- **Form State**: React Hook Form with Zod validation

### Performance Patterns
- **Code Splitting**: React.lazy for route-based splitting
- **Memoization**: React.memo for expensive components
- **Virtual Scrolling**: For large data lists (contractors, bids)
- **Optimistic Updates**: Immediate UI feedback with rollback capability

## Security Patterns

### Defense in Depth
1. **Input Validation**: Zod schemas for all API inputs
2. **SQL Injection Prevention**: Parameterized queries via ORM
3. **XSS Protection**: Content sanitization and CSP headers
4. **CSRF Protection**: Token-based validation for state changes
5. **Rate Limiting**: Per-user and per-API-key throttling

### Audit Logging Pattern
```typescript
interface AuditEvent {
  userId: string;
  action: string;
  resource: string;
  organizationId: string;
  success: boolean;
  ipAddress: string;
  timestamp: Date;
}
```

## Error Handling Patterns

### Graceful Degradation
- **AI Processing**: Fallback chain ensures processing continues
- **File Upload**: Multiple extraction methods for reliability
- **API Failures**: Retry logic with exponential backoff
- **UI Errors**: Error boundaries with user-friendly fallbacks

### Centralized Error Management
```typescript
// Global error handler with context
app.use((err, req, res, next) => {
  logger.error('API Error', {
    error: err.message,
    stack: err.stack,
    userId: req.user?.id,
    organizationId: req.user?.organizationId,
    endpoint: req.path
  });
  
  res.status(err.status || 500).json({
    message: err.message || 'Internal Server Error'
  });
});
```

## Performance Patterns

### Caching Strategy
- **Memory Cache**: In-memory with TTL for frequently accessed data
- **Query Optimization**: Strategic database indexes for common queries
- **CDN Integration**: Static asset delivery optimization
- **Background Processing**: Non-blocking operations for heavy tasks

### Scalability Patterns
- **Stateless Design**: No server-side session storage
- **Horizontal Scaling**: Load balancer ready architecture
- **Database Optimization**: Read replicas and connection pooling
- **File Storage**: Cloud-based object storage for scalability

## Integration Patterns

### File Storage Integration
- **Replit Object Storage**: Primary file storage with secure access
- **Chunked Upload**: Large file handling with progress tracking
- **Unique Naming**: Timestamp-based keys for conflict prevention
- **Access Control**: Organization-scoped file access

### External API Integration
- **Circuit Breaker**: Prevent cascade failures from external services
- **Retry Logic**: Exponential backoff for transient failures
- **Timeout Management**: Prevent hanging requests
- **Health Checks**: Monitor external service availability

## Development Patterns

### Code Organization
- **Feature-Based Structure**: Organize by business capability
- **Service Layer**: Business logic separation from controllers
- **Middleware Stack**: Layered request processing
- **Type Safety**: End-to-end TypeScript with strict mode

### Testing Strategy
- **Unit Tests**: Service layer and utility functions
- **Integration Tests**: API endpoints with database
- **E2E Tests**: Critical user workflows
- **Performance Tests**: Load testing for scalability

These patterns form the foundation of Bidaible's architecture, ensuring scalability, reliability, and maintainability while delivering exceptional user experience.
