# Notification System Implementation Plan

**Date:** July 30, 2025  
**Phase:** Immediate Implementation (This Week)  
**Status:** Ready for Review & Approval  

---

## Implementation Overview

This plan covers the immediate implementation of the notification system foundation, focusing on database schema, email service integration, and Pipedream Connect setup. The implementation is designed to work with your existing infrastructure while adding real-time notification capabilities.

---

## Phase 1: Database Schema Implementation (Day 1-2)

### 1.1 Notification Tables Creation

#### New Tables to Add:
```sql
-- Core notifications table
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR NOT NULL REFERENCES users(id),
  organization_id UUID REFERENCES organizations(id),
  type VARCHAR NOT NULL,
  title VARCHAR NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  priority VARCHAR NOT NULL DEFAULT 'medium',
  read_at TIMESTAMP,
  delivered_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP
);

-- Notification preferences per user
CREATE TABLE notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR NOT NULL REFERENCES users(id),
  type VARCHAR NOT NULL,
  in_app_enabled BOOLEAN DEFAULT true,
  email_enabled BOOLEAN DEFAULT true,
  sms_enabled BOOLEAN DEFAULT false,
  frequency VARCHAR DEFAULT 'immediate',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, type)
);

-- Email delivery tracking
CREATE TABLE notification_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID NOT NULL REFERENCES notifications(id),
  delivery_method VARCHAR NOT NULL,
  recipient VARCHAR NOT NULL,
  status VARCHAR NOT NULL DEFAULT 'pending',
  provider_response JSONB,
  attempted_at TIMESTAMP DEFAULT NOW(),
  delivered_at TIMESTAMP,
  error_message TEXT
);
```

#### Database Indexes for Performance:
```sql
-- Performance indexes
CREATE INDEX IDX_notifications_user_id_read ON notifications(user_id, read_at);
CREATE INDEX IDX_notifications_org_created ON notifications(organization_id, created_at);
CREATE INDEX IDX_notifications_type_created ON notifications(type, created_at);
CREATE INDEX IDX_notification_prefs_user_type ON notification_preferences(user_id, type);
CREATE INDEX IDX_deliveries_notification ON notification_deliveries(notification_id);
CREATE INDEX IDX_deliveries_status_attempted ON notification_deliveries(status, attempted_at);
```

### 1.2 Schema Integration with Drizzle

#### Update `shared/schema.ts`:
```typescript
// Add to existing schema file
export const notifications = pgTable("notifications", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  organizationId: uuid("organization_id").references(() => organizations.id),
  type: varchar("type").notNull(),
  title: varchar("title").notNull(),
  message: text("message").notNull(),
  data: jsonb("data"),
  priority: varchar("priority").default("medium").notNull(),
  readAt: timestamp("read_at"),
  deliveredAt: timestamp("delivered_at"),
  createdAt: timestamp("created_at").defaultNow(),
  expiresAt: timestamp("expires_at"),
}, (table) => [
  index("IDX_notifications_user_id_read").on(table.userId, table.readAt),
  index("IDX_notifications_org_created").on(table.organizationId, table.createdAt),
  index("IDX_notifications_type_created").on(table.type, table.createdAt),
]);

export const notificationPreferences = pgTable("notification_preferences", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  type: varchar("type").notNull(),
  inAppEnabled: boolean("in_app_enabled").default(true),
  emailEnabled: boolean("email_enabled").default(true),
  smsEnabled: boolean("sms_enabled").default(false),
  frequency: varchar("frequency").default("immediate"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_notification_prefs_user_type").on(table.userId, table.type),
  unique("UQ_user_notification_type").on(table.userId, table.type),
]);

export const notificationDeliveries = pgTable("notification_deliveries", {
  id: uuid("id").primaryKey().defaultRandom(),
  notificationId: uuid("notification_id").references(() => notifications.id).notNull(),
  deliveryMethod: varchar("delivery_method").notNull(),
  recipient: varchar("recipient").notNull(),
  status: varchar("status").default("pending").notNull(),
  providerResponse: jsonb("provider_response"),
  attemptedAt: timestamp("attempted_at").defaultNow(),
  deliveredAt: timestamp("delivered_at"),
  errorMessage: text("error_message"),
}, (table) => [
  index("IDX_deliveries_notification").on(table.notificationId),
  index("IDX_deliveries_status_attempted").on(table.status, table.attemptedAt),
]);

// Add insert schemas
export const insertNotificationSchema = createInsertSchema(notifications);
export const insertNotificationPreferencesSchema = createInsertSchema(notificationPreferences);
export const insertNotificationDeliverySchema = createInsertSchema(notificationDeliveries);

// Add select types
export type Notification = typeof notifications.$inferSelect;
export type NotificationPreferences = typeof notificationPreferences.$inferSelect;
export type NotificationDelivery = typeof notificationDeliveries.$inferSelect;

// Add insert types
export type InsertNotification = z.infer<typeof insertNotificationSchema>;
export type InsertNotificationPreferences = z.infer<typeof insertNotificationPreferencesSchema>;
export type InsertNotificationDelivery = z.infer<typeof insertNotificationDeliverySchema>;
```

### 1.3 Storage Layer Updates

#### Update `server/storage.ts`:
```typescript
// Add to existing storage interface and implementation
interface IStorage {
  // ... existing methods
  
  // Notification methods
  createNotification(notification: InsertNotification): Promise<Notification>;
  getNotifications(userId: string, limit?: number): Promise<Notification[]>;
  markNotificationRead(notificationId: string, userId: string): Promise<boolean>;
  getUserNotificationPreferences(userId: string): Promise<NotificationPreferences[]>;
  updateNotificationPreferences(userId: string, type: string, preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences>;
  createNotificationDelivery(delivery: InsertNotificationDelivery): Promise<NotificationDelivery>;
  updateDeliveryStatus(deliveryId: string, status: string, response?: any): Promise<boolean>;
}
```

---

## Phase 2: Resend Email Service Setup (Day 2)

### 2.1 Service Account Creation
- **Action Required**: Sign up for Resend account at resend.com  
- **Plan**: Start with free tier (3,000 emails/month)
- **Domain Setup**: Configure domain authentication for professional emails
- **API Key**: Generate and secure API key for server use

### 2.2 Environment Configuration
```env
# Add to .env file
RESEND_API_KEY=re_xxxxxxxxxx
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Bidaible Notifications
```

### 2.3 Email Service Implementation

#### Create `server/services/emailService.ts`:
```typescript
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export class EmailService {
  async sendNotificationEmail(
    to: string,
    subject: string,
    htmlContent: string,
    textContent?: string
  ) {
    try {
      const result = await resend.emails.send({
        from: `${process.env.RESEND_FROM_NAME} <${process.env.RESEND_FROM_EMAIL}>`,
        to: [to],
        subject,
        html: htmlContent,
        text: textContent || this.htmlToText(htmlContent),
      });

      return {
        success: true,
        messageId: result.data?.id,
        response: result
      };
    } catch (error) {
      console.error('Email send failed:', error);
      return {
        success: false,
        error: error.message,
        response: error
      };
    }
  }

  private htmlToText(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}

export const emailService = new EmailService();
```

### 2.4 Email Templates

#### Create `server/templates/emailTemplates.ts`:
```typescript
export const EMAIL_TEMPLATES = {
  rfq_uploaded: {
    subject: (data: any) => `New RFQ Available: ${data.projectName}`,
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #f75100;">New RFQ Available</h2>
        <p>Hello,</p>
        <p>A new RFQ has been posted that matches your trade specialties:</p>
        <div style="background: #f5f5f4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>${data.projectName}</h3>
          <p><strong>Location:</strong> ${data.projectLocation}</p>
          <p><strong>Trade Category:</strong> ${data.tradeCategory}</p>
          <p><strong>Due Date:</strong> ${new Date(data.dueDate).toLocaleDateString()}</p>
        </div>
        <a href="${data.viewUrl}" style="background: #f75100; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          View RFQ Details
        </a>
        <p style="margin-top: 30px; font-size: 14px; color: #666;">
          Best regards,<br>
          The Bidaible Team
        </p>
      </div>
    `
  },
  
  bid_accepted: {
    subject: (data: any) => `Congratulations! Your bid has been accepted`,
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #22c55e;">Bid Accepted!</h2>
        <p>Hello ${data.contractorName},</p>
        <p>Great news! Your bid has been accepted for the following project:</p>
        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>${data.projectName}</h3>
          <p><strong>Accepted Bid Amount:</strong> $${data.bidAmount.toLocaleString()}</p>
          <p><strong>Project Location:</strong> ${data.projectLocation}</p>
        </div>
        <a href="${data.projectUrl}" style="background: #22c55e; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          View Project Details
        </a>
        <p style="margin-top: 30px; font-size: 14px; color: #666;">
          Congratulations and best regards,<br>
          The Bidaible Team
        </p>
      </div>
    `
  }
};
```

---

## Phase 3: Pipedream Connect Setup (Day 3)

### 3.1 CLI Installation & Project Setup
```bash
# Install Pipedream CLI (if not already installed)
npm install -g @pipedream/cli

# Authenticate with Pipedream
pd login

# Initialize Connect project
pd init connect
# Follow prompts to create project named "bidaible-integrations"

# Navigate to project directory
cd bidaible-integrations
```

### 3.2 Environment Configuration
```env
# Add to .env file
PIPEDREAM_CLIENT_ID=pc_xxxxxxxxxx
PIPEDREAM_CLIENT_SECRET=pcs_xxxxxxxxxx
PIPEDREAM_PROJECT_ID=prj_xxxxxxxxxx
```

### 3.3 Basic Integration Service

#### Create `server/services/integrationService.ts`:
```typescript
import { PipedreamClient } from '@pipedream/sdk';

const pipedreamClient = new PipedreamClient({
  projectEnvironment: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  clientId: process.env.PIPEDREAM_CLIENT_ID,
  clientSecret: process.env.PIPEDREAM_CLIENT_SECRET,
  projectId: process.env.PIPEDREAM_PROJECT_ID,
});

export class IntegrationService {
  async triggerWorkflow(workflowId: string, data: any) {
    try {
      const result = await pipedreamClient.workflows.trigger({
        id: workflowId,
        data
      });

      return {
        success: true,
        workflowId,
        result
      };
    } catch (error) {
      console.error(`Pipedream workflow ${workflowId} failed:`, error);
      return {
        success: false,
        workflowId,
        error: error.message
      };
    }
  }

  async syncProjectBudgetToERP(rfqId: string, bidId: string, erpSystem: string) {
    // Get bid line items for cost code breakdown
    const bidLineItems = await storage.getBidLineItems(bidId);
    const projectBudget = bidLineItems.map(item => ({
      costCode: item.costCode,
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice,
      unitType: item.unitOfMeasure,
      category: item.category
    }));

    const workflowId = `sync-to-${erpSystem.toLowerCase()}`;
    return await this.triggerWorkflow(workflowId, {
      rfqId,
      bidId,
      projectBudget,
      totalBudget: bidLineItems.reduce((sum, item) => sum + parseFloat(item.totalPrice || '0'), 0)
    });
  }
}

export const integrationService = new IntegrationService();
```

---

## Phase 4: Notification Service Implementation (Day 3-4)

### 4.1 Core Notification Service

#### Create `server/services/notificationService.ts`:
```typescript
import { storage } from '../storage';
import { emailService } from './emailService';
import { EMAIL_TEMPLATES } from '../templates/emailTemplates';

export class NotificationService {
  
  async createNotification(event: {
    userId: string;
    type: string;
    title: string;
    message: string;
    data?: any;
    priority?: string;
  }) {
    // Create notification record
    const notification = await storage.createNotification({
      userId: event.userId,
      type: event.type,
      title: event.title,
      message: event.message,
      data: event.data,
      priority: event.priority || 'medium'
    });

    // Get user preferences
    const preferences = await this.getUserPreferences(event.userId, event.type);

    // Deliver via enabled channels
    const deliveryPromises = [];
    
    if (preferences.inAppEnabled) {
      deliveryPromises.push(this.deliverInApp(notification));
    }
    
    if (preferences.emailEnabled) {
      deliveryPromises.push(this.deliverEmail(notification));
    }

    await Promise.allSettled(deliveryPromises);
    return notification;
  }

  private async getUserPreferences(userId: string, type: string) {
    const userPrefs = await storage.getUserNotificationPreferences(userId);
    const typePrefs = userPrefs.find(p => p.type === type);
    
    // Default preferences if not set
    return {
      inAppEnabled: typePrefs?.inAppEnabled ?? true,
      emailEnabled: typePrefs?.emailEnabled ?? true,
      smsEnabled: typePrefs?.smsEnabled ?? false,
      frequency: typePrefs?.frequency ?? 'immediate'
    };
  }

  private async deliverInApp(notification: Notification) {
    // Mark as delivered in-app (SSE implementation comes later)
    await storage.createNotificationDelivery({
      notificationId: notification.id,
      deliveryMethod: 'in_app',
      recipient: notification.userId,
      status: 'delivered',
      deliveredAt: new Date()
    });
  }

  private async deliverEmail(notification: Notification) {
    const template = EMAIL_TEMPLATES[notification.type];
    if (!template) {
      console.warn(`No email template for notification type: ${notification.type}`);
      return;
    }

    // Get user email
    const user = await storage.getUser(notification.userId);
    if (!user?.email) {
      console.warn(`No email found for user: ${notification.userId}`);
      return;
    }

    const subject = template.subject(notification.data);
    const htmlContent = template.html(notification.data);

    const result = await emailService.sendNotificationEmail(
      user.email,
      subject,
      htmlContent
    );

    // Track delivery
    await storage.createNotificationDelivery({
      notificationId: notification.id,
      deliveryMethod: 'email',
      recipient: user.email,
      status: result.success ? 'delivered' : 'failed',
      providerResponse: result.response,
      deliveredAt: result.success ? new Date() : undefined,
      errorMessage: result.success ? undefined : result.error
    });
  }

  // Event-specific notification methods
  async onRfqUploaded(rfq: any) {
    // Find eligible contractors based on trade types and location
    const eligibleContractors = await this.getEligibleContractors(rfq);
    
    for (const contractor of eligibleContractors) {
      await this.createNotification({
        userId: contractor.userId,
        type: 'rfq_uploaded',
        title: 'New RFQ Available',
        message: `${rfq.projectName} - ${rfq.projectLocation}`,
        data: {
          rfqId: rfq.id,
          projectName: rfq.projectName,
          projectLocation: rfq.projectLocation,
          tradeCategory: rfq.tradeCategory,
          dueDate: rfq.dueDate,
          viewUrl: `${process.env.BASE_URL}/rfqs/${rfq.id}`
        },
        priority: 'high'
      });
    }
  }

  async onBidAccepted(bid: any, rfq: any) {
    const contractor = await storage.getContractor(bid.contractorId);
    
    await this.createNotification({
      userId: contractor.userId,
      type: 'bid_accepted',
      title: 'Bid Accepted!',
      message: `Your bid for ${rfq.projectName} has been accepted`,
      data: {
        bidId: bid.id,
        rfqId: rfq.id,
        projectName: rfq.projectName,
        projectLocation: rfq.projectLocation,
        bidAmount: bid.bidAmount,
        contractorName: contractor.companyName,
        projectUrl: `${process.env.BASE_URL}/projects/${rfq.id}`
      },
      priority: 'urgent'
    });
  }

  private async getEligibleContractors(rfq: any) {
    // Implementation to find contractors matching RFQ criteria
    return await storage.getContractorsByTradeAndLocation(rfq.tradeCategory, rfq.projectLocation);
  }
}

export const notificationService = new NotificationService();
```

---

## Phase 5: API Integration (Day 4-5)

### 5.1 Notification API Endpoints

#### Add to `server/routes.ts`:
```typescript
// Notification endpoints
app.get('/api/notifications', isAuthenticated, async (req: any, res) => {
  try {
    const userId = req.user.claims.sub;
    const limit = parseInt(req.query.limit) || 50;
    
    const notifications = await storage.getNotifications(userId, limit);
    res.json(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.status(500).json({ message: 'Failed to fetch notifications' });
  }
});

app.patch('/api/notifications/:id/read', isAuthenticated, async (req: any, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.claims.sub;
    
    const success = await storage.markNotificationRead(id, userId);
    if (!success) {
      return res.status(404).json({ message: 'Notification not found' });
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ message: 'Failed to update notification' });
  }
});

app.get('/api/notifications/preferences', isAuthenticated, async (req: any, res) => {
  try {
    const userId = req.user.claims.sub;
    const preferences = await storage.getUserNotificationPreferences(userId);
    res.json(preferences);
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    res.status(500).json({ message: 'Failed to fetch preferences' });
  }
});

app.patch('/api/notifications/preferences/:type', isAuthenticated, async (req: any, res) => {
  try {
    const { type } = req.params;
    const userId = req.user.claims.sub;
    const updates = req.body;
    
    const preferences = await storage.updateNotificationPreferences(userId, type, updates);
    res.json(preferences);
  } catch (error) {
    console.error('Error updating notification preferences:', error);
    res.status(500).json({ message: 'Failed to update preferences' });
  }
});
```

### 5.2 Integration with Existing Events

#### Update existing RFQ creation and bid management:
```typescript
// In RFQ creation route
app.post('/api/rfqs', upload.array('documents'), isAuthenticated, async (req: any, res) => {
  try {
    // ... existing RFQ creation logic
    
    // Add notification trigger
    await notificationService.onRfqUploaded(newRfq);
    
    res.json(newRfq);
  } catch (error) {
    // ... error handling
  }
});

// In bid acceptance route
app.patch('/api/bids/:bidId/accept', isAuthenticated, async (req: any, res) => {
  try {
    // ... existing bid acceptance logic
    
    // Add notification trigger
    await notificationService.onBidAccepted(updatedBid, rfq);
    
    res.json(updatedBid);
  } catch (error) {
    // ... error handling
  }
});
```

---

## Phase 6: Testing & Validation (Day 5)

### 6.1 Database Migration Test
```bash
# Push schema changes
npm run db:push

# Verify tables created
# Check in database that all tables and indexes exist
```

### 6.2 Email Service Test
```typescript
// Create test script: test-email.js
import { emailService } from './server/services/emailService.js';

async function testEmail() {
  const result = await emailService.sendNotificationEmail(
    '<EMAIL>',
    'Test Notification',
    '<h1>Test Email</h1><p>This is a test notification email.</p>'
  );
  
  console.log('Email test result:', result);
}

testEmail();
```

### 6.3 Pipedream Connection Test
```typescript
// Create test script: test-pipedream.js
import { integrationService } from './server/services/integrationService.js';

async function testPipedream() {
  const result = await integrationService.triggerWorkflow(
    'test-workflow',
    { message: 'Hello from Bidaible!' }
  );
  
  console.log('Pipedream test result:', result);
}

testPipedream();
```

---

## Implementation Dependencies & Prerequisites

### Required Services:
1. **Resend Account**: resend.com signup required
2. **Pipedream Account**: pipedream.com signup required  
3. **Domain Setup**: For email authentication (optional but recommended)

### Required Environment Variables:
```env
RESEND_API_KEY=re_xxxxxxxxxx
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Bidaible Notifications
PIPEDREAM_CLIENT_ID=pc_xxxxxxxxxx
PIPEDREAM_CLIENT_SECRET=pcs_xxxxxxxxxx
PIPEDREAM_PROJECT_ID=prj_xxxxxxxxxx
BASE_URL=https://your-domain.replit.app
```

### Package Dependencies:
```json
{
  "resend": "^3.2.0",
  "@pipedream/sdk": "^0.3.0"
}
```

---

## Risk Assessment & Mitigation

### Potential Issues:
1. **Email Deliverability**: New domain may have deliverability issues
   - **Mitigation**: Implement domain authentication, start with low volume
   
2. **Database Performance**: New tables may impact existing queries
   - **Mitigation**: Proper indexing implemented, monitor query performance
   
3. **Third-party Service Limits**: Resend/Pipedream rate limits
   - **Mitigation**: Implement proper rate limiting and retry logic

### Rollback Plan:
- Database changes can be rolled back using Drizzle migrations
- Email service is additive and can be disabled via environment variables
- Pipedream integration is optional and won't affect core functionality

---

## Success Criteria

### Phase 1 Complete When:
- ✅ All database tables created and indexed
- ✅ Notification service can create and retrieve notifications
- ✅ Email service can send test emails successfully
- ✅ Pipedream Connect project initialized and responding
- ✅ API endpoints return proper responses
- ✅ Integration with existing RFQ/bid workflows working

### Performance Targets:
- **Database**: <200ms for notification queries
- **Email**: <5 seconds for email delivery
- **API**: <100ms for notification API responses

---

## Timeline Summary

| Day | Tasks | Expected Duration |
|-----|-------|------------------|
| 1 | Database schema + storage layer | 4-6 hours |
| 2 | Resend setup + email service | 3-4 hours |
| 3 | Pipedream setup + integration service | 4-5 hours |
| 4 | Notification service + API endpoints | 5-6 hours |
| 5 | Testing, validation, integration | 3-4 hours |

**Total Estimated Time**: 19-25 work hours over 5 days

---

This implementation plan provides a solid foundation for the notification system while maintaining compatibility with your existing infrastructure. The modular approach allows for easy testing and rollback if needed.

**Ready for your review and approval to proceed.**