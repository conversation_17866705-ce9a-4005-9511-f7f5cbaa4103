import {
  pgTable,
  text,
  varchar,
  timestamp,
  jsonb,
  index,
  serial,
  boolean,
  decimal,
  integer,
  uuid,
  pgEnum,
  date,
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// Session storage table (required for Replit Auth)
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User roles enum
export const userRoleEnum = pgEnum("user_role", ["SuperUser", "OrganizationAdmin", "StandardUser"]);

// User classification enum
export const userClassificationEnum = pgEnum("user_classification", ["general_contractor", "contractor"]);

// API key permissions enum
export const apiKeyPermissionEnum = pgEnum("api_key_permission", ["read-only", "upload-only", "full-access"]);

// Notification priority enum
export const notificationPriorityEnum = pgEnum("notification_priority", ["low", "medium", "high", "urgent"]);

// Notification delivery method enum
export const deliveryMethodEnum = pgEnum("delivery_method", ["in_app", "email", "sms"]);

// Notification delivery status enum
export const deliveryStatusEnum = pgEnum("delivery_status", ["pending", "delivered", "failed", "bounced"]);

// Notification frequency enum
export const notificationFrequencyEnum = pgEnum("notification_frequency", ["immediate", "hourly", "daily", "weekly"]);

// File type enum for multi-file RFQ support
export const fileTypeEnum = pgEnum("file_type", ["main", "drawings", "specifications", "addendum", "supporting"]);

// Processing status enum for multi-file RFQ support
export const processingStatusEnum = pgEnum("processing_status", ["pending", "processing", "complete", "failed"]);



// Organizations table for multi-tenant support
export const organizations = pgTable("organizations", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name").notNull(),
  slug: varchar("slug").unique().notNull(), // URL-friendly identifier
  description: text("description"),
  userLimit: integer("user_limit").default(15), // Maximum users per tenant
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_organizations_slug").on(table.slug),
  index("IDX_organizations_active").on(table.isActive),
  index("IDX_organizations_created_at").on(table.createdAt),
]);

// User storage table (required for Replit Auth)
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  role: userRoleEnum("role").default("StandardUser"),
  organizationId: uuid("organization_id").references(() => organizations.id),
  isSuperUser: boolean("is_super_user").default(false), // Database-driven SuperUser identification
  isActive: boolean("is_active").default(true),
  lastLoginAt: timestamp("last_login_at"),
  termsAccepted: boolean("terms_accepted").default(false), // Terms and conditions acceptance
  termsAcceptedAt: timestamp("terms_accepted_at"), // When terms were accepted
  userClassification: userClassificationEnum("user_classification"), // General Contractor vs Contractor
  onboardingCompleted: boolean("onboarding_completed").default(false), // Onboarding completion tracking
  onboardingCompletedAt: timestamp("onboarding_completed_at"), // When onboarding was completed
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_users_email").on(table.email),
  index("IDX_users_role").on(table.role),
  index("IDX_users_organization_id").on(table.organizationId),
  index("IDX_users_active").on(table.isActive),
  index("IDX_users_created_at").on(table.createdAt),
  index("IDX_users_terms_accepted").on(table.termsAccepted),
  index("IDX_users_classification").on(table.userClassification),
  index("IDX_users_onboarding").on(table.onboardingCompleted),
]);

// Waitlist table
export const waitlist = pgTable("waitlist", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: varchar("email").notNull().unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  companyName: varchar("company_name"),
  jobTitle: varchar("job_title"),
  source: varchar("source").default("landing_page"),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_waitlist_email").on(table.email),
  index("IDX_waitlist_created_at").on(table.createdAt),
]);

// Role change audit log
export const roleAuditLog = pgTable("role_audit_log", {
  id: uuid("id").primaryKey().defaultRandom(),
  targetUserId: varchar("target_user_id").references(() => users.id).notNull(),
  changedByUserId: varchar("changed_by_user_id").references(() => users.id).notNull(),
  organizationId: uuid("organization_id").references(() => organizations.id),
  previousRole: userRoleEnum("previous_role"),
  newRole: userRoleEnum("new_role").notNull(),
  reason: text("reason"),
  ipAddress: varchar("ip_address"),
  userAgent: text("user_agent"),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_role_audit_target_user").on(table.targetUserId),
  index("IDX_role_audit_changed_by").on(table.changedByUserId),
  index("IDX_role_audit_organization").on(table.organizationId),
  index("IDX_role_audit_created_at").on(table.createdAt),
]);

// Access attempt audit log
export const accessAuditLog = pgTable("access_audit_log", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id),
  action: varchar("action").notNull(), // API endpoint or action attempted
  resource: varchar("resource"), // Resource being accessed
  organizationId: uuid("organization_id").references(() => organizations.id),
  success: boolean("success").notNull(),
  reason: text("reason"), // Reason for failure if unsuccessful
  ipAddress: varchar("ip_address"),
  userAgent: text("user_agent"),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_access_audit_user").on(table.userId),
  index("IDX_access_audit_organization").on(table.organizationId),
  index("IDX_access_audit_success").on(table.success),
  index("IDX_access_audit_action").on(table.action),
  index("IDX_access_audit_created_at").on(table.createdAt),
]);

// API Keys table for programmatic access
export const apiKeys = pgTable("api_keys", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  name: varchar("name").notNull(), // Human-readable name for the API key
  keyHash: varchar("key_hash").notNull(), // SHA-256 hash of the API key
  permissions: apiKeyPermissionEnum("permissions").default("read-only"),
  rateLimit: integer("rate_limit").default(100), // Requests per hour
  isActive: boolean("is_active").default(true),
  lastUsedAt: timestamp("last_used_at"),
  expiresAt: timestamp("expires_at"), // Optional expiration
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_api_keys_user_id").on(table.userId),
  index("IDX_api_keys_key_hash").on(table.keyHash),
  index("IDX_api_keys_active").on(table.isActive),
  index("IDX_api_keys_expires_at").on(table.expiresAt),
  index("IDX_api_keys_created_at").on(table.createdAt),
]);

// API Key usage tracking for rate limiting and analytics
export const apiKeyUsage = pgTable("api_key_usage", {
  id: uuid("id").primaryKey().defaultRandom(),
  apiKeyId: uuid("api_key_id").references(() => apiKeys.id).notNull(),
  endpoint: varchar("endpoint").notNull(),
  method: varchar("method").notNull(),
  requestCount: integer("request_count").default(1),
  lastRequestAt: timestamp("last_request_at").defaultNow(),
  requestDate: date("request_date").defaultNow(), // For daily rate limiting
}, (table) => [
  index("IDX_api_key_usage_api_key_id").on(table.apiKeyId),
  index("IDX_api_key_usage_date").on(table.requestDate),
  index("IDX_api_key_usage_endpoint").on(table.endpoint),
  index("IDX_api_key_usage_last_request").on(table.lastRequestAt),
]);

// Contractor profiles
export const contractors = pgTable("contractors", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id),
  organizationId: uuid("organization_id").references(() => organizations.id).notNull(), // Multi-tenant isolation

  // Business Identity & Contact
  companyName: varchar("company_name").notNull(),
  companyWebsite: varchar("company_website"),
  legalStructure: varchar("legal_structure"),
  taxId: varchar("tax_id"),
  dba: varchar("dba"),
  primaryAddress: text("primary_address"),
  mailingAddress: text("mailing_address"),
  primaryContactName: varchar("primary_contact_name"),
  primaryContactEmail: varchar("primary_contact_email"),
  primaryContactPhone: varchar("primary_contact_phone"),
  primaryContactTitle: varchar("primary_contact_title"),

  // Classification & Capability
  tradeTypes: jsonb("trade_types"),
  unionStatus: varchar("union_status"),
  unionAffiliations: text("union_affiliations"),
  certifications: jsonb("certifications"),
  serviceAreas: text("service_areas"),

  // Credentials & Compliance
  licenseNumber: varchar("license_number"),
  licenseState: varchar("license_state"),
  licenseExpiration: timestamp("license_expiration"),
  generalLiability: text("general_liability"),
  workersComp: text("workers_comp"),
  autoInsurance: text("auto_insurance"),
  bondingSingle: integer("bonding_single"),
  bondingAggregate: integer("bonding_aggregate"),
  emr: decimal("emr", { precision: 4, scale: 2 }),

  // Financial & Reference Data
  bankReference: text("bank_reference"),
  suretyReference: text("surety_reference"),
  creditRating: varchar("credit_rating"),
  paymentTerms: varchar("payment_terms"),
  litigationHistory: text("litigation_history"),
  projectReferences: text("project_references"),

  // Performance & Experience
  yearsInBusiness: integer("years_in_business"),
  specializations: jsonb("specializations"),
  awards: text("awards"),
  environmentalPrograms: text("environmental_programs"),

  // Operational Details
  workforceSize: integer("workforce_size"),
  workforceBreakdown: text("workforce_breakdown"),
  equipment: text("equipment"),
  availability: text("availability"),

  // Custom Tags & Preferences
  keywordTags: jsonb("keyword_tags"),
  preferredProjectTypes: jsonb("preferred_project_types"),

  // Legacy fields for compatibility
  businessAddress: text("business_address"),
  contactEmail: varchar("contact_email"),
  contactPhone: varchar("contact_phone"),

  // System fields
  isApproved: boolean("is_approved").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_contractors_organization_id").on(table.organizationId), // Critical for multi-tenant queries
  index("IDX_contractors_user_id").on(table.userId),
  index("IDX_contractors_approved").on(table.isApproved),
  index("IDX_contractors_email").on(table.primaryContactEmail),
  index("IDX_contractors_trade_types").on(table.tradeTypes),
  index("IDX_contractors_created_at").on(table.createdAt),
  index("IDX_contractors_org_approved").on(table.organizationId, table.isApproved), // Composite index for filtering
]);

// RFQ status enum
export const rfqStatusEnum = pgEnum("rfq_status", ["Draft", "Active", "Review", "Closed", "Awarded"]);

// RFQ trade enum
export const tradeEnum = pgEnum("trade", ["electrical", "plumbing", "hvac", "concrete", "general", "site_work"]);

// RFQs table
export const rfqs = pgTable("rfqs", {
  id: uuid("id").primaryKey().defaultRandom(),
  createdBy: varchar("created_by").references(() => users.id),
  organizationId: uuid("organization_id").references(() => organizations.id).notNull(), // Multi-tenant isolation
  projectName: varchar("project_name").notNull(),
  projectLocation: varchar("project_location").notNull(),
  tradeCategory: tradeEnum("trade_category").notNull(),
  description: text("description"),
  dueDate: timestamp("due_date").notNull(),
  status: rfqStatusEnum("status").default("Draft"),
  extractedData: jsonb("extracted_data"), // AI-extracted data from documents
  extractedText: text("extracted_text"), // Raw extracted text from all documents
  aiSummary: text("ai_summary"), // AI-generated markdown summary for contractors
  masterSummary: text("master_summary"),
  conflictFlags: jsonb("conflict_flags"),
  summaryGeneratedAt: timestamp("summary_generated_at"),
  // Buffer management for bid evaluation
  bufferPercentage: decimal("buffer_percentage", { precision: 5, scale: 2 }).default("10.00"), // Default 10% buffer
  bufferNotes: text("buffer_notes"), // Notes about buffer reasoning

  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_rfqs_organization_id").on(table.organizationId), // Critical for multi-tenant queries
  index("IDX_rfqs_created_by").on(table.createdBy),
  index("IDX_rfqs_status").on(table.status),
  index("IDX_rfqs_trade_category").on(table.tradeCategory),
  index("IDX_rfqs_due_date").on(table.dueDate),
  index("IDX_rfqs_created_at").on(table.createdAt),
  index("IDX_rfqs_project_location").on(table.projectLocation),
  index("IDX_rfqs_org_status").on(table.organizationId, table.status), // Composite index for efficient filtering
]);

// RFQ documents - Enhanced for multi-file batch support
export const rfqDocuments = pgTable("rfq_documents", {
  id: uuid("id").primaryKey().defaultRandom(),
  rfqId: uuid("rfq_id").references(() => rfqs.id),
  fileName: varchar("file_name").notNull(),
  objectKey: varchar("object_key").notNull(), // Object Storage key
  fileSize: integer("file_size"),
  mimeType: varchar("mime_type"),
  extractedText: text("extracted_text"), // AI-extracted text content
  
  // Multi-file batch support columns
  uploadBatchId: varchar("upload_batch_id", { length: 21 }), // nanoid for grouping files (nullable for backward compatibility)
  fileSequence: integer("file_sequence").default(1), // Order within batch (1-8)
  fileType: fileTypeEnum("file_type").default("supporting"), // File categorization
  processingStatus: processingStatusEnum("processing_status").default("pending"), // Processing state
  individualAiData: jsonb("individual_ai_data"), // Per-file AI extraction results
  
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_rfq_documents_rfq_id").on(table.rfqId),
  index("IDX_rfq_documents_created_at").on(table.createdAt),
  index("IDX_rfq_documents_batch").on(table.uploadBatchId), // New: batch grouping
  index("IDX_rfq_documents_sequence").on(table.uploadBatchId, table.fileSequence), // New: batch ordering
  index("IDX_rfq_documents_processing").on(table.processingStatus), // New: processing status filtering
  index("IDX_rfq_documents_type").on(table.fileType), // New: file type filtering
]);

// Contractor favorites (GC's preferred contractor lists)
export const contractorFavorites = pgTable("contractor_favorites", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id).notNull(), // General contractor user
  contractorId: uuid("contractor_id").references(() => contractors.id).notNull(),
  notes: text("notes"), // Optional notes about the contractor
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_contractor_favorites_user_id").on(table.userId),
  index("IDX_contractor_favorites_contractor_id").on(table.contractorId),
  index("IDX_contractor_favorites_unique").on(table.userId, table.contractorId),
]);

// RFQ distribution tracking (who received which RFQs)
export const rfqDistribution = pgTable("rfq_distribution", {
  id: uuid("id").primaryKey().defaultRandom(),
  rfqId: uuid("rfq_id").references(() => rfqs.id).notNull(),
  contractorId: uuid("contractor_id").references(() => contractors.id).notNull(),
  distributionMethod: varchar("distribution_method").notNull(), // "favorites" or "broadcast"
  sentAt: timestamp("sent_at").defaultNow(),
  viewedAt: timestamp("viewed_at"),
  declinedAt: timestamp("declined_at"), // If contractor declines to bid
  declineReason: text("decline_reason"),
}, (table) => [
  index("IDX_rfq_distribution_rfq_id").on(table.rfqId),
  index("IDX_rfq_distribution_contractor_id").on(table.contractorId),
  index("IDX_rfq_distribution_sent_at").on(table.sentAt),
  index("IDX_rfq_distribution_viewed_at").on(table.viewedAt),
  index("IDX_rfq_distribution_unique").on(table.rfqId, table.contractorId),
]);

// Enhanced bid responses with AI extraction
export const bids = pgTable("bids", {
  id: uuid("id").primaryKey().defaultRandom(),
  rfqId: uuid("rfq_id").references(() => rfqs.id),
  contractorId: uuid("contractor_id").references(() => contractors.id),

  // Manual bid fields
  bidAmount: decimal("bid_amount", { precision: 12, scale: 2 }),
  proposalText: text("proposal_text"),
  timeline: text("timeline"), // Project duration/schedule
  notes: text("notes"), // Additional conditions or notes

  // Structured contact information
  bidContactName: varchar("bid_contact_name", { length: 255 }),
  bidContactEmail: varchar("bid_contact_email", { length: 255 }),
  bidContactPhone: varchar("bid_contact_phone", { length: 50 }),
  
  // Pricing structure
  lineItemsTotal: decimal("line_items_total", { precision: 12, scale: 2 }),
  markupPercentage: decimal("markup_percentage", { precision: 5, scale: 2 }), // e.g., 10.50 for 10.5%

  // AI-extracted fields from uploaded documents
  extractedAmount: decimal("extracted_amount", { precision: 12, scale: 2 }),
  extractedTimeline: text("extracted_timeline"),
  extractedScope: text("extracted_scope"),
  extractedConditions: text("extracted_conditions"),
  extractedData: jsonb("extracted_data"), // Full AI extraction results
  extractionConfidence: decimal("extraction_confidence", { precision: 3, scale: 2 }), // 0.00-1.00

  // Status and timing
  status: varchar("status").default("Draft"), // Draft, Submitted, Under Review, Accepted, Rejected, Request Info
  submittedAt: timestamp("submitted_at"),
  reviewedAt: timestamp("reviewed_at"), // When RFQ owner takes action
  reviewedBy: varchar("reviewed_by"), // User ID who reviewed
  reviewNotes: text("review_notes"), // Notes from reviewer
  requestInfoMessage: text("request_info_message"), // Message when requesting additional info

  // AI-powered competitive analysis
  aiAnalysis: jsonb("ai_analysis"), // AI-generated bid analysis and insights
  aiSummary: text("ai_summary"), // AI-generated bid summary in markdown format
  competitiveScore: decimal("competitive_score", { precision: 3, scale: 2 }), // 0.00-1.00 relative to other bids

  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_bids_rfq_id").on(table.rfqId),
  index("IDX_bids_contractor_id").on(table.contractorId),
  index("IDX_bids_status").on(table.status),
  index("IDX_bids_submitted_at").on(table.submittedAt),
  index("IDX_bids_created_at").on(table.createdAt),
]);

// Bid documents with Object Storage
export const bidDocuments = pgTable("bid_documents", {
  id: uuid("id").primaryKey().defaultRandom(),
  bidId: uuid("bid_id").references(() => bids.id),
  fileName: varchar("file_name").notNull(),
  fileUrl: varchar("file_url").notNull(), // Legacy field for compatibility
  objectKey: varchar("object_key"), // Object Storage key
  fileSize: integer("file_size"),
  mimeType: varchar("mime_type"),
  extractedText: text("extracted_text"), // AI-extracted text content
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_bid_documents_bid_id").on(table.bidId),
  index("IDX_bid_documents_created_at").on(table.createdAt),
]);

// Bid line items with cost codes and pricing breakdown
export const bidLineItems = pgTable("bid_line_items", {
  id: uuid("id").primaryKey().defaultRandom(),
  bidId: uuid("bid_id").references(() => bids.id).notNull(),
  costCode: varchar("cost_code", { length: 20 }).notNull(),
  description: text("description").notNull(),
  quantity: decimal("quantity", { precision: 10, scale: 2 }),
  unitPrice: decimal("unit_price", { precision: 12, scale: 2 }),
  totalPrice: decimal("total_price", { precision: 12, scale: 2 }),
  unitOfMeasure: varchar("unit_of_measure", { length: 20 }),
  category: varchar("category", { length: 100 }), // Optional grouping like "Electrical", "Plumbing"
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_bid_line_items_bid_id").on(table.bidId),
  index("IDX_bid_line_items_cost_code").on(table.costCode),
  index("IDX_bid_line_items_category").on(table.category),
  index("IDX_bid_line_items_created_at").on(table.createdAt),
]);

// Bid inclusions and exclusions for scope definition
export const bidInclusionsExclusions = pgTable("bid_inclusions_exclusions", {
  id: uuid("id").primaryKey().defaultRandom(),
  bidId: uuid("bid_id").references(() => bids.id).notNull(),
  itemType: varchar("item_type", { length: 10 }).notNull(), // 'included' or 'excluded'
  description: text("description").notNull(),
  category: varchar("category", { length: 50 }), // Optional grouping like "Materials", "Labor", "Equipment"
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_bid_inclusions_exclusions_bid_id").on(table.bidId),
  index("IDX_bid_inclusions_exclusions_type").on(table.itemType),
  index("IDX_bid_inclusions_exclusions_category").on(table.category),
  index("IDX_bid_inclusions_exclusions_created_at").on(table.createdAt),
]);

// Forecast materials table
export const forecastMaterials = pgTable("forecast_materials", {
  id: varchar("id").primaryKey().notNull(),
  costCode: varchar("cost_code").notNull(), // CSI cost code (e.g., "03.01", "07.02")
  category: varchar("category").notNull(),
  name: varchar("name").notNull(),
  unit: varchar("unit").notNull(),
  currentPrice: decimal("current_price", { precision: 10, scale: 2 }),
  previousPrice: decimal("previous_price", { precision: 10, scale: 2 }),
  changePercent: decimal("change_percent", { precision: 5, scale: 2 }),
  volume: varchar("volume"),
  time: varchar("time"),
  ytdChange: decimal("ytd_change", { precision: 5, scale: 2 }),
  ytdChangePercent: decimal("ytd_change_percent", { precision: 5, scale: 2 }),
  lastUpdated: timestamp("last_updated").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_forecast_materials_cost_code").on(table.costCode),
  index("IDX_forecast_materials_category").on(table.category),
  index("IDX_forecast_materials_last_updated").on(table.lastUpdated),
  index("IDX_forecast_materials_created_at").on(table.createdAt),
]);

// Relations
export const organizationsRelations = relations(organizations, ({ many }) => ({
  users: many(users),
  contractors: many(contractors),
  rfqs: many(rfqs),
  roleAudits: many(roleAuditLog),
  accessAudits: many(accessAuditLog),
}));

export const usersRelations = relations(users, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [users.organizationId],
    references: [organizations.id],
  }),
  contractors: many(contractors),
  createdRfqs: many(rfqs),
  apiKeys: many(apiKeys),
  contractorFavorites: many(contractorFavorites),
  roleChangesTarget: many(roleAuditLog, {relationName: "targetUser"}),
  roleChangesPerformed: many(roleAuditLog, {relationName: "changedByUser"}),
  accessAudits: many(accessAuditLog),
}));

export const waitlistRelations = relations(waitlist, ({ }) => ({}));

export const roleAuditLogRelations = relations(roleAuditLog, ({ one }) => ({
  targetUser: one(users, {
    fields: [roleAuditLog.targetUserId],
    references: [users.id],
    relationName: "targetUser",
  }),
  changedByUser: one(users, {
    fields: [roleAuditLog.changedByUserId],
    references: [users.id],
    relationName: "changedByUser",
  }),
  organization: one(organizations, {
    fields: [roleAuditLog.organizationId],
    references: [organizations.id],
  }),
}));

export const accessAuditLogRelations = relations(accessAuditLog, ({ one }) => ({
  user: one(users, {
    fields: [accessAuditLog.userId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [accessAuditLog.organizationId],
    references: [organizations.id],
  }),
}));

export const contractorsRelations = relations(contractors, ({ one, many }) => ({
  user: one(users, {
    fields: [contractors.userId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [contractors.organizationId],
    references: [organizations.id],
  }),
  bids: many(bids),
  favoritedBy: many(contractorFavorites),
  rfqDistributions: many(rfqDistribution),
}));

export const rfqsRelations = relations(rfqs, ({ one, many }) => ({
  createdByUser: one(users, {
    fields: [rfqs.createdBy],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [rfqs.organizationId],
    references: [organizations.id],
  }),
  documents: many(rfqDocuments),
  bids: many(bids),
  distributions: many(rfqDistribution),
}));

export const rfqDocumentsRelations = relations(rfqDocuments, ({ one }) => ({
  rfq: one(rfqs, {
    fields: [rfqDocuments.rfqId],
    references: [rfqs.id],
  }),
}));

export const bidsRelations = relations(bids, ({ one, many }) => ({
  rfq: one(rfqs, {
    fields: [bids.rfqId],
    references: [rfqs.id],
  }),
  contractor: one(contractors, {
    fields: [bids.contractorId],
    references: [contractors.id],
  }),
  documents: many(bidDocuments),
  lineItems: many(bidLineItems),
  inclusionsExclusions: many(bidInclusionsExclusions),
}));

export const bidDocumentsRelations = relations(bidDocuments, ({ one }) => ({
  bid: one(bids, {
    fields: [bidDocuments.bidId],
    references: [bids.id],
  }),
}));

// Contractor favorites relations
export const contractorFavoritesRelations = relations(contractorFavorites, ({ one }) => ({
  user: one(users, {
    fields: [contractorFavorites.userId],
    references: [users.id],
  }),
  contractor: one(contractors, {
    fields: [contractorFavorites.contractorId],
    references: [contractors.id],
  }),
}));

// API Keys relations
export const apiKeysRelations = relations(apiKeys, ({ one, many }) => ({
  user: one(users, {
    fields: [apiKeys.userId],
    references: [users.id],
  }),
  usage: many(apiKeyUsage),
}));

export const apiKeyUsageRelations = relations(apiKeyUsage, ({ one }) => ({
  apiKey: one(apiKeys, {
    fields: [apiKeyUsage.apiKeyId],
    references: [apiKeys.id],
  }),
}));

// Bid line items relations
export const bidLineItemsRelations = relations(bidLineItems, ({ one }) => ({
  bid: one(bids, {
    fields: [bidLineItems.bidId],
    references: [bids.id],
  }),
}));

// Bid inclusions/exclusions relations
export const bidInclusionsExclusionsRelations = relations(bidInclusionsExclusions, ({ one }) => ({
  bid: one(bids, {
    fields: [bidInclusionsExclusions.bidId],
    references: [bids.id],
  }),
}));

// RFQ distribution relations
export const rfqDistributionRelations = relations(rfqDistribution, ({ one }) => ({
  rfq: one(rfqs, {
    fields: [rfqDistribution.rfqId],
    references: [rfqs.id],
  }),
  contractor: one(contractors, {
    fields: [rfqDistribution.contractorId],
    references: [contractors.id],
  }),
}));

// User Feedback table
export const userFeedback = pgTable("user_feedback", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  type: varchar("type").notNull(), // "bug" or "suggestion"
  message: text("message").notNull(),
  status: varchar("status").default("open"), // "open", "in_progress", "resolved", "closed"
  priority: varchar("priority").default("medium"), // "low", "medium", "high", "critical"
  adminNotes: text("admin_notes"),
  resolvedAt: timestamp("resolved_at"),
  resolvedBy: varchar("resolved_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_user_feedback_user_id").on(table.userId),
  index("IDX_user_feedback_type").on(table.type),
  index("IDX_user_feedback_status").on(table.status),
  index("IDX_user_feedback_created_at").on(table.createdAt),
]);

// User feedback relations
export const userFeedbackRelations = relations(userFeedback, ({ one }) => ({
  user: one(users, {
    fields: [userFeedback.userId],
    references: [users.id],
  }),
  resolvedByUser: one(users, {
    fields: [userFeedback.resolvedBy],
    references: [users.id],
  }),
}));

// Business audit log for tracking critical business events
export const businessAuditLog = pgTable("business_audit_log", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  eventType: varchar("event_type").notNull(), // 'file_upload', 'bid_action', 'rfq_creation', 'rfq_distribution'
  eventData: jsonb("event_data").notNull(), // Structured event-specific data
  resourceId: varchar("resource_id"), // RFQ ID, Bid ID, File ID, etc.
  ipAddress: varchar("ip_address"),
  userAgent: text("user_agent"),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("IDX_business_audit_user_id").on(table.userId),
  index("IDX_business_audit_event_type").on(table.eventType),
  index("IDX_business_audit_resource_id").on(table.resourceId),
  index("IDX_business_audit_created_at").on(table.createdAt),
]);

// Business audit log relations
export const businessAuditLogRelations = relations(businessAuditLog, ({ one }) => ({
  user: one(users, {
    fields: [businessAuditLog.userId],
    references: [users.id],
  }),
}));

// Notifications table for in-app and email notifications
export const notifications = pgTable("notifications", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  organizationId: uuid("organization_id").references(() => organizations.id),
  type: varchar("type").notNull(), // 'rfq_uploaded', 'bid_accepted', 'bid_rejected', etc.
  title: varchar("title").notNull(),
  message: text("message").notNull(),
  data: jsonb("data"), // Event-specific data for rendering
  priority: notificationPriorityEnum("priority").default("medium").notNull(),
  readAt: timestamp("read_at"),
  deliveredAt: timestamp("delivered_at"),
  createdAt: timestamp("created_at").defaultNow(),
  expiresAt: timestamp("expires_at"), // Optional expiration for notifications
}, (table) => [
  index("IDX_notifications_user_id_read").on(table.userId, table.readAt),
  index("IDX_notifications_org_created").on(table.organizationId, table.createdAt),
  index("IDX_notifications_type_created").on(table.type, table.createdAt),
  index("IDX_notifications_priority").on(table.priority),
  index("IDX_notifications_expires").on(table.expiresAt),
]);

// Notification preferences per user and notification type
export const notificationPreferences = pgTable("notification_preferences", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  type: varchar("type").notNull(), // Notification type this preference applies to
  inAppEnabled: boolean("in_app_enabled").default(true),
  emailEnabled: boolean("email_enabled").default(true),
  smsEnabled: boolean("sms_enabled").default(false),
  frequency: notificationFrequencyEnum("frequency").default("immediate"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => [
  index("IDX_notification_prefs_user_type").on(table.userId, table.type),
  index("IDX_notification_prefs_user_id").on(table.userId),
]);

// Notification delivery tracking for audit and analytics
export const notificationDeliveries = pgTable("notification_deliveries", {
  id: uuid("id").primaryKey().defaultRandom(),
  notificationId: uuid("notification_id").references(() => notifications.id).notNull(),
  deliveryMethod: deliveryMethodEnum("delivery_method").notNull(),
  recipient: varchar("recipient").notNull(), // Email address, phone number, or user ID
  status: deliveryStatusEnum("status").default("pending").notNull(),
  providerResponse: jsonb("provider_response"), // Response from email/SMS provider
  attemptedAt: timestamp("attempted_at").defaultNow(),
  deliveredAt: timestamp("delivered_at"),
  errorMessage: text("error_message"),
}, (table) => [
  index("IDX_deliveries_notification").on(table.notificationId),
  index("IDX_deliveries_status_attempted").on(table.status, table.attemptedAt),
  index("IDX_deliveries_method_status").on(table.deliveryMethod, table.status),
  index("IDX_deliveries_recipient").on(table.recipient),
]);

// Notification relations
export const notificationRelations = relations(notifications, ({ one, many }) => ({
  user: one(users, {
    fields: [notifications.userId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [notifications.organizationId],
    references: [organizations.id],
  }),
  deliveries: many(notificationDeliveries),
}));

export const notificationPreferencesRelations = relations(notificationPreferences, ({ one }) => ({
  user: one(users, {
    fields: [notificationPreferences.userId],
    references: [users.id],
  }),
}));

export const notificationDeliveryRelations = relations(notificationDeliveries, ({ one }) => ({
  notification: one(notifications, {
    fields: [notificationDeliveries.notificationId],
    references: [notifications.id],
  }),
}));

// Insert schemas
export const insertOrganizationSchema = createInsertSchema(organizations);
export const insertRoleAuditLogSchema = createInsertSchema(roleAuditLog);
export const insertAccessAuditLogSchema = createInsertSchema(accessAuditLog);
export const insertUserSchema = createInsertSchema(users);
export const insertWaitlistSchema = createInsertSchema(waitlist);
export const insertContractorSchema = createInsertSchema(contractors);
export const insertRfqSchema = createInsertSchema(rfqs);
export const insertBidSchema = createInsertSchema(bids);
export const insertBidDocumentSchema = createInsertSchema(bidDocuments);
export const insertContractorFavoriteSchema = createInsertSchema(contractorFavorites);
export const insertRfqDistributionSchema = createInsertSchema(rfqDistribution);
export const insertForecastMaterialSchema = createInsertSchema(forecastMaterials);
export const insertApiKeySchema = createInsertSchema(apiKeys);
export const insertApiKeyUsageSchema = createInsertSchema(apiKeyUsage);
export const insertBidLineItemSchema = createInsertSchema(bidLineItems);
export const insertBidInclusionsExclusionsSchema = createInsertSchema(bidInclusionsExclusions);
export const insertUserFeedbackSchema = createInsertSchema(userFeedback);
export const insertBusinessAuditLogSchema = createInsertSchema(businessAuditLog);
export const insertNotificationSchema = createInsertSchema(notifications);
export const insertNotificationPreferencesSchema = createInsertSchema(notificationPreferences);
export const insertNotificationDeliverySchema = createInsertSchema(notificationDeliveries);
export const insertRfqDocumentSchema = createInsertSchema(rfqDocuments);

// Export types
export type User = typeof users.$inferSelect;
export type InsertUser = typeof users.$inferInsert;
export type UpsertUser = typeof users.$inferInsert;
export type Waitlist = typeof waitlist.$inferSelect;
export type InsertWaitlist = typeof waitlist.$inferInsert;
export type Organization = typeof organizations.$inferSelect;
export type InsertOrganization = typeof organizations.$inferInsert;
export type Contractor = typeof contractors.$inferSelect;
export type InsertContractor = typeof contractors.$inferInsert;
export type Rfq = typeof rfqs.$inferSelect;
export type InsertRfq = typeof rfqs.$inferInsert;
export type Bid = typeof bids.$inferSelect;
export type InsertBid = typeof bids.$inferInsert;
export type BidDocument = typeof bidDocuments.$inferSelect;
export type InsertBidDocument = typeof bidDocuments.$inferInsert;
export type ForecastMaterial = typeof forecastMaterials.$inferSelect;
export type InsertForecastMaterial = typeof forecastMaterials.$inferInsert;
export type ApiKey = typeof apiKeys.$inferSelect;
export type InsertApiKey = typeof apiKeys.$inferInsert;
export type BidLineItem = typeof bidLineItems.$inferSelect;
export type InsertBidLineItem = typeof bidLineItems.$inferInsert;
export type BidInclusionsExclusions = typeof bidInclusionsExclusions.$inferSelect;
export type InsertBidInclusionsExclusions = typeof bidInclusionsExclusions.$inferInsert;
export type UserFeedback = typeof userFeedback.$inferSelect;
export type InsertUserFeedback = typeof userFeedback.$inferInsert;
export type BusinessAuditLog = typeof businessAuditLog.$inferSelect;
export type InsertBusinessAuditLog = typeof businessAuditLog.$inferInsert;
export type Notification = typeof notifications.$inferSelect;
export type InsertNotification = typeof notifications.$inferInsert;
export type NotificationPreferences = typeof notificationPreferences.$inferSelect;
export type InsertNotificationPreferences = typeof notificationPreferences.$inferInsert;
export type NotificationDelivery = typeof notificationDeliveries.$inferSelect;
export type InsertNotificationDelivery = typeof notificationDeliveries.$inferInsert;
export type RfqDocument = typeof rfqDocuments.$inferSelect;
export type InsertRfqDocument = typeof rfqDocuments.$inferInsert;
export type ContractorFavorite = typeof contractorFavorites.$inferSelect;
export type InsertContractorFavorite = typeof contractorFavorites.$inferInsert;
export type RfqDistribution = typeof rfqDistribution.$inferSelect;
export type InsertRfqDistribution = typeof rfqDistribution.$inferInsert;
export type ApiKeyUsage = typeof apiKeyUsage.$inferSelect;
export type InsertApiKeyUsage = typeof apiKeyUsage.$inferInsert;
export type RoleAuditLog = typeof roleAuditLog.$inferSelect;
export type InsertRoleAuditLog = typeof roleAuditLog.$inferInsert;
export type AccessAuditLog = typeof accessAuditLog.$inferSelect;
export type InsertAccessAuditLog = typeof accessAuditLog.$inferInsert;
export type RFQ = typeof rfqs.$inferSelect;
export type InsertRFQ = typeof rfqs.$inferInsert;
