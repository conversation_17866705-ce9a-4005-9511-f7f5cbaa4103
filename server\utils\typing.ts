// Lightweight typing helpers for Drizzle insert/update payloads
// Use these to coerce Zod-validated objects to Drizzle Insert* / Update* types

export function asInsert<T>(value: unknown): T {
  return value as T;
}

export function asUpdate<T>(value: Partial<T>): Partial<T> {
  return value as Partial<T>;
}

// Convenience helper to add updatedAt while keeping typing concise
export function withUpdatedAt<T extends { updatedAt?: Date }>(value: Partial<T>): Partial<T> {
  return { ...value, updatedAt: new Date() } as Partial<T>;
}
