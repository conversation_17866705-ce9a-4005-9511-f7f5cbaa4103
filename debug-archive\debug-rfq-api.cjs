const axios = require('axios');

async function debugRfqAPI() {
  console.log('🔍 Debugging /api/rfqs endpoint...');
  console.log('');
  
  try {
    // Make request to the API endpoint (this will fail due to auth, but will show us what's happening)
    const response = await axios.get('http://localhost:5000/api/rfqs', {
      timeout: 10000,
      validateStatus: function (status) {
        return status < 500; // Don't throw error for 4xx responses
      }
    });
    
    if (response.status === 200) {
      console.log('✅ API call successful!');
      console.log(`📋 Found ${response.data.length} RFQs:`);
      
      if (response.data.length > 0) {
        response.data.forEach((rfq, index) => {
          console.log(`  ${index + 1}. ${rfq.projectName} - ${rfq.status} (${rfq.id})`);
          console.log(`     Created by: ${rfq.createdBy}`);
          console.log(`     Organization: ${rfq.organizationId}`);
          console.log(`     Created: ${rfq.createdAt}`);
          console.log('');
        });
      } else {
        console.log('❌ No RFQs returned from API');
      }
    } else {
      console.log(`❌ API call failed with status: ${response.status}`);
      console.log(`   Response: ${response.data?.message || 'No message'}`);
      
      if (response.status === 401) {
        console.log('');
        console.log('💡 This is expected - the API requires authentication.');
        console.log('   The issue is likely in the backend logic, not authentication.');
      } else if (response.status === 403) {
        console.log('');
        console.log('🚨 User not associated with organization!');
        console.log('   This suggests the user record might not have an organizationId.');
      }
    }
    
  } catch (error) {
    console.log(`❌ Request failed: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Server is not running on localhost:5000');
    }
  }
  
  console.log('');
  console.log('🔍 DEBUGGING CHECKLIST:');
  console.log('');
  console.log('1. ✅ Server is running');
  console.log('2. ❓ User is authenticated (check browser network tab)');
  console.log('3. ❓ User has organizationId in database');
  console.log('4. ❓ RFQ was created with correct organizationId');
  console.log('5. ❓ Role-based filtering is working (StandardUser = see all RFQs)');
  console.log('');
  console.log('🎯 NEXT STEPS:');
  console.log('1. Open browser Developer Tools -> Network tab');
  console.log('2. Go to Dashboard or My RFQs page');
  console.log('3. Look for the /api/rfqs request');
  console.log('4. Check the response - is it empty array [] or error?');
  console.log('5. If empty array, the issue is in backend filtering');
  console.log('6. If error, check the error message');
  console.log('');
  console.log('📊 Expected behavior for StandardUser (contractor):');
  console.log('• Should see ALL RFQs in the system (cross-organization)');
  console.log('• No organization filtering should be applied');
  console.log('• Recent uploads should appear at the top');
}

debugRfqAPI();
